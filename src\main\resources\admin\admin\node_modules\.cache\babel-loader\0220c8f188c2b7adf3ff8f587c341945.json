{"remainingRequest": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\src\\views\\register.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\src\\views\\register.vue", "mtime": 1755434670143}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\babel.config.js", "mtime": 1711079474000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["data", "ruleForm", "forgetForm", "pageFlag", "tableName", "rules", "yuangongxingbieOptions", "yuangongbumenOptions", "yuangongzhiweiOptions", "mounted", "_this", "$route", "query", "table", "$storage", "get", "gonghao", "mima", "xing<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "lianxidianhua", "bumen", "zhiwei", "required", "message", "trigger", "split", "$http", "url", "method", "then", "_ref", "code", "$message", "error", "msg", "created", "destroyed", "methods", "changeRules", "name", "getUUID", "Date", "getTime", "close", "$router", "push", "path", "yuangongtouxiangUploadChange", "fileUrls", "yuangongchange1", "e", "conditionColumn", "_this2", "concat", "_ref2", "login", "_this3", "mima2", "replace", "RegExp", "$base", "$validate", "isMobile", "_ref3", "type", "duration", "onClose"], "sources": ["src/views/register.vue"], "sourcesContent": ["<template>\r\n\t<div>\r\n\t\t<div class=\"container\" :style='{\"minHeight\":\"100vh\",\"padding\":\"0px 180px 0px 0px\",\"alignItems\":\"center\",\"background\":\"url(http://codegen.caihongy.cn/20240129/17e5d014970b46b0a6da2629973d8845.png) no-repeat\",\"display\":\"flex\",\"width\":\"100%\",\"backgroundSize\":\"cover\",\"justifyContent\":\"flex-end\"}'>\r\n\t\t\t<el-form v-if=\"pageFlag=='register'\" :style='{\"padding\":\"20px\",\"boxShadow\":\"0 1px 20px rgba( 255,  255, 255, .8)\",\"margin\":\"20px 0\",\"borderRadius\":\"4px\",\"background\":\"#fff\",\"width\":\"400px\",\"height\":\"auto\"}' ref=\"rgsForm\" class=\"rgs-form\" :model=\"rgsForm\" :rules=\"rules\">\r\n\t\t\t\t<div v-if=\"true\" :style='{\"width\":\"100%\",\"margin\":\"0 0 10px 0\",\"lineHeight\":\"44px\",\"fontSize\":\"20px\",\"color\":\"#374254\",\"textAlign\":\"center\"}' class=\"title\">公司财物管理系统注册</div>\r\n\t\t\t\t<el-form-item :style='{\"width\":\"80%\",\"padding\":\"0\",\"margin\":\"0 auto 15px\",\"height\":\"auto\"}' class=\"list-item\" v-if=\"tableName=='yuangong'\">\r\n\t\t\t\t\t<div v-if=\"false\" :style='{\"width\":\"64px\",\"lineHeight\":\"44px\",\"fontSize\":\"14px\",\"position\":\"relative\",\"color\":\"rgba(64, 158, 255, 1)\"}' class=\"lable\" :class=\"changeRules('gonghao')?'required':''\">工号：</div>\r\n\t\t\t\t\t<el-input  v-model=\"ruleForm.gonghao\"  autocomplete=\"off\" placeholder=\"工号\"  type=\"text\"  />\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"width\":\"80%\",\"padding\":\"0\",\"margin\":\"0 auto 15px\",\"height\":\"auto\"}' class=\"list-item\" v-if=\"tableName=='yuangong'\">\r\n\t\t\t\t\t<div v-if=\"false\" :style='{\"width\":\"64px\",\"lineHeight\":\"44px\",\"fontSize\":\"14px\",\"position\":\"relative\",\"color\":\"rgba(64, 158, 255, 1)\"}' class=\"lable\" :class=\"changeRules('mima')?'required':''\">密码：</div>\r\n\t\t\t\t\t<el-input  v-model=\"ruleForm.mima\"  autocomplete=\"off\" placeholder=\"密码\"  type=\"password\"  />\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"width\":\"80%\",\"padding\":\"0\",\"margin\":\"0 auto 15px\",\"height\":\"auto\"}' class=\"list-item\" v-if=\"tableName=='yuangong'\">\r\n\t\t\t\t\t<div v-if=\"false\" :style='{\"width\":\"64px\",\"lineHeight\":\"44px\",\"fontSize\":\"14px\",\"position\":\"relative\",\"color\":\"rgba(64, 158, 255, 1)\"}' class=\"lable\" :class=\"changeRules('mima')?'required':''\">确认密码：</div>\r\n\t\t\t\t\t<el-input  v-model=\"ruleForm.mima2\" autocomplete=\"off\" placeholder=\"确认密码\" type=\"password\" />\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"width\":\"80%\",\"padding\":\"0\",\"margin\":\"0 auto 15px\",\"height\":\"auto\"}' class=\"list-item\" v-if=\"tableName=='yuangong'\">\r\n\t\t\t\t\t<div v-if=\"false\" :style='{\"width\":\"64px\",\"lineHeight\":\"44px\",\"fontSize\":\"14px\",\"position\":\"relative\",\"color\":\"rgba(64, 158, 255, 1)\"}' class=\"lable\" :class=\"changeRules('xingming')?'required':''\">姓名：</div>\r\n\t\t\t\t\t<el-input  v-model=\"ruleForm.xingming\"  autocomplete=\"off\" placeholder=\"姓名\"  type=\"text\"  />\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"width\":\"80%\",\"padding\":\"0\",\"margin\":\"0 auto 15px\",\"height\":\"auto\"}' class=\"list-item\" v-if=\"tableName=='yuangong'\">\r\n\t\t\t\t\t<div v-if=\"false\" :style='{\"width\":\"64px\",\"lineHeight\":\"44px\",\"fontSize\":\"14px\",\"position\":\"relative\",\"color\":\"rgba(64, 158, 255, 1)\"}' class=\"lable\" :class=\"changeRules('touxiang')?'required':''\">头像：</div>\r\n                    <file-upload\r\n                        tip=\"点击上传头像\"\r\n                        action=\"file/upload\"\r\n                        :limit=\"3\"\r\n                        :multiple=\"true\"\r\n                        :fileUrls=\"ruleForm.touxiang?ruleForm.touxiang:''\"\r\n                        @change=\"yuangongtouxiangUploadChange\"\r\n                    ></file-upload>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"width\":\"80%\",\"padding\":\"0\",\"margin\":\"0 auto 15px\",\"height\":\"auto\"}' class=\"list-item\" v-if=\"tableName=='yuangong'\">\r\n\t\t\t\t\t<div v-if=\"false\" :style='{\"width\":\"64px\",\"lineHeight\":\"44px\",\"fontSize\":\"14px\",\"position\":\"relative\",\"color\":\"rgba(64, 158, 255, 1)\"}' class=\"lable\" :class=\"changeRules('xingbie')?'required':''\">性别：</div>\r\n                    <el-select v-model=\"ruleForm.xingbie\" placeholder=\"请选择性别\" >\r\n                        <el-option\r\n                            v-for=\"(item,index) in yuangongxingbieOptions\"\r\n                            v-bind:key=\"index\"\r\n                            :label=\"item\"\r\n                            :value=\"item\">\r\n                        </el-option>\r\n                    </el-select>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"width\":\"80%\",\"padding\":\"0\",\"margin\":\"0 auto 15px\",\"height\":\"auto\"}' class=\"list-item\" v-if=\"tableName=='yuangong'\">\r\n\t\t\t\t\t<div v-if=\"false\" :style='{\"width\":\"64px\",\"lineHeight\":\"44px\",\"fontSize\":\"14px\",\"position\":\"relative\",\"color\":\"rgba(64, 158, 255, 1)\"}' class=\"lable\" :class=\"changeRules('lianxidianhua')?'required':''\">联系电话：</div>\r\n\t\t\t\t\t<el-input  v-model=\"ruleForm.lianxidianhua\"  autocomplete=\"off\" placeholder=\"联系电话\"  type=\"text\"  />\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<button :style='{\"border\":\"0\",\"cursor\":\"pointer\",\"padding\":\"0 10px\",\"margin\":\"20px auto 5px\",\"outline\":\"none\",\"color\":\"#fff\",\"borderRadius\":\"4px\",\"background\":\"#dc4e41\",\"display\":\"block\",\"width\":\"80%\",\"fontSize\":\"16px\",\"height\":\"44px\"}' type=\"button\" class=\"r-btn\" @click=\"login()\">注册</button>\r\n\t\t\t\t<div :style='{\"cursor\":\"pointer\",\"padding\":\"0 10%\",\"color\":\"rgba(159, 159, 159, 1)\",\"display\":\"inline-block\",\"lineHeight\":\"1\",\"fontSize\":\"12px\",\"textDecoration\":\"underline\"}' class=\"r-login\" @click=\"close()\">已有账号，直接登录</div>\r\n\t\t\t</el-form>\r\n\t\t\t\r\n\t\t</div>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\truleForm: {\r\n\t\t\t},\r\n\t\t\tforgetForm: {},\r\n            pageFlag : '',\r\n\t\t\ttableName:\"\",\r\n\t\t\trules: {},\r\n            yuangongxingbieOptions: [],\r\n            yuangongbumenOptions: [],\r\n            yuangongzhiweiOptions: [],\r\n\t\t};\r\n\t},\r\n\tmounted(){\r\n\t\tthis.pageFlag = this.$route.query.pageFlag\r\n\t\tif(this.$route.query.pageFlag=='register'){\r\n\t\t\t\r\n\t\t\tlet table = this.$storage.get(\"loginTable\");\r\n\t\t\tthis.tableName = table;\r\n\t\t\tif(this.tableName=='yuangong'){\r\n\t\t\t\tthis.ruleForm = {\r\n\t\t\t\t\tgonghao: '',\r\n\t\t\t\t\tmima: '',\r\n\t\t\t\t\txingming: '',\r\n\t\t\t\t\ttouxiang: '',\r\n\t\t\t\t\txingbie: '',\r\n\t\t\t\t\tlianxidianhua: '',\r\n\t\t\t\t\tbumen: '',\r\n\t\t\t\t\tzhiwei: '',\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif ('yuangong' == this.tableName) {\r\n\t\t\t\tthis.rules.gonghao = [{ required: true, message: '请输入工号', trigger: 'blur' }]\r\n\t\t\t}\r\n\t\t\tif ('yuangong' == this.tableName) {\r\n\t\t\t\tthis.rules.mima = [{ required: true, message: '请输入密码', trigger: 'blur' }]\r\n\t\t\t}\r\n\t\t\tif ('yuangong' == this.tableName) {\r\n\t\t\t\tthis.rules.xingming = [{ required: true, message: '请输入姓名', trigger: 'blur' }]\r\n\t\t\t}\r\n\t\t\tthis.yuangongxingbieOptions = \"男,女\".split(',')\r\n\t\t\tthis.$http({\r\n\t\t\t\turl: `option/bumen/bumen`,\r\n\t\t\t\tmethod: \"get\"\r\n\t\t\t}).then(({ data }) => {\r\n\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\tthis.yuangongbumenOptions = data.data;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t}\r\n\t},\r\n\tcreated() {\r\n\t},\r\n\tdestroyed() {\r\n\t\t  \t},\r\n\tmethods: {\r\n\t\tchangeRules(name){\r\n\t\t\tif(this.rules[name]){\r\n\t\t\t\treturn true\r\n\t\t\t}\r\n\t\t\treturn false\r\n\t\t},\r\n\t\t// 获取uuid\r\n\t\tgetUUID () {\r\n\t\t\treturn new Date().getTime();\r\n\t\t},\r\n\t\tclose(){\r\n\t\t\tthis.$router.push({ path: \"/login\" });\r\n\t\t},\r\n        yuangongtouxiangUploadChange(fileUrls) {\r\n            this.ruleForm.touxiang = fileUrls;\r\n        },\r\n\r\n        // 多级联动参数\r\n        yuangongchange1(e,conditionColumn){\r\n            this.ruleForm.zhiwei = '';\r\n            this.yuangongzhiweiOptions = [];\r\n            this.$http({\r\n                url: `option/zhiwei/zhiwei?conditionColumn=${conditionColumn}&conditionValue=${e}`,\r\n                method: \"get\"\r\n            }).then(({ data }) => {\r\n                if (data && data.code === 0) {\r\n                    this.yuangongzhiweiOptions = data.data;\r\n                } else {\r\n                    this.$message.error(data.msg);\r\n                }\r\n            });\r\n        },\r\n\r\n\r\n\t\t// 注册\r\n\t\tlogin() {\r\n\t\t\tvar url=this.tableName+\"/register\";\r\n\t\t\t\t\tif((!this.ruleForm.gonghao) && `yuangong` == this.tableName){\r\n\t\t\t\t\t\tthis.$message.error(`工号不能为空`);\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif((!this.ruleForm.mima) && `yuangong` == this.tableName){\r\n\t\t\t\t\t\tthis.$message.error(`密码不能为空`);\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif((this.ruleForm.mima!=this.ruleForm.mima2) && `yuangong` == this.tableName){\r\n\t\t\t\t\t\tthis.$message.error(`两次密码输入不一致`);\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif((!this.ruleForm.xingming) && `yuangong` == this.tableName){\r\n\t\t\t\t\t\tthis.$message.error(`姓名不能为空`);\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n            if(this.ruleForm.touxiang!=null) {\r\n                this.ruleForm.touxiang = this.ruleForm.touxiang.replace(new RegExp(this.$base.url,\"g\"),\"\");\r\n            }\r\n\t\t\t\t\tif(`yuangong` == this.tableName && this.ruleForm.lianxidianhua &&(!this.$validate.isMobile(this.ruleForm.lianxidianhua))){\r\n\t\t\t\t\t\tthis.$message.error(`联系电话应输入手机格式`);\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\tthis.$http({\r\n\t\t\t\turl: url,\r\n\t\t\t\tmethod: \"post\",\r\n\t\t\t\tdata:this.ruleForm\r\n\t\t\t}).then(({ data }) => {\r\n\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\tmessage: \"注册成功\",\r\n\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\tduration: 1500,\r\n\t\t\t\t\t\tonClose: () => {\r\n\t\t\t\t\t\t\tthis.$router.replace({ path: \"/login\" });\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.container {\r\n\t  position: relative;\r\n\t  background: url(http://codegen.caihongy.cn/20240129/17e5d014970b46b0a6da2629973d8845.png) no-repeat;\r\n\r\n\t\t.el-date-editor.el-input {\r\n\t\t  width: 100%;\r\n\t\t}\r\n\t\t\r\n\t\t.rgs-form .el-input /deep/ .el-input__inner {\r\n\t\t\t\t\t\tborder: 1px solid rgba(167, 180, 201,.3)    ;\r\n\t\t\t\t\t\tborder-radius: 0;\r\n\t\t\t\t\t\tpadding: 0 10px;\r\n\t\t\t\t\t\toutline: none;\r\n\t\t\t\t\t\tcolor: #a7b4c9 ;\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\tfont-size: 14px;\r\n\t\t\t\t\t\theight: 44px;\r\n\t\t\t\t\t}\r\n\t\t\r\n\t\t.rgs-form .el-select /deep/ .el-input__inner {\r\n\t\t\t\t\t\tborder: 1px solid rgba(167, 180, 201,.3)    ;\r\n\t\t\t\t\t\tborder-radius: 0;\r\n\t\t\t\t\t\tpadding: 0 10px;\r\n\t\t\t\t\t\toutline: none;\r\n\t\t\t\t\t\tcolor: #a7b4c9 ;\r\n\t\t\t\t\t\twidth: 288px;\r\n\t\t\t\t\t\tfont-size: 14px;\r\n\t\t\t\t\t\theight: 44px;\r\n\t\t\t\t\t}\r\n\t\t\r\n\t\t.rgs-form .el-date-editor /deep/ .el-input__inner {\r\n\t\t\t\t\t\tborder: 1px solid rgba(167, 180, 201,.3)    ;\r\n\t\t\t\t\t\tborder-radius: 0;\r\n\t\t\t\t\t\tpadding: 0 10px 0 30px;\r\n\t\t\t\t\t\toutline: none;\r\n\t\t\t\t\t\tcolor: #a7b4c9 ;\r\n\t\t\t\t\t\twidth: 288px;\r\n\t\t\t\t\t\tfont-size: 14px;\r\n\t\t\t\t\t\theight: 44px;\r\n\t\t\t\t\t}\r\n\t\t\r\n\t\t.rgs-form .el-date-editor /deep/ .el-input__inner {\r\n\t\t\t\t\t\tborder: 1px solid rgba(167, 180, 201,.3)    ;\r\n\t\t\t\t\t\tborder-radius: 0;\r\n\t\t\t\t\t\tpadding: 0 10px 0 30px;\r\n\t\t\t\t\t\toutline: none;\r\n\t\t\t\t\t\tcolor: #a7b4c9 ;\r\n\t\t\t\t\t\twidth: 288px;\r\n\t\t\t\t\t\tfont-size: 14px;\r\n\t\t\t\t\t\theight: 44px;\r\n\t\t\t\t\t}\r\n\t\t\r\n\t\t.rgs-form /deep/ .el-upload--picture-card {\r\n\t\t\tbackground: transparent;\r\n\t\t\tborder: 0;\r\n\t\t\tborder-radius: 0;\r\n\t\t\twidth: auto;\r\n\t\t\theight: auto;\r\n\t\t\tline-height: initial;\r\n\t\t\tvertical-align: middle;\r\n\t\t}\r\n\t\t\r\n\t\t.rgs-form /deep/ .upload .upload-img {\r\n\t\t  \t\t  border: 1px dashed  rgba(167, 180, 201,.3)    ;\r\n\t\t  \t\t  cursor: pointer;\r\n\t\t  \t\t  border-radius: 8px;\r\n\t\t  \t\t  color: rgba(167, 180, 201,.3);\r\n\t\t  \t\t  width: 160px;\r\n\t\t  \t\t  font-size: 32px;\r\n\t\t  \t\t  line-height: 160px;\r\n\t\t  \t\t  text-align: center;\r\n\t\t  \t\t  height: 160px;\r\n\t\t  \t\t}\r\n\t\t\r\n\t\t.rgs-form /deep/ .el-upload-list .el-upload-list__item {\r\n\t\t  \t\t  border: 1px dashed  rgba(167, 180, 201,.3)    ;\r\n\t\t  \t\t  cursor: pointer;\r\n\t\t  \t\t  border-radius: 8px;\r\n\t\t  \t\t  color: rgba(167, 180, 201,.3);\r\n\t\t  \t\t  width: 160px;\r\n\t\t  \t\t  font-size: 32px;\r\n\t\t  \t\t  line-height: 160px;\r\n\t\t  \t\t  text-align: center;\r\n\t\t  \t\t  height: 160px;\r\n\t\t  \t\t}\r\n\t\t\r\n\t\t.rgs-form /deep/ .el-upload .el-icon-plus {\r\n\t\t  \t\t  border: 1px dashed  rgba(167, 180, 201,.3)    ;\r\n\t\t  \t\t  cursor: pointer;\r\n\t\t  \t\t  border-radius: 8px;\r\n\t\t  \t\t  color: rgba(167, 180, 201,.3);\r\n\t\t  \t\t  width: 160px;\r\n\t\t  \t\t  font-size: 32px;\r\n\t\t  \t\t  line-height: 160px;\r\n\t\t  \t\t  text-align: center;\r\n\t\t  \t\t  height: 160px;\r\n\t\t  \t\t}\r\n\t}\r\n\t.required {\r\n\t\tposition: relative;\r\n\t}\r\n\t.required::after{\r\n\t\t\t\tcolor: red;\r\n\t\t\t\tleft: -10px;\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tcontent: \"*\";\r\n\t\t\t}\r\n\t.editor>.avatar-uploader {\r\n\t\tline-height: 0;\r\n\t\theight: 0;\r\n\t}\r\n</style>\r\n"], "mappings": ";;;;;;;;AAwDA;EACAA,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA,GACA;MACAC,UAAA;MACAC,QAAA;MACAC,SAAA;MACAC,KAAA;MACAC,sBAAA;MACAC,oBAAA;MACAC,qBAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAP,QAAA,QAAAQ,MAAA,CAAAC,KAAA,CAAAT,QAAA;IACA,SAAAQ,MAAA,CAAAC,KAAA,CAAAT,QAAA;MAEA,IAAAU,KAAA,QAAAC,QAAA,CAAAC,GAAA;MACA,KAAAX,SAAA,GAAAS,KAAA;MACA,SAAAT,SAAA;QACA,KAAAH,QAAA;UACAe,OAAA;UACAC,IAAA;UACAC,QAAA;UACAC,QAAA;UACAC,OAAA;UACAC,aAAA;UACAC,KAAA;UACAC,MAAA;QACA;MACA;MACA,uBAAAnB,SAAA;QACA,KAAAC,KAAA,CAAAW,OAAA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;MACA,uBAAAtB,SAAA;QACA,KAAAC,KAAA,CAAAY,IAAA;UAAAO,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;MACA,uBAAAtB,SAAA;QACA,KAAAC,KAAA,CAAAa,QAAA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;MACA,KAAApB,sBAAA,SAAAqB,KAAA;MACA,KAAAC,KAAA;QACAC,GAAA;QACAC,MAAA;MACA,GAAAC,IAAA,WAAAC,IAAA;QAAA,IAAAhC,IAAA,GAAAgC,IAAA,CAAAhC,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAiC,IAAA;UACAvB,KAAA,CAAAH,oBAAA,GAAAP,IAAA,CAAAA,IAAA;QACA;UACAU,KAAA,CAAAwB,QAAA,CAAAC,KAAA,CAAAnC,IAAA,CAAAoC,GAAA;QACA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA,GACA;EACAC,SAAA,WAAAA,UAAA,GACA;EACAC,OAAA;IACAC,WAAA,WAAAA,YAAAC,IAAA;MACA,SAAApC,KAAA,CAAAoC,IAAA;QACA;MACA;MACA;IACA;IACA;IACAC,OAAA,WAAAA,QAAA;MACA,WAAAC,IAAA,GAAAC,OAAA;IACA;IACAC,KAAA,WAAAA,MAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;QAAAC,IAAA;MAAA;IACA;IACAC,4BAAA,WAAAA,6BAAAC,QAAA;MACA,KAAAjD,QAAA,CAAAkB,QAAA,GAAA+B,QAAA;IACA;IAEA;IACAC,eAAA,WAAAA,gBAAAC,CAAA,EAAAC,eAAA;MAAA,IAAAC,MAAA;MACA,KAAArD,QAAA,CAAAsB,MAAA;MACA,KAAAf,qBAAA;MACA,KAAAoB,KAAA;QACAC,GAAA,0CAAA0B,MAAA,CAAAF,eAAA,sBAAAE,MAAA,CAAAH,CAAA;QACAtB,MAAA;MACA,GAAAC,IAAA,WAAAyB,KAAA;QAAA,IAAAxD,IAAA,GAAAwD,KAAA,CAAAxD,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAiC,IAAA;UACAqB,MAAA,CAAA9C,qBAAA,GAAAR,IAAA,CAAAA,IAAA;QACA;UACAsD,MAAA,CAAApB,QAAA,CAAAC,KAAA,CAAAnC,IAAA,CAAAoC,GAAA;QACA;MACA;IACA;IAGA;IACAqB,KAAA,WAAAA,MAAA;MAAA,IAAAC,MAAA;MACA,IAAA7B,GAAA,QAAAzB,SAAA;MACA,UAAAH,QAAA,CAAAe,OAAA,uBAAAZ,SAAA;QACA,KAAA8B,QAAA,CAAAC,KAAA;QACA;MACA;MACA,UAAAlC,QAAA,CAAAgB,IAAA,uBAAAb,SAAA;QACA,KAAA8B,QAAA,CAAAC,KAAA;QACA;MACA;MACA,SAAAlC,QAAA,CAAAgB,IAAA,SAAAhB,QAAA,CAAA0D,KAAA,uBAAAvD,SAAA;QACA,KAAA8B,QAAA,CAAAC,KAAA;QACA;MACA;MACA,UAAAlC,QAAA,CAAAiB,QAAA,uBAAAd,SAAA;QACA,KAAA8B,QAAA,CAAAC,KAAA;QACA;MACA;MACA,SAAAlC,QAAA,CAAAkB,QAAA;QACA,KAAAlB,QAAA,CAAAkB,QAAA,QAAAlB,QAAA,CAAAkB,QAAA,CAAAyC,OAAA,KAAAC,MAAA,MAAAC,KAAA,CAAAjC,GAAA;MACA;MACA,uBAAAzB,SAAA,SAAAH,QAAA,CAAAoB,aAAA,UAAA0C,SAAA,CAAAC,QAAA,MAAA/D,QAAA,CAAAoB,aAAA;QACA,KAAAa,QAAA,CAAAC,KAAA;QACA;MACA;MACA,KAAAP,KAAA;QACAC,GAAA,EAAAA,GAAA;QACAC,MAAA;QACA9B,IAAA,OAAAC;MACA,GAAA8B,IAAA,WAAAkC,KAAA;QAAA,IAAAjE,IAAA,GAAAiE,KAAA,CAAAjE,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAiC,IAAA;UACAyB,MAAA,CAAAxB,QAAA;YACAT,OAAA;YACAyC,IAAA;YACAC,QAAA;YACAC,OAAA,WAAAA,QAAA;cACAV,MAAA,CAAAZ,OAAA,CAAAc,OAAA;gBAAAZ,IAAA;cAAA;YACA;UACA;QACA;UACAU,MAAA,CAAAxB,QAAA,CAAAC,KAAA,CAAAnC,IAAA,CAAAoC,GAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}