{"remainingRequest": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\src\\components\\common\\FileUpload.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\src\\components\\common\\FileUpload.vue", "mtime": 1711079474000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\babel.config.js", "mtime": 1711079474000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["storage", "base", "data", "dialogVisible", "dialogImageUrl", "fileList", "fileUrlList", "myHeaders", "props", "mounted", "init", "get", "watch", "fileUrls", "val", "oldVal", "computed", "getActionUrl", "concat", "$base", "name", "action", "methods", "split", "fileArray", "for<PERSON>ach", "item", "index", "url", "file", "push", "setFileList", "handleBeforeUpload", "handleUploadSuccess", "res", "code", "length", "response", "$emit", "join", "$message", "error", "msg", "handleUploadErr", "err", "handleRemove", "handleUploadPreview", "handleExceed", "files", "warning", "limit", "fileUrlArray", "token", "_this", "startsWith"], "sources": ["src/components/common/FileUpload.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- 上传文件组件 -->\r\n    <el-upload\r\n      ref=\"upload\"\r\n      :action=\"getActionUrl\"\r\n      list-type=\"picture-card\"\r\n      :multiple=\"multiple\"\r\n      :limit=\"limit\"\r\n      :headers=\"myHeaders\"\r\n      :file-list=\"fileList\"\r\n      :on-exceed=\"handleExceed\"\r\n      :on-preview=\"handleUploadPreview\"\r\n      :on-remove=\"handleRemove\"\r\n      :on-success=\"handleUploadSuccess\"\r\n      :on-error=\"handleUploadErr\"\r\n      :before-upload=\"handleBeforeUpload\"\r\n    >\r\n      <i class=\"el-icon-plus\"></i>\r\n      <div slot=\"tip\" class=\"el-upload__tip\" style=\"color:#838fa1;\">{{tip}}</div>\r\n    </el-upload>\r\n    <el-dialog :visible.sync=\"dialogVisible\" size=\"tiny\" append-to-body>\r\n      <img width=\"100%\" :src=\"dialogImageUrl\" alt>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n<script>\r\nimport storage from \"@/utils/storage\";\r\nimport base from \"@/utils/base\";\r\nexport default {\r\n  data() {\r\n    return {\r\n      // 查看大图\r\n      dialogVisible: false,\r\n      // 查看大图\r\n      dialogImageUrl: \"\",\r\n      // 组件渲染图片的数组字段，有特殊格式要求\r\n      fileList: [],\r\n      fileUrlList: [],\r\n      myHeaders:{}\r\n    };\r\n  },\r\n  props: [\"tip\", \"action\", \"limit\", \"multiple\", \"fileUrls\"],\r\n  mounted() {\r\n    this.init();\r\n    this.myHeaders= {\r\n      'Token':storage.get(\"Token\")\r\n    }\r\n  },\r\n  watch: {\r\n    fileUrls: function(val, oldVal) {\r\n      //   console.log(\"new: %s, old: %s\", val, oldVal);\r\n      this.init();\r\n    }\r\n  },\r\n  computed: {\r\n    // 计算属性的 getter\r\n    getActionUrl: function() {\r\n      // return base.url + this.action + \"?token=\" + storage.get(\"token\");\r\n      return `/${this.$base.name}/` + this.action;\r\n    }\r\n  },\r\n  methods: {\r\n    // 初始化\r\n    init() {\r\n      //   console.log(this.fileUrls);\r\n      if (this.fileUrls) {\r\n        this.fileUrlList = this.fileUrls.split(\",\");\r\n        let fileArray = [];\r\n        this.fileUrlList.forEach(function(item, index) {\r\n          var url = item;\r\n          var name = index;\r\n          var file = {\r\n            name: name,\r\n            url: url\r\n          };\r\n          fileArray.push(file);\r\n        });\r\n        this.setFileList(fileArray);\r\n      }\r\n    },\r\n    handleBeforeUpload(file) {\r\n\t\r\n    },\r\n    // 上传文件成功后执行\r\n    handleUploadSuccess(res, file, fileList) {\r\n      if (res && res.code === 0) {\r\n        fileList[fileList.length - 1][\"url\"] = \"upload/\" + file.response.file;\r\n        this.setFileList(fileList);\r\n        this.$emit(\"change\", this.fileUrlList.join(\",\"));\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n    // 图片上传失败\r\n    handleUploadErr(err, file, fileList) {\r\n      this.$message.error(\"文件上传失败\");\r\n    },\r\n    // 移除图片\r\n    handleRemove(file, fileList) {\r\n      this.setFileList(fileList);\r\n      this.$emit(\"change\", this.fileUrlList.join(\",\"));\r\n    },\r\n    // 查看大图\r\n    handleUploadPreview(file) {\r\n      this.dialogImageUrl = file.url;\r\n      this.dialogVisible = true;\r\n    },\r\n    // 限制图片数量\r\n    handleExceed(files, fileList) {\r\n      this.$message.warning(`最多上传${this.limit}张图片`);\r\n    },\r\n    // 重新对fileList进行赋值\r\n    setFileList(fileList) {\r\n      var fileArray = [];\r\n      var fileUrlArray = [];\r\n      // 有些图片不是公开的，所以需要携带token信息做权限校验\r\n      var token = storage.get(\"token\");\r\n      let _this = this;\r\n      fileList.forEach(function(item, index) {\r\n        var url = item.url.split(\"?\")[0];\r\n\tif(!url.startsWith(\"http\")) {\r\n\t  url = _this.$base.url+url\r\n\t}\r\n        var name = item.name;\r\n        var file = {\r\n          name: name,\r\n          url: url + \"?token=\" + token\r\n        };\r\n        fileArray.push(file);\r\n        fileUrlArray.push(url);\r\n      });\r\n      this.fileList = fileArray;\r\n      this.fileUrlList = fileUrlArray;\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n</style>\r\n"], "mappings": ";;;;;;;;;AA2BA,OAAAA,OAAA;AACA,OAAAC,IAAA;AACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,aAAA;MACA;MACAC,cAAA;MACA;MACAC,QAAA;MACAC,WAAA;MACAC,SAAA;IACA;EACA;EACAC,KAAA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,IAAA;IACA,KAAAH,SAAA;MACA,SAAAP,OAAA,CAAAW,GAAA;IACA;EACA;EACAC,KAAA;IACAC,QAAA,WAAAA,SAAAC,GAAA,EAAAC,MAAA;MACA;MACA,KAAAL,IAAA;IACA;EACA;EACAM,QAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA;MACA,WAAAC,MAAA,MAAAC,KAAA,CAAAC,IAAA,cAAAC,MAAA;IACA;EACA;EACAC,OAAA;IACA;IACAZ,IAAA,WAAAA,KAAA;MACA;MACA,SAAAG,QAAA;QACA,KAAAP,WAAA,QAAAO,QAAA,CAAAU,KAAA;QACA,IAAAC,SAAA;QACA,KAAAlB,WAAA,CAAAmB,OAAA,WAAAC,IAAA,EAAAC,KAAA;UACA,IAAAC,GAAA,GAAAF,IAAA;UACA,IAAAN,IAAA,GAAAO,KAAA;UACA,IAAAE,IAAA;YACAT,IAAA,EAAAA,IAAA;YACAQ,GAAA,EAAAA;UACA;UACAJ,SAAA,CAAAM,IAAA,CAAAD,IAAA;QACA;QACA,KAAAE,WAAA,CAAAP,SAAA;MACA;IACA;IACAQ,kBAAA,WAAAA,mBAAAH,IAAA,GAEA;IACA;IACAI,mBAAA,WAAAA,oBAAAC,GAAA,EAAAL,IAAA,EAAAxB,QAAA;MACA,IAAA6B,GAAA,IAAAA,GAAA,CAAAC,IAAA;QACA9B,QAAA,CAAAA,QAAA,CAAA+B,MAAA,2BAAAP,IAAA,CAAAQ,QAAA,CAAAR,IAAA;QACA,KAAAE,WAAA,CAAA1B,QAAA;QACA,KAAAiC,KAAA,gBAAAhC,WAAA,CAAAiC,IAAA;MACA;QACA,KAAAC,QAAA,CAAAC,KAAA,CAAAP,GAAA,CAAAQ,GAAA;MACA;IACA;IACA;IACAC,eAAA,WAAAA,gBAAAC,GAAA,EAAAf,IAAA,EAAAxB,QAAA;MACA,KAAAmC,QAAA,CAAAC,KAAA;IACA;IACA;IACAI,YAAA,WAAAA,aAAAhB,IAAA,EAAAxB,QAAA;MACA,KAAA0B,WAAA,CAAA1B,QAAA;MACA,KAAAiC,KAAA,gBAAAhC,WAAA,CAAAiC,IAAA;IACA;IACA;IACAO,mBAAA,WAAAA,oBAAAjB,IAAA;MACA,KAAAzB,cAAA,GAAAyB,IAAA,CAAAD,GAAA;MACA,KAAAzB,aAAA;IACA;IACA;IACA4C,YAAA,WAAAA,aAAAC,KAAA,EAAA3C,QAAA;MACA,KAAAmC,QAAA,CAAAS,OAAA,4BAAA/B,MAAA,MAAAgC,KAAA;IACA;IACA;IACAnB,WAAA,WAAAA,YAAA1B,QAAA;MACA,IAAAmB,SAAA;MACA,IAAA2B,YAAA;MACA;MACA,IAAAC,KAAA,GAAApD,OAAA,CAAAW,GAAA;MACA,IAAA0C,KAAA;MACAhD,QAAA,CAAAoB,OAAA,WAAAC,IAAA,EAAAC,KAAA;QACA,IAAAC,GAAA,GAAAF,IAAA,CAAAE,GAAA,CAAAL,KAAA;QACA,KAAAK,GAAA,CAAA0B,UAAA;UACA1B,GAAA,GAAAyB,KAAA,CAAAlC,KAAA,CAAAS,GAAA,GAAAA,GAAA;QACA;QACA,IAAAR,IAAA,GAAAM,IAAA,CAAAN,IAAA;QACA,IAAAS,IAAA;UACAT,IAAA,EAAAA,IAAA;UACAQ,GAAA,EAAAA,GAAA,eAAAwB;QACA;QACA5B,SAAA,CAAAM,IAAA,CAAAD,IAAA;QACAsB,YAAA,CAAArB,IAAA,CAAAF,GAAA;MACA;MACA,KAAAvB,QAAA,GAAAmB,SAAA;MACA,KAAAlB,WAAA,GAAA6C,YAAA;IACA;EACA;AACA", "ignoreList": []}]}