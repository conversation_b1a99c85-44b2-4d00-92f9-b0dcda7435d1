{"remainingRequest": "D:\\project\\admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\project\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\project\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\admin\\src\\App.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\admin\\src\\App.vue", "mtime": 1711079474000}, {"path": "D:\\project\\admin\\babel.config.js", "mtime": 1711079474000}, {"path": "D:\\project\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\project\\admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\project\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\project\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\project\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJhcHAiCn07"}, {"version": 3, "names": ["name"], "sources": ["src/App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\" class=\"\">\n    <router-view></router-view>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"app\",\n};\n</script>\n\n<style lang=\"scss\">\n*{\n  padding: 0;\n  margin:0;\r\n  box-sizing: border-box;\n}\nhtml,body{\n  width: 100%;\n  height: 100%;\n}\n#app{\n  height:100%;\n}\nbody {\n  padding: 0;\n  margin: 0;\n  \n}\n</style>\n"], "mappings": "AAOA;EACAA,IAAA;AACA", "ignoreList": []}]}