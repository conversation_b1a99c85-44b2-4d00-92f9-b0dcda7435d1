{"remainingRequest": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\babel-loader\\lib\\index.js!G:\\Develop\\springboot\\公司财务管理系统\\admin\\src\\utils\\http.js", "dependencies": [{"path": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\src\\utils\\http.js", "mtime": 1711079474000}, {"path": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\babel.config.js", "mtime": 1711079474000}, {"path": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiOwppbXBvcnQgYXhpb3MgZnJvbSAnYXhpb3MnOwppbXBvcnQgcm91dGVyIGZyb20gJ0Avcm91dGVyL3JvdXRlci1zdGF0aWMnOwppbXBvcnQgc3RvcmFnZSBmcm9tICdAL3V0aWxzL3N0b3JhZ2UnOwp2YXIgaHR0cCA9IGF4aW9zLmNyZWF0ZSh7CiAgdGltZW91dDogMTAwMCAqIDg2NDAwLAogIHdpdGhDcmVkZW50aWFsczogdHJ1ZSwKICBiYXNlVVJMOiAnL3NwcmluZ2Jvb3QyZzQzdDNrMCcsCiAgaGVhZGVyczogewogICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uOyBjaGFyc2V0PXV0Zi04JwogIH0KfSk7Ci8vIOivt+axguaLpuaIqgpodHRwLmludGVyY2VwdG9ycy5yZXF1ZXN0LnVzZShmdW5jdGlvbiAoY29uZmlnKSB7CiAgY29uZmlnLmhlYWRlcnNbJ1Rva2VuJ10gPSBzdG9yYWdlLmdldCgnVG9rZW4nKTsgLy8g6K+35rGC5aS05bim5LiKdG9rZW4KICByZXR1cm4gY29uZmlnOwp9LCBmdW5jdGlvbiAoZXJyb3IpIHsKICByZXR1cm4gUHJvbWlzZS5yZWplY3QoZXJyb3IpOwp9KTsKLy8g5ZON5bqU5oum5oiqCmh0dHAuaW50ZXJjZXB0b3JzLnJlc3BvbnNlLnVzZShmdW5jdGlvbiAocmVzcG9uc2UpIHsKICBpZiAocmVzcG9uc2UuZGF0YSAmJiByZXNwb25zZS5kYXRhLmNvZGUgPT09IDQwMSkgewogICAgLy8gNDAxLCB0b2tlbuWkseaViAogICAgcm91dGVyLnB1c2goewogICAgICBuYW1lOiAnbG9naW4nCiAgICB9KTsKICB9CiAgcmV0dXJuIHJlc3BvbnNlOwp9LCBmdW5jdGlvbiAoZXJyb3IpIHsKICByZXR1cm4gUHJvbWlzZS5yZWplY3QoZXJyb3IpOwp9KTsKZXhwb3J0IGRlZmF1bHQgaHR0cDs="}, {"version": 3, "names": ["axios", "router", "storage", "http", "create", "timeout", "withCredentials", "baseURL", "headers", "interceptors", "request", "use", "config", "get", "error", "Promise", "reject", "response", "data", "code", "push", "name"], "sources": ["G:/Develop/springboot/公司财务管理系统/admin/src/utils/http.js"], "sourcesContent": ["import axios from 'axios'\r\nimport router from '@/router/router-static'\r\nimport storage from '@/utils/storage'\r\n\r\nconst http = axios.create({\r\n    timeout: 1000 * 86400,\r\n    withCredentials: true,\r\n    baseURL: '/springboot2g43t3k0',\r\n    headers: {\r\n        'Content-Type': 'application/json; charset=utf-8'\r\n    }\r\n})\r\n// 请求拦截\r\nhttp.interceptors.request.use(config => {\r\n    config.headers['Token'] = storage.get('Token') // 请求头带上token\r\n    return config\r\n}, error => {\r\n    return Promise.reject(error)\r\n})\r\n// 响应拦截\r\nhttp.interceptors.response.use(response => {\r\n    if (response.data && response.data.code === 401) { // 401, token失效\r\n        router.push({ name: 'login' })\r\n    }\r\n    return response\r\n}, error => {\r\n    return Promise.reject(error)\r\n})\r\nexport default http"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,wBAAwB;AAC3C,OAAOC,OAAO,MAAM,iBAAiB;AAErC,IAAMC,IAAI,GAAGH,KAAK,CAACI,MAAM,CAAC;EACtBC,OAAO,EAAE,IAAI,GAAG,KAAK;EACrBC,eAAe,EAAE,IAAI;EACrBC,OAAO,EAAE,qBAAqB;EAC9BC,OAAO,EAAE;IACL,cAAc,EAAE;EACpB;AACJ,CAAC,CAAC;AACF;AACAL,IAAI,CAACM,YAAY,CAACC,OAAO,CAACC,GAAG,CAAC,UAAAC,MAAM,EAAI;EACpCA,MAAM,CAACJ,OAAO,CAAC,OAAO,CAAC,GAAGN,OAAO,CAACW,GAAG,CAAC,OAAO,CAAC,EAAC;EAC/C,OAAOD,MAAM;AACjB,CAAC,EAAE,UAAAE,KAAK,EAAI;EACR,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAChC,CAAC,CAAC;AACF;AACAX,IAAI,CAACM,YAAY,CAACQ,QAAQ,CAACN,GAAG,CAAC,UAAAM,QAAQ,EAAI;EACvC,IAAIA,QAAQ,CAACC,IAAI,IAAID,QAAQ,CAACC,IAAI,CAACC,IAAI,KAAK,GAAG,EAAE;IAAE;IAC/ClB,MAAM,CAACmB,IAAI,CAAC;MAAEC,IAAI,EAAE;IAAQ,CAAC,CAAC;EAClC;EACA,OAAOJ,QAAQ;AACnB,CAAC,EAAE,UAAAH,KAAK,EAAI;EACR,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAChC,CAAC,CAAC;AACF,eAAeX,IAAI", "ignoreList": []}]}