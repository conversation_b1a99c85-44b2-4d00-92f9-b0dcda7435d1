{"remainingRequest": "D:\\project\\admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\project\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!D:\\project\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\project\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\admin\\src\\components\\index\\TagsView\\index.vue?vue&type=template&id=78b39507&scoped=true", "dependencies": [{"path": "D:\\project\\admin\\src\\components\\index\\TagsView\\index.vue", "mtime": 1711079474000}, {"path": "D:\\project\\admin\\babel.config.js", "mtime": 1711079474000}, {"path": "D:\\project\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\project\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\project\\admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\project\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\project\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\project\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\project\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "style", "attrs", "ref", "_l", "visitedViews", "tag", "key", "path", "refInFor", "class", "isActive", "query", "fullPath", "nativeOn", "mouseup", "$event", "button", "closeSelectedTag", "contextmenu", "preventDefault", "openMenu", "_v", "_s", "name", "meta", "affix", "on", "click", "stopPropagation", "_e", "directives", "rawName", "value", "visible", "expression", "left", "top", "selectedTag", "closeAllTags", "staticRenderFns"], "sources": ["D:/project/admin/src/components/index/TagsView/index.vue"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"tags-view-container\",style:({\"padding\":\"4px 30px\",\"margin\":\"0px 0 0px\",\"borderColor\":\"#d8dce5\",\"background\":\"none\",\"borderWidth\":\"0 0 1px\",\"width\":\"100%\",\"borderStyle\":\"solid\",\"height\":\"34px\"}),attrs:{\"id\":\"tags-view-container\"}},[_c('scroll-pane',{ref:\"scrollPane\",staticClass:\"tags-view-wrapper\"},[_c('div',{staticClass:\"tags-view-box\",style:({\"width\":\"100%\",\"whiteSpace\":\"nowrap\",\"position\":\"relative\",\"background\":\"none\"})},_vm._l((_vm.visitedViews),function(tag){return _c('router-link',{key:tag.path,ref:\"tag\",refInFor:true,staticClass:\"tags-view-item\",class:_vm.isActive(tag)?'active':'',attrs:{\"to\":{ path: tag.path, query: tag.query, fullPath: tag.fullPath },\"tag\":\"span\"},nativeOn:{\"mouseup\":function($event){if('button' in $event && $event.button !== 1)return null;return _vm.closeSelectedTag(tag)},\"contextmenu\":function($event){$event.preventDefault();return _vm.openMenu(tag,$event)}}},[_c('span',{staticClass:\"text\"},[_vm._v(_vm._s(tag.name))]),(!tag.meta.affix)?_c('span',{staticClass:\"el-icon-close\",on:{\"click\":function($event){$event.preventDefault();$event.stopPropagation();return _vm.closeSelectedTag(tag)}}}):_vm._e()])}),1)]),_c('ul',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.visible),expression:\"visible\"}],staticClass:\"contextmenu\",style:({left:_vm.left+'px',top:_vm.top+'px'})},[(!(_vm.selectedTag.meta&&_vm.selectedTag.meta.affix))?_c('li',{on:{\"click\":function($event){return _vm.closeSelectedTag(_vm.selectedTag)}}},[_vm._v(\"Close\")]):_vm._e(),_c('li',{on:{\"click\":function($event){return _vm.closeAllTags(_vm.selectedTag)}}},[_vm._v(\"Close All\")])])],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"], "mappings": ";AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAE;EAAC,IAAIC,GAAG,GAAC,IAAI;IAACC,EAAE,GAACD,GAAG,CAACE,KAAK,CAACD,EAAE;EAAC,OAAOA,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC,qBAAqB;IAACC,KAAK,EAAE;MAAC,SAAS,EAAC,UAAU;MAAC,QAAQ,EAAC,WAAW;MAAC,aAAa,EAAC,SAAS;MAAC,YAAY,EAAC,MAAM;MAAC,aAAa,EAAC,SAAS;MAAC,OAAO,EAAC,MAAM;MAAC,aAAa,EAAC,OAAO;MAAC,QAAQ,EAAC;IAAM,CAAE;IAACC,KAAK,EAAC;MAAC,IAAI,EAAC;IAAqB;EAAC,CAAC,EAAC,CAACJ,EAAE,CAAC,aAAa,EAAC;IAACK,GAAG,EAAC,YAAY;<PERSON>AA<PERSON>,WAAW,EAAC;EAAmB,CAAC,EAAC,CAACF,EAAE,CAAC,KAAK,EAAC;IAACE,WAAW,EAAC,eAAe;IAACC,KAAK,EAAE;MAAC,OAAO,EAAC,MAAM;MAAC,YAAY,EAAC,QAAQ;MAAC,UAAU,EAAC,UAAU;MAAC,YAAY,EAAC;IAAM;EAAE,CAAC,EAACJ,GAAG,CAACO,EAAE,CAAEP,GAAG,CAACQ,YAAY,EAAE,UAASC,GAAG,EAAC;IAAC,OAAOR,EAAE,CAAC,aAAa,EAAC;MAACS,GAAG,EAACD,GAAG,CAACE,IAAI;MAACL,GAAG,EAAC,KAAK;MAACM,QAAQ,EAAC,IAAI;MAACT,WAAW,EAAC,gBAAgB;MAACU,KAAK,EAACb,GAAG,CAACc,QAAQ,CAACL,GAAG,CAAC,GAAC,QAAQ,GAAC,EAAE;MAACJ,KAAK,EAAC;QAAC,IAAI,EAAC;UAAEM,IAAI,EAAEF,GAAG,CAACE,IAAI;UAAEI,KAAK,EAAEN,GAAG,CAACM,KAAK;UAAEC,QAAQ,EAAEP,GAAG,CAACO;QAAS,CAAC;QAAC,KAAK,EAAC;MAAM,CAAC;MAACC,QAAQ,EAAC;QAAC,SAAS,EAAC,SAAVC,OAASA,CAAUC,MAAM,EAAC;UAAC,IAAG,QAAQ,IAAIA,MAAM,IAAIA,MAAM,CAACC,MAAM,KAAK,CAAC,EAAC,OAAO,IAAI;UAAC,OAAOpB,GAAG,CAACqB,gBAAgB,CAACZ,GAAG,CAAC;QAAA,CAAC;QAAC,aAAa,EAAC,SAAda,WAAaA,CAAUH,MAAM,EAAC;UAACA,MAAM,CAACI,cAAc,CAAC,CAAC;UAAC,OAAOvB,GAAG,CAACwB,QAAQ,CAACf,GAAG,EAACU,MAAM,CAAC;QAAA;MAAC;IAAC,CAAC,EAAC,CAAClB,EAAE,CAAC,MAAM,EAAC;MAACE,WAAW,EAAC;IAAM,CAAC,EAAC,CAACH,GAAG,CAACyB,EAAE,CAACzB,GAAG,CAAC0B,EAAE,CAACjB,GAAG,CAACkB,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAClB,GAAG,CAACmB,IAAI,CAACC,KAAK,GAAE5B,EAAE,CAAC,MAAM,EAAC;MAACE,WAAW,EAAC,eAAe;MAAC2B,EAAE,EAAC;QAAC,OAAO,EAAC,SAARC,KAAOA,CAAUZ,MAAM,EAAC;UAACA,MAAM,CAACI,cAAc,CAAC,CAAC;UAACJ,MAAM,CAACa,eAAe,CAAC,CAAC;UAAC,OAAOhC,GAAG,CAACqB,gBAAgB,CAACZ,GAAG,CAAC;QAAA;MAAC;IAAC,CAAC,CAAC,GAACT,GAAG,CAACiC,EAAE,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,EAAChC,EAAE,CAAC,IAAI,EAAC;IAACiC,UAAU,EAAC,CAAC;MAACP,IAAI,EAAC,MAAM;MAACQ,OAAO,EAAC,QAAQ;MAACC,KAAK,EAAEpC,GAAG,CAACqC,OAAQ;MAACC,UAAU,EAAC;IAAS,CAAC,CAAC;IAACnC,WAAW,EAAC,aAAa;IAACC,KAAK,EAAE;MAACmC,IAAI,EAACvC,GAAG,CAACuC,IAAI,GAAC,IAAI;MAACC,GAAG,EAACxC,GAAG,CAACwC,GAAG,GAAC;IAAI;EAAE,CAAC,EAAC,CAAE,EAAExC,GAAG,CAACyC,WAAW,CAACb,IAAI,IAAE5B,GAAG,CAACyC,WAAW,CAACb,IAAI,CAACC,KAAK,CAAC,GAAE5B,EAAE,CAAC,IAAI,EAAC;IAAC6B,EAAE,EAAC;MAAC,OAAO,EAAC,SAARC,KAAOA,CAAUZ,MAAM,EAAC;QAAC,OAAOnB,GAAG,CAACqB,gBAAgB,CAACrB,GAAG,CAACyC,WAAW,CAAC;MAAA;IAAC;EAAC,CAAC,EAAC,CAACzC,GAAG,CAACyB,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,GAACzB,GAAG,CAACiC,EAAE,CAAC,CAAC,EAAChC,EAAE,CAAC,IAAI,EAAC;IAAC6B,EAAE,EAAC;MAAC,OAAO,EAAC,SAARC,KAAOA,CAAUZ,MAAM,EAAC;QAAC,OAAOnB,GAAG,CAAC0C,YAAY,CAAC1C,GAAG,CAACyC,WAAW,CAAC;MAAA;IAAC;EAAC,CAAC,EAAC,CAACzC,GAAG,CAACyB,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC;AACtpD,CAAC;AACD,IAAIkB,eAAe,GAAG,EAAE;AAExB,SAAS5C,MAAM,EAAE4C,eAAe", "ignoreList": []}]}