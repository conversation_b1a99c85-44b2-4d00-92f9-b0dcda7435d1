{"remainingRequest": "D:\\project\\admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\project\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\project\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\admin\\src\\components\\index\\TagsView\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\admin\\src\\components\\index\\TagsView\\index.vue", "mtime": 1711079474000}, {"path": "D:\\project\\admin\\babel.config.js", "mtime": 1711079474000}, {"path": "D:\\project\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\project\\admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\project\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\project\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\project\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["ScrollPane", "path", "generateTitle", "menu", "routes", "components", "data", "visible", "top", "left", "selectedTag", "affixTags", "computed", "visitedViews", "$store", "state", "tagsView", "watch", "$route", "addTags", "moveToCurrentTag", "value", "document", "body", "addEventListener", "closeMenu", "removeEventListener", "mounted", "initTags", "created", "_this", "menuList", "menus", "list", "params", "page", "limit", "sort", "$http", "url", "method", "then", "_ref", "code", "JSON", "parse", "<PERSON><PERSON><PERSON>", "$storage", "set", "role", "get", "i", "length", "<PERSON><PERSON><PERSON>", "backMenu", "concat", "methods", "isActive", "route", "filterAffixTags", "_this2", "basePath", "arguments", "undefined", "tags", "for<PERSON>ach", "meta", "affix", "tagPath", "resolve", "push", "fullPath", "name", "_objectSpread", "children", "tempTags", "_toConsumableArray", "_iterator", "_createForOfIteratorHelper", "_step", "s", "n", "done", "tag", "dispatch", "err", "e", "f", "_this3", "$refs", "$nextTick", "_iterator2", "_step2", "to", "scrollPane", "move<PERSON><PERSON><PERSON>arget", "refreshSelectedTag", "view", "_this4", "$router", "replace", "closeSelectedTag", "_this5", "_ref2", "toLastView", "closeOthersTags", "_this6", "closeAllTags", "_this7", "_ref3", "some", "latestView", "slice", "openMenu", "menu<PERSON>in<PERSON>idth", "offsetLeft", "$el", "getBoundingClientRect", "offsetWidth", "maxLeft", "clientX", "clientY"], "sources": ["src/components/index/TagsView/index.vue"], "sourcesContent": ["<template>\r\n\t<div id=\"tags-view-container\" class=\"tags-view-container\" :style='{\"padding\":\"4px 30px\",\"margin\":\"0px 0 0px\",\"borderColor\":\"#d8dce5\",\"background\":\"none\",\"borderWidth\":\"0 0 1px\",\"width\":\"100%\",\"borderStyle\":\"solid\",\"height\":\"34px\"}'>\r\n\t\t<scroll-pane ref=\"scrollPane\" class=\"tags-view-wrapper\">\r\n\t\t\t<div class=\"tags-view-box\" :style='{\"width\":\"100%\",\"whiteSpace\":\"nowrap\",\"position\":\"relative\",\"background\":\"none\"}'>\r\n\t\t\t\t<router-link v-for=\"tag in visitedViews\" ref=\"tag\" :key=\"tag.path\" :class=\"isActive(tag)?'active':''\"\r\n\t\t\t\t\t:to=\"{ path: tag.path, query: tag.query, fullPath: tag.fullPath }\" tag=\"span\" class=\"tags-view-item\"\r\n\t\t\t\t\*********************=\"closeSelectedTag(tag)\" @contextmenu.prevent.native=\"openMenu(tag,$event)\">\r\n\t\t\t\t\t<span class=\"text\">{{ tag.name }}</span>\r\n\t\t\t\t\t<span v-if=\"!tag.meta.affix\" class=\"el-icon-close\" @click.prevent.stop=\"closeSelectedTag(tag)\" />\r\n\t\t\t\t</router-link>\r\n\t\t\t</div>\r\n\t\t</scroll-pane>\r\n\t\t<ul v-show=\"visible\" :style=\"{left:left+'px',top:top+'px'}\" class=\"contextmenu\">\r\n\t\t\t<li v-if=\"!(selectedTag.meta&&selectedTag.meta.affix)\" @click=\"closeSelectedTag(selectedTag)\">Close</li>\r\n\t\t\t<li @click=\"closeAllTags(selectedTag)\">Close All</li>\r\n\t\t</ul>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\n\timport ScrollPane from './ScrollPane'\r\n\timport path from 'path'\r\n\timport {\r\n\t\tgenerateTitle\r\n\t} from '@/utils/i18n'\r\n\timport menu from '@/utils/menu'\r\n\timport { routes } from '@/router/router-static.js'\r\n\t\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tScrollPane\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tvisible: false,\r\n\t\t\t\ttop: 0,\r\n\t\t\t\tleft: 0,\r\n\t\t\t\tselectedTag: {},\r\n\t\t\t\taffixTags: [],\r\n\t\t\t\troutes: []\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tvisitedViews() {\r\n\t\t\t\treturn this.$store.state.tagsView.visitedViews\r\n\t\t\t},\r\n\t\t\t// routes() {\r\n\t\t\t//   return this.$store.state.menu.routes\r\n\t\t\t// }\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\t$route() {\r\n\t\t\t\tthis.addTags()\r\n\t\t\t\tthis.moveToCurrentTag()\r\n\t\t\t},\r\n\t\t\tvisible(value) {\r\n\t\t\t\tif (value) {\r\n\t\t\t\t\tdocument.body.addEventListener('click', this.closeMenu)\r\n\t\t\t\t} else {\r\n\t\t\t\t\tdocument.body.removeEventListener('click', this.closeMenu)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tthis.initTags()\r\n\t\t\tthis.addTags()\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.routes = menu\r\n\t\t\t\r\n\t\t\tlet menuList = []\r\n\t\t\tconst menus = menu.list()\r\n\t\t\tif (menus) {\r\n\t\t\t\tmenuList = menus\r\n\t\t\t} else {\r\n\t\t\t\tlet params = {\r\n\t\t\t\t\tpage: 1,\r\n\t\t\t\t\tlimit: 1,\r\n\t\t\t\t\tsort: 'id',\r\n\t\t\t\t}\r\n\t\t\t\r\n\t\t\t\tthis.$http({\r\n\t\t\t\t\turl: \"menu/list\",\r\n\t\t\t\t\tmethod: \"get\",\r\n\t\t\t\t\tparams: params\r\n\t\t\t\t}).then(({\r\n\t\t\t\t\tdata\r\n\t\t\t\t}) => {\r\n\t\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\t\tmenuList = JSON.parse(data.data.list[0].menujson);\r\n\t\t\t\t\t\tthis.$storage.set(\"menus\", menuList);\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\tthis.role = this.$storage.get('role')\r\n\t\t\t\r\n\t\t\tfor (let i = 0; i < menuList.length; i++) {\r\n\t\t\t\tif (menuList[i].roleName == this.role) {\r\n\t\t\t\t\tthis.routes = menuList[i].backMenu;\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tthis.routes = routes.concat(this.routes);\r\n\t\t\t// console.log(this.visitedViews)\r\n\t\t\t// console.log(this.routes)\r\n\t\t\t// this.initTags()\r\n\t\t\t// this.addTags()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tisActive(route) {\r\n\t\t\t\treturn route.path === this.$route.path\r\n\t\t\t},\r\n\t\t\tfilterAffixTags(routes, basePath = '/') {\r\n\t\t\t\tlet tags = []\r\n\t\t\t\troutes.forEach(route => {\r\n\t\t\t\t\tif (route.meta && route.meta.affix) {\r\n\t\t\t\t\t\tconst tagPath = path.resolve(basePath, route.path)\r\n\t\t\t\t\t\ttags.push({\r\n\t\t\t\t\t\t\tfullPath: tagPath,\r\n\t\t\t\t\t\t\tpath: tagPath,\r\n\t\t\t\t\t\t\tname: route.name,\r\n\t\t\t\t\t\t\tmeta: {\r\n\t\t\t\t\t\t\t\t...route.meta\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (route.children) {\r\n\t\t\t\t\t\tconst tempTags = this.filterAffixTags(route.children, route.path)\r\n\t\t\t\t\t\tif (tempTags.length >= 1) {\r\n\t\t\t\t\t\t\ttags = [...tags, ...tempTags]\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\treturn tags\r\n\t\t\t},\r\n\t\t\tgenerateTitle,\r\n\t\t\tinitTags() {\r\n\t\t\t\tconst affixTags = this.affixTags = this.filterAffixTags(this.routes)\r\n\t\t\t\tfor (const tag of affixTags) {\r\n\t\t\t\t\t// Must have tag name\r\n\t\t\t\t\tif (tag.name) {\r\n\t\t\t\t\t\tthis.$store.dispatch('tagsView/addVisitedView', tag)\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\taddTags() {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tname\r\n\t\t\t\t} = this.$route\r\n\t\t\t\tif (name) {\r\n\t\t\t\t\tthis.$store.dispatch('tagsView/addView', this.$route)\r\n\t\t\t\t}\r\n\t\t\t\treturn false\r\n\t\t\t},\r\n\t\t\tmoveToCurrentTag() {\r\n\t\t\t\tconst tags = this.$refs.tag\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tfor (const tag of tags) {\r\n\t\t\t\t\t\tif (tag.to.path === this.$route.path) {\r\n\t\t\t\t\t\t\tthis.$refs.scrollPane.moveToTarget(tag)\r\n\t\t\t\t\t\t\t// when query is different then update\r\n\t\t\t\t\t\t\tif (tag.to.fullPath !== this.$route.fullPath) {\r\n\t\t\t\t\t\t\t\tthis.$store.dispatch('tagsView/updateVisitedView', this.$route)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tbreak\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\trefreshSelectedTag(view) {\r\n\t\t\t\tthis.$store.dispatch('tagsView/delCachedView', view).then(() => {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tfullPath\r\n\t\t\t\t\t} = view\r\n\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\tthis.$router.replace({\r\n\t\t\t\t\t\t\tpath: '/redirect' + fullPath\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tcloseSelectedTag(view) {\r\n\t\t\t\tthis.$store.dispatch('tagsView/delView', view).then(({\r\n\t\t\t\t\tvisitedViews\r\n\t\t\t\t}) => {\r\n\t\t\t\t\tif (this.isActive(view)) {\r\n\t\t\t\t\t\tthis.toLastView(visitedViews, view)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tcloseOthersTags() {\r\n\t\t\t\tthis.$router.push(this.selectedTag)\r\n\t\t\t\tthis.$store.dispatch('tagsView/delOthersViews', this.selectedTag).then(() => {\r\n\t\t\t\t\tthis.moveToCurrentTag()\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tcloseAllTags(view) {\r\n\t\t\t\tthis.$store.dispatch('tagsView/delAllViews').then(({\r\n\t\t\t\t\tvisitedViews\r\n\t\t\t\t}) => {\r\n\t\t\t\t\tif (this.affixTags.some(tag => tag.path === view.path)) {\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.toLastView(visitedViews, view)\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\ttoLastView(visitedViews, view) {\r\n\t\t\t\tconst latestView = visitedViews.slice(-1)[0]\r\n\t\t\t\tif (latestView) {\r\n\t\t\t\t\tthis.$router.push(latestView)\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// now the default is to redirect to the home page if there is no tags-view,\r\n\t\t\t\t\t// you can adjust it according to your needs.\r\n\t\t\t\t\tif (view.name === 'Dashboard') {\r\n\t\t\t\t\t\t// to reload home page\r\n\t\t\t\t\t\tthis.$router.replace({\r\n\t\t\t\t\t\t\tpath: '/redirect' + view.fullPath\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.$router.push('/')\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\topenMenu(tag, e) {\r\n\t\t\t\tconst menuMinWidth = 105\r\n\t\t\t\tconst offsetLeft = this.$el.getBoundingClientRect().left // container margin left\r\n\t\t\t\tconst offsetWidth = this.$el.offsetWidth // container width\r\n\t\t\t\tconst maxLeft = offsetWidth - menuMinWidth // left boundary\r\n\t\t\t\tconst left = e.clientX - offsetLeft + 15 // 15: margin right\r\n\r\n\t\t\t\tif (left > maxLeft) {\r\n\t\t\t\t\tthis.left = maxLeft\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.left = left\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthis.top = e.clientY\r\n\t\t\t\tthis.visible = true\r\n\t\t\t\tthis.selectedTag = tag\r\n\t\t\t},\r\n\t\t\tcloseMenu() {\r\n\t\t\t\tthis.visible = false\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.tags-view-container {\r\n\t\theight: 34px;\r\n\t\twidth: 100%;\r\n\t\tbackground: #fff;\r\n\t\tborder-bottom: 1px solid #d8dce5;\r\n\t\tbox-shadow: 0 1px 3px 0 rgba(0, 0, 0, .12), 0 0 3px 0 rgba(0, 0, 0, .04);\r\n\r\n\t\t.contextmenu {\r\n\t\t\tmargin: 0;\r\n\t\t\tbackground: #fff;\r\n\t\t\tz-index: 3000;\r\n\t\t\tposition: absolute;\r\n\t\t\tlist-style-type: none;\r\n\t\t\tpadding: 5px 0;\r\n\t\t\tborder-radius: 4px;\r\n\t\t\tfont-size: 12px;\r\n\t\t\tfont-weight: 400;\r\n\t\t\tcolor: #333;\r\n\t\t\tbox-shadow: 2px 2px 3px 0 rgba(0, 0, 0, .3);\r\n\r\n\t\t\tli {\r\n\t\t\t\tmargin: 0;\r\n\t\t\t\tpadding: 7px 16px;\r\n\t\t\t\tcursor: pointer;\r\n\r\n\t\t\t\t&:hover {\r\n\t\t\t\t\tbackground: #eee;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.tags-view-container .tags-view-wrapper .tags-view-item {\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tpadding: 0 8px;\r\n\t\t\t\tmargin: 0 5px 0 0;\r\n\t\t\t\tcolor: #a3b1c9;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tfont-size: 12px;\r\n\t\t\t\tborder-color: #d8dce5;\r\n\t\t\t\tline-height: 25px;\r\n\t\t\t\tbackground: #fff;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tborder-width: 1px 1px 0 1px;\r\n\t\t\t\tborder-style: solid;\r\n\t\t\t\theight: 25px;\r\n\t\t\t}\r\n\t\r\n\t.tags-view-container .tags-view-wrapper .tags-view-item:hover {\r\n\t\t\t\tcolor: #2ddcd3;\r\n\t\t\t}\r\n\t\r\n\t.tags-view-container .tags-view-wrapper .tags-view-item.active {\r\n\t\t\t\tcolor: #2ddcd3;\r\n\t\t\t}\r\n\t\r\n\t.tags-view-container .tags-view-wrapper .tags-view-item .text {\r\n\t\t\t\tcolor: inherit;\r\n\t\t\t\tfont-size: inherit;\r\n\t\t\t}\r\n\t\r\n\t.tags-view-container .tags-view-wrapper .tags-view-item .el-icon-close {\r\n\t\t\t\tcolor: inherit;\r\n\t\t\t\tfont-size: inherit;\r\n\t\t\t}\r\n</style>"], "mappings": ";;;;;;;;;;;;;;;;;AAoBA,OAAAA,UAAA;AACA,OAAAC,IAAA;AACA,SACAC,aAAA,QACA;AACA,OAAAC,IAAA;AACA,SAAAC,MAAA;AAEA;EACAC,UAAA;IACAL,UAAA,EAAAA;EACA;EACAM,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,GAAA;MACAC,IAAA;MACAC,WAAA;MACAC,SAAA;MACAP,MAAA;IACA;EACA;EACAQ,QAAA;IACAC,YAAA,WAAAA,aAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAH,YAAA;IACA,EACA;IACA;IACA;EACA;EACAI,KAAA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAC,OAAA;MACA,KAAAC,gBAAA;IACA;IACAb,OAAA,WAAAA,QAAAc,KAAA;MACA,IAAAA,KAAA;QACAC,QAAA,CAAAC,IAAA,CAAAC,gBAAA,eAAAC,SAAA;MACA;QACAH,QAAA,CAAAC,IAAA,CAAAG,mBAAA,eAAAD,SAAA;MACA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA,KAAAC,QAAA;IACA,KAAAT,OAAA;EACA;EACAU,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAA1B,MAAA,GAAAD,IAAA;IAEA,IAAA4B,QAAA;IACA,IAAAC,KAAA,GAAA7B,IAAA,CAAA8B,IAAA;IACA,IAAAD,KAAA;MACAD,QAAA,GAAAC,KAAA;IACA;MACA,IAAAE,MAAA;QACAC,IAAA;QACAC,KAAA;QACAC,IAAA;MACA;MAEA,KAAAC,KAAA;QACAC,GAAA;QACAC,MAAA;QACAN,MAAA,EAAAA;MACA,GAAAO,IAAA,WAAAC,IAAA,EAEA;QAAA,IADApC,IAAA,GAAAoC,IAAA,CAAApC,IAAA;QAEA,IAAAA,IAAA,IAAAA,IAAA,CAAAqC,IAAA;UACAZ,QAAA,GAAAa,IAAA,CAAAC,KAAA,CAAAvC,IAAA,CAAAA,IAAA,CAAA2B,IAAA,IAAAa,QAAA;UACAhB,KAAA,CAAAiB,QAAA,CAAAC,GAAA,UAAAjB,QAAA;QACA;MACA;IACA;IACA,KAAAkB,IAAA,QAAAF,QAAA,CAAAG,GAAA;IAEA,SAAAC,CAAA,MAAAA,CAAA,GAAApB,QAAA,CAAAqB,MAAA,EAAAD,CAAA;MACA,IAAApB,QAAA,CAAAoB,CAAA,EAAAE,QAAA,SAAAJ,IAAA;QACA,KAAA7C,MAAA,GAAA2B,QAAA,CAAAoB,CAAA,EAAAG,QAAA;QACA;MACA;IACA;IACA,KAAAlD,MAAA,GAAAA,MAAA,CAAAmD,MAAA,MAAAnD,MAAA;IACA;IACA;IACA;IACA;EACA;EACAoD,OAAA;IACAC,QAAA,WAAAA,SAAAC,KAAA;MACA,OAAAA,KAAA,CAAAzD,IAAA,UAAAiB,MAAA,CAAAjB,IAAA;IACA;IACA0D,eAAA,WAAAA,gBAAAvD,MAAA;MAAA,IAAAwD,MAAA;MAAA,IAAAC,QAAA,GAAAC,SAAA,CAAAV,MAAA,QAAAU,SAAA,QAAAC,SAAA,GAAAD,SAAA;MACA,IAAAE,IAAA;MACA5D,MAAA,CAAA6D,OAAA,WAAAP,KAAA;QACA,IAAAA,KAAA,CAAAQ,IAAA,IAAAR,KAAA,CAAAQ,IAAA,CAAAC,KAAA;UACA,IAAAC,OAAA,GAAAnE,IAAA,CAAAoE,OAAA,CAAAR,QAAA,EAAAH,KAAA,CAAAzD,IAAA;UACA+D,IAAA,CAAAM,IAAA;YACAC,QAAA,EAAAH,OAAA;YACAnE,IAAA,EAAAmE,OAAA;YACAI,IAAA,EAAAd,KAAA,CAAAc,IAAA;YACAN,IAAA,EAAAO,aAAA,KACAf,KAAA,CAAAQ,IAAA;UAEA;QACA;QACA,IAAAR,KAAA,CAAAgB,QAAA;UACA,IAAAC,QAAA,GAAAf,MAAA,CAAAD,eAAA,CAAAD,KAAA,CAAAgB,QAAA,EAAAhB,KAAA,CAAAzD,IAAA;UACA,IAAA0E,QAAA,CAAAvB,MAAA;YACAY,IAAA,MAAAT,MAAA,CAAAqB,kBAAA,CAAAZ,IAAA,GAAAY,kBAAA,CAAAD,QAAA;UACA;QACA;MACA;MACA,OAAAX,IAAA;IACA;IACA9D,aAAA,EAAAA,aAAA;IACA0B,QAAA,WAAAA,SAAA;MACA,IAAAjB,SAAA,QAAAA,SAAA,QAAAgD,eAAA,MAAAvD,MAAA;MAAA,IAAAyE,SAAA,GAAAC,0BAAA,CACAnE,SAAA;QAAAoE,KAAA;MAAA;QAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;UAAA,IAAAC,GAAA,GAAAJ,KAAA,CAAA1D,KAAA;UACA;UACA,IAAA8D,GAAA,CAAAX,IAAA;YACA,KAAA1D,MAAA,CAAAsE,QAAA,4BAAAD,GAAA;UACA;QACA;MAAA,SAAAE,GAAA;QAAAR,SAAA,CAAAS,CAAA,CAAAD,GAAA;MAAA;QAAAR,SAAA,CAAAU,CAAA;MAAA;IACA;IACApE,OAAA,WAAAA,QAAA;MACA,IACAqD,IAAA,GACA,KAAAtD,MAAA,CADAsD,IAAA;MAEA,IAAAA,IAAA;QACA,KAAA1D,MAAA,CAAAsE,QAAA,0BAAAlE,MAAA;MACA;MACA;IACA;IACAE,gBAAA,WAAAA,iBAAA;MAAA,IAAAoE,MAAA;MACA,IAAAxB,IAAA,QAAAyB,KAAA,CAAAN,GAAA;MACA,KAAAO,SAAA;QAAA,IAAAC,UAAA,GAAAb,0BAAA,CACAd,IAAA;UAAA4B,MAAA;QAAA;UAAA,KAAAD,UAAA,CAAAX,CAAA,MAAAY,MAAA,GAAAD,UAAA,CAAAV,CAAA,IAAAC,IAAA;YAAA,IAAAC,GAAA,GAAAS,MAAA,CAAAvE,KAAA;YACA,IAAA8D,GAAA,CAAAU,EAAA,CAAA5F,IAAA,KAAAuF,MAAA,CAAAtE,MAAA,CAAAjB,IAAA;cACAuF,MAAA,CAAAC,KAAA,CAAAK,UAAA,CAAAC,YAAA,CAAAZ,GAAA;cACA;cACA,IAAAA,GAAA,CAAAU,EAAA,CAAAtB,QAAA,KAAAiB,MAAA,CAAAtE,MAAA,CAAAqD,QAAA;gBACAiB,MAAA,CAAA1E,MAAA,CAAAsE,QAAA,+BAAAI,MAAA,CAAAtE,MAAA;cACA;cACA;YACA;UACA;QAAA,SAAAmE,GAAA;UAAAM,UAAA,CAAAL,CAAA,CAAAD,GAAA;QAAA;UAAAM,UAAA,CAAAJ,CAAA;QAAA;MACA;IACA;IACAS,kBAAA,WAAAA,mBAAAC,IAAA;MAAA,IAAAC,MAAA;MACA,KAAApF,MAAA,CAAAsE,QAAA,2BAAAa,IAAA,EAAAxD,IAAA;QACA,IACA8B,QAAA,GACA0B,IAAA,CADA1B,QAAA;QAEA2B,MAAA,CAAAR,SAAA;UACAQ,MAAA,CAAAC,OAAA,CAAAC,OAAA;YACAnG,IAAA,gBAAAsE;UACA;QACA;MACA;IACA;IACA8B,gBAAA,WAAAA,iBAAAJ,IAAA;MAAA,IAAAK,MAAA;MACA,KAAAxF,MAAA,CAAAsE,QAAA,qBAAAa,IAAA,EAAAxD,IAAA,WAAA8D,KAAA,EAEA;QAAA,IADA1F,YAAA,GAAA0F,KAAA,CAAA1F,YAAA;QAEA,IAAAyF,MAAA,CAAA7C,QAAA,CAAAwC,IAAA;UACAK,MAAA,CAAAE,UAAA,CAAA3F,YAAA,EAAAoF,IAAA;QACA;MACA;IACA;IACAQ,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAAP,OAAA,CAAA7B,IAAA,MAAA5D,WAAA;MACA,KAAAI,MAAA,CAAAsE,QAAA,iCAAA1E,WAAA,EAAA+B,IAAA;QACAiE,MAAA,CAAAtF,gBAAA;MACA;IACA;IACAuF,YAAA,WAAAA,aAAAV,IAAA;MAAA,IAAAW,MAAA;MACA,KAAA9F,MAAA,CAAAsE,QAAA,yBAAA3C,IAAA,WAAAoE,KAAA,EAEA;QAAA,IADAhG,YAAA,GAAAgG,KAAA,CAAAhG,YAAA;QAEA,IAAA+F,MAAA,CAAAjG,SAAA,CAAAmG,IAAA,WAAA3B,GAAA;UAAA,OAAAA,GAAA,CAAAlF,IAAA,KAAAgG,IAAA,CAAAhG,IAAA;QAAA;UACA;QACA;QACA2G,MAAA,CAAAJ,UAAA,CAAA3F,YAAA,EAAAoF,IAAA;MACA;IACA;IACAO,UAAA,WAAAA,WAAA3F,YAAA,EAAAoF,IAAA;MACA,IAAAc,UAAA,GAAAlG,YAAA,CAAAmG,KAAA;MACA,IAAAD,UAAA;QACA,KAAAZ,OAAA,CAAA7B,IAAA,CAAAyC,UAAA;MACA;QACA;QACA;QACA,IAAAd,IAAA,CAAAzB,IAAA;UACA;UACA,KAAA2B,OAAA,CAAAC,OAAA;YACAnG,IAAA,gBAAAgG,IAAA,CAAA1B;UACA;QACA;UACA,KAAA4B,OAAA,CAAA7B,IAAA;QACA;MACA;IACA;IACA2C,QAAA,WAAAA,SAAA9B,GAAA,EAAAG,CAAA;MACA,IAAA4B,YAAA;MACA,IAAAC,UAAA,QAAAC,GAAA,CAAAC,qBAAA,GAAA5G,IAAA;MACA,IAAA6G,WAAA,QAAAF,GAAA,CAAAE,WAAA;MACA,IAAAC,OAAA,GAAAD,WAAA,GAAAJ,YAAA;MACA,IAAAzG,IAAA,GAAA6E,CAAA,CAAAkC,OAAA,GAAAL,UAAA;;MAEA,IAAA1G,IAAA,GAAA8G,OAAA;QACA,KAAA9G,IAAA,GAAA8G,OAAA;MACA;QACA,KAAA9G,IAAA,GAAAA,IAAA;MACA;MAEA,KAAAD,GAAA,GAAA8E,CAAA,CAAAmC,OAAA;MACA,KAAAlH,OAAA;MACA,KAAAG,WAAA,GAAAyE,GAAA;IACA;IACA1D,SAAA,WAAAA,UAAA;MACA,KAAAlB,OAAA;IACA;EACA;AACA", "ignoreList": []}]}