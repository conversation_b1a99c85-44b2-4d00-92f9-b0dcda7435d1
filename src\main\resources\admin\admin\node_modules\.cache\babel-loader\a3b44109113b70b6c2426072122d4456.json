{"remainingRequest": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\src\\views\\home.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\src\\views\\home.vue", "mtime": 1755434670143}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\babel.config.js", "mtime": 1711079474000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["router", "echarts", "data", "caiwuxinxiCount", "line", "bar", "pie", "funnel", "boardBase", "gauge", "mounted", "init", "getcaiwuxinxiCount", "caiwuxinxiChat1", "caiwuxinxiChat2", "caiwuxinxiChat3", "methods", "wordclouds", "wordcloudData", "echartsId", "wordcloud", "$template2", "back", "board", "JSON", "parse", "stringify", "k", "v", "indexOf", "eval", "option", "series", "myChart0", "document", "getElementById", "myChart", "img", "maskImage", "Image", "src", "onload", "clear", "setOption", "myChartInterval", "type", "xAxisData", "seriesData", "$nextTick", "setInterval", "xAxis", "shift", "push", "yAxis", "base", "interval", "$storage", "get", "$http", "url", "concat", "method", "then", "_ref", "code", "name", "_this", "_ref2", "_this2", "caiwuxinxiChart1", "_ref3", "res", "p<PERSON><PERSON>y", "i", "length", "funnelNum", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFloat", "total", "value", "titleObj", "title", "text", "legend<PERSON>bj", "Object", "assign", "legend", "tooltipObj", "trigger", "formatter", "tooltip", "seriesObj", "left", "top", "bottom", "width", "minSize", "maxSize", "backgroundColor", "color", "window", "onresize", "resize", "_this3", "caiwuxinxiChart2", "_ref4", "barNum", "xAxisObj", "axisLabel", "rotate", "yAxisObj", "_this4", "caiwuxinxiChart3", "_ref5", "lineNum", "boundaryGap"], "sources": ["src/views/home.vue"], "sourcesContent": ["<template>\r\n<div class=\"content\" :style='{\"padding\":\"30px\"}'>\r\n\t<!-- notice -->\r\n\t<!-- title -->\r\n\t<div class=\"text\" :style='{\"margin\":\"20px auto\",\"fontSize\":\"24px\",\"color\":\" #374254\",\"textAlign\":\"center\",\"fontWeight\":\"bold\"}'>欢迎使用 {{this.$project.projectName}}</div>\r\n\t<!-- statis -->\r\n\t<div :style='{\"width\":\"100%\",\"margin\":\"0 0 20px 0\",\"alignItems\":\"center\",\"flexWrap\":\"wrap\",\"justifyContent\":\"center\",\"display\":\"flex\"}'>\r\n\t\t<div :style='{\"border\":\" 1px solid rgba(167, 180, 201,.3)    \",\"margin\":\"0 10px\",\"borderRadius\":\"4px\",\"background\":\"#fff\",\"display\":\"flex\"}' v-if=\"isAuth('caiwuxinxi','首页总数')\">\r\n\t\t\t<div :style='{\"alignItems\":\"center\",\"background\":\"#00e682\",\"display\":\"flex\",\"width\":\"80px\",\"justifyContent\":\"center\",\"height\":\"80px\",\"order\":\"2\"}'>\r\n\t\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"color\":\"#fff\",\"fontSize\":\"24px\"}'></span>\r\n\t\t\t</div>\r\n\t\t\t<div :style='{\"width\":\"120px\",\"alignItems\":\"center\",\"flexDirection\":\"column\",\"justifyContent\":\"center\",\"display\":\"flex\",\"order\":\"1\"}'>\r\n\t\t\t\t<div :style='{\"margin\":\"5px 0\",\"lineHeight\":\"24px\",\"fontSize\":\"20px\",\"color\":\"1\",\"fontWeight\":\"bold\",\"height\":\"24px\"}'>{{caiwuxinxiCount}}</div>\r\n\t\t\t\t<div :style='{\"margin\":\"5px 0\",\"lineHeight\":\"24px\",\"fontSize\":\"14px\",\"color\":\" #a3b1c9\",\"height\":\"24px\"}'>财物信息总数</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t</div>\r\n\t<!-- statis -->\r\n\t\r\n\r\n\t\r\n\t<!-- echarts -->\r\n\t<!-- 3 -->\r\n\t<div class=\"type3\" :style='{\"alignContent\":\"flex-start\",\"padding\":\"10px 20px\",\"flexWrap\":\"wrap\",\"background\":\"rebeccapurple\",\"display\":\"flex\",\"width\":\"100%\",\"position\":\"relative\",\"justifyContent\":\"space-between\",\"height\":\"auto\"}'>\r\n\t\t<div id=\"caiwuxinxiChart1\" class=\"echarts1\" v-if=\"isAuth('caiwuxinxi','首页统计')\"></div>\r\n\t\t<div id=\"caiwuxinxiChart2\" class=\"echarts2\" v-if=\"isAuth('caiwuxinxi','首页统计')\"></div>\r\n\t\t<div id=\"caiwuxinxiChart3\" class=\"echarts3\" v-if=\"isAuth('caiwuxinxi','首页统计')\"></div>\r\n\t</div>\r\n</div>\r\n</template>\r\n<script>\r\n//3\r\nimport router from '@/router/router-static'\r\nimport * as echarts from 'echarts'\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n            caiwuxinxiCount: 0,\r\n\t\t\tline: {\"backgroundColor\":\"transparent\",\"yAxis\":{\"axisLabel\":{\"borderType\":\"solid\",\"rotate\":0,\"padding\":0,\"shadowOffsetX\":0,\"margin\":15,\"backgroundColor\":\"transparent\",\"borderColor\":\"#000\",\"shadowOffsetY\":0,\"color\":\"#fff\",\"shadowBlur\":0,\"show\":true,\"inside\":false,\"ellipsis\":\"...\",\"overflow\":\"none\",\"borderRadius\":0,\"borderWidth\":0,\"width\":\"\",\"fontSize\":12,\"lineHeight\":24,\"shadowColor\":\"transparent\",\"fontWeight\":\"normal\",\"height\":\"\"},\"axisTick\":{\"show\":true,\"length\":5,\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"cap\":\"butt\",\"color\":\"#fff\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"rgba(0,0,0,.5)\"},\"inside\":false},\"splitLine\":{\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"cap\":\"butt\",\"color\":\"#ccc\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"rgba(0,0,0,.5)\"},\"show\":true},\"axisLine\":{\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"cap\":\"butt\",\"color\":\"#fff\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"rgba(0,0,0,.5)\"},\"show\":true},\"splitArea\":{\"show\":false,\"areaStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"color\":\"rgba(250,250,250,0.3)\",\"opacity\":1,\"shadowBlur\":10,\"shadowColor\":\"rgba(0,0,0,.5)\"}}},\"xAxis\":{\"axisLabel\":{\"borderType\":\"solid\",\"rotate\":0,\"padding\":0,\"shadowOffsetX\":0,\"margin\":4,\"backgroundColor\":\"transparent\",\"borderColor\":\"#000\",\"shadowOffsetY\":0,\"color\":\"#fff\",\"shadowBlur\":0,\"show\":true,\"inside\":false,\"ellipsis\":\"...\",\"overflow\":\"none\",\"borderRadius\":0,\"borderWidth\":0,\"width\":\"\",\"fontSize\":12,\"lineHeight\":24,\"shadowColor\":\"transparent\",\"fontWeight\":\"normal\",\"height\":\"\"},\"axisTick\":{\"show\":true,\"length\":5,\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"cap\":\"butt\",\"color\":\"#fff\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"rgba(0,0,0,.5)\"},\"inside\":false},\"splitLine\":{\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"cap\":\"butt\",\"color\":\"#fff\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"rgba(0,0,0,.5)\"},\"show\":false},\"axisLine\":{\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"cap\":\"butt\",\"color\":\"#fff\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"rgba(0,0,0,.5)\"},\"show\":true},\"splitArea\":{\"show\":false,\"areaStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"color\":\"rgba(255,255,255,.3)\",\"opacity\":1,\"shadowBlur\":10,\"shadowColor\":\"rgba(0,0,0,.5)\"}}},\"color\":[\"#5470c6\",\"#91cc75\",\"#fac858\",\"#ee6666\",\"#73c0de\",\"#3ba272\",\"#fc8452\",\"#9a60b4\",\"#ea7ccc\"],\"legend\":{\"padding\":5,\"itemGap\":10,\"shadowOffsetX\":0,\"backgroundColor\":\"transparent\",\"borderColor\":\"#ccc\",\"shadowOffsetY\":0,\"orient\":\"horizontal\",\"shadowBlur\":0,\"bottom\":\"auto\",\"itemHeight\":14,\"show\":true,\"icon\":\"roundRect\",\"itemStyle\":{\"borderType\":\"solid\",\"shadowOffsetX\":0,\"borderColor\":\"inherit\",\"shadowOffsetY\":0,\"color\":\"rgb(255,255,255)\",\"shadowBlur\":0,\"borderWidth\":0,\"opacity\":1,\"shadowColor\":\"transparent\"},\"right\":\"auto\",\"top\":\"auto\",\"borderRadius\":0,\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"color\":\"inherit\",\"shadowBlur\":0,\"width\":\"auto\",\"type\":\"inherit\",\"opacity\":1,\"shadowColor\":\"transparent\"},\"left\":\"right\",\"borderWidth\":0,\"width\":\"auto\",\"itemWidth\":25,\"textStyle\":{\"textBorderWidth\":0,\"color\":\"#fff\",\"textShadowColor\":\"transparent\",\"ellipsis\":\"...\",\"overflow\":\"none\",\"fontSize\":12,\"lineHeight\":24,\"textShadowOffsetX\":0,\"textShadowOffsetY\":0,\"textBorderType\":\"solid\",\"fontWeight\":500,\"textBorderColor\":\"transparent\",\"textShadowBlur\":0},\"shadowColor\":\"rgba(0,0,0,.3)\",\"height\":\"auto\"},\"series\":{\"symbol\":\"emptyCircle\",\"itemStyle\":{\"borderType\":\"solid\",\"shadowOffsetX\":0,\"borderColor\":\"#fff\",\"shadowOffsetY\":0,\"color\":\"\",\"shadowBlur\":0,\"borderWidth\":0,\"opacity\":1,\"shadowColor\":\"#000\"},\"showSymbol\":true,\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"color\":\"\",\"shadowBlur\":0,\"width\":2,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"#000\"},\"symbolSize\":4},\"tooltip\":{\"backgroundColor\":\"#123\",\"textStyle\":{\"color\":\"#fff\"}},\"title\":{\"borderType\":\"solid\",\"padding\":2,\"shadowOffsetX\":0,\"backgroundColor\":\"transparent\",\"borderColor\":\"#ccc\",\"shadowOffsetY\":0,\"shadowBlur\":0,\"bottom\":\"auto\",\"show\":true,\"right\":\"auto\",\"top\":\"auto\",\"borderRadius\":0,\"left\":\"left\",\"borderWidth\":0,\"textStyle\":{\"textBorderWidth\":0,\"color\":\"#fff\",\"textShadowColor\":\"transparent\",\"fontSize\":14,\"lineHeight\":24,\"textShadowOffsetX\":0,\"textShadowOffsetY\":0,\"textBorderType\":\"solid\",\"fontWeight\":500,\"textBorderColor\":\"#ccc\",\"textShadowBlur\":0},\"shadowColor\":\"transparent\"}},\r\n\t\t\tbar: {\"backgroundColor\":\"transparent\",\"yAxis\":{\"axisLabel\":{\"borderType\":\"solid\",\"rotate\":0,\"padding\":0,\"shadowOffsetX\":0,\"margin\":12,\"backgroundColor\":\"transparent\",\"borderColor\":\"#000\",\"shadowOffsetY\":0,\"color\":\"#fff\",\"shadowBlur\":0,\"show\":true,\"inside\":false,\"ellipsis\":\"...\",\"overflow\":\"none\",\"borderRadius\":0,\"borderWidth\":0,\"width\":\"\",\"fontSize\":12,\"lineHeight\":24,\"shadowColor\":\"transparent\",\"fontWeight\":\"normal\",\"height\":\"\"},\"axisTick\":{\"show\":true,\"length\":5,\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"cap\":\"butt\",\"color\":\"#fff\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"rgba(0,0,0,.5)\"},\"inside\":false},\"splitLine\":{\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"cap\":\"butt\",\"color\":\"#ccc\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"rgba(0,0,0,.5)\"},\"show\":true},\"axisLine\":{\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"cap\":\"butt\",\"color\":\"#fff\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"rgba(0,0,0,.5)\"},\"show\":true},\"splitArea\":{\"show\":false,\"areaStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"color\":\"rgba(250,250,250,0.3)\",\"opacity\":1,\"shadowBlur\":10,\"shadowColor\":\"rgba(0,0,0,.5)\"}}},\"xAxis\":{\"axisLabel\":{\"borderType\":\"solid\",\"rotate\":0,\"padding\":0,\"shadowOffsetX\":0,\"margin\":4,\"backgroundColor\":\"transparent\",\"borderColor\":\"#000\",\"shadowOffsetY\":0,\"color\":\"#fff\",\"shadowBlur\":0,\"show\":true,\"inside\":false,\"ellipsis\":\"...\",\"overflow\":\"none\",\"borderRadius\":0,\"borderWidth\":0,\"width\":\"\",\"fontSize\":12,\"lineHeight\":24,\"shadowColor\":\"transparent\",\"fontWeight\":\"normal\",\"height\":\"\"},\"axisTick\":{\"show\":true,\"length\":5,\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"cap\":\"butt\",\"color\":\"#fff\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"rgba(0,0,0,.5)\"},\"inside\":false},\"splitLine\":{\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"cap\":\"butt\",\"color\":\"#fff\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"rgba(0,0,0,.5)\"},\"show\":false},\"axisLine\":{\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"cap\":\"butt\",\"color\":\"#fff\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"rgba(0,0,0,.5)\"},\"show\":true},\"splitArea\":{\"show\":false,\"areaStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"color\":\"rgba(255,255,255,.3)\",\"opacity\":1,\"shadowBlur\":10,\"shadowColor\":\"rgba(0,0,0,.5)\"}}},\"color\":[\"#00ff00\",\"#91cc75\",\"#fac858\",\"#ee6666\",\"#73c0de\",\"#3ba272\",\"#fc8452\",\"#9a60b4\",\"#ea7ccc\"],\"legend\":{\"padding\":5,\"itemGap\":10,\"shadowOffsetX\":0,\"backgroundColor\":\"transparent\",\"borderColor\":\"#ccc\",\"shadowOffsetY\":0,\"orient\":\"horizontal\",\"shadowBlur\":0,\"bottom\":\"auto\",\"itemHeight\":14,\"show\":true,\"icon\":\"roundRect\",\"itemStyle\":{\"borderType\":\"solid\",\"shadowOffsetX\":0,\"borderColor\":\"inherit\",\"shadowOffsetY\":0,\"color\":\"rgb(255,255,255)\",\"shadowBlur\":0,\"borderWidth\":0,\"opacity\":1,\"shadowColor\":\"transparent\"},\"right\":\"auto\",\"top\":\"auto\",\"borderRadius\":0,\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"color\":\"inherit\",\"shadowBlur\":0,\"width\":\"auto\",\"type\":\"inherit\",\"opacity\":1,\"shadowColor\":\"transparent\"},\"left\":\"right\",\"borderWidth\":0,\"width\":\"auto\",\"itemWidth\":25,\"textStyle\":{\"textBorderWidth\":0,\"color\":\"#fff\",\"textShadowColor\":\"transparent\",\"ellipsis\":\"...\",\"overflow\":\"none\",\"fontSize\":12,\"lineHeight\":24,\"textShadowOffsetX\":0,\"textShadowOffsetY\":0,\"textBorderType\":\"solid\",\"fontWeight\":500,\"textBorderColor\":\"transparent\",\"textShadowBlur\":0},\"shadowColor\":\"rgba(0,0,0,.3)\",\"height\":\"auto\"},\"series\":{\"barWidth\":\"auto\",\"itemStyle\":{\"borderType\":\"solid\",\"shadowOffsetX\":0,\"borderColor\":\"#fff\",\"shadowOffsetY\":0,\"color\":\"\",\"shadowBlur\":0,\"borderWidth\":0,\"opacity\":1,\"shadowColor\":\"#000\"},\"colorBy\":\"data\",\"barCategoryGap\":\"20%\"},\"tooltip\":{\"backgroundColor\":\"#123\",\"textStyle\":{\"color\":\"#fff\"}},\"title\":{\"borderType\":\"solid\",\"padding\":2,\"shadowOffsetX\":0,\"backgroundColor\":\"transparent\",\"borderColor\":\"#ccc\",\"shadowOffsetY\":0,\"shadowBlur\":0,\"bottom\":\"auto\",\"show\":true,\"right\":\"auto\",\"top\":\"auto\",\"borderRadius\":0,\"left\":\"left\",\"borderWidth\":0,\"textStyle\":{\"textBorderWidth\":0,\"color\":\"#fff\",\"textShadowColor\":\"transparent\",\"fontSize\":14,\"lineHeight\":24,\"textShadowOffsetX\":0,\"textShadowOffsetY\":0,\"textBorderType\":\"solid\",\"fontWeight\":500,\"textBorderColor\":\"#ccc\",\"textShadowBlur\":0},\"shadowColor\":\"transparent\"},\"base\":{\"animate\":false,\"interval\":2000}},\r\n\t\t\tpie: {\"tooltip\":{\"backgroundColor\":\"#123\",\"textStyle\":{\"color\":\"#fff\"}},\"backgroundColor\":\"transparent\",\"color\":[\"#5470c6\",\"#91cc75\",\"#fac858\",\"#ee6666\",\"#73c0de\",\"#3ba272\",\"#fc8452\",\"#9a60b4\",\"#ea7ccc\"],\"title\":{\"borderType\":\"solid\",\"padding\":2,\"shadowOffsetX\":0,\"backgroundColor\":\"transparent\",\"borderColor\":\"#ccc\",\"shadowOffsetY\":0,\"shadowBlur\":0,\"bottom\":\"auto\",\"show\":true,\"right\":\"auto\",\"top\":\"auto\",\"borderRadius\":0,\"left\":\"left\",\"borderWidth\":0,\"textStyle\":{\"textBorderWidth\":0,\"color\":\"#fff\",\"textShadowColor\":\"transparent\",\"fontSize\":14,\"lineHeight\":24,\"textShadowOffsetX\":0,\"textShadowOffsetY\":0,\"textBorderType\":\"solid\",\"fontWeight\":500,\"textBorderColor\":\"#ccc\",\"textShadowBlur\":0},\"shadowColor\":\"transparent\"},\"legend\":{\"padding\":5,\"itemGap\":10,\"shadowOffsetX\":0,\"backgroundColor\":\"transparent\",\"borderColor\":\"#ccc\",\"shadowOffsetY\":0,\"orient\":\"horizontal\",\"shadowBlur\":0,\"bottom\":\"auto\",\"itemHeight\":14,\"show\":true,\"icon\":\"roundRect\",\"itemStyle\":{\"borderType\":\"solid\",\"shadowOffsetX\":0,\"borderColor\":\"inherit\",\"shadowOffsetY\":0,\"color\":\"inherit\",\"shadowBlur\":0,\"borderWidth\":0,\"opacity\":1,\"shadowColor\":\"transparent\"},\"right\":\"auto\",\"top\":\"auto\",\"borderRadius\":0,\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"color\":\"inherit\",\"shadowBlur\":0,\"width\":\"auto\",\"type\":\"inherit\",\"opacity\":1,\"shadowColor\":\"transparent\"},\"left\":\"right\",\"borderWidth\":0,\"width\":\"auto\",\"itemWidth\":25,\"textStyle\":{\"textBorderWidth\":0,\"color\":\"#fff\",\"textShadowColor\":\"transparent\",\"ellipsis\":\"...\",\"overflow\":\"none\",\"fontSize\":12,\"lineHeight\":24,\"textShadowOffsetX\":0,\"textShadowOffsetY\":0,\"textBorderType\":\"solid\",\"fontWeight\":500,\"textBorderColor\":\"transparent\",\"textShadowBlur\":0},\"shadowColor\":\"rgba(0,0,0,.3)\",\"height\":\"auto\"},\"series\":{\"itemStyle\":{\"borderType\":\"solid\",\"shadowOffsetX\":0,\"borderColor\":\"#fff\",\"shadowOffsetY\":0,\"color\":\"\",\"shadowBlur\":0,\"borderWidth\":0,\"opacity\":1,\"shadowColor\":\"#000\"},\"label\":{\"borderType\":\"solid\",\"rotate\":0,\"padding\":0,\"textBorderWidth\":0,\"backgroundColor\":\"transparent\",\"borderColor\":\"#fff\",\"color\":\"#fff\",\"show\":true,\"textShadowColor\":\"transparent\",\"distanceToLabelLine\":5,\"ellipsis\":\"...\",\"overflow\":\"none\",\"borderRadius\":0,\"borderWidth\":0,\"fontSize\":12,\"lineHeight\":18,\"textShadowOffsetX\":0,\"position\":\"outside\",\"textShadowOffsetY\":0,\"textBorderType\":\"solid\",\"textBorderColor\":\"#fff\",\"textShadowBlur\":0},\"labelLine\":{\"show\":true,\"length\":10,\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"color\":\"#fff\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"#000\"},\"length2\":14,\"smooth\":false}}},\r\n\t\t\tfunnel: {\"tooltip\":{\"backgroundColor\":\"#123\",\"textStyle\":{\"color\":\"#fff\"}},\"backgroundColor\":\"transparent\",\"color\":[\"#5470c6\",\"#91cc75\",\"#fac858\",\"#ee6666\",\"#73c0de\",\"#3ba272\",\"#fc8452\",\"#9a60b4\",\"#ea7ccc\"],\"title\":{\"borderType\":\"solid\",\"padding\":2,\"shadowOffsetX\":0,\"backgroundColor\":\"transparent\",\"borderColor\":\"#ccc\",\"shadowOffsetY\":0,\"shadowBlur\":0,\"bottom\":\"auto\",\"show\":true,\"right\":\"auto\",\"top\":\"auto\",\"borderRadius\":0,\"left\":\"center\",\"borderWidth\":1,\"textStyle\":{\"textBorderWidth\":0,\"color\":\"#fff\",\"textShadowColor\":\"transparent\",\"fontSize\":14,\"lineHeight\":24,\"textShadowOffsetX\":0,\"textShadowOffsetY\":0,\"textBorderType\":\"solid\",\"fontWeight\":500,\"textBorderColor\":\"#ccc\",\"textShadowBlur\":0},\"shadowColor\":\"transparent\"},\"legend\":{\"padding\":5,\"itemGap\":10,\"shadowOffsetX\":0,\"backgroundColor\":\"transparent\",\"borderColor\":\"#ccc\",\"shadowOffsetY\":0,\"orient\":\"vertical\",\"shadowBlur\":0,\"bottom\":\"auto\",\"itemHeight\":14,\"show\":true,\"icon\":\"roundRect\",\"itemStyle\":{\"borderType\":\"solid\",\"shadowOffsetX\":0,\"borderColor\":\"inherit\",\"shadowOffsetY\":0,\"color\":\"inherit\",\"shadowBlur\":0,\"borderWidth\":0,\"opacity\":1,\"shadowColor\":\"transparent\"},\"top\":\"auto\",\"borderRadius\":0,\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"color\":\"inherit\",\"shadowBlur\":0,\"width\":\"auto\",\"type\":\"inherit\",\"opacity\":1,\"shadowColor\":\"transparent\"},\"left\":\"left\",\"borderWidth\":0,\"width\":\"auto\",\"itemWidth\":25,\"textStyle\":{\"textBorderWidth\":0,\"color\":\"#fff\",\"textShadowColor\":\"transparent\",\"ellipsis\":\"...\",\"overflow\":\"none\",\"fontSize\":12,\"lineHeight\":24,\"textShadowOffsetX\":0,\"textShadowOffsetY\":0,\"textBorderType\":\"solid\",\"fontWeight\":500,\"textBorderColor\":\"transparent\",\"textShadowBlur\":0},\"shadowColor\":\"rgba(0,0,0,.3)\",\"height\":\"auto\"},\"series\":{\"itemStyle\":{\"borderType\":\"solid\",\"shadowOffsetX\":0,\"borderColor\":\"#000\",\"shadowOffsetY\":0,\"color\":\"\",\"shadowBlur\":0,\"borderWidth\":0,\"opacity\":1,\"shadowColor\":\"#000\"},\"label\":{\"borderType\":\"solid\",\"rotate\":0,\"padding\":0,\"textBorderWidth\":0,\"backgroundColor\":\"transparent\",\"borderColor\":\"#fff\",\"color\":\"\",\"show\":true,\"textShadowColor\":\"transparent\",\"distanceToLabelLine\":5,\"ellipsis\":\"...\",\"overflow\":\"none\",\"borderRadius\":0,\"borderWidth\":0,\"fontSize\":12,\"lineHeight\":18,\"textShadowOffsetX\":0,\"position\":\"outside\",\"textShadowOffsetY\":0,\"textBorderType\":\"solid\",\"textBorderColor\":\"#fff\",\"textShadowBlur\":0},\"labelLine\":{\"show\":true,\"length\":10,\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"#000\"},\"length2\":14,\"smooth\":false}}},\r\n\t\t\tboardBase: {\"funnelNum\":8,\"lineNum\":8,\"gaugeNum\":8,\"barNum\":8,\"pieNum\":8},\r\n\t\t\tgauge: {\"tooltip\":{\"backgroundColor\":\"#123\",\"textStyle\":{\"color\":\"#fff\"}},\"backgroundColor\":\"transparent\",\"color\":[\"#5470c6\",\"#91cc75\",\"#fac858\",\"#ee6666\",\"#73c0de\",\"#3ba272\",\"#fc8452\",\"#9a60b4\",\"#ea7ccc\"],\"title\":{\"top\":\"bottom\",\"left\":\"left\"},\"series\":{\"pointer\":{\"offsetCenter\":[0,\"10%\"],\"icon\":\"path://M2.9,0.7L2.9,0.7c1.4,0,2.6,1.2,2.6,2.6v115c0,1.4-1.2,2.6-2.6,2.6l0,0c-1.4,0-2.6-1.2-2.6-2.6V3.3C0.3,1.9,1.4,0.7,2.9,0.7z\",\"width\":8,\"length\":\"80%\"},\"axisLine\":{\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"opacity\":0.5,\"shadowBlur\":1,\"shadowColor\":\"#000\"},\"roundCap\":true},\"anchor\":{\"show\":true,\"itemStyle\":{\"color\":\"inherit\"},\"size\":18,\"showAbove\":true},\"emphasis\":{\"disabled\":false},\"progress\":{\"show\":true,\"roundCap\":true,\"overlap\":true},\"splitNumber\":25,\"detail\":{\"formatter\":\"{value}\",\"backgroundColor\":\"inherit\",\"color\":\"#fff\",\"borderRadius\":3,\"width\":20,\"fontSize\":14,\"height\":16},\"title\":{\"fontSize\":14},\"animation\":true}},\r\n\t\t};\r\n\t},\r\n\tmounted(){\r\n\t\tthis.init();\r\n\t\tthis.getcaiwuxinxiCount();\r\n\t\tthis.caiwuxinxiChat1();\r\n\t\tthis.caiwuxinxiChat2();\r\n\t\tthis.caiwuxinxiChat3();\r\n\t},\r\n\tmethods:{\r\n\t\t// 词云\r\n\t\twordclouds(wordcloudData,echartsId) {\r\n\t\t\tlet wordcloud = $template2.back.board.wordcloud\r\n\t\t\twordcloud = JSON.parse(JSON.stringify(wordcloud), (k, v) => {\r\n\t\t\t  if(typeof v == 'string' && v.indexOf('function') > -1){\r\n\t\t\t\treturn eval(\"(function(){return \"+v+\" })()\")\r\n\t\t\t  }\r\n\t\t\t  return v;\r\n\t\t\t})\r\n\t\t\twordcloud.option.series[0].data=wordcloudData;\r\n\t\t\t\r\n\t\t\tthis.myChart0 = echarts.init(document.getElementById(echartsId));\r\n\t\t\tlet myChart = this.myChart0\r\n\t\t\tlet img = wordcloud.maskImage\r\n\t\t\r\n\t\t\tif (img) {\r\n\t\t\t\tvar maskImage = new Image();\r\n\t\t\t\tmaskImage.src = img\r\n\t\t\t\tmaskImage.onload = function() {\r\n\t\t\t\t\twordcloud.option.series[0].maskImage = maskImage\r\n\t\t\t\t\tmyChart.clear()\r\n\t\t\t\t\tmyChart.setOption(wordcloud.option)\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tdelete wordcloud.option.series[0].maskImage\r\n\t\t\t\tmyChart.clear()\r\n\t\t\t\tmyChart.setOption(wordcloud.option)\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 统计图动画\r\n\t\tmyChartInterval(type, xAxisData, seriesData, myChart) {\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tsetInterval(() => {\r\n\t\t\t\t\tlet xAxis = xAxisData.shift()\r\n\t\t\t\t\txAxisData.push(xAxis)\r\n\t\t\t\t\tlet series = seriesData.shift()\r\n\t\t\t\t\tseriesData.push(series)\r\n\t\t\t\t\r\n\t\t\t\t\tif (type == 1) {\r\n\t\t\t\t\t\tmyChart.setOption({\r\n\t\t\t\t\t\t\txAxis: [{\r\n\t\t\t\t\t\t\t\tdata: xAxisData\r\n\t\t\t\t\t\t\t}],\r\n\t\t\t\t\t\t\tseries: [{\r\n\t\t\t\t\t\t\t\tdata: seriesData\r\n\t\t\t\t\t\t\t}]\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (type == 2) {\r\n\t\t\t\t\t\tmyChart.setOption({\r\n\t\t\t\t\t\t\tyAxis: [{\r\n\t\t\t\t\t\t\t\tdata: xAxisData\r\n\t\t\t\t\t\t\t}],\r\n\t\t\t\t\t\t\tseries: [{\r\n\t\t\t\t\t\t\t\tdata: seriesData\r\n\t\t\t\t\t\t\t}]\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t}, $template2.back.board.bar.base.interval);\r\n\t\t\t})\r\n\t\t},\r\n\t\tinit(){\r\n\t\t\tif(this.$storage.get('Token')){\r\n\t\t\tthis.$http({\r\n\t\t\t\turl: `${this.$storage.get('sessionTable')}/session`,\r\n\t\t\t\tmethod: \"get\"\r\n\t\t\t}).then(({ data }) => {\r\n\t\t\t\tif (data && data.code != 0) {\r\n\t\t\t\trouter.push({ name: 'login' })\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t}else{\r\n\t\t\t\trouter.push({ name: 'login' })\r\n\t\t\t}\r\n\t\t},\r\n\t\tgetcaiwuxinxiCount() {\r\n\t\t\tthis.$http({\r\n\t\t\t\turl: `caiwuxinxi/count`,\r\n\t\t\t\tmethod: \"get\"\r\n\t\t\t}).then(({\r\n\t\t\t\tdata\r\n\t\t\t}) => {\r\n\t\t\t\tif (data && data.code == 0) {\r\n\t\t\t\t\tthis.caiwuxinxiCount = data.data\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tcaiwuxinxiChat1() {\r\n\t\t\tthis.$nextTick(()=>{\r\n\r\n        var caiwuxinxiChart1 = echarts.init(document.getElementById(\"caiwuxinxiChart1\"),'macarons');\r\n        this.$http({\r\n            url: `caiwuxinxi/value/dengjiriqi/shourujine/年`,\r\n            method: \"get\",\r\n        }).then(({ data }) => {\r\n            if (data && data.code === 0) {\r\n                let res = data.data;\r\n                let xAxis = [];\r\n                let yAxis = [];\r\n                let pArray = []\r\n                for(let i=0;i<res.length;i++){\r\n\t\t\t\t\tif(this.boardBase&&i==this.boardBase.funnelNum){\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n                    xAxis.push(res[i].dengjiriqi);\r\n                    yAxis.push(parseFloat((res[i].total)));\r\n                    pArray.push({\r\n                        value: parseFloat((res[i].total)),\r\n                        name: res[i].dengjiriqi\r\n                    })\r\n                }\r\n                var option = {};\r\n\t\t\t\tlet titleObj = this.funnel.title\r\n\t\t\t\ttitleObj.text = '收入统计'\r\n\t\t\t\t\r\n\t\t\t\tlet legendObj = {\r\n\t\t\t\t\tdata: xAxis,\r\n\t\t\t\t}\r\n\t\t\t\tlegendObj = Object.assign(legendObj , this.funnel.legend)\r\n\t\t\t\tlet tooltipObj = {trigger: 'item',formatter: '{b} : {c}'}\r\n\t\t\t\ttooltipObj = Object.assign(tooltipObj , this.funnel.tooltip?this.funnel.tooltip:{})\r\n\t\t\t\tlet seriesObj = {\r\n\t\t\t\t\tname: '收入统计',\r\n\t\t\t\t\tdata: pArray,\r\n\t\t\t\t\ttype: 'funnel',\r\n\t\t\t\t\tleft: '10%',\r\n\t\t\t\t\ttop: 60,\r\n\t\t\t\t\tbottom: 60,\r\n\t\t\t\t\twidth: '80%',\r\n\t\t\t\t\tminSize: '0%',\r\n\t\t\t\t\tmaxSize: '100%',\r\n\t\t\t\t}\r\n\t\t\t\tseriesObj = Object.assign(seriesObj , this.funnel.series)\r\n\t\t\t\toption = {\r\n\t\t\t\t\tbackgroundColor: this.funnel.backgroundColor,\r\n\t\t\t\t\tcolor: this.funnel.color,\r\n\t\t\t\t    title: titleObj,\r\n\t\t\t\t    legend: legendObj,\r\n\t\t\t\t    tooltip: tooltipObj,\r\n\t\t\t\t    series: seriesObj,\r\n\t\t\t\t}\r\n                // 使用刚指定的配置项和数据显示图表。\r\n                caiwuxinxiChart1.setOption(option);\r\n\t\t\t\t\r\n                  //根据窗口的大小变动图表\r\n                window.onresize = function() {\r\n                    caiwuxinxiChart1.resize();\r\n                };\r\n            }\r\n        });\r\n      })\r\n    },\r\n\r\n    caiwuxinxiChat2() {\r\n      this.$nextTick(()=>{\r\n\r\n        var caiwuxinxiChart2 = echarts.init(document.getElementById(\"caiwuxinxiChart2\"),'macarons');\r\n        this.$http({\r\n            url: `caiwuxinxi/value/dengjiriqi/zhichujine/年`,\r\n            method: \"get\",\r\n        }).then(({ data }) => {\r\n            if (data && data.code === 0) {\r\n                let res = data.data;\r\n                let xAxis = [];\r\n                let yAxis = [];\r\n                let pArray = []\r\n                for(let i=0;i<res.length;i++){\r\n\t\t\t\t\tif(this.boardBase&&i==this.boardBase.barNum){\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n                    xAxis.push(res[i].dengjiriqi);\r\n                    yAxis.push(parseFloat((res[i].total)));\r\n                    pArray.push({\r\n                        value: parseFloat((res[i].total)),\r\n                        name: res[i].dengjiriqi\r\n                    })\r\n                }\r\n                var option = {};\r\n                let titleObj = this.bar.title\r\n\t\t\t\ttitleObj.text = '支出统计'\r\n\t\t\t\t\r\n\t\t\t\tconst legendObj = this.bar.legend\r\n\t\t\t\tlet tooltipObj = {trigger: 'item',formatter: '{b} : {c}'}\r\n\t\t\t\ttooltipObj = Object.assign(tooltipObj , this.bar.tooltip?this.bar.tooltip:{})\r\n\t\t\t\t\r\n\t\t\t\tlet xAxisObj = this.bar.xAxis\r\n\t\t\t\txAxisObj.type = 'category'\r\n\t\t\t\txAxisObj.data = xAxis\r\n                xAxisObj.axisLabel.rotate=40\r\n\t\t\t\t\r\n\t\t\t\tlet yAxisObj = this.bar.yAxis\r\n\t\t\t\tyAxisObj.type = 'value'\r\n\t\t\t\tlet seriesObj = {\r\n\t\t\t\t\t\tdata: yAxis,\r\n\t\t\t\t\t\ttype: 'bar'\r\n\t\t\t\t\t}\r\n\t\t\t\tseriesObj = Object.assign(seriesObj , this.bar.series)\r\n\r\n\t\t\t\toption = {\r\n\t\t\t\t\tbackgroundColor: this.bar.backgroundColor,\r\n\t\t\t\t\tcolor: this.bar.color,\r\n\t\t\t\t\ttitle: titleObj,\r\n\t\t\t\t\tlegend: legendObj,\r\n\t\t\t\t\ttooltip: tooltipObj,\r\n\t\t\t\t\txAxis: xAxisObj,\r\n\t\t\t\t\tyAxis: yAxisObj,\r\n\t\t\t\t\tseries: [seriesObj]\r\n\t\t\t\t};\r\n                // 使用刚指定的配置项和数据显示图表。\r\n                caiwuxinxiChart2.setOption(option);\r\n\t\t\t\t\r\n                  //根据窗口的大小变动图表\r\n                window.onresize = function() {\r\n                    caiwuxinxiChart2.resize();\r\n                };\r\n            }\r\n        });\r\n      })\r\n    },\r\n\r\n    caiwuxinxiChat3() {\r\n      this.$nextTick(()=>{\r\n\r\n        var caiwuxinxiChart3 = echarts.init(document.getElementById(\"caiwuxinxiChart3\"),'macarons');\r\n        this.$http({\r\n            url: `caiwuxinxi/value/dengjiriqi/lirun/年`,\r\n            method: \"get\",\r\n        }).then(({ data }) => {\r\n            if (data && data.code === 0) {\r\n                let res = data.data;\r\n                let xAxis = [];\r\n                let yAxis = [];\r\n                let pArray = []\r\n                for(let i=0;i<res.length;i++){\r\n\t\t\t\t\tif(this.boardBase&&i==this.boardBase.lineNum){\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n                    xAxis.push(res[i].dengjiriqi);\r\n                    yAxis.push(parseFloat((res[i].total)));\r\n                    pArray.push({\r\n                        value: parseFloat((res[i].total)),\r\n                        name: res[i].dengjiriqi\r\n                    })\r\n                }\r\n                var option = {};\r\n                let titleObj = this.line.title\r\n\t\t\t\ttitleObj.text = '利润统计'\r\n\t\t\t\t\r\n\t\t\t\tconst legendObj = this.line.legend\r\n\t\t\t\tlet tooltipObj = { trigger: 'item',formatter: '{b} : {c}'}\r\n\t\t\t\ttooltipObj = Object.assign(tooltipObj , this.line.tooltip?this.line.tooltip:{})\r\n\t\t\t\t\r\n\t\t\t\tlet xAxisObj = this.line.xAxis\r\n\t\t\t\txAxisObj.type = 'category'\r\n\t\t\t\txAxisObj.boundaryGap = false\r\n\t\t\t\txAxisObj.data = xAxis\r\n                xAxisObj.axisLabel.rotate=70\r\n\t\t\t\t\r\n\t\t\t\tlet yAxisObj = this.line.yAxis\r\n\t\t\t\tyAxisObj.type = 'value'\r\n\t\t\t\t\r\n\t\t\t\tlet seriesObj = {\r\n\t\t\t\t\tdata: yAxis,\r\n\t\t\t\t\ttype: 'line',\r\n\t\t\t\t}\r\n\t\t\t\tseriesObj = Object.assign(seriesObj , this.line.series)\r\n\t\t\t\t\r\n\t\t\t\toption = {\r\n\t\t\t\t\tbackgroundColor: this.line.backgroundColor,\r\n\t\t\t\t\tcolor: this.line.color,\r\n\t\t\t\t\ttitle: titleObj,\r\n\t\t\t\t\tlegend: legendObj,\r\n\t\t\t\t\ttooltip: tooltipObj,\r\n\t\t\t\t\txAxis: xAxisObj,\r\n\t\t\t\t\tyAxis: yAxisObj,\r\n\t\t\t\t\tseries: [seriesObj]\r\n\t\t\t\t};\r\n                // 使用刚指定的配置项和数据显示图表。\r\n                caiwuxinxiChart3.setOption(option);\r\n                  //根据窗口的大小变动图表\r\n                window.onresize = function() {\r\n                    caiwuxinxiChart3.resize();\r\n                };\r\n            }\r\n        });\r\n      })\r\n    },\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n    .cardView {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        width: 100%;\r\n\r\n        .cards {\r\n            display: flex;\r\n            align-items: center;\r\n            width: 100%;\r\n            margin-bottom: 10px;\r\n            justify-content: center;\r\n            .card {\r\n                width: calc(25% - 20px);\r\n                margin: 0 10px;\r\n                /deep/.el-card__body{\r\n                    padding: 0;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\t\r\n\t// 日历\r\n\t.calendar td .text {\r\n\t\t\t\tborder-radius: 12px;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\tbackground: #fff;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\theight: 100%;\r\n\t\t\t}\r\n\t.calendar td .text:hover {\r\n\t\t\t\tbackground: rgba(78,110,242,.1);\r\n\t\t\t}\r\n\t.calendar td .text .new {\r\n\t\t\t\tcolor: #000;\r\n\t\t\t\tfont-size: 24px;\r\n\t\t\t\tline-height: 1.4;\r\n\t\t\t}\r\n\t.calendar td .text .old {\r\n\t\t\t\tcolor: #666;\r\n\t\t\t\tfont-size: 16px;\r\n\t\t\t\tline-height: 1.4;\r\n\t\t\t}\r\n\t.calendar td.festival .text {\r\n\t\t\t\tborder-radius: 12px;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\tbackground: rgba(235,51,51,.05);\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\theight: 100%;\r\n\t\t\t}\r\n\t.calendar td.festival .text:hover {\r\n\t\t\t\tbackground: rgba(78,110,242,.1);\r\n\t\t\t}\r\n\t.calendar td.festival .text .new {\r\n\t\t\t\tcolor: #000;\r\n\t\t\t\tfont-size: 24px;\r\n\t\t\t\tline-height: 1.4;\r\n\t\t\t}\r\n\t.calendar td.festival .text .old {\r\n\t\t\t\tcolor: #666;\r\n\t\t\t\tfont-size: 16px;\r\n\t\t\t\tline-height: 1.4;\r\n\t\t\t}\r\n\t.calendar td.other .text {\r\n\t\t\t\tborder-radius: 12px;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\tbackground: #fff;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\topacity: 0.3;\r\n\t\t\t\theight: 100%;\r\n\t\t\t}\r\n\t.calendar td.other .text:hover {\r\n\t\t\t\tbackground: rgba(78,110,242,.1);\r\n\t\t\t}\r\n\t.calendar td.other .text .new {\r\n\t\t\t\tcolor: #000;\r\n\t\t\t\tfont-size: 24px;\r\n\t\t\t\tline-height: 1.4;\r\n\t\t\t}\r\n\t.calendar td.other .text .old {\r\n\t\t\t\tcolor: #666;\r\n\t\t\t\tfont-size: 16px;\r\n\t\t\t\tline-height: 1.4;\r\n\t\t\t}\r\n\t.calendar td.today .text {\r\n\t\t\t\tborder-radius: 12px;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #2ddcd3;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\theight: 100%;\r\n\t\t\t}\r\n\t.calendar td.today .text:hover {\r\n\t\t\t\tbackground: rgba(64, 158, 255,.5);\r\n\t\t\t}\r\n\t.calendar td.today .text .new {\r\n\t\t\t\tcolor: inherit;\r\n\t\t\t\tfont-size: 24px;\r\n\t\t\t\tline-height: 1.4;\r\n\t\t\t}\r\n\t.calendar td.today .text .old {\r\n\t\t\t\tcolor: inherit;\r\n\t\t\t\tfont-size: 16px;\r\n\t\t\t\tline-height: 1.4;\r\n\t\t\t}\r\n\t\r\n\t// echarts1\r\n\t.type1 .echarts1 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: auto;\r\n\t\t\t}\r\n\t.type1 .echarts1:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t// echarts2\r\n\t.type2 .echarts1 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 49%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type2 .echarts1:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t.type2 .echarts2 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 49%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type2 .echarts2:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t// echarts3\r\n\t.type3 .echarts1 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type3 .echarts1:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t.type3 .echarts2 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 49%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type3 .echarts2:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t.type3 .echarts3 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 49%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type3 .echarts3:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t// echarts4\r\n\t.type4 .echarts1 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 49%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type4 .echarts1:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t.type4 .echarts2 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 49%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type4 .echarts2:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t.type4 .echarts3 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 49%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type4 .echarts3:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t.type4 .echarts4 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 49%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type4 .echarts4:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t// echarts5\r\n\t.type5 .echarts1 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type5 .echarts1:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t.type5 .echarts2 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 49%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type5 .echarts2:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t.type5 .echarts3 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 49%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type5 .echarts3:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t.type5 .echarts4 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 49%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type5 .echarts4:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t.type5 .echarts5 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 49%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type5 .echarts5:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t\r\n\t.echarts-flag-2 {\r\n\t  display: flex;\r\n\t  flex-wrap: wrap;\r\n\t  justify-content: space-between;\r\n\t  padding: 10px 20px;\r\n\t  background: rebeccapurple;\r\n\t\r\n\t  &>div {\r\n\t    width: 32%;\r\n\t    height: 300px;\r\n\t    margin: 10px 0;\r\n\t    background: rgba(255,255,255,.1);\r\n\t    border-radius: 8px;\r\n\t    padding: 10px 20px;\r\n\t  }\r\n\t}\r\n</style>\r\n"], "mappings": ";;;;;;AA+BA;AACA,OAAAA,MAAA;AACA,YAAAC,OAAA;AACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,eAAA;MACAC,IAAA;QAAA;QAAA;UAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;UAAA;UAAA;YAAA;YAAA;YAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;YAAA;YAAA;UAAA;UAAA;YAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;YAAA;YAAA;UAAA;UAAA;YAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;YAAA;YAAA;UAAA;UAAA;YAAA;YAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;YAAA;UAAA;QAAA;QAAA;UAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;UAAA;UAAA;YAAA;YAAA;YAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;YAAA;YAAA;UAAA;UAAA;YAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;YAAA;YAAA;UAAA;UAAA;YAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;YAAA;YAAA;UAAA;UAAA;YAAA;YAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;YAAA;UAAA;QAAA;QAAA;QAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;UAAA;UAAA;UAAA;UAAA;UAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;UAAA;UAAA;UAAA;QAAA;QAAA;UAAA;UAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;UAAA;UAAA;UAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;UAAA;UAAA;QAAA;QAAA;UAAA;UAAA;YAAA;UAAA;QAAA;QAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;UAAA;UAAA;QAAA;MAAA;MACAC,GAAA;QAAA;QAAA;UAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;UAAA;UAAA;YAAA;YAAA;YAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;YAAA;YAAA;UAAA;UAAA;YAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;YAAA;YAAA;UAAA;UAAA;YAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;YAAA;YAAA;UAAA;UAAA;YAAA;YAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;YAAA;UAAA;QAAA;QAAA;UAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;UAAA;UAAA;YAAA;YAAA;YAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;YAAA;YAAA;UAAA;UAAA;YAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;YAAA;YAAA;UAAA;UAAA;YAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;YAAA;YAAA;UAAA;UAAA;YAAA;YAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;YAAA;UAAA;QAAA;QAAA;QAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;UAAA;UAAA;UAAA;UAAA;UAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;UAAA;UAAA;UAAA;QAAA;QAAA;UAAA;UAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;UAAA;UAAA;UAAA;QAAA;QAAA;UAAA;UAAA;YAAA;UAAA;QAAA;QAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;UAAA;UAAA;QAAA;QAAA;UAAA;UAAA;QAAA;MAAA;MACAC,GAAA;QAAA;UAAA;UAAA;YAAA;UAAA;QAAA;QAAA;QAAA;QAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;UAAA;UAAA;QAAA;QAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;UAAA;UAAA;UAAA;UAAA;UAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;UAAA;UAAA;UAAA;QAAA;QAAA;UAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;UAAA;UAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;UAAA;UAAA;YAAA;YAAA;YAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;YAAA;YAAA;YAAA;UAAA;QAAA;MAAA;MACAC,MAAA;QAAA;UAAA;UAAA;YAAA;UAAA;QAAA;QAAA;QAAA;QAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;UAAA;UAAA;QAAA;QAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;UAAA;UAAA;UAAA;UAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;UAAA;UAAA;UAAA;UAAA;UAAA;UAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;UAAA;UAAA;UAAA;QAAA;QAAA;UAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;UAAA;UAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;UAAA;UAAA;YAAA;YAAA;YAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;cAAA;YAAA;YAAA;YAAA;UAAA;QAAA;MAAA;MACAC,SAAA;QAAA;QAAA;QAAA;QAAA;QAAA;MAAA;MACAC,KAAA;QAAA;UAAA;UAAA;YAAA;UAAA;QAAA;QAAA;QAAA;QAAA;UAAA;UAAA;QAAA;QAAA;UAAA;YAAA;YAAA;YAAA;YAAA;UAAA;UAAA;YAAA;cAAA;cAAA;cAAA;cAAA;cAAA;YAAA;YAAA;UAAA;UAAA;YAAA;YAAA;cAAA;YAAA;YAAA;YAAA;UAAA;UAAA;YAAA;UAAA;UAAA;YAAA;YAAA;YAAA;UAAA;UAAA;UAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;UAAA;UAAA;YAAA;UAAA;UAAA;QAAA;MAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,IAAA;IACA,KAAAC,kBAAA;IACA,KAAAC,eAAA;IACA,KAAAC,eAAA;IACA,KAAAC,eAAA;EACA;EACAC,OAAA;IACA;IACAC,UAAA,WAAAA,WAAAC,aAAA,EAAAC,SAAA;MACA,IAAAC,SAAA,GAAAC,UAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAH,SAAA;MACAA,SAAA,GAAAI,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAN,SAAA,aAAAO,CAAA,EAAAC,CAAA;QACA,WAAAA,CAAA,gBAAAA,CAAA,CAAAC,OAAA;UACA,OAAAC,IAAA,yBAAAF,CAAA;QACA;QACA,OAAAA,CAAA;MACA;MACAR,SAAA,CAAAW,MAAA,CAAAC,MAAA,IAAA9B,IAAA,GAAAgB,aAAA;MAEA,KAAAe,QAAA,GAAAhC,OAAA,CAAAU,IAAA,CAAAuB,QAAA,CAAAC,cAAA,CAAAhB,SAAA;MACA,IAAAiB,OAAA,QAAAH,QAAA;MACA,IAAAI,GAAA,GAAAjB,SAAA,CAAAkB,SAAA;MAEA,IAAAD,GAAA;QACA,IAAAC,SAAA,OAAAC,KAAA;QACAD,SAAA,CAAAE,GAAA,GAAAH,GAAA;QACAC,SAAA,CAAAG,MAAA;UACArB,SAAA,CAAAW,MAAA,CAAAC,MAAA,IAAAM,SAAA,GAAAA,SAAA;UACAF,OAAA,CAAAM,KAAA;UACAN,OAAA,CAAAO,SAAA,CAAAvB,SAAA,CAAAW,MAAA;QACA;MACA;QACA,OAAAX,SAAA,CAAAW,MAAA,CAAAC,MAAA,IAAAM,SAAA;QACAF,OAAA,CAAAM,KAAA;QACAN,OAAA,CAAAO,SAAA,CAAAvB,SAAA,CAAAW,MAAA;MACA;IACA;IACA;IACAa,eAAA,WAAAA,gBAAAC,IAAA,EAAAC,SAAA,EAAAC,UAAA,EAAAX,OAAA;MACA,KAAAY,SAAA;QACAC,WAAA;UACA,IAAAC,KAAA,GAAAJ,SAAA,CAAAK,KAAA;UACAL,SAAA,CAAAM,IAAA,CAAAF,KAAA;UACA,IAAAlB,MAAA,GAAAe,UAAA,CAAAI,KAAA;UACAJ,UAAA,CAAAK,IAAA,CAAApB,MAAA;UAEA,IAAAa,IAAA;YACAT,OAAA,CAAAO,SAAA;cACAO,KAAA;gBACAhD,IAAA,EAAA4C;cACA;cACAd,MAAA;gBACA9B,IAAA,EAAA6C;cACA;YACA;UACA;UACA,IAAAF,IAAA;YACAT,OAAA,CAAAO,SAAA;cACAU,KAAA;gBACAnD,IAAA,EAAA4C;cACA;cACAd,MAAA;gBACA9B,IAAA,EAAA6C;cACA;YACA;UACA;QACA,GAAA1B,UAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAlB,GAAA,CAAAiD,IAAA,CAAAC,QAAA;MACA;IACA;IACA5C,IAAA,WAAAA,KAAA;MACA,SAAA6C,QAAA,CAAAC,GAAA;QACA,KAAAC,KAAA;UACAC,GAAA,KAAAC,MAAA,MAAAJ,QAAA,CAAAC,GAAA;UACAI,MAAA;QACA,GAAAC,IAAA,WAAAC,IAAA;UAAA,IAAA7D,IAAA,GAAA6D,IAAA,CAAA7D,IAAA;UACA,IAAAA,IAAA,IAAAA,IAAA,CAAA8D,IAAA;YACAhE,MAAA,CAAAoD,IAAA;cAAAa,IAAA;YAAA;UACA;QACA;MACA;QACAjE,MAAA,CAAAoD,IAAA;UAAAa,IAAA;QAAA;MACA;IACA;IACArD,kBAAA,WAAAA,mBAAA;MAAA,IAAAsD,KAAA;MACA,KAAAR,KAAA;QACAC,GAAA;QACAE,MAAA;MACA,GAAAC,IAAA,WAAAK,KAAA,EAEA;QAAA,IADAjE,IAAA,GAAAiE,KAAA,CAAAjE,IAAA;QAEA,IAAAA,IAAA,IAAAA,IAAA,CAAA8D,IAAA;UACAE,KAAA,CAAA/D,eAAA,GAAAD,IAAA,CAAAA,IAAA;QACA;MACA;IACA;IACAW,eAAA,WAAAA,gBAAA;MAAA,IAAAuD,MAAA;MACA,KAAApB,SAAA;QAEA,IAAAqB,gBAAA,GAAApE,OAAA,CAAAU,IAAA,CAAAuB,QAAA,CAAAC,cAAA;QACAiC,MAAA,CAAAV,KAAA;UACAC,GAAA;UACAE,MAAA;QACA,GAAAC,IAAA,WAAAQ,KAAA;UAAA,IAAApE,IAAA,GAAAoE,KAAA,CAAApE,IAAA;UACA,IAAAA,IAAA,IAAAA,IAAA,CAAA8D,IAAA;YACA,IAAAO,GAAA,GAAArE,IAAA,CAAAA,IAAA;YACA,IAAAgD,KAAA;YACA,IAAAG,KAAA;YACA,IAAAmB,MAAA;YACA,SAAAC,CAAA,MAAAA,CAAA,GAAAF,GAAA,CAAAG,MAAA,EAAAD,CAAA;cACA,IAAAL,MAAA,CAAA5D,SAAA,IAAAiE,CAAA,IAAAL,MAAA,CAAA5D,SAAA,CAAAmE,SAAA;gBACA;cACA;cACAzB,KAAA,CAAAE,IAAA,CAAAmB,GAAA,CAAAE,CAAA,EAAAG,UAAA;cACAvB,KAAA,CAAAD,IAAA,CAAAyB,UAAA,CAAAN,GAAA,CAAAE,CAAA,EAAAK,KAAA;cACAN,MAAA,CAAApB,IAAA;gBACA2B,KAAA,EAAAF,UAAA,CAAAN,GAAA,CAAAE,CAAA,EAAAK,KAAA;gBACAb,IAAA,EAAAM,GAAA,CAAAE,CAAA,EAAAG;cACA;YACA;YACA,IAAA7C,MAAA;YACA,IAAAiD,QAAA,GAAAZ,MAAA,CAAA7D,MAAA,CAAA0E,KAAA;YACAD,QAAA,CAAAE,IAAA;YAEA,IAAAC,SAAA;cACAjF,IAAA,EAAAgD;YACA;YACAiC,SAAA,GAAAC,MAAA,CAAAC,MAAA,CAAAF,SAAA,EAAAf,MAAA,CAAA7D,MAAA,CAAA+E,MAAA;YACA,IAAAC,UAAA;cAAAC,OAAA;cAAAC,SAAA;YAAA;YACAF,UAAA,GAAAH,MAAA,CAAAC,MAAA,CAAAE,UAAA,EAAAnB,MAAA,CAAA7D,MAAA,CAAAmF,OAAA,GAAAtB,MAAA,CAAA7D,MAAA,CAAAmF,OAAA;YACA,IAAAC,SAAA;cACA1B,IAAA;cACA/D,IAAA,EAAAsE,MAAA;cACA3B,IAAA;cACA+C,IAAA;cACAC,GAAA;cACAC,MAAA;cACAC,KAAA;cACAC,OAAA;cACAC,OAAA;YACA;YACAN,SAAA,GAAAP,MAAA,CAAAC,MAAA,CAAAM,SAAA,EAAAvB,MAAA,CAAA7D,MAAA,CAAAyB,MAAA;YACAD,MAAA;cACAmE,eAAA,EAAA9B,MAAA,CAAA7D,MAAA,CAAA2F,eAAA;cACAC,KAAA,EAAA/B,MAAA,CAAA7D,MAAA,CAAA4F,KAAA;cACAlB,KAAA,EAAAD,QAAA;cACAM,MAAA,EAAAH,SAAA;cACAO,OAAA,EAAAH,UAAA;cACAvD,MAAA,EAAA2D;YACA;YACA;YACAtB,gBAAA,CAAA1B,SAAA,CAAAZ,MAAA;;YAEA;YACAqE,MAAA,CAAAC,QAAA;cACAhC,gBAAA,CAAAiC,MAAA;YACA;UACA;QACA;MACA;IACA;IAEAxF,eAAA,WAAAA,gBAAA;MAAA,IAAAyF,MAAA;MACA,KAAAvD,SAAA;QAEA,IAAAwD,gBAAA,GAAAvG,OAAA,CAAAU,IAAA,CAAAuB,QAAA,CAAAC,cAAA;QACAoE,MAAA,CAAA7C,KAAA;UACAC,GAAA;UACAE,MAAA;QACA,GAAAC,IAAA,WAAA2C,KAAA;UAAA,IAAAvG,IAAA,GAAAuG,KAAA,CAAAvG,IAAA;UACA,IAAAA,IAAA,IAAAA,IAAA,CAAA8D,IAAA;YACA,IAAAO,GAAA,GAAArE,IAAA,CAAAA,IAAA;YACA,IAAAgD,KAAA;YACA,IAAAG,KAAA;YACA,IAAAmB,MAAA;YACA,SAAAC,CAAA,MAAAA,CAAA,GAAAF,GAAA,CAAAG,MAAA,EAAAD,CAAA;cACA,IAAA8B,MAAA,CAAA/F,SAAA,IAAAiE,CAAA,IAAA8B,MAAA,CAAA/F,SAAA,CAAAkG,MAAA;gBACA;cACA;cACAxD,KAAA,CAAAE,IAAA,CAAAmB,GAAA,CAAAE,CAAA,EAAAG,UAAA;cACAvB,KAAA,CAAAD,IAAA,CAAAyB,UAAA,CAAAN,GAAA,CAAAE,CAAA,EAAAK,KAAA;cACAN,MAAA,CAAApB,IAAA;gBACA2B,KAAA,EAAAF,UAAA,CAAAN,GAAA,CAAAE,CAAA,EAAAK,KAAA;gBACAb,IAAA,EAAAM,GAAA,CAAAE,CAAA,EAAAG;cACA;YACA;YACA,IAAA7C,MAAA;YACA,IAAAiD,QAAA,GAAAuB,MAAA,CAAAlG,GAAA,CAAA4E,KAAA;YACAD,QAAA,CAAAE,IAAA;YAEA,IAAAC,SAAA,GAAAoB,MAAA,CAAAlG,GAAA,CAAAiF,MAAA;YACA,IAAAC,UAAA;cAAAC,OAAA;cAAAC,SAAA;YAAA;YACAF,UAAA,GAAAH,MAAA,CAAAC,MAAA,CAAAE,UAAA,EAAAgB,MAAA,CAAAlG,GAAA,CAAAqF,OAAA,GAAAa,MAAA,CAAAlG,GAAA,CAAAqF,OAAA;YAEA,IAAAiB,QAAA,GAAAJ,MAAA,CAAAlG,GAAA,CAAA6C,KAAA;YACAyD,QAAA,CAAA9D,IAAA;YACA8D,QAAA,CAAAzG,IAAA,GAAAgD,KAAA;YACAyD,QAAA,CAAAC,SAAA,CAAAC,MAAA;YAEA,IAAAC,QAAA,GAAAP,MAAA,CAAAlG,GAAA,CAAAgD,KAAA;YACAyD,QAAA,CAAAjE,IAAA;YACA,IAAA8C,SAAA;cACAzF,IAAA,EAAAmD,KAAA;cACAR,IAAA;YACA;YACA8C,SAAA,GAAAP,MAAA,CAAAC,MAAA,CAAAM,SAAA,EAAAY,MAAA,CAAAlG,GAAA,CAAA2B,MAAA;YAEAD,MAAA;cACAmE,eAAA,EAAAK,MAAA,CAAAlG,GAAA,CAAA6F,eAAA;cACAC,KAAA,EAAAI,MAAA,CAAAlG,GAAA,CAAA8F,KAAA;cACAlB,KAAA,EAAAD,QAAA;cACAM,MAAA,EAAAH,SAAA;cACAO,OAAA,EAAAH,UAAA;cACArC,KAAA,EAAAyD,QAAA;cACAtD,KAAA,EAAAyD,QAAA;cACA9E,MAAA,GAAA2D,SAAA;YACA;YACA;YACAa,gBAAA,CAAA7D,SAAA,CAAAZ,MAAA;;YAEA;YACAqE,MAAA,CAAAC,QAAA;cACAG,gBAAA,CAAAF,MAAA;YACA;UACA;QACA;MACA;IACA;IAEAvF,eAAA,WAAAA,gBAAA;MAAA,IAAAgG,MAAA;MACA,KAAA/D,SAAA;QAEA,IAAAgE,gBAAA,GAAA/G,OAAA,CAAAU,IAAA,CAAAuB,QAAA,CAAAC,cAAA;QACA4E,MAAA,CAAArD,KAAA;UACAC,GAAA;UACAE,MAAA;QACA,GAAAC,IAAA,WAAAmD,KAAA;UAAA,IAAA/G,IAAA,GAAA+G,KAAA,CAAA/G,IAAA;UACA,IAAAA,IAAA,IAAAA,IAAA,CAAA8D,IAAA;YACA,IAAAO,GAAA,GAAArE,IAAA,CAAAA,IAAA;YACA,IAAAgD,KAAA;YACA,IAAAG,KAAA;YACA,IAAAmB,MAAA;YACA,SAAAC,CAAA,MAAAA,CAAA,GAAAF,GAAA,CAAAG,MAAA,EAAAD,CAAA;cACA,IAAAsC,MAAA,CAAAvG,SAAA,IAAAiE,CAAA,IAAAsC,MAAA,CAAAvG,SAAA,CAAA0G,OAAA;gBACA;cACA;cACAhE,KAAA,CAAAE,IAAA,CAAAmB,GAAA,CAAAE,CAAA,EAAAG,UAAA;cACAvB,KAAA,CAAAD,IAAA,CAAAyB,UAAA,CAAAN,GAAA,CAAAE,CAAA,EAAAK,KAAA;cACAN,MAAA,CAAApB,IAAA;gBACA2B,KAAA,EAAAF,UAAA,CAAAN,GAAA,CAAAE,CAAA,EAAAK,KAAA;gBACAb,IAAA,EAAAM,GAAA,CAAAE,CAAA,EAAAG;cACA;YACA;YACA,IAAA7C,MAAA;YACA,IAAAiD,QAAA,GAAA+B,MAAA,CAAA3G,IAAA,CAAA6E,KAAA;YACAD,QAAA,CAAAE,IAAA;YAEA,IAAAC,SAAA,GAAA4B,MAAA,CAAA3G,IAAA,CAAAkF,MAAA;YACA,IAAAC,UAAA;cAAAC,OAAA;cAAAC,SAAA;YAAA;YACAF,UAAA,GAAAH,MAAA,CAAAC,MAAA,CAAAE,UAAA,EAAAwB,MAAA,CAAA3G,IAAA,CAAAsF,OAAA,GAAAqB,MAAA,CAAA3G,IAAA,CAAAsF,OAAA;YAEA,IAAAiB,QAAA,GAAAI,MAAA,CAAA3G,IAAA,CAAA8C,KAAA;YACAyD,QAAA,CAAA9D,IAAA;YACA8D,QAAA,CAAAQ,WAAA;YACAR,QAAA,CAAAzG,IAAA,GAAAgD,KAAA;YACAyD,QAAA,CAAAC,SAAA,CAAAC,MAAA;YAEA,IAAAC,QAAA,GAAAC,MAAA,CAAA3G,IAAA,CAAAiD,KAAA;YACAyD,QAAA,CAAAjE,IAAA;YAEA,IAAA8C,SAAA;cACAzF,IAAA,EAAAmD,KAAA;cACAR,IAAA;YACA;YACA8C,SAAA,GAAAP,MAAA,CAAAC,MAAA,CAAAM,SAAA,EAAAoB,MAAA,CAAA3G,IAAA,CAAA4B,MAAA;YAEAD,MAAA;cACAmE,eAAA,EAAAa,MAAA,CAAA3G,IAAA,CAAA8F,eAAA;cACAC,KAAA,EAAAY,MAAA,CAAA3G,IAAA,CAAA+F,KAAA;cACAlB,KAAA,EAAAD,QAAA;cACAM,MAAA,EAAAH,SAAA;cACAO,OAAA,EAAAH,UAAA;cACArC,KAAA,EAAAyD,QAAA;cACAtD,KAAA,EAAAyD,QAAA;cACA9E,MAAA,GAAA2D,SAAA;YACA;YACA;YACAqB,gBAAA,CAAArE,SAAA,CAAAZ,MAAA;YACA;YACAqE,MAAA,CAAAC,QAAA;cACAW,gBAAA,CAAAV,MAAA;YACA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}