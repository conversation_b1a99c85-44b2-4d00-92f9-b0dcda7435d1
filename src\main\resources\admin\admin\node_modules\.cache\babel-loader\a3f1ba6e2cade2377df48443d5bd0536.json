{"remainingRequest": "D:\\project\\admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\project\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\project\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\admin\\src\\components\\index\\IndexMain.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\admin\\src\\components\\index\\IndexMain.vue", "mtime": 1711079474000}, {"path": "D:\\project\\admin\\babel.config.js", "mtime": 1711079474000}, {"path": "D:\\project\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\project\\admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\project\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\project\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\project\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["IndexAside", "IndexHeader", "TagsView", "menu", "components", "data", "menuList", "role", "currentIndex", "itemMenu", "title", "isCollapse", "mounted", "menus", "list", "$storage", "get", "created", "init", "methods", "$nextTick", "collapseChange", "collapse", "menu<PERSON><PERSON><PERSON>", "$router", "push", "name", "tableName", "titleChange", "index", "homeChange", "centerChange"], "sources": ["src/components/index/IndexMain.vue"], "sourcesContent": ["<template>\r\n\t<div style=\"height: 100%;\">\r\n\t\t<el-main :style='\"vertical\" == \"vertical\" ? (2 == 1 ? {\"minHeight\":\"100%\",\"padding\":\"0\",\"margin\":\"0 0 0 210px\",\"position\":\"relative\",\"display\":\"block\"} : (2 == 2 ? (isCollapse ? {\"minHeight\":\"100%\",\"padding\":\"0\",\"margin\":\"0 0 0 64px\",\"position\":\"relative\",\"display\":\"block\"} : {\"minHeight\":\"100%\",\"padding\":\"0\",\"margin\":\"0 0 0 210px\",\"background\":\"rgb(235, 242, 251)\",\"display\":\"block\",\"overflow-x\":\"hidden\",\"position\":\"relative\"}) : \"\")) : {\"minHeight\":\"100%\",\"margin\":\"0\",\"position\":\"relative\"}'>\r\n\t\t\t<!-- top -->\r\n\t\t\t<index-header :style='{\"padding\":\"8px 20px\",\"alignItems\":\"center\",\"top\":\"0\",\"left\":\"0\",\"background\":\"linear-gradient(120deg, #0f75ff 60%, #2ddcd3  100%)\",\"display\":\"flex\",\"width\":\"100%\",\"position\":\"fixed\",\"zIndex\":\"1002\",\"height\":\"60px\"}'></index-header>\r\n\t\t\t\r\n\t\t\t<!-- menu -->\r\n\t\t\t<template v-if=\"'vertical' == 'vertical'\">\r\n\t\t\t  <template v-if=\"2 == 1\">\r\n\t\t\t\t<index-aside :style='{\"boxShadow\":\"1px 0 6px  rgba(64, 158, 255, .3)\",\"overflow\":\"hidden\",\"top\":\"0\",\"left\":\"0\",\"background\":\"#304156\",\"bottom\":\"0\",\"width\":\"210px\",\"fontSize\":\"0px\",\"position\":\"fixed\",\"height\":\"100%\",\"zIndex\":\"1001\"}'></index-aside>\r\n\t\t\t  </template>\r\n\t\t\t  <template v-if=\"2 == 2\">\r\n\t\t\t\t<index-aside :is-collapse=\"isCollapse\" @oncollapsechange=\"collapseChange\" :style='isCollapse ? {\"boxShadow\":\"1px 0 6px  rgba(64, 158, 255, .3)\",\"overflow\":\"hidden\",\"top\":\"0\",\"left\":\"0\",\"background\":\"#304156\",\"bottom\":\"0\",\"width\":\"64px\",\"fontSize\":\"0px\",\"position\":\"fixed\",\"transition\":\"width 0.3s\",\"height\":\"100%\",\"zIndex\":\"1001\"} : {\"boxShadow\":\"1px 0 6px  rgba(64, 158, 255, .3)\",\"padding\":\"0 0 60px\",\"overflow\":\"hidden\",\"top\":\"60px\",\"left\":\"0\",\"background\":\"#0d102c\",\"bottom\":\"0\",\"width\":\"210px\",\"position\":\"fixed\",\"transition\":\"width 0.3s\",\"height\":\"100%\",\"zIndex\":\"1001\"}'></index-aside>\r\n\t\t\t  </template>\r\n\t\t\t</template>\r\n\t\t\t<template v-if=\"'vertical' == 'horizontal'\">\r\n\t\t\t  <template v-if=\"2 == 1\">\r\n\t\t\t\t<index-aside :style='{\"width\":\"100%\",\"borderColor\":\"#efefef\",\"borderStyle\":\"solid\",\"background\":\"#304156\",\"borderWidth\":\"0 0 1px 0\",\"height\":\"auto\"}'></index-aside>\r\n\t\t\t  </template>\r\n\t\t\t  <template v-if=\"2 == 2\">\r\n\t\t\t\t<index-aside :style='{\"borderColor\":\"#efefef\",\"background\":\"#FFF\",\"borderWidth\":\"0 0 1px 0\",\"display\":\"flex\",\"width\":\"100%\",\"borderStyle\":\"solid\",\"height\":\"auto\"}'></index-aside>\r\n\t\t\t  </template>\r\n\t\t\t</template>\r\n\t\t\t\r\n\t\t\t<!-- breadcrumb -->\r\n\t\t\t<bread-crumbs :title=\"title\" :style='{\"width\":\"calc(100% - 60px)\",\"padding\":\"20px\",\"margin\":\"60px 0 0 0px \",\"borderColor\":\"#eee\",\"borderStyle\":\"solid\",\"borderWidth\":\"0 0 1px 0\"}' class=\"bread-crumbs\"></bread-crumbs>\r\n\t\t\t\r\n\t\t\t<!-- TagsView -->\r\n\t\t\t<tags-view />\r\n\t\t\t\n\t\t\t<router-view class=\"router-view\"></router-view>\n\t\t</el-main>\r\n\t</div>\n</template>\r\n\n<script>\r\n\timport IndexAside from '@/components/index/IndexAsideStatic'\r\n\timport IndexHeader from '@/components/index/IndexHeader'\r\n\timport TagsView from '@/components/index/TagsView'\n\timport menu from \"@/utils/menu\";\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tIndexAside,\r\n\t\t\tIndexHeader,\r\n\t\t\tTagsView\r\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tmenuList: [],\n\t\t\t\trole: \"\",\n\t\t\t\tcurrentIndex: -2,\n\t\t\t\titemMenu: [],\n\t\t\t\ttitle: '',\r\n\t\t\t\tisCollapse: false,\r\n\t\t\t};\n\t\t},\n\t\tmounted() {\n\t\t\tlet menus = menu.list();\n\t\t\tthis.menuList = menus;\n\t\t\tthis.role = this.$storage.get(\"role\");\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.init();\r\n\t\t},\n\t\tmethods: {\r\n\t\t\tinit(){\r\n\t\t\t\tthis.$nextTick(()=>{\r\n\t\t\t\t\t\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tcollapseChange(collapse) {\r\n\t\t\t\tthis.isCollapse = collapse\r\n\t\t\t},\n\t\t\tmenuHandler(menu) {\n\t\t\t\tthis.$router.push({\n\t\t\t\t\tname: menu.tableName\n\t\t\t\t});\n\t\t\t\tthis.title = menu.menu;\n\t\t\t},\n\t\t\ttitleChange(index, menus) {\n\t\t\t\tthis.currentIndex = index\n\t\t\t\tthis.itemMenu = menus;\n\t\t\t},\n\t\t\thomeChange(index) {\n\t\t\t\tthis.itemMenu = [];\n\t\t\t\tthis.title = \"\"\n\t\t\t\tthis.currentIndex = index\n\t\t\t\tthis.$router.push({\n\t\t\t\t\tname: 'home'\n\t\t\t\t});\n\t\t\t},\n\t\t\tcenterChange(index) {\n\t\t\t\tthis.itemMenu = [{\n\t\t\t\t\t\"buttons\": [\"新增\", \"查看\", \"修改\", \"删除\"],\n\t\t\t\t\t\"menu\": \"修改密码\",\n\t\t\t\t\t\"tableName\": \"updatePassword\"\n\t\t\t\t}, {\n\t\t\t\t\t\"buttons\": [\"新增\", \"查看\", \"修改\", \"删除\"],\n\t\t\t\t\t\"menu\": \"个人信息\",\n\t\t\t\t\t\"tableName\": \"center\"\n\t\t\t\t}];\n\t\t\t\tthis.title = \"\"\n\t\t\t\tthis.currentIndex = index\n\t\t\t\tthis.$router.push({\n\t\t\t\t\tname: 'home'\n\t\t\t\t});\r\n\t\t\t\t\n\t\t\t}\n\t\t}\n\t};\n</script>\n<style lang=\"scss\" scoped>\n\ta {\n\t\ttext-decoration: none;\n\t\tcolor: #555;\n\t}\n\n\ta:hover {\n\t\tbackground: #00c292;\n\t}\r\n\t\r\n\t.el-main {\r\n\t\tpadding: 0;\r\n\t\tdisplay: block;\r\n\t}\n\n\t.nav-list {\n\t\twidth: 100%;\n\t\tmargin: 0 auto;\n\t\ttext-align: left;\n\t\tmargin-top: 20px;\n\n\t\t.nav-title {\n\t\t\tdisplay: inline-block;\n\t\t\tfont-size: 15px;\n\t\t\tcolor: #333;\n\t\t\tpadding: 15px 25px;\n\t\t\tborder: none;\n\t\t}\n\n\t\t.nav-title.active {\n\t\t\tcolor: #555;\n\t\t\tcursor: default;\n\t\t\tbackground-color: #fff;\n\t\t}\n\t}\n\n\t.nav-item {\n\t\tmargin-top: 20px;\n\t\tbackground: #FFFFFF;\n\t\tpadding: 15px 0;\n\n\t\t.menu {\n\t\t\tpadding: 15px 25px;\n\t\t}\n\t}\n\t\r\n\t.detail-form-content {\r\n\t    background: transparent;\r\n\t}\n</style>\n"], "mappings": ";AAoCA,OAAAA,UAAA;AACA,OAAAC,WAAA;AACA,OAAAC,QAAA;AACA,OAAAC,IAAA;AACA;EACAC,UAAA;IACAJ,UAAA,EAAAA,UAAA;IACAC,WAAA,EAAAA,WAAA;IACAC,QAAA,EAAAA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,IAAA;MACAC,YAAA;MACAC,QAAA;MACAC,KAAA;MACAC,UAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,IAAAC,KAAA,GAAAV,IAAA,CAAAW,IAAA;IACA,KAAAR,QAAA,GAAAO,KAAA;IACA,KAAAN,IAAA,QAAAQ,QAAA,CAAAC,GAAA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA;IACAD,IAAA,WAAAA,KAAA;MACA,KAAAE,SAAA,cAEA;IACA;IACAC,cAAA,WAAAA,eAAAC,QAAA;MACA,KAAAX,UAAA,GAAAW,QAAA;IACA;IACAC,WAAA,WAAAA,YAAApB,IAAA;MACA,KAAAqB,OAAA,CAAAC,IAAA;QACAC,IAAA,EAAAvB,IAAA,CAAAwB;MACA;MACA,KAAAjB,KAAA,GAAAP,IAAA,CAAAA,IAAA;IACA;IACAyB,WAAA,WAAAA,YAAAC,KAAA,EAAAhB,KAAA;MACA,KAAAL,YAAA,GAAAqB,KAAA;MACA,KAAApB,QAAA,GAAAI,KAAA;IACA;IACAiB,UAAA,WAAAA,WAAAD,KAAA;MACA,KAAApB,QAAA;MACA,KAAAC,KAAA;MACA,KAAAF,YAAA,GAAAqB,KAAA;MACA,KAAAL,OAAA,CAAAC,IAAA;QACAC,IAAA;MACA;IACA;IACAK,YAAA,WAAAA,aAAAF,KAAA;MACA,KAAApB,QAAA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;MACA;MACA,KAAAC,KAAA;MACA,KAAAF,YAAA,GAAAqB,KAAA;MACA,KAAAL,OAAA,CAAAC,IAAA;QACAC,IAAA;MACA;IAEA;EACA;AACA", "ignoreList": []}]}