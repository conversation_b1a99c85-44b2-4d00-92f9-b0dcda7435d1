{"remainingRequest": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\babel-loader\\lib\\index.js!G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!G:\\Develop\\springboot\\公司财务管理系统\\admin\\src\\components\\common\\BreadCrumbs.vue?vue&type=script&lang=js", "dependencies": [{"path": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\src\\components\\common\\BreadCrumbs.vue", "mtime": 1711079474000}, {"path": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\babel.config.js", "mtime": 1711079474000}, {"path": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["pathToRegexp", "generateTitle", "data", "levelList", "watch", "$route", "getBreadcrumb", "created", "methods", "route", "matched", "filter", "item", "meta", "first", "path", "concat", "isDashboard", "name", "trim", "toLocaleLowerCase", "pathCompile", "params", "to<PERSON><PERSON>", "compile", "handleLink", "redirect", "$router", "push"], "sources": ["src/components/common/BreadCrumbs.vue"], "sourcesContent": ["<template>\r\n\t<div class=\"breadcrumb-preview\">\r\n\t\t<el-breadcrumb :style='{\"fontSize\":\"14px\",\"lineHeight\":\"1\"}' separator=\"Ξ\">\r\n\t\t\t<transition-group name=\"breadcrumb\" class=\"box\">\r\n\t\t\t\t<el-breadcrumb-item v-for=\"(item,index) in levelList\" :key=\"item.path\">\r\n\t\t\t\t\t<span v-if=\"item.redirect==='noRedirect'||index==levelList.length-1\" class=\"no-redirect\">{{ item.name }}</span>\r\n\t\t\t\t\t<a v-else @click.prevent=\"handleLink(item)\">\r\n\t\t\t\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"letterSpacing\":\"1px\",\"margin\":\"0 2px\",\"lineHeight\":\"1\",\"fontSize\":\"25px\",\"color\":\"#374254\"}'></span>首页\r\n\t\t\t\t\t</a>\r\n\t\t\t\t</el-breadcrumb-item>\r\n\t\t\t</transition-group>\r\n\t\t</el-breadcrumb>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nimport pathToRegexp from 'path-to-regexp'\r\nimport { generateTitle } from '@/utils/i18n'\r\nexport default {\r\n  data() {\r\n    return {\r\n      levelList: null\r\n    }\r\n  },\r\n  watch: {\r\n    $route() {\r\n      this.getBreadcrumb()\r\n    }\r\n  },\r\n  created() {\r\n    this.getBreadcrumb()\r\n  },\r\n  methods: {\r\n    generateTitle,\r\n    getBreadcrumb() {\r\n      // only show routes with meta.title\r\n      let route = this.$route\r\n      let matched = route.matched.filter(item => item.meta)\r\n      const first = matched[0]\r\n      matched = [{ path: '/index' }].concat(matched)\r\n\r\n      this.levelList = matched.filter(item => item.meta)\r\n    },\r\n    isDashboard(route) {\r\n      const name = route && route.name\r\n      if (!name) {\r\n        return false\r\n      }\r\n      return name.trim().toLocaleLowerCase() === 'Index'.toLocaleLowerCase()\r\n    },\r\n    pathCompile(path) {\r\n      // To solve this problem https://github.com/PanJiaChen/vue-element-admin/issues/561\r\n      const { params } = this.$route\r\n      var toPath = pathToRegexp.compile(path)\r\n      return toPath(params)\r\n    },\r\n    handleLink(item) {\r\n      const { redirect, path } = item\r\n      if (redirect) {\r\n        this.$router.push(redirect)\r\n        return\r\n      }\r\n      if(path){\r\n      \t\t  this.$router.push(path)\r\n      }else{\r\n      \t\t  this.$router.push('/')\r\n      }\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.el-breadcrumb {\r\n\t\t& /deep/ .el-breadcrumb__separator {\r\n\t\t  \t\t  margin: 0 9px;\r\n\t\t  \t\t  color: #ccc;\r\n\t\t  \t\t  font-weight: 500;\r\n\t\t  \t\t}\r\n\t\t\r\n\t\t& /deep/ .el-breadcrumb__inner a {\r\n\t\t  \t\t  color: #374254;\r\n\t\t  \t\t  font-weight: 600;\r\n\t\t  \t\t  display: inline-block;\r\n\t\t  \t\t  font-size: 25px;\r\n\t\t  \t\t}\r\n\t\t\r\n\t\t& /deep/ .el-breadcrumb__inner {\r\n\t\t  \t\t  color: #374254;\r\n\t\t  \t\t  letter-spacing: 1px;\r\n\t\t  \t\t  display: inline-block;\r\n\t\t  \t\t  font-size: 25px;\r\n\t\t  \t\t}\r\n\t}\r\n</style>\r\n"], "mappings": ";;;;;;;;AAgBA,OAAAA,YAAA;AACA,SAAAC,aAAA;AACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;IACA;EACA;EACAC,KAAA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAC,aAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAD,aAAA;EACA;EACAE,OAAA;IACAP,aAAA,EAAAA,aAAA;IACAK,aAAA,WAAAA,cAAA;MACA;MACA,IAAAG,KAAA,QAAAJ,MAAA;MACA,IAAAK,OAAA,GAAAD,KAAA,CAAAC,OAAA,CAAAC,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,IAAA;MAAA;MACA,IAAAC,KAAA,GAAAJ,OAAA;MACAA,OAAA;QAAAK,IAAA;MAAA,GAAAC,MAAA,CAAAN,OAAA;MAEA,KAAAP,SAAA,GAAAO,OAAA,CAAAC,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,IAAA;MAAA;IACA;IACAI,WAAA,WAAAA,YAAAR,KAAA;MACA,IAAAS,IAAA,GAAAT,KAAA,IAAAA,KAAA,CAAAS,IAAA;MACA,KAAAA,IAAA;QACA;MACA;MACA,OAAAA,IAAA,CAAAC,IAAA,GAAAC,iBAAA,eAAAA,iBAAA;IACA;IACAC,WAAA,WAAAA,YAAAN,IAAA;MACA;MACA,IAAAO,MAAA,QAAAjB,MAAA,CAAAiB,MAAA;MACA,IAAAC,MAAA,GAAAvB,YAAA,CAAAwB,OAAA,CAAAT,IAAA;MACA,OAAAQ,MAAA,CAAAD,MAAA;IACA;IACAG,UAAA,WAAAA,WAAAb,IAAA;MACA,IAAAc,QAAA,GAAAd,IAAA,CAAAc,QAAA;QAAAX,IAAA,GAAAH,IAAA,CAAAG,IAAA;MACA,IAAAW,QAAA;QACA,KAAAC,OAAA,CAAAC,IAAA,CAAAF,QAAA;QACA;MACA;MACA,IAAAX,IAAA;QACA,KAAAY,OAAA,CAAAC,IAAA,CAAAb,IAAA;MACA;QACA,KAAAY,OAAA,CAAAC,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}