{"remainingRequest": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\src\\store\\index.js", "dependencies": [{"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\src\\store\\index.js", "mtime": 1711079474000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\babel.config.js", "mtime": 1711079474000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZ1ZSBmcm9tICd2dWUnOwppbXBvcnQgVnVleCBmcm9tICd2dWV4JzsKaW1wb3J0IHRhZ3NWaWV3IGZyb20gJy4vbW9kdWxlcy90YWdzVmlldyc7ClZ1ZS51c2UoVnVleCk7CnZhciBzdG9yZSA9IG5ldyBWdWV4LlN0b3JlKHsKICBtb2R1bGVzOiB7CiAgICB0YWdzVmlldzogdGFnc1ZpZXcKICB9Cn0pOwpleHBvcnQgZGVmYXVsdCBzdG9yZTs="}, {"version": 3, "names": ["<PERSON><PERSON>", "Vuex", "tagsView", "use", "store", "Store", "modules"], "sources": ["D:/桌面/springboot2g43t3k0/src/main/resources/admin/admin/src/store/index.js"], "sourcesContent": ["import Vue from 'vue'\r\nimport Vuex from 'vuex'\r\nimport tagsView from './modules/tagsView'\r\n\r\nVue.use(Vuex)\r\n\r\nconst store = new Vuex.Store({\r\n  modules: {\r\n    tagsView,\r\n  }\r\n})\r\n\r\nexport default store\r\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,QAAQ,MAAM,oBAAoB;AAEzCF,GAAG,CAACG,GAAG,CAACF,IAAI,CAAC;AAEb,IAAMG,KAAK,GAAG,IAAIH,IAAI,CAACI,KAAK,CAAC;EAC3BC,OAAO,EAAE;IACPJ,QAAQ,EAARA;EACF;AACF,CAAC,CAAC;AAEF,eAAeE,KAAK", "ignoreList": []}]}