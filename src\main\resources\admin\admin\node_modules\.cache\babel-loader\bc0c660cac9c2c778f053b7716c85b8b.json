{"remainingRequest": "D:\\project\\admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\project\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\admin\\src\\utils\\storage.js", "dependencies": [{"path": "D:\\project\\admin\\src\\utils\\storage.js", "mtime": 1711079474000}, {"path": "D:\\project\\admin\\babel.config.js", "mtime": 1711079474000}, {"path": "D:\\project\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\project\\admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\project\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuZGF0ZS50by1qc29uLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuanNvbi5zdHJpbmdpZnkuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3Qua2V5cy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnJlZ2V4cC5leGVjLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLnJlcGxhY2UuanMiOwp2YXIgc3RvcmFnZSA9IHsKICBzZXQ6IGZ1bmN0aW9uIHNldChrZXksIHZhbHVlKSB7CiAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShrZXksIEpTT04uc3RyaW5naWZ5KHZhbHVlKSk7CiAgfSwKICBnZXQ6IGZ1bmN0aW9uIGdldChrZXkpIHsKICAgIHJldHVybiBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShrZXkpID8gbG9jYWxTdG9yYWdlLmdldEl0ZW0oa2V5KS5yZXBsYWNlKCciJywgJycpLnJlcGxhY2UoJyInLCAnJykgOiAiIjsKICB9LAogIGdldE9iajogZnVuY3Rpb24gZ2V0T2JqKGtleSkgewogICAgcmV0dXJuIGxvY2FsU3RvcmFnZS5nZXRJdGVtKGtleSkgPyBKU09OLnBhcnNlKGxvY2FsU3RvcmFnZS5nZXRJdGVtKGtleSkpIDogbnVsbDsKICB9LAogIHJlbW92ZTogZnVuY3Rpb24gcmVtb3ZlKGtleSkgewogICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oa2V5KTsKICB9LAogIGNsZWFyOiBmdW5jdGlvbiBjbGVhcigpIHsKICAgIGxvY2FsU3RvcmFnZS5jbGVhcigpOwogIH0KfTsKZXhwb3J0IGRlZmF1bHQgc3RvcmFnZTs="}, {"version": 3, "names": ["storage", "set", "key", "value", "localStorage", "setItem", "JSON", "stringify", "get", "getItem", "replace", "get<PERSON><PERSON>j", "parse", "remove", "removeItem", "clear"], "sources": ["D:/project/admin/src/utils/storage.js"], "sourcesContent": ["const storage = {\r\n    set(key, value) {\r\n        localStorage.setItem(key, JSON.stringify(value));\r\n    },\r\n    get(key) {\r\n        return localStorage.getItem(key)?localStorage.getItem(key).replace('\"','').replace('\"',''):\"\";\r\n    },\r\n    getObj(key) {\r\n        return localStorage.getItem(key)?JSON.parse(localStorage.getItem(key)):null;\r\n    },\r\n    remove(key) {\r\n        localStorage.removeItem(key);\r\n    },\r\n    clear() {\r\n\t\tlocalStorage.clear();\r\n    }\r\n}\r\nexport default storage;\r\n"], "mappings": ";;;;;AAAA,IAAMA,OAAO,GAAG;EACZC,GAAG,WAAHA,GAAGA,CAACC,GAAG,EAAEC,KAAK,EAAE;IACZC,YAAY,CAACC,OAAO,CAACH,GAAG,EAAEI,IAAI,CAACC,SAAS,CAACJ,KAAK,CAAC,CAAC;EACpD,CAAC;EACDK,GAAG,WAAHA,GAAGA,CAACN,GAAG,EAAE;IACL,OAAOE,YAAY,CAACK,OAAO,CAACP,GAAG,CAAC,GAACE,YAAY,CAACK,OAAO,CAACP,GAAG,CAAC,CAACQ,OAAO,CAAC,GAAG,EAAC,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAC,EAAE,CAAC,GAAC,EAAE;EACjG,CAAC;EACDC,MAAM,WAANA,MAAMA,CAACT,GAAG,EAAE;IACR,OAAOE,YAAY,CAACK,OAAO,CAACP,GAAG,CAAC,GAACI,IAAI,CAACM,KAAK,CAACR,YAAY,CAACK,OAAO,CAACP,GAAG,CAAC,CAAC,GAAC,IAAI;EAC/E,CAAC;EACDW,MAAM,WAANA,MAAMA,CAACX,GAAG,EAAE;IACRE,YAAY,CAACU,UAAU,CAACZ,GAAG,CAAC;EAChC,CAAC;EACDa,KAAK,WAALA,KAAKA,CAAA,EAAG;IACVX,YAAY,CAACW,KAAK,CAAC,CAAC;EAClB;AACJ,CAAC;AACD,eAAef,OAAO", "ignoreList": []}]}