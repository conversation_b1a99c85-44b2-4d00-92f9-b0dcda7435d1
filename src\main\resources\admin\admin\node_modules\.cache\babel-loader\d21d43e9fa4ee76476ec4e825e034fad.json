{"remainingRequest": "D:\\project\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!D:\\project\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\project\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\admin\\src\\components\\common\\Editor.vue?vue&type=template&id=4c8019d1", "dependencies": [{"path": "D:\\project\\admin\\src\\components\\common\\Editor.vue", "mtime": 1711079474000}, {"path": "D:\\project\\admin\\babel.config.js", "mtime": 1711079474000}, {"path": "D:\\project\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\project\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\project\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\project\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\project\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\project\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIHJlbmRlciA9IGZ1bmN0aW9uIHJlbmRlcigpIHsKICB2YXIgX3ZtID0gdGhpcywKICAgIF9jID0gX3ZtLl9zZWxmLl9jOwogIHJldHVybiBfYygiZGl2IiwgW19jKCJlbC11cGxvYWQiLCB7CiAgICBzdGF0aWNDbGFzczogImF2YXRhci11cGxvYWRlciIsCiAgICBhdHRyczogewogICAgICBhY3Rpb246IF92bS5nZXRBY3Rpb25VcmwsCiAgICAgIG5hbWU6ICJmaWxlIiwKICAgICAgaGVhZGVyczogX3ZtLmhlYWRlciwKICAgICAgInNob3ctZmlsZS1saXN0IjogZmFsc2UsCiAgICAgICJvbi1zdWNjZXNzIjogX3ZtLnVwbG9hZFN1Y2Nlc3MsCiAgICAgICJvbi1lcnJvciI6IF92bS51cGxvYWRFcnJvciwKICAgICAgImJlZm9yZS11cGxvYWQiOiBfdm0uYmVmb3JlVXBsb2FkCiAgICB9CiAgfSksIF9jKCJxdWlsbC1lZGl0b3IiLCB7CiAgICByZWY6ICJteVF1aWxsRWRpdG9yIiwKICAgIHN0YXRpY0NsYXNzOiAiZWRpdG9yIiwKICAgIGF0dHJzOiB7CiAgICAgIG9wdGlvbnM6IF92bS5lZGl0b3JPcHRpb24KICAgIH0sCiAgICBvbjogewogICAgICBibHVyOiBmdW5jdGlvbiBibHVyKCRldmVudCkgewogICAgICAgIHJldHVybiBfdm0ub25FZGl0b3JCbHVyKCRldmVudCk7CiAgICAgIH0sCiAgICAgIGZvY3VzOiBmdW5jdGlvbiBmb2N1cygkZXZlbnQpIHsKICAgICAgICByZXR1cm4gX3ZtLm9uRWRpdG9yRm9jdXMoJGV2ZW50KTsKICAgICAgfSwKICAgICAgY2hhbmdlOiBmdW5jdGlvbiBjaGFuZ2UoJGV2ZW50KSB7CiAgICAgICAgcmV0dXJuIF92bS5vbkVkaXRvckNoYW5nZSgkZXZlbnQpOwogICAgICB9CiAgICB9LAogICAgbW9kZWw6IHsKICAgICAgdmFsdWU6IF92bS52YWx1ZSwKICAgICAgY2FsbGJhY2s6IGZ1bmN0aW9uIGNhbGxiYWNrKCQkdikgewogICAgICAgIF92bS52YWx1ZSA9ICQkdjsKICAgICAgfSwKICAgICAgZXhwcmVzc2lvbjogInZhbHVlIgogICAgfQogIH0pXSwgMSk7Cn07CnZhciBzdGF0aWNSZW5kZXJGbnMgPSBbXTsKcmVuZGVyLl93aXRoU3RyaXBwZWQgPSB0cnVlOwpleHBvcnQgeyByZW5kZXIsIHN0YXRpY1JlbmRlckZucyB9Ow=="}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "attrs", "action", "getActionUrl", "name", "headers", "header", "uploadSuccess", "uploadError", "beforeUpload", "ref", "options", "editorOption", "on", "blur", "$event", "onEditorBlur", "focus", "onEditorFocus", "change", "onEditorChange", "model", "value", "callback", "$$v", "expression", "staticRenderFns", "_withStripped"], "sources": ["D:/project/admin/src/components/common/Editor.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    [\n      _c(\"el-upload\", {\n        staticClass: \"avatar-uploader\",\n        attrs: {\n          action: _vm.getActionUrl,\n          name: \"file\",\n          headers: _vm.header,\n          \"show-file-list\": false,\n          \"on-success\": _vm.uploadSuccess,\n          \"on-error\": _vm.uploadError,\n          \"before-upload\": _vm.beforeUpload,\n        },\n      }),\n      _c(\"quill-editor\", {\n        ref: \"myQuillEditor\",\n        staticClass: \"editor\",\n        attrs: { options: _vm.editorOption },\n        on: {\n          blur: function ($event) {\n            return _vm.onEditorBlur($event)\n          },\n          focus: function ($event) {\n            return _vm.onEditorFocus($event)\n          },\n          change: function ($event) {\n            return _vm.onEditorChange($event)\n          },\n        },\n        model: {\n          value: _vm.value,\n          callback: function ($$v) {\n            _vm.value = $$v\n          },\n          expression: \"value\",\n        },\n      }),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL,CACEA,EAAE,CAAC,WAAW,EAAE;IACdE,WAAW,EAAE,iBAAiB;IAC9BC,KAAK,EAAE;MACLC,MAAM,EAAEL,GAAG,CAACM,YAAY;MACxBC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAER,GAAG,CAACS,MAAM;MACnB,gBAAgB,EAAE,KAAK;MACvB,YAAY,EAAET,GAAG,CAACU,aAAa;MAC/B,UAAU,EAAEV,GAAG,CAACW,WAAW;MAC3B,eAAe,EAAEX,GAAG,CAACY;IACvB;EACF,CAAC,CAAC,EACFX,EAAE,CAAC,cAAc,EAAE;IACjBY,GAAG,EAAE,eAAe;IACpBV,WAAW,EAAE,QAAQ;IACrBC,KAAK,EAAE;MAAEU,OAAO,EAAEd,GAAG,CAACe;IAAa,CAAC;IACpCC,EAAE,EAAE;MACFC,IAAI,EAAE,SAANA,IAAIA,CAAYC,MAAM,EAAE;QACtB,OAAOlB,GAAG,CAACmB,YAAY,CAACD,MAAM,CAAC;MACjC,CAAC;MACDE,KAAK,EAAE,SAAPA,KAAKA,CAAYF,MAAM,EAAE;QACvB,OAAOlB,GAAG,CAACqB,aAAa,CAACH,MAAM,CAAC;MAClC,CAAC;MACDI,MAAM,EAAE,SAARA,MAAMA,CAAYJ,MAAM,EAAE;QACxB,OAAOlB,GAAG,CAACuB,cAAc,CAACL,MAAM,CAAC;MACnC;IACF,CAAC;IACDM,KAAK,EAAE;MACLC,KAAK,EAAEzB,GAAG,CAACyB,KAAK;MAChBC,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvB3B,GAAG,CAACyB,KAAK,GAAGE,GAAG;MACjB,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxB9B,MAAM,CAAC+B,aAAa,GAAG,IAAI;AAE3B,SAAS/B,MAAM,EAAE8B,eAAe", "ignoreList": []}]}