{"remainingRequest": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\babel-loader\\lib\\index.js!G:\\Develop\\springboot\\公司财务管理系统\\admin\\src\\utils\\base.js", "dependencies": [{"path": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\src\\utils\\base.js", "mtime": 1713231142000}, {"path": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\babel.config.js", "mtime": 1711079474000}, {"path": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIGJhc2UgPSB7CiAgZ2V0OiBmdW5jdGlvbiBnZXQoKSB7CiAgICByZXR1cm4gewogICAgICB1cmw6ICJodHRwOi8vbG9jYWxob3N0OjgwODAvc3ByaW5nYm9vdDJnNDN0M2swLyIsCiAgICAgIG5hbWU6ICJzcHJpbmdib290Mmc0M3QzazAiLAogICAgICAvLyDpgIDlh7rliLDpppbpobXpk77mjqUKICAgICAgaW5kZXhVcmw6ICcnCiAgICB9OwogIH0sCiAgZ2V0UHJvamVjdE5hbWU6IGZ1bmN0aW9uIGdldFByb2plY3ROYW1lKCkgewogICAgcmV0dXJuIHsKICAgICAgcHJvamVjdE5hbWU6ICLlhazlj7jotKLliqHnrqHnkIbns7vnu58iCiAgICB9OwogIH0KfTsKZXhwb3J0IGRlZmF1bHQgYmFzZTs="}, {"version": 3, "names": ["base", "get", "url", "name", "indexUrl", "getProjectName", "projectName"], "sources": ["G:/Develop/springboot/公司财务管理系统/admin/src/utils/base.js"], "sourcesContent": ["const base = {\r\n    get() {\r\n        return {\r\n            url : \"http://localhost:8080/springboot2g43t3k0/\",\r\n            name: \"springboot2g43t3k0\",\r\n            // 退出到首页链接\r\n            indexUrl: ''\r\n        };\r\n    },\r\n    getProjectName(){\r\n        return {\r\n            projectName: \"公司财务管理系统\"\r\n        } \r\n    }\r\n}\r\nexport default base\r\n"], "mappings": "AAAA,IAAMA,IAAI,GAAG;EACTC,GAAG,WAAHA,GAAGA,CAAA,EAAG;IACF,OAAO;MACHC,GAAG,EAAG,2CAA2C;MACjDC,IAAI,EAAE,oBAAoB;MAC1B;MACAC,QAAQ,EAAE;IACd,CAAC;EACL,CAAC;EACDC,cAAc,WAAdA,cAAcA,CAAA,EAAE;IACZ,OAAO;MACHC,WAAW,EAAE;IACjB,CAAC;EACL;AACJ,CAAC;AACD,eAAeN,IAAI", "ignoreList": []}]}