{"remainingRequest": "D:\\project\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\project\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\admin\\src\\components\\SvgIcon\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\admin\\src\\components\\SvgIcon\\index.vue", "mtime": 1711079474000}, {"path": "D:\\project\\admin\\babel.config.js", "mtime": 1711079474000}, {"path": "D:\\project\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\project\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\project\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\project\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdTdmdJY29uJywKICBwcm9wczogewogICAgaWNvbkNsYXNzOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgcmVxdWlyZWQ6IHRydWUKICAgIH0sCiAgICBjbGFzc05hbWU6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAnJwogICAgfQogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIGljb25OYW1lOiBmdW5jdGlvbiBpY29uTmFtZSgpIHsKICAgICAgcmV0dXJuICIjaWNvbi0iLmNvbmNhdCh0aGlzLmljb25DbGFzcyk7CiAgICB9LAogICAgc3ZnQ2xhc3M6IGZ1bmN0aW9uIHN2Z0NsYXNzKCkgewogICAgICBpZiAodGhpcy5jbGFzc05hbWUpIHsKICAgICAgICByZXR1cm4gJ3N2Zy1pY29uICcgKyB0aGlzLmNsYXNzTmFtZTsKICAgICAgfSBlbHNlIHsKICAgICAgICByZXR1cm4gJ3N2Zy1pY29uJzsKICAgICAgfQogICAgfQogIH0KfTs="}, {"version": 3, "names": ["name", "props", "iconClass", "type", "String", "required", "className", "default", "computed", "iconName", "concat", "svgClass"], "sources": ["src/components/SvgIcon/index.vue"], "sourcesContent": ["<template>\n  <svg :class=\"svgClass\" aria-hidden=\"true\" v-on=\"$listeners\">\n    <use :xlink:href=\"iconName\" />\n  </svg>\n</template>\n\n<script>\nexport default {\n  name: 'SvgIcon',\n  props: {\n    iconClass: {\n      type: String,\n      required: true\n    },\n    className: {\n      type: String,\n      default: ''\n    }\n  },\n  computed: {\n    iconName() {\n      return `#icon-${this.iconClass}`\n    },\n    svgClass() {\n      if (this.className) {\n        return 'svg-icon ' + this.className\n      } else {\n        return 'svg-icon'\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.svg-icon {\n  width: 1em;\n  height: 1em;\n  vertical-align: -0.15em;\n  fill: currentColor;\n  overflow: hidden;\n}\n</style>\n"], "mappings": "AAOA;EACAA,IAAA;EACAC,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACAC,SAAA;MACAH,IAAA,EAAAC,MAAA;MACAG,OAAA;IACA;EACA;EACAC,QAAA;IACAC,QAAA,WAAAA,SAAA;MACA,gBAAAC,MAAA,MAAAR,SAAA;IACA;IACAS,QAAA,WAAAA,SAAA;MACA,SAAAL,SAAA;QACA,0BAAAA,SAAA;MACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}