{"remainingRequest": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\babel-loader\\lib\\index.js!G:\\Develop\\springboot\\公司财务管理系统\\admin\\src\\utils\\validate.js", "dependencies": [{"path": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\src\\utils\\validate.js", "mtime": 1711079474000}, {"path": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\babel.config.js", "mtime": 1711079474000}, {"path": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLmV4ZWMuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAudGVzdC5qcyI7Ci8qKg0KICog6YKu566xDQogKiBAcGFyYW0geyp9IHMNCiAqLwpleHBvcnQgZnVuY3Rpb24gaXNFbWFpbChzKSB7CiAgcmV0dXJuIC9eKFthLXpBLVowLTlfLV0pK0AoW2EtekEtWjAtOV8tXSkrKCguW2EtekEtWjAtOV8tXXsyLDN9KXsxLDJ9KSQvLnRlc3Qocyk7Cn0KCi8qKg0KICog5omL5py65Y+356CBDQogKiBAcGFyYW0geyp9IHMNCiAqLwpleHBvcnQgZnVuY3Rpb24gaXNNb2JpbGUocykgewogIHJldHVybiAvXjFbMzQ1Njc4OV1cZHs5fSQvLnRlc3Qocyk7Cn0KCi8qKg0KICog55S16K+d5Y+356CBDQogKiBAcGFyYW0geyp9IHMNCiAqLwpleHBvcnQgZnVuY3Rpb24gaXNQaG9uZShzKSB7CiAgcmV0dXJuIC9eKFswLTldezMsNH0tKT9bMC05XXs3LDh9JC8udGVzdChzKTsKfQoKLyoqDQogKiBVUkzlnLDlnYANCiAqIEBwYXJhbSB7Kn0gcw0KICovCmV4cG9ydCBmdW5jdGlvbiBpc1VSTChzKSB7CiAgcmV0dXJuIC9eaHR0cFtzXT86XC9cLy4qLy50ZXN0KHMpOwp9CgovKioNCiAqIOWMuemFjeaVsOWtl++8jOWPr+S7peaYr+Wwj+aVsO+8jOS4jeWPr+S7peaYr+i0n+aVsCzlj6/ku6XkuLrnqboNCiAqIEBwYXJhbSB7Kn0gcyANCiAqLwpleHBvcnQgZnVuY3Rpb24gaXNOdW1iZXIocykgewogIHJldHVybiAvKF4tP1srLV0/KFswLTldKlwuP1swLTldK3xbMC05XStcLj9bMC05XSopKFtlRV1bKy1dP1swLTldKyk/JCl8KF4kKS8udGVzdChzKTsKfQovKioNCiAqIOWMuemFjeaVtOaVsO+8jOWPr+S7peS4uuepug0KICogQHBhcmFtIHsqfSBzIA0KICovCmV4cG9ydCBmdW5jdGlvbiBpc0ludE51bWVyKHMpIHsKICByZXR1cm4gLyheLT9cZCskKXwoXiQpLy50ZXN0KHMpOwp9Ci8qKg0KICog6Lqr5Lu96K+B5qCh6aqMDQogKi8KZXhwb3J0IGZ1bmN0aW9uIGNoZWNrSWRDYXJkKGlkY2FyZCkgewogIHZhciByZWdJZENhcmQgPSAvKF5cZHsxNX0kKXwoXlxkezE4fSQpfCheXGR7MTd9KFxkfFh8eCkkKS87CiAgaWYgKCFyZWdJZENhcmQudGVzdChpZGNhcmQpKSB7CiAgICByZXR1cm4gZmFsc2U7CiAgfSBlbHNlIHsKICAgIHJldHVybiB0cnVlOwogIH0KfQ=="}, {"version": 3, "names": ["isEmail", "s", "test", "isMobile", "isPhone", "isURL", "isNumber", "isIntNumer", "checkIdCard", "idcard", "regIdCard"], "sources": ["G:/Develop/springboot/公司财务管理系统/admin/src/utils/validate.js"], "sourcesContent": ["/**\r\n * 邮箱\r\n * @param {*} s\r\n */\r\nexport function isEmail(s) {\r\n\treturn /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((.[a-zA-Z0-9_-]{2,3}){1,2})$/.test(s)\r\n}\r\n\r\n/**\r\n * 手机号码\r\n * @param {*} s\r\n */\r\nexport function isMobile(s) {\r\n\treturn /^1[3456789]\\d{9}$/.test(s)\r\n}\r\n\r\n/**\r\n * 电话号码\r\n * @param {*} s\r\n */\r\nexport function isPhone(s) {\r\n\treturn /^([0-9]{3,4}-)?[0-9]{7,8}$/.test(s)\r\n}\r\n\r\n/**\r\n * URL地址\r\n * @param {*} s\r\n */\r\nexport function isURL(s) {\r\n\treturn /^http[s]?:\\/\\/.*/.test(s)\r\n}\r\n\r\n/**\r\n * 匹配数字，可以是小数，不可以是负数,可以为空\r\n * @param {*} s \r\n */\r\nexport function isNumber(s) {\r\n\treturn /(^-?[+-]?([0-9]*\\.?[0-9]+|[0-9]+\\.?[0-9]*)([eE][+-]?[0-9]+)?$)|(^$)/.test(s);\r\n}\r\n/**\r\n * 匹配整数，可以为空\r\n * @param {*} s \r\n */\r\nexport function isIntNumer(s) {\r\n\treturn /(^-?\\d+$)|(^$)/.test(s);\r\n}\r\n/**\r\n * 身份证校验\r\n */\r\nexport function checkIdCard(idcard) {\r\n\tconst regIdCard = /(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/;\r\n\tif (!regIdCard.test(idcard)) {\r\n\t\treturn false;\r\n\t} else {\r\n\t\treturn true;\r\n\t}\r\n}"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA,OAAO,SAASA,OAAOA,CAACC,CAAC,EAAE;EAC1B,OAAO,iEAAiE,CAACC,IAAI,CAACD,CAAC,CAAC;AACjF;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASE,QAAQA,CAACF,CAAC,EAAE;EAC3B,OAAO,mBAAmB,CAACC,IAAI,CAACD,CAAC,CAAC;AACnC;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASG,OAAOA,CAACH,CAAC,EAAE;EAC1B,OAAO,4BAA4B,CAACC,IAAI,CAACD,CAAC,CAAC;AAC5C;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASI,KAAKA,CAACJ,CAAC,EAAE;EACxB,OAAO,kBAAkB,CAACC,IAAI,CAACD,CAAC,CAAC;AAClC;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASK,QAAQA,CAACL,CAAC,EAAE;EAC3B,OAAO,qEAAqE,CAACC,IAAI,CAACD,CAAC,CAAC;AACrF;AACA;AACA;AACA;AACA;AACA,OAAO,SAASM,UAAUA,CAACN,CAAC,EAAE;EAC7B,OAAO,gBAAgB,CAACC,IAAI,CAACD,CAAC,CAAC;AAChC;AACA;AACA;AACA;AACA,OAAO,SAASO,WAAWA,CAACC,MAAM,EAAE;EACnC,IAAMC,SAAS,GAAG,0CAA0C;EAC5D,IAAI,CAACA,SAAS,CAACR,IAAI,CAACO,MAAM,CAAC,EAAE;IAC5B,OAAO,KAAK;EACb,CAAC,MAAM;IACN,OAAO,IAAI;EACZ;AACD", "ignoreList": []}]}