{"remainingRequest": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\babel-loader\\lib\\index.js!G:\\Develop\\springboot\\公司财务管理系统\\admin\\src\\router\\router-static.js", "dependencies": [{"path": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\src\\router\\router-static.js", "mtime": 1711079474000}, {"path": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\babel.config.js", "mtime": 1711079474000}, {"path": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "use", "Index", "Home", "<PERSON><PERSON>", "NotFound", "UpdatePassword", "pay", "register", "center", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jiangchengxinxi", "zichan<PERSON><PERSON>ing", "caiwuxinxi", "qingjiaxinxi", "bumen", "yuangongdangan", "zhiwei", "kaoqinxinxi", "gonggaoxinxi", "routes", "path", "name", "component", "children", "meta", "icon", "title", "affix", "router", "mode", "originalPush", "prototype", "push", "location", "call", "catch", "err"], "sources": ["G:/Develop/springboot/公司财务管理系统/admin/src/router/router-static.js"], "sourcesContent": ["import Vue from 'vue';\r\n//配置路由\r\nimport VueRouter from 'vue-router'\r\nVue.use(VueRouter);\r\n//1.创建组件\r\nimport Index from '@/views/index'\r\nimport Home from '@/views/home'\r\nimport Login from '@/views/login'\r\nimport NotFound from '@/views/404'\r\nimport UpdatePassword from '@/views/update-password'\r\nimport pay from '@/views/pay'\r\nimport register from '@/views/register'\r\nimport center from '@/views/center'\r\n    import yuangong from '@/views/modules/yuangong/list'\r\n    import zichancaigou from '@/views/modules/zichancaigou/list'\r\n    import yuangonggongzi from '@/views/modules/yuangonggongzi/list'\r\n    import gudingzichan from '@/views/modules/gudingzichan/list'\r\n    import zichanshenling from '@/views/modules/zichanshenling/list'\r\n    import jiangchengxinxi from '@/views/modules/jiangchengxinxi/list'\r\n    import zichanleixing from '@/views/modules/zichanleixing/list'\r\n    import caiwuxinxi from '@/views/modules/caiwuxinxi/list'\r\n    import qingjiaxinxi from '@/views/modules/qingjiaxinxi/list'\r\n    import bumen from '@/views/modules/bumen/list'\r\n    import yuangongdangan from '@/views/modules/yuangongdangan/list'\r\n    import zhiwei from '@/views/modules/zhiwei/list'\r\n    import kaoqinxinxi from '@/views/modules/kaoqinxinxi/list'\r\n    import gonggaoxinxi from '@/views/modules/gonggaoxinxi/list'\r\n\n\r\n//2.配置路由   注意：名字\r\nexport const routes = [{\r\n    path: '/',\r\n    name: '系统首页',\r\n    component: Index,\r\n    children: [{\r\n      // 这里不设置值，是把main作为默认页面\r\n      path: '/',\r\n      name: '系统首页',\r\n      component: Home,\n      meta: {icon:'', title:'center', affix: true}\r\n    }, {\r\n      path: '/updatePassword',\r\n      name: '修改密码',\r\n      component: UpdatePassword,\n      meta: {icon:'', title:'updatePassword'}\r\n    }, {\r\n      path: '/pay',\r\n      name: '支付',\r\n      component: pay,\n      meta: {icon:'', title:'pay'}\r\n    }, {\r\n      path: '/center',\r\n      name: '个人信息',\r\n      component: center,\n      meta: {icon:'', title:'center'}\r\n    }\n      ,{\n\tpath: '/yuangong',\n        name: '员工',\n        component: yuangong\n      }\n      ,{\n\tpath: '/zichancaigou',\n        name: '资产采购',\n        component: zichancaigou\n      }\n      ,{\n\tpath: '/yuangonggongzi',\n        name: '员工工资',\n        component: yuangonggongzi\n      }\n      ,{\n\tpath: '/gudingzichan',\n        name: '固定资产',\n        component: gudingzichan\n      }\n      ,{\n\tpath: '/zichanshenling',\n        name: '资产申领',\n        component: zichanshenling\n      }\n      ,{\n\tpath: '/jiangchengxinxi',\n        name: '奖惩信息',\n        component: jiangchengxinxi\n      }\n      ,{\n\tpath: '/zichanleixing',\n        name: '资产类型',\n        component: zichanleixing\n      }\n      ,{\n\tpath: '/caiwuxinxi',\n        name: '财务信息',\n        component: caiwuxinxi\n      }\n      ,{\n\tpath: '/qingjiaxinxi',\n        name: '请假信息',\n        component: qingjiaxinxi\n      }\n      ,{\n\tpath: '/bumen',\n        name: '部门',\n        component: bumen\n      }\n      ,{\n\tpath: '/yuangongdangan',\n        name: '员工档案',\n        component: yuangongdangan\n      }\n      ,{\n\tpath: '/zhiwei',\n        name: '职位',\n        component: zhiwei\n      }\n      ,{\n\tpath: '/kaoqinxinxi',\n        name: '考勤信息',\n        component: kaoqinxinxi\n      }\n      ,{\n\tpath: '/gonggaoxinxi',\n        name: '公告信息',\n        component: gonggaoxinxi\n      }\n    ]\r\n  },\r\n  {\r\n    path: '/login',\r\n    name: 'login',\r\n    component: Login,\n    meta: {icon:'', title:'login'}\r\n  },\r\n  {\r\n    path: '/register',\r\n    name: 'register',\r\n    component: register,\n    meta: {icon:'', title:'register'}\r\n  },\r\n  {\r\n    path: '*',\r\n    component: NotFound\r\n  }\r\n]\r\n//3.实例化VueRouter  注意：名字\r\nconst router = new VueRouter({\r\n  mode: 'hash',\r\n  /*hash模式改为history*/\r\n  routes // （缩写）相当于 routes: routes\r\n})\r\nconst originalPush = VueRouter.prototype.push\n//修改原型对象中的push方法\nVueRouter.prototype.push = function push(location) {\n   return originalPush.call(this, location).catch(err => err)\n}\nexport default router;\n"], "mappings": ";AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB;AACA,OAAOC,SAAS,MAAM,YAAY;AAClCD,GAAG,CAACE,GAAG,CAACD,SAAS,CAAC;AAClB;AACA,OAAOE,KAAK,MAAM,eAAe;AACjC,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,QAAQ,MAAM,aAAa;AAClC,OAAOC,cAAc,MAAM,yBAAyB;AACpD,OAAOC,GAAG,MAAM,aAAa;AAC7B,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,MAAM,MAAM,gBAAgB;AAC/B,OAAOC,QAAQ,MAAM,+BAA+B;AACpD,OAAOC,YAAY,MAAM,mCAAmC;AAC5D,OAAOC,cAAc,MAAM,qCAAqC;AAChE,OAAOC,YAAY,MAAM,mCAAmC;AAC5D,OAAOC,cAAc,MAAM,qCAAqC;AAChE,OAAOC,eAAe,MAAM,sCAAsC;AAClE,OAAOC,aAAa,MAAM,oCAAoC;AAC9D,OAAOC,UAAU,MAAM,iCAAiC;AACxD,OAAOC,YAAY,MAAM,mCAAmC;AAC5D,OAAOC,KAAK,MAAM,4BAA4B;AAC9C,OAAOC,cAAc,MAAM,qCAAqC;AAChE,OAAOC,MAAM,MAAM,6BAA6B;AAChD,OAAOC,WAAW,MAAM,kCAAkC;AAC1D,OAAOC,YAAY,MAAM,mCAAmC;;AAGhE;AACA,OAAO,IAAMC,MAAM,GAAG,CAAC;EACnBC,IAAI,EAAE,GAAG;EACTC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAEzB,KAAK;EAChB0B,QAAQ,EAAE,CAAC;IACT;IACAH,IAAI,EAAE,GAAG;IACTC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAExB,IAAI;IACf0B,IAAI,EAAE;MAACC,IAAI,EAAC,EAAE;MAAEC,KAAK,EAAC,QAAQ;MAAEC,KAAK,EAAE;IAAI;EAC7C,CAAC,EAAE;IACDP,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAErB,cAAc;IACzBuB,IAAI,EAAE;MAACC,IAAI,EAAC,EAAE;MAAEC,KAAK,EAAC;IAAgB;EACxC,CAAC,EAAE;IACDN,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,IAAI;IACVC,SAAS,EAAEpB,GAAG;IACdsB,IAAI,EAAE;MAACC,IAAI,EAAC,EAAE;MAAEC,KAAK,EAAC;IAAK;EAC7B,CAAC,EAAE;IACDN,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAElB,MAAM;IACjBoB,IAAI,EAAE;MAACC,IAAI,EAAC,EAAE;MAAEC,KAAK,EAAC;IAAQ;EAChC,CAAC,EACE;IACNN,IAAI,EAAE,WAAW;IACVC,IAAI,EAAE,IAAI;IACVC,SAAS,EAAEjB;EACb,CAAC,EACA;IACNe,IAAI,EAAE,eAAe;IACdC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEhB;EACb,CAAC,EACA;IACNc,IAAI,EAAE,iBAAiB;IAChBC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEf;EACb,CAAC,EACA;IACNa,IAAI,EAAE,eAAe;IACdC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEd;EACb,CAAC,EACA;IACNY,IAAI,EAAE,iBAAiB;IAChBC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEb;EACb,CAAC,EACA;IACNW,IAAI,EAAE,kBAAkB;IACjBC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEZ;EACb,CAAC,EACA;IACNU,IAAI,EAAE,gBAAgB;IACfC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEX;EACb,CAAC,EACA;IACNS,IAAI,EAAE,aAAa;IACZC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEV;EACb,CAAC,EACA;IACNQ,IAAI,EAAE,eAAe;IACdC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAET;EACb,CAAC,EACA;IACNO,IAAI,EAAE,QAAQ;IACPC,IAAI,EAAE,IAAI;IACVC,SAAS,EAAER;EACb,CAAC,EACA;IACNM,IAAI,EAAE,iBAAiB;IAChBC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEP;EACb,CAAC,EACA;IACNK,IAAI,EAAE,SAAS;IACRC,IAAI,EAAE,IAAI;IACVC,SAAS,EAAEN;EACb,CAAC,EACA;IACNI,IAAI,EAAE,cAAc;IACbC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEL;EACb,CAAC,EACA;IACNG,IAAI,EAAE,eAAe;IACdC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEJ;EACb,CAAC;AAEL,CAAC,EACD;EACEE,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEvB,KAAK;EAChByB,IAAI,EAAE;IAACC,IAAI,EAAC,EAAE;IAAEC,KAAK,EAAC;EAAO;AAC/B,CAAC,EACD;EACEN,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEnB,QAAQ;EACnBqB,IAAI,EAAE;IAACC,IAAI,EAAC,EAAE;IAAEC,KAAK,EAAC;EAAU;AAClC,CAAC,EACD;EACEN,IAAI,EAAE,GAAG;EACTE,SAAS,EAAEtB;AACb,CAAC,CACF;AACD;AACA,IAAM4B,MAAM,GAAG,IAAIjC,SAAS,CAAC;EAC3BkC,IAAI,EAAE,MAAM;EACZ;EACAV,MAAM,EAANA,MAAM,CAAC;AACT,CAAC,CAAC;AACF,IAAMW,YAAY,GAAGnC,SAAS,CAACoC,SAAS,CAACC,IAAI;AAC7C;AACArC,SAAS,CAACoC,SAAS,CAACC,IAAI,GAAG,SAASA,IAAIA,CAACC,QAAQ,EAAE;EAChD,OAAOH,YAAY,CAACI,IAAI,CAAC,IAAI,EAAED,QAAQ,CAAC,CAACE,KAAK,CAAC,UAAAC,GAAG;IAAA,OAAIA,GAAG;EAAA,EAAC;AAC7D,CAAC;AACD,eAAeR,MAAM", "ignoreList": []}]}