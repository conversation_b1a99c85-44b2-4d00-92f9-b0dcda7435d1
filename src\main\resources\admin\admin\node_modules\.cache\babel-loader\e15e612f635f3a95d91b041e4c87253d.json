{"remainingRequest": "D:\\project\\admin\\node_modules\\thread-loader\\dist\\cjs.js!D:\\project\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\admin\\src\\utils\\validate.js", "dependencies": [{"path": "D:\\project\\admin\\src\\utils\\validate.js", "mtime": 1755434868855}, {"path": "D:\\project\\admin\\babel.config.js", "mtime": 1711079474000}, {"path": "D:\\project\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\project\\admin\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\project\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLmV4ZWMuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAudGVzdC5qcyI7Ci8qKg0KICog6YKu566xDQogKiBAcGFyYW0geyp9IHMNCiAqLwpleHBvcnQgZnVuY3Rpb24gaXNFbWFpbChzKSB7CiAgcmV0dXJuIC9eKFthLXpBLVowLTlfLV0pK0AoW2EtekEtWjAtOV8tXSkrKCguW2EtekEtWjAtOV8tXXsyLDN9KXsxLDJ9KSQvLnRlc3Qocyk7Cn0KCi8qKg0KICog5omL5py65Y+356CBDQogKiBAcGFyYW0geyp9IHMNCiAqLwpleHBvcnQgZnVuY3Rpb24gaXNNb2JpbGUocykgewogIHJldHVybiB0cnVlOwp9CgovKioNCiAqIOeUteivneWPt+eggQ0KICogQHBhcmFtIHsqfSBzDQogKi8KZXhwb3J0IGZ1bmN0aW9uIGlzUGhvbmUocykgewogIHJldHVybiAvXihbMC05XXszLDR9LSk/WzAtOV17Nyw4fSQvLnRlc3Qocyk7Cn0KCi8qKg0KICogVVJM5Zyw5Z2ADQogKiBAcGFyYW0geyp9IHMNCiAqLwpleHBvcnQgZnVuY3Rpb24gaXNVUkwocykgewogIHJldHVybiAvXmh0dHBbc10/OlwvXC8uKi8udGVzdChzKTsKfQoKLyoqDQogKiDljLnphY3mlbDlrZfvvIzlj6/ku6XmmK/lsI/mlbDvvIzkuI3lj6/ku6XmmK/otJ/mlbAs5Y+v5Lul5Li656m6DQogKiBAcGFyYW0geyp9IHMgDQogKi8KZXhwb3J0IGZ1bmN0aW9uIGlzTnVtYmVyKHMpIHsKICByZXR1cm4gLyheLT9bKy1dPyhbMC05XSpcLj9bMC05XSt8WzAtOV0rXC4/WzAtOV0qKShbZUVdWystXT9bMC05XSspPyQpfCheJCkvLnRlc3Qocyk7Cn0KLyoqDQogKiDljLnphY3mlbTmlbDvvIzlj6/ku6XkuLrnqboNCiAqIEBwYXJhbSB7Kn0gcyANCiAqLwpleHBvcnQgZnVuY3Rpb24gaXNJbnROdW1lcihzKSB7CiAgcmV0dXJuIC8oXi0/XGQrJCl8KF4kKS8udGVzdChzKTsKfQovKioNCiAqIOi6q+S7veivgeagoemqjA0KICovCmV4cG9ydCBmdW5jdGlvbiBjaGVja0lkQ2FyZChpZGNhcmQpIHsKICB2YXIgcmVnSWRDYXJkID0gLyheXGR7MTV9JCl8KF5cZHsxOH0kKXwoXlxkezE3fShcZHxYfHgpJCkvOwogIGlmICghcmVnSWRDYXJkLnRlc3QoaWRjYXJkKSkgewogICAgcmV0dXJuIGZhbHNlOwogIH0gZWxzZSB7CiAgICByZXR1cm4gdHJ1ZTsKICB9Cn0="}, {"version": 3, "names": ["isEmail", "s", "test", "isMobile", "isPhone", "isURL", "isNumber", "isIntNumer", "checkIdCard", "idcard", "regIdCard"], "sources": ["D:/project/admin/src/utils/validate.js"], "sourcesContent": ["/**\r\n * 邮箱\r\n * @param {*} s\r\n */\r\nexport function isEmail(s) {\r\n\treturn /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((.[a-zA-Z0-9_-]{2,3}){1,2})$/.test(s)\r\n}\r\n\r\n/**\r\n * 手机号码\r\n * @param {*} s\r\n */\r\nexport function isMobile(s) {\r\n\treturn true\r\n}\r\n\r\n/**\r\n * 电话号码\r\n * @param {*} s\r\n */\r\nexport function isPhone(s) {\r\n\treturn /^([0-9]{3,4}-)?[0-9]{7,8}$/.test(s)\r\n}\r\n\r\n/**\r\n * URL地址\r\n * @param {*} s\r\n */\r\nexport function isURL(s) {\r\n\treturn /^http[s]?:\\/\\/.*/.test(s)\r\n}\r\n\r\n/**\r\n * 匹配数字，可以是小数，不可以是负数,可以为空\r\n * @param {*} s \r\n */\r\nexport function isNumber(s) {\r\n\treturn /(^-?[+-]?([0-9]*\\.?[0-9]+|[0-9]+\\.?[0-9]*)([eE][+-]?[0-9]+)?$)|(^$)/.test(s);\r\n}\r\n/**\r\n * 匹配整数，可以为空\r\n * @param {*} s \r\n */\r\nexport function isIntNumer(s) {\r\n\treturn /(^-?\\d+$)|(^$)/.test(s);\r\n}\r\n/**\r\n * 身份证校验\r\n */\r\nexport function checkIdCard(idcard) {\r\n\tconst regIdCard = /(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/;\r\n\tif (!regIdCard.test(idcard)) {\r\n\t\treturn false;\r\n\t} else {\r\n\t\treturn true;\r\n\t}\r\n}"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA,OAAO,SAASA,OAAOA,CAACC,CAAC,EAAE;EAC1B,OAAO,iEAAiE,CAACC,IAAI,CAACD,CAAC,CAAC;AACjF;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASE,QAAQA,CAACF,CAAC,EAAE;EAC3B,OAAO,IAAI;AACZ;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASG,OAAOA,CAACH,CAAC,EAAE;EAC1B,OAAO,4BAA4B,CAACC,IAAI,CAACD,CAAC,CAAC;AAC5C;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASI,KAAKA,CAACJ,CAAC,EAAE;EACxB,OAAO,kBAAkB,CAACC,IAAI,CAACD,CAAC,CAAC;AAClC;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASK,QAAQA,CAACL,CAAC,EAAE;EAC3B,OAAO,qEAAqE,CAACC,IAAI,CAACD,CAAC,CAAC;AACrF;AACA;AACA;AACA;AACA;AACA,OAAO,SAASM,UAAUA,CAACN,CAAC,EAAE;EAC7B,OAAO,gBAAgB,CAACC,IAAI,CAACD,CAAC,CAAC;AAChC;AACA;AACA;AACA;AACA,OAAO,SAASO,WAAWA,CAACC,MAAM,EAAE;EACnC,IAAMC,SAAS,GAAG,0CAA0C;EAC5D,IAAI,CAACA,SAAS,CAACR,IAAI,CAACO,MAAM,CAAC,EAAE;IAC5B,OAAO,KAAK;EACb,CAAC,MAAM;IACN,OAAO,IAAI;EACZ;AACD", "ignoreList": []}]}