{"remainingRequest": "D:\\project\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\project\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\admin\\src\\views\\404.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\admin\\src\\views\\404.vue", "mtime": 1711079474000}, {"path": "D:\\project\\admin\\babel.config.js", "mtime": 1711079474000}, {"path": "D:\\project\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\project\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\project\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\project\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmV4cG9ydCBkZWZhdWx0IHsKICBtZXRob2RzOiB7CiAgICBiYWNrOiBmdW5jdGlvbiBiYWNrKCkgewogICAgICB3aW5kb3cuaGlzdG9yeS5sZW5ndGggPiAxID8gdGhpcy4kcm91dGVyLmdvKC0xKSA6IHRoaXMuJHJvdXRlci5wdXNoKCIvIik7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["methods", "back", "window", "history", "length", "$router", "go", "push"], "sources": ["src/views/404.vue"], "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <img class=\"backgroud\" src=\"@/assets/img/404.png\" alt>\r\n    <div class=\"text main-text\">出错了...页面失踪了</div>\r\n    <div>\r\n      <el-button class=\"text\" @click=\"back()\" type=\"text\" icon=\"el-icon-back\">返回</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  methods: {\r\n    back() {\r\n      window.history.length > 1 ? this.$router.go(-1) : this.$router.push(\"/\");\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  display: flex;\r\n  align-items: center;\r\n  flex-direction: column;\r\n  width: 100%;\r\n  height: 100%;\r\n  min-height: 900px;\r\n  text-align: center;\r\n  .backgroud {\r\n    display: inline-block;\r\n    width: 200px;\r\n    height: 200px;\r\n    margin-top: 80px;\r\n  }\r\n  .main-text{\r\n    margin-top: 80px;\r\n  }\r\n  .text {\r\n    font-size: 24px;\r\n    font-weight: bold;\r\n    color: #333;\r\n  }\r\n}\r\n</style>\r\n\r\n"], "mappings": ";AAWA;EACAA,OAAA;IACAC,IAAA,WAAAA,KAAA;MACAC,MAAA,CAAAC,OAAA,CAAAC,MAAA,YAAAC,OAAA,CAAAC,EAAA,YAAAD,OAAA,CAAAE,IAAA;IACA;EACA;AACA", "ignoreList": []}]}