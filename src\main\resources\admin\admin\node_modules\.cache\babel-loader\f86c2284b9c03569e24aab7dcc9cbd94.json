{"remainingRequest": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\babel-loader\\lib\\index.js!G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!G:\\Develop\\springboot\\公司财务管理系统\\admin\\src\\views\\login.vue?vue&type=script&lang=js", "dependencies": [{"path": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\src\\views\\login.vue", "mtime": 1711079474000}, {"path": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\babel.config.js", "mtime": 1711079474000}, {"path": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuY29uY2F0LmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnJlZ2V4cC5leGVjLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLnJlcGxhY2UuanMiOwppbXBvcnQgbWVudSBmcm9tICJAL3V0aWxzL21lbnUiOwpleHBvcnQgZGVmYXVsdCB7CiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHZlcmlmeUNoZWNrMjogZmFsc2UsCiAgICAgIGZsYWc6IGZhbHNlLAogICAgICBiYXNlVXJsOiB0aGlzLiRiYXNlLnVybCwKICAgICAgbG9naW5UeXBlOiAxLAogICAgICBydWxlc0Zvcm06IHsKICAgICAgICB1c2VybmFtZTogIiIsCiAgICAgICAgcGFzc3dvcmQ6ICIiLAogICAgICAgIHJvbGU6ICIiCiAgICAgIH0sCiAgICAgIG1lbnVzOiBbXSwKICAgICAgcm9sZXM6IFtdLAogICAgICB0YWJsZU5hbWU6ICIiCiAgICB9OwogIH0sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIHZhciBtZW51cyA9IG1lbnUubGlzdCgpOwogICAgdGhpcy5tZW51cyA9IG1lbnVzOwogICAgZm9yICh2YXIgaSA9IDA7IGkgPCB0aGlzLm1lbnVzLmxlbmd0aDsgaSsrKSB7CiAgICAgIGlmICh0aGlzLm1lbnVzW2ldLmhhc0JhY2tMb2dpbiA9PSAn5pivJykgewogICAgICAgIHRoaXMucm9sZXMucHVzaCh0aGlzLm1lbnVzW2ldKTsKICAgICAgfQogICAgfQogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHt9LAogIGRlc3Ryb3llZDogZnVuY3Rpb24gZGVzdHJveWVkKCkge30sCiAgY29tcG9uZW50czoge30sCiAgbWV0aG9kczogewogICAgLy/ms6jlhowKICAgIHJlZ2lzdGVyOiBmdW5jdGlvbiByZWdpc3Rlcih0YWJsZU5hbWUpIHsKICAgICAgdGhpcy4kc3RvcmFnZS5zZXQoImxvZ2luVGFibGUiLCB0YWJsZU5hbWUpOwogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7CiAgICAgICAgcGF0aDogJy9yZWdpc3RlcicsCiAgICAgICAgcXVlcnk6IHsKICAgICAgICAgIHBhZ2VGbGFnOiAncmVnaXN0ZXInCiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvLyDnmbvpmYYKICAgIGxvZ2luOiBmdW5jdGlvbiBsb2dpbigpIHsKICAgICAgaWYgKCF0aGlzLnJ1bGVzRm9ybS51c2VybmFtZSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuivt+i+k+WFpeeUqOaIt+WQjSIpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICBpZiAoIXRoaXMucnVsZXNGb3JtLnBhc3N3b3JkKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi6K+36L6T5YWl5a+G56CBIik7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIGlmICh0aGlzLnJvbGVzLmxlbmd0aCA+IDEpIHsKICAgICAgICBpZiAoIXRoaXMucnVsZXNGb3JtLnJvbGUpIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuivt+mAieaLqeinkuiJsiIpOwogICAgICAgICAgcmV0dXJuOwogICAgICAgIH0KICAgICAgICB2YXIgbWVudXMgPSB0aGlzLm1lbnVzOwogICAgICAgIGZvciAodmFyIGkgPSAwOyBpIDwgbWVudXMubGVuZ3RoOyBpKyspIHsKICAgICAgICAgIGlmIChtZW51c1tpXS5yb2xlTmFtZSA9PSB0aGlzLnJ1bGVzRm9ybS5yb2xlKSB7CiAgICAgICAgICAgIHRoaXMudGFibGVOYW1lID0gbWVudXNbaV0udGFibGVOYW1lOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnRhYmxlTmFtZSA9IHRoaXMucm9sZXNbMF0udGFibGVOYW1lOwogICAgICAgIHRoaXMucnVsZXNGb3JtLnJvbGUgPSB0aGlzLnJvbGVzWzBdLnJvbGVOYW1lOwogICAgICB9CiAgICAgIHRoaXMubG9naW5Qb3N0KCk7CiAgICB9LAogICAgbG9naW5Qb3N0OiBmdW5jdGlvbiBsb2dpblBvc3QoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIHRoaXMuJGh0dHAoewogICAgICAgIHVybDogIiIuY29uY2F0KHRoaXMudGFibGVOYW1lLCAiL2xvZ2luP3VzZXJuYW1lPSIpLmNvbmNhdCh0aGlzLnJ1bGVzRm9ybS51c2VybmFtZSwgIiZwYXNzd29yZD0iKS5jb25jYXQodGhpcy5ydWxlc0Zvcm0ucGFzc3dvcmQpLAogICAgICAgIG1ldGhvZDogInBvc3QiCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKF9yZWYpIHsKICAgICAgICB2YXIgZGF0YSA9IF9yZWYuZGF0YTsKICAgICAgICBpZiAoZGF0YSAmJiBkYXRhLmNvZGUgPT09IDApIHsKICAgICAgICAgIF90aGlzLiRzdG9yYWdlLnNldCgiVG9rZW4iLCBkYXRhLnRva2VuKTsKICAgICAgICAgIF90aGlzLiRzdG9yYWdlLnNldCgicm9sZSIsIF90aGlzLnJ1bGVzRm9ybS5yb2xlKTsKICAgICAgICAgIF90aGlzLiRzdG9yYWdlLnNldCgic2Vzc2lvblRhYmxlIiwgX3RoaXMudGFibGVOYW1lKTsKICAgICAgICAgIF90aGlzLiRzdG9yYWdlLnNldCgiYWRtaW5OYW1lIiwgX3RoaXMucnVsZXNGb3JtLnVzZXJuYW1lKTsKICAgICAgICAgIF90aGlzLiRyb3V0ZXIucmVwbGFjZSh7CiAgICAgICAgICAgIHBhdGg6ICIvIgogICAgICAgICAgfSk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIF90aGlzLiRtZXNzYWdlLmVycm9yKGRhdGEubXNnKTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["menu", "data", "verifyCheck2", "flag", "baseUrl", "$base", "url", "loginType", "rulesForm", "username", "password", "role", "menus", "roles", "tableName", "mounted", "list", "i", "length", "hasBackLogin", "push", "created", "destroyed", "components", "methods", "register", "$storage", "set", "$router", "path", "query", "pageFlag", "login", "$message", "error", "<PERSON><PERSON><PERSON>", "loginPost", "_this", "$http", "concat", "method", "then", "_ref", "code", "token", "replace", "msg"], "sources": ["src/views/login.vue"], "sourcesContent": ["<template>\r\n  <div>\n    <div class=\"container\" :style='{\"minHeight\":\"100vh\",\"padding\":\"100px  180px  0 0\",\"alignItems\":\"center\",\"background\":\"url(http://codegen.caihongy.cn/20240127/c69581b3923448a1bec896fd2013b84c.png) no-repeat\",\"display\":\"flex\",\"width\":\"100%\",\"backgroundSize\":\"cover\",\"justifyContent\":\"flex-end\"}'>\r\n      <el-form :style='{\"padding\":\"40px 20px 20px\",\"boxShadow\":\"0 1px 20px rgba( 255,  255, 255, .8)\",\"margin\":\"0\",\"borderRadius\":\"4px\",\"background\":\"#fff\",\"width\":\"400px\",\"height\":\"auto\"}'>\r\n        <div v-if=\"true\" :style='{\"width\":\"100%\",\"margin\":\"0 0 10px 0\",\"lineHeight\":\"44px\",\"fontSize\":\"20px\",\"color\":\"#374254\",\"textAlign\":\"center\"}' class=\"title-container\">公司财务管理系统登录</div>\r\n        <div v-if=\"loginType==1\" class=\"list-item\" :style='{\"width\":\"80%\",\"margin\":\"0 auto 10px\",\"alignItems\":\"center\",\"flexWrap\":\"wrap\",\"display\":\"flex\"}'>\r\n          <div v-if=\"true\" class=\"lable\" :style='{\"width\":\"64px\",\"lineHeight\":\"44px\",\"fontSize\":\"14px\",\"color\":\" #374254\",\"fontWeight\":\"600\"}'>用户名：</div>\r\n          <input :style='{\"border\":\"1px solid rgba(167, 180, 201,.3) \",\"padding\":\"0 10px\",\"color\":\" #a7b4c9 \",\"outlineOffset\":\"4px\",\"width\":\"100%\",\"fontSize\":\"14px\",\"height\":\"44px\"}' placeholder=\"请输入用户名\" name=\"username\" type=\"text\" v-model=\"rulesForm.username\">\r\n        </div>\r\n        <div v-if=\"loginType==1\" class=\"list-item\" :style='{\"width\":\"80%\",\"margin\":\"0 auto 10px\",\"alignItems\":\"center\",\"flexWrap\":\"wrap\",\"display\":\"flex\"}'>\r\n          <div v-if=\"true\" class=\"lable\" :style='{\"width\":\"64px\",\"lineHeight\":\"44px\",\"fontSize\":\"14px\",\"color\":\" #374254\",\"fontWeight\":\"600\"}'>密码：</div>\r\n          <input :style='{\"border\":\"1px solid rgba(167, 180, 201,.3) \",\"padding\":\"0 10px\",\"color\":\" #a7b4c9 \",\"outlineOffset\":\"4px\",\"width\":\"100%\",\"fontSize\":\"14px\",\"height\":\"44px\"}' placeholder=\"请输入密码\" name=\"password\" type=\"password\" v-model=\"rulesForm.password\">\r\n        </div>\r\n\r\n        <div :style='{\"width\":\"80%\",\"margin\":\"20px auto\"}' v-if=\"roles.length>1\" prop=\"loginInRole\" class=\"list-type\">\r\n          <el-radio v-for=\"item in roles\" v-bind:key=\"item.roleName\" v-model=\"rulesForm.role\" :label=\"item.roleName\">{{item.roleName}}</el-radio>\r\n        </div>\r\n\r\n\t\t\r\n        <div :style='{\"width\":\"80%\",\"margin\":\"20px auto\",\"alignItems\":\"center\",\"flexWrap\":\"wrap\",\"display\":\"flex\"}'>\r\n          <el-button v-if=\"loginType==1\" :style='{\"border\":\"0\",\"cursor\":\"pointer\",\"padding\":\"0 24px\",\"margin\":\"10px\",\"outline\":\"none\",\"color\":\"#fff\",\"borderRadius\":\"4px\",\"background\":\"rgb(45, 220, 211)\",\"width\":\"auto\",\"fontSize\":\"14px\",\"height\":\"44px\"}' type=\"primary\" @click=\"login()\" class=\"loginInBt\">登录</el-button>\n          <el-button :style='{\"cursor\":\"pointer\",\"border\":\"0\",\"padding\":\"0 24px\",\"margin\":\"10px\",\"outline\":\"none\",\"color\":\"#fff\",\"borderRadius\":\"4px\",\"background\":\"rgb(15, 116, 253)\",\"width\":\"auto\",\"fontSize\":\"14px\",\"height\":\"44px\"}' type=\"primary\" @click=\"register('yuangong')\" class=\"register\">注册员工</el-button>\n        </div>\r\n      </el-form>\r\n\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport menu from \"@/utils/menu\";\nexport default {\r\n  data() {\r\n    return {\r\n\t\tverifyCheck2: false,\r\n\t\tflag: false,\r\n      baseUrl:this.$base.url,\n      loginType: 1,\n      rulesForm: {\r\n        username: \"\",\r\n        password: \"\",\r\n        role: \"\",\r\n      },\r\n      menus: [],\r\n      roles: [],\n      tableName: \"\",\r\n    };\r\n  },\r\n  mounted() {\r\n    let menus = menu.list();\n    this.menus = menus;\n\r\n    for (let i = 0; i < this.menus.length; i++) {\n      if (this.menus[i].hasBackLogin=='是') {\n        this.roles.push(this.menus[i])\n      }\n    }\n\r\n  },\r\n  created() {\r\n\r\n  },\r\n  destroyed() {\r\n\t    },\r\n  components: {\n  },\n  methods: {\r\n\n    //注册\n    register(tableName){\n\t\tthis.$storage.set(\"loginTable\", tableName);\n\t\tthis.$router.push({path:'/register',query:{pageFlag:'register'}})\n    },\r\n    // 登陆\r\n    login() {\r\n\n\t\tif (!this.rulesForm.username) {\r\n\t\t\tthis.$message.error(\"请输入用户名\");\r\n\t\t\treturn;\r\n\t\t}\r\n\t\tif (!this.rulesForm.password) {\r\n\t\t\tthis.$message.error(\"请输入密码\");\r\n\t\t\treturn;\r\n\t\t}\r\n\t\tif(this.roles.length>1) {\n\t\t\tif (!this.rulesForm.role) {\n\t\t\t\tthis.$message.error(\"请选择角色\");\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tlet menus = this.menus;\n\t\t\tfor (let i = 0; i < menus.length; i++) {\n\t\t\t\tif (menus[i].roleName == this.rulesForm.role) {\n\t\t\t\t\tthis.tableName = menus[i].tableName;\n\t\t\t\t}\n\t\t\t}\n\t\t} else {\n\t\t\tthis.tableName = this.roles[0].tableName;\n\t\t\tthis.rulesForm.role = this.roles[0].roleName;\n\t\t}\r\n\t\t\r\n\t\tthis.loginPost()\r\n    },\r\n\tloginPost() {\r\n\t\tthis.$http({\r\n\t\t\turl: `${this.tableName}/login?username=${this.rulesForm.username}&password=${this.rulesForm.password}`,\r\n\t\t\tmethod: \"post\"\r\n\t\t}).then(({ data }) => {\r\n\t\t\tif (data && data.code === 0) {\r\n\t\t\t\tthis.$storage.set(\"Token\", data.token);\r\n\t\t\t\tthis.$storage.set(\"role\", this.rulesForm.role);\r\n\t\t\t\tthis.$storage.set(\"sessionTable\", this.tableName);\r\n\t\t\t\tthis.$storage.set(\"adminName\", this.rulesForm.username);\r\n\t\t\t\tthis.$router.replace({ path: \"/\" });\r\n\t\t\t} else {\r\n\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t}\r\n\t\t});\r\n\t},\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.container {\r\n  min-height: 100vh;\r\n  position: relative;\r\n  background-repeat: no-repeat;\r\n  background-position: center center;\r\n  background-size: cover;\r\n      background: url(http://codegen.caihongy.cn/20240127/c69581b3923448a1bec896fd2013b84c.png) no-repeat;\r\n        \r\n  .list-item /deep/ .el-input .el-input__inner {\r\n\t\tborder: 1px solid rgba(167, 180, 201,.3) ;\r\n\t\tpadding: 0 10px;\r\n\t\tcolor:  #a7b4c9 ;\r\n\t\twidth: 100%;\r\n\t\tfont-size: 14px;\r\n\t\toutline-offset: 4px;\r\n\t\theight: 44px;\r\n\t  }\r\n  \r\n  .list-item.select /deep/ .el-select .el-input__inner {\r\n\t\tborder: 1px solid rgba(64, 158, 255, 1);\r\n\t\tpadding: 0 10px;\r\n\t\tbox-shadow: 0 0 6px rgba(64, 158, 255, .5);\r\n\t\toutline: 1px solid #efefef;\r\n\t\tcolor: rgba(64, 158, 255, 1);\r\n\t\twidth: 288px;\r\n\t\tfont-size: 14px;\r\n\t\toutline-offset: 4px;\r\n\t\theight: 44px;\r\n\t  }\r\n  \r\n  .list-code /deep/ .el-input .el-input__inner {\r\n  \t  \tborder: 1px solid rgba(167, 180, 201,.3) ;\r\n  \t  \tpadding: 0 10px;\r\n  \t  \toutline: none;\r\n  \t  \tcolor:  #a7b4c9 ;\r\n  \t  \twidth: calc(100% - 80px);\r\n  \t  \tfont-size: 14px;\r\n  \t  \theight: 44px;\r\n  \t  }\n\n  .list-type /deep/ .el-radio__input .el-radio__inner {\n\t\tbackground: rgba(53, 53, 53, 0);\r\n\t\tborder-color: #666666;\r\n\t  }\n  .list-type /deep/ .el-radio__input.is-checked .el-radio__inner {\n        background: #3b5998;\r\n        border-color: #3b5998;\r\n      }\n  .list-type /deep/ .el-radio__label {\n\t\tcolor: #666666;\r\n\t\tfont-size: 14px;\r\n\t  }\n  .list-type /deep/ .el-radio__input.is-checked+.el-radio__label {\n        color: #3b5998;\n        font-size: 14px;\n      }\r\n}\r\n\r\n</style>\n"], "mappings": ";;;;AA6BA,OAAAA,IAAA;AACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,YAAA;MACAC,IAAA;MACAC,OAAA,OAAAC,KAAA,CAAAC,GAAA;MACAC,SAAA;MACAC,SAAA;QACAC,QAAA;QACAC,QAAA;QACAC,IAAA;MACA;MACAC,KAAA;MACAC,KAAA;MACAC,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,IAAAH,KAAA,GAAAZ,IAAA,CAAAgB,IAAA;IACA,KAAAJ,KAAA,GAAAA,KAAA;IAEA,SAAAK,CAAA,MAAAA,CAAA,QAAAL,KAAA,CAAAM,MAAA,EAAAD,CAAA;MACA,SAAAL,KAAA,CAAAK,CAAA,EAAAE,YAAA;QACA,KAAAN,KAAA,CAAAO,IAAA,MAAAR,KAAA,CAAAK,CAAA;MACA;IACA;EAEA;EACAI,OAAA,WAAAA,QAAA,GAEA;EACAC,SAAA,WAAAA,UAAA,GACA;EACAC,UAAA,GACA;EACAC,OAAA;IAEA;IACAC,QAAA,WAAAA,SAAAX,SAAA;MACA,KAAAY,QAAA,CAAAC,GAAA,eAAAb,SAAA;MACA,KAAAc,OAAA,CAAAR,IAAA;QAAAS,IAAA;QAAAC,KAAA;UAAAC,QAAA;QAAA;MAAA;IACA;IACA;IACAC,KAAA,WAAAA,MAAA;MAEA,UAAAxB,SAAA,CAAAC,QAAA;QACA,KAAAwB,QAAA,CAAAC,KAAA;QACA;MACA;MACA,UAAA1B,SAAA,CAAAE,QAAA;QACA,KAAAuB,QAAA,CAAAC,KAAA;QACA;MACA;MACA,SAAArB,KAAA,CAAAK,MAAA;QACA,UAAAV,SAAA,CAAAG,IAAA;UACA,KAAAsB,QAAA,CAAAC,KAAA;UACA;QACA;QAEA,IAAAtB,KAAA,QAAAA,KAAA;QACA,SAAAK,CAAA,MAAAA,CAAA,GAAAL,KAAA,CAAAM,MAAA,EAAAD,CAAA;UACA,IAAAL,KAAA,CAAAK,CAAA,EAAAkB,QAAA,SAAA3B,SAAA,CAAAG,IAAA;YACA,KAAAG,SAAA,GAAAF,KAAA,CAAAK,CAAA,EAAAH,SAAA;UACA;QACA;MACA;QACA,KAAAA,SAAA,QAAAD,KAAA,IAAAC,SAAA;QACA,KAAAN,SAAA,CAAAG,IAAA,QAAAE,KAAA,IAAAsB,QAAA;MACA;MAEA,KAAAC,SAAA;IACA;IACAA,SAAA,WAAAA,UAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,KAAA;QACAhC,GAAA,KAAAiC,MAAA,MAAAzB,SAAA,sBAAAyB,MAAA,MAAA/B,SAAA,CAAAC,QAAA,gBAAA8B,MAAA,MAAA/B,SAAA,CAAAE,QAAA;QACA8B,MAAA;MACA,GAAAC,IAAA,WAAAC,IAAA;QAAA,IAAAzC,IAAA,GAAAyC,IAAA,CAAAzC,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAA0C,IAAA;UACAN,KAAA,CAAAX,QAAA,CAAAC,GAAA,UAAA1B,IAAA,CAAA2C,KAAA;UACAP,KAAA,CAAAX,QAAA,CAAAC,GAAA,SAAAU,KAAA,CAAA7B,SAAA,CAAAG,IAAA;UACA0B,KAAA,CAAAX,QAAA,CAAAC,GAAA,iBAAAU,KAAA,CAAAvB,SAAA;UACAuB,KAAA,CAAAX,QAAA,CAAAC,GAAA,cAAAU,KAAA,CAAA7B,SAAA,CAAAC,QAAA;UACA4B,KAAA,CAAAT,OAAA,CAAAiB,OAAA;YAAAhB,IAAA;UAAA;QACA;UACAQ,KAAA,CAAAJ,QAAA,CAAAC,KAAA,CAAAjC,IAAA,CAAA6C,GAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}