{"remainingRequest": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\src\\components\\common\\Editor.vue?vue&type=style&index=0&id=4c8019d1&lang=css", "dependencies": [{"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\src\\components\\common\\Editor.vue", "mtime": 1711079474000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Editor.vue"], "names": [], "mappings": ";AAkKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Editor.vue", "sourceRoot": "src/components/common", "sourcesContent": ["<template>\n  <div>\n    <!-- 图片上传组件辅助-->\n    <el-upload\n      class=\"avatar-uploader\"\n      :action=\"getActionUrl\"\n      name=\"file\"\n      :headers=\"header\"\n      :show-file-list=\"false\"\n      :on-success=\"uploadSuccess\"\n      :on-error=\"uploadError\"\n      :before-upload=\"beforeUpload\"\n    ></el-upload>\n\n    <quill-editor\n      class=\"editor\"\n      v-model=\"value\"\n      ref=\"myQuillEditor\"\n      :options=\"editorOption\"\n      @blur=\"onEditorBlur($event)\"\n      @focus=\"onEditorFocus($event)\"\n      @change=\"onEditorChange($event)\"\n    ></quill-editor>\n  </div>\n</template>\n<script>\n// 工具栏配置\nconst toolbarOptions = [\n  [\"bold\", \"italic\", \"underline\", \"strike\"], // 加粗 斜体 下划线 删除线\n  [\"blockquote\", \"code-block\"], // 引用  代码块\n  [{ header: 1 }, { header: 2 }], // 1、2 级标题\n  [{ list: \"ordered\" }, { list: \"bullet\" }], // 有序、无序列表\n  [{ script: \"sub\" }, { script: \"super\" }], // 上标/下标\n  [{ indent: \"-1\" }, { indent: \"+1\" }], // 缩进\n  // [{'direction': 'rtl'}],                         // 文本方向\n  [{ size: [\"small\", false, \"large\", \"huge\"] }], // 字体大小\n  [{ header: [1, 2, 3, 4, 5, 6, false] }], // 标题\n  [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色\n  [{ font: [] }], // 字体种类\n  [{ align: [] }], // 对齐方式\n  [\"clean\"], // 清除文本格式\n  [\"link\", \"image\", \"video\"] // 链接、图片、视频\n];\n\nimport { quillEditor } from \"vue-quill-editor\";\nimport \"quill/dist/quill.core.css\";\nimport \"quill/dist/quill.snow.css\";\nimport \"quill/dist/quill.bubble.css\";\n\nexport default {\n  props: {\n    /*编辑器的内容*/\n    value: {\n      type: String\n    },\n    action: {\n      type: String\n    },\n    /*图片大小*/\n    maxSize: {\n      type: Number,\n      default: 4000 //kb\n    }\n  },\n\n  components: {\n    quillEditor\n  },\n\n  data() {\n    return {\n      content: this.value,\n      quillUpdateImg: false, // 根据图片上传状态来确定是否显示loading动画，刚开始是false,不显示\n      editorOption: {\n        placeholder: \"\",\n        theme: \"snow\", // or 'bubble'\n        modules: {\n          toolbar: {\n            container: toolbarOptions,\n            // container: \"#toolbar\",\n            handlers: {\n              image: function(value) {\n                if (value) {\n                  // 触发input框选择图片文件\n                  document.querySelector(\".avatar-uploader input\").click();\n                } else {\n                  this.quill.format(\"image\", false);\n                }\n              }\n              // link: function(value) {\n              //   if (value) {\n              //     var href = prompt('请输入url');\n              //     this.quill.format(\"link\", href);\n              //   } else {\n              //     this.quill.format(\"link\", false);\n              //   }\n              // },\n            }\n          }\n        }\n      },\n      // serverUrl: `${base.url}sys/storage/uploadSwiper?token=${storage.get('token')}`, // 这里写你要上传的图片服务器地址\n      header: {\n        // token: sessionStorage.token\n       'Token': this.$storage.get(\"Token\")\n      } // 有的图片服务器要求请求头需要有token\n    };\n  },\n  computed: {\n    // 计算属性的 getter\n    getActionUrl: function() {\n      // return this.$base.url + this.action + \"?token=\" + this.$storage.get(\"token\");\n      return `/${this.$base.name}/` + this.action;\n    }\n  },\n  methods: {\n    onEditorBlur() {\n      //失去焦点事件\n    },\n    onEditorFocus() {\n      //获得焦点事件\n    },\n    onEditorChange() {\n      console.log(this.value);\n      //内容改变事件\n      this.$emit(\"input\", this.value);\n    },\n    // 富文本图片上传前\n    beforeUpload() {\n      // 显示loading动画\n      this.quillUpdateImg = true;\n    },\n\n    uploadSuccess(res, file) {\n      // res为图片服务器返回的数据\n      // 获取富文本组件实例\n      let quill = this.$refs.myQuillEditor.quill;\n      // 如果上传成功\n      if (res.code === 0) {\n        // 获取光标所在位置\n        let length = quill.getSelection().index;\n        // 插入图片  res.url为服务器返回的图片地址\n        quill.insertEmbed(length, \"image\", this.$base.url+ \"upload/\" +res.file);\n        // 调整光标到最后\n        quill.setSelection(length + 1);\n      } else {\n        this.$message.error(\"图片插入失败\");\n      }\n      // loading动画消失\n      this.quillUpdateImg = false;\n    },\n    // 富文本图片上传失败\n    uploadError() {\n      // loading动画消失\n      this.quillUpdateImg = false;\n      this.$message.error(\"图片插入失败\");\n    }\n  }\n};\n</script> \n\n<style>\n.editor {\n  line-height: normal !important;\n}\n.ql-snow .ql-tooltip[data-mode=\"link\"]::before {\n  content: \"请输入链接地址:\";\n}\n.ql-snow .ql-tooltip.ql-editing a.ql-action::after {\n  border-right: 0px;\n  content: \"保存\";\n  padding-right: 0px;\n}\n\n.ql-snow .ql-tooltip[data-mode=\"video\"]::before {\n  content: \"请输入视频地址:\";\n}\n.ql-container {\n\theight: 400px;\n}\n\n.ql-snow .ql-picker.ql-size .ql-picker-label::before,\n.ql-snow .ql-picker.ql-size .ql-picker-item::before {\n  content: \"14px\";\n}\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"small\"]::before,\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"small\"]::before {\n  content: \"10px\";\n}\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"large\"]::before,\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"large\"]::before {\n  content: \"18px\";\n}\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"huge\"]::before,\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"huge\"]::before {\n  content: \"32px\";\n}\n\n.ql-snow .ql-picker.ql-header .ql-picker-label::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item::before {\n  content: \"文本\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"1\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"1\"]::before {\n  content: \"标题1\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"2\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"2\"]::before {\n  content: \"标题2\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"3\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"3\"]::before {\n  content: \"标题3\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"4\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"4\"]::before {\n  content: \"标题4\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"5\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"5\"]::before {\n  content: \"标题5\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"6\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"6\"]::before {\n  content: \"标题6\";\n}\n\n.ql-snow .ql-picker.ql-font .ql-picker-label::before,\n.ql-snow .ql-picker.ql-font .ql-picker-item::before {\n  content: \"标准字体\";\n}\n.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=\"serif\"]::before,\n.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=\"serif\"]::before {\n  content: \"衬线字体\";\n}\n.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=\"monospace\"]::before,\n.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=\"monospace\"]::before {\n  content: \"等宽字体\";\n}\n</style>"]}]}