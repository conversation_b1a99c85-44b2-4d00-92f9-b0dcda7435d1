{"remainingRequest": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\src\\views\\modules\\kaoqinxinxi\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\src\\views\\modules\\kaoqinxinxi\\list.vue", "mtime": 1711079474000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["list.vue"], "names": [], "mappings": ";AAoLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAGA;;AAEA", "file": "list.vue", "sourceRoot": "src/views/modules/kaoqinxinxi", "sourcesContent": ["<template>\n\t<div class=\"main-content\" :style='{\"padding\":\"30px\",\"margin\":\"0\"}'>\n\t\t<!-- 列表页 -->\n\t\t<template v-if=\"showFlag\">\n\t\t\t<el-form class=\"center-form-pv\" :style='{\"margin\":\"0 0 20px\"}' :inline=\"true\" :model=\"searchForm\">\n\t\t\t\t<el-row :style='{\"display\":\"block\"}' >\n\t\t\t\t\t<div :style='{\"margin\":\"0 10px 0 0\",\"display\":\"inline-block\"}' class=\"select\" label=\"工号\" prop=\"gonghao\">\n\t\t\t\t\t\t<label :style='{\"margin\":\"0 10px 0 0\",\"color\":\"#374254\",\"display\":\"inline-block\",\"lineHeight\":\"40px\",\"fontSize\":\"14px\",\"fontWeight\":\"600\",\"height\":\"40px\"}' class=\"item-label\">工号</label>\r\n\t\t\t\t\t\t<el-select clearable v-model=\"searchForm.gonghao\" placeholder=\"请选择工号\" >\n\t\t\t\t\t\t\t<el-option v-for=\"(item,index) in gonghaoOptions\" v-bind:key=\"index\" :label=\"item\" :value=\"item\"></el-option>\n\t\t\t\t\t\t</el-select>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div :style='{\"margin\":\"0 10px 0 0\",\"display\":\"inline-block\"}'>\r\n\t\t\t\t\t\t<label :style='{\"margin\":\"0 10px 0 0\",\"color\":\"#374254\",\"display\":\"inline-block\",\"lineHeight\":\"40px\",\"fontSize\":\"14px\",\"fontWeight\":\"600\",\"height\":\"40px\"}' class=\"item-label\">姓名</label>\n\t\t\t\t\t\t<el-input v-model=\"searchForm.xingming\" placeholder=\"姓名\" @keydown.enter.native=\"search()\" clearable></el-input>\n\t\t\t\t\t</div>\n\t\t\t\t\t<el-button class=\"search\" type=\"success\" @click=\"search()\">\r\n\t\t\t\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"margin\":\"0 2px\",\"fontSize\":\"14px\",\"color\":\"#fff\",\"height\":\"40px\"}'></span>\r\n\t\t\t\t\t\t查询\r\n\t\t\t\t\t</el-button>\n\t\t\t\t</el-row>\n\n\t\t\t\t<el-row class=\"actions\" :style='{\"flexWrap\":\"wrap\",\"margin\":\"20px 0\",\"display\":\"flex\"}'>\n\t\t\t\t\t<el-button class=\"add\" v-if=\"isAuth('kaoqinxinxi','新增')\" type=\"success\" @click=\"addOrUpdateHandler()\">\r\n\t\t\t\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"margin\":\"0 2px\",\"fontSize\":\"14px\",\"color\":\"#fff\",\"height\":\"40px\"}'></span>\r\n\t\t\t\t\t\t添加\r\n\t\t\t\t\t</el-button>\r\n\t\t\t\t\t<el-button class=\"del\" v-if=\"isAuth('kaoqinxinxi','删除')\" :disabled=\"dataListSelections.length?false:true\" type=\"danger\" @click=\"deleteHandler()\">\r\n\t\t\t\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"margin\":\"0 2px\",\"fontSize\":\"14px\",\"color\":\"#fff\",\"height\":\"40px\"}'></span>\r\n\t\t\t\t\t\t删除\r\n\t\t\t\t\t</el-button>\n\n\n\n\t\t\t\t</el-row>\n\t\t\t</el-form>\r\n\t\t\t<div :style='{\"width\":\"100%\",\"padding\":\"10px\"}'>\n\t\t\t\t<el-table class=\"tables\"\r\n\t\t\t\t\t:stripe='false'\r\n\t\t\t\t\t:style='{\"width\":\"100%\",\"padding\":\"0\",\"borderColor\":\"#eee\",\"borderStyle\":\"solid\",\"borderWidth\":\"1px 0 0 1px\",\"background\":\"#fff\"}' \r\n\t\t\t\t\t:border='true'\r\n\t\t\t\t\tv-if=\"isAuth('kaoqinxinxi','查看')\"\n\t\t\t\t\t:data=\"dataList\"\n\t\t\t\t\tv-loading=\"dataListLoading\"\n\t\t\t\t@selection-change=\"selectionChangeHandler\">\r\n\t\t\t\t\t<el-table-column :resizable='true' type=\"selection\" align=\"center\" width=\"50\"></el-table-column>\n\t\t\t\t\t<el-table-column :resizable='true' :sortable='false' label=\"序号\" type=\"index\" width=\"50\" />\r\n\t\t\t\t\t<el-table-column :resizable='true' :sortable='false'  \n\t\t\t\t\t\tprop=\"yuefen\"\n\t\t\t\t\t\tlabel=\"月份\">\n\t\t\t\t\t\t<template slot-scope=\"scope\">\n\t\t\t\t\t\t\t{{scope.row.yuefen}}\n\t\t\t\t\t\t</template>\n\t\t\t\t\t</el-table-column>\n\t\t\t\t\t<el-table-column :resizable='true' :sortable='false'  \n\t\t\t\t\t\tprop=\"gonghao\"\n\t\t\t\t\t\tlabel=\"工号\">\n\t\t\t\t\t\t<template slot-scope=\"scope\">\n\t\t\t\t\t\t\t{{scope.row.gonghao}}\n\t\t\t\t\t\t</template>\n\t\t\t\t\t</el-table-column>\n\t\t\t\t\t<el-table-column :resizable='true' :sortable='false'  \n\t\t\t\t\t\tprop=\"xingming\"\n\t\t\t\t\t\tlabel=\"姓名\">\n\t\t\t\t\t\t<template slot-scope=\"scope\">\n\t\t\t\t\t\t\t{{scope.row.xingming}}\n\t\t\t\t\t\t</template>\n\t\t\t\t\t</el-table-column>\n\t\t\t\t\t<el-table-column :resizable='true' :sortable='false'  \n\t\t\t\t\t\tprop=\"bumen\"\n\t\t\t\t\t\tlabel=\"部门\">\n\t\t\t\t\t\t<template slot-scope=\"scope\">\n\t\t\t\t\t\t\t{{scope.row.bumen}}\n\t\t\t\t\t\t</template>\n\t\t\t\t\t</el-table-column>\n\t\t\t\t\t<el-table-column :resizable='true' :sortable='false'  \n\t\t\t\t\t\tprop=\"zhiwei\"\n\t\t\t\t\t\tlabel=\"职位\">\n\t\t\t\t\t\t<template slot-scope=\"scope\">\n\t\t\t\t\t\t\t{{scope.row.zhiwei}}\n\t\t\t\t\t\t</template>\n\t\t\t\t\t</el-table-column>\n\t\t\t\t\t<el-table-column :resizable='true' :sortable='false'  \n\t\t\t\t\t\tprop=\"yingchuqintianshu\"\n\t\t\t\t\t\tlabel=\"应出勤天数\">\n\t\t\t\t\t\t<template slot-scope=\"scope\">\n\t\t\t\t\t\t\t{{scope.row.yingchuqintianshu}}\n\t\t\t\t\t\t</template>\n\t\t\t\t\t</el-table-column>\n\t\t\t\t\t<el-table-column :resizable='true' :sortable='false'  \n\t\t\t\t\t\tprop=\"qingjiatianshu\"\n\t\t\t\t\t\tlabel=\"请假天数\">\n\t\t\t\t\t\t<template slot-scope=\"scope\">\n\t\t\t\t\t\t\t{{scope.row.qingjiatianshu}}\n\t\t\t\t\t\t</template>\n\t\t\t\t\t</el-table-column>\n\t\t\t\t\t<el-table-column :resizable='true' :sortable='false'  \n\t\t\t\t\t\tprop=\"chidaocishu\"\n\t\t\t\t\t\tlabel=\"迟到次数\">\n\t\t\t\t\t\t<template slot-scope=\"scope\">\n\t\t\t\t\t\t\t{{scope.row.chidaocishu}}\n\t\t\t\t\t\t</template>\n\t\t\t\t\t</el-table-column>\n\t\t\t\t\t<el-table-column :resizable='true' :sortable='false'  \n\t\t\t\t\t\tprop=\"zaotuicishu\"\n\t\t\t\t\t\tlabel=\"早退次数\">\n\t\t\t\t\t\t<template slot-scope=\"scope\">\n\t\t\t\t\t\t\t{{scope.row.zaotuicishu}}\n\t\t\t\t\t\t</template>\n\t\t\t\t\t</el-table-column>\n\t\t\t\t\t<el-table-column :resizable='true' :sortable='false'  \n\t\t\t\t\t\tprop=\"chuchatianshu\"\n\t\t\t\t\t\tlabel=\"出差天数\">\n\t\t\t\t\t\t<template slot-scope=\"scope\">\n\t\t\t\t\t\t\t{{scope.row.chuchatianshu}}\n\t\t\t\t\t\t</template>\n\t\t\t\t\t</el-table-column>\n\t\t\t\t\t<el-table-column :resizable='true' :sortable='false'  \n\t\t\t\t\t\tprop=\"shidaotianshu\"\n\t\t\t\t\t\tlabel=\"实到天数\">\n\t\t\t\t\t\t<template slot-scope=\"scope\">\n\t\t\t\t\t\t\t{{scope.row.shidaotianshu}}\n\t\t\t\t\t\t</template>\n\t\t\t\t\t</el-table-column>\n\t\t\t\t\t<el-table-column :resizable='true' :sortable='false'  \n\t\t\t\t\t\tprop=\"dengjishijian\"\n\t\t\t\t\t\tlabel=\"登记时间\">\n\t\t\t\t\t\t<template slot-scope=\"scope\">\n\t\t\t\t\t\t\t{{scope.row.dengjishijian}}\n\t\t\t\t\t\t</template>\n\t\t\t\t\t</el-table-column>\n\t\t\t\t\t<el-table-column width=\"300\" label=\"操作\">\n\t\t\t\t\t\t<template slot-scope=\"scope\">\r\n\t\t\t\t\t\t\t<el-button class=\"view\" v-if=\" isAuth('kaoqinxinxi','查看')\" type=\"success\" @click=\"addOrUpdateHandler(scope.row.id,'info')\">\r\n\t\t\t\t\t\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"margin\":\"0 2px\",\"fontSize\":\"14px\",\"color\":\"#fff\",\"height\":\"40px\"}'></span>\r\n\t\t\t\t\t\t\t\t查看\r\n\t\t\t\t\t\t\t</el-button>\n\t\t\t\t\t\t\t<el-button class=\"edit\" v-if=\" isAuth('kaoqinxinxi','修改') \" type=\"success\" @click=\"addOrUpdateHandler(scope.row.id)\">\r\n\t\t\t\t\t\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"margin\":\"0 2px\",\"fontSize\":\"14px\",\"color\":\"#fff\",\"height\":\"40px\"}'></span>\r\n\t\t\t\t\t\t\t\t修改\r\n\t\t\t\t\t\t\t</el-button>\n\n\n\n\n\t\t\t\t\t\t\t<el-button class=\"del\" v-if=\"isAuth('kaoqinxinxi','删除') \" type=\"primary\" @click=\"deleteHandler(scope.row.id )\">\r\n\t\t\t\t\t\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"margin\":\"0 2px\",\"fontSize\":\"14px\",\"color\":\"#fff\",\"height\":\"40px\"}'></span>\r\n\t\t\t\t\t\t\t\t删除\r\n\t\t\t\t\t\t\t</el-button>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t</el-table-column>\n\t\t\t\t</el-table>\n\t\t\t</div>\r\n\t\t\t<el-pagination\r\n\t\t\t\t@size-change=\"sizeChangeHandle\"\n\t\t\t\t@current-change=\"currentChangeHandle\"\n\t\t\t\t:current-page=\"pageIndex\"\n\t\t\t\tbackground\r\n\t\t\t\t:page-sizes=\"[10, 50, 100, 200]\"\r\n\t\t\t\t:page-size=\"pageSize\"\r\n\t\t\t\t:layout=\"layouts.join()\"\r\n\t\t\t\t:total=\"totalPage\"\r\n\t\t\t\tprev-text=\"< \"\r\n\t\t\t\tnext-text=\"> \"\r\n\t\t\t\t:hide-on-single-page=\"true\"\r\n\t\t\t\t:style='{\"width\":\"100%\",\"padding\":\"0\",\"margin\":\"20px 0 0\",\"whiteSpace\":\"nowrap\",\"color\":\"#333\",\"fontWeight\":\"500\"}'\n\t\t\t></el-pagination>\n\t\t</template>\r\n\t\t\n\t\t<!-- 添加/修改页面  将父组件的search方法传递给子组件-->\n\t\t<add-or-update v-if=\"addOrUpdateFlag\" :parent=\"this\" ref=\"addOrUpdate\"></add-or-update>\n\n\n\n\n\n\t</div>\n</template>\r\n\n<script>\nimport axios from 'axios'\nimport AddOrUpdate from \"./add-or-update\";\n\texport default {\n\t\tdata() {\n\t\t\treturn {\r\n\t\t\t\tgonghaoOptions: [],\n\t\t\t\tsearchForm: {\n\t\t\t\t\tkey: \"\"\n\t\t\t\t},\n\t\t\t\tform:{},\n\t\t\t\tdataList: [],\n\t\t\t\tpageIndex: 1,\n\t\t\t\tpageSize: 10,\n\t\t\t\ttotalPage: 0,\n\t\t\t\tdataListLoading: false,\n\t\t\t\tdataListSelections: [],\n\t\t\t\tshowFlag: true,\n\t\t\t\tsfshVisiable: false,\n\t\t\t\tshForm: {},\n\t\t\t\tchartVisiable: false,\n\t\t\t\tchartVisiable1: false,\n\t\t\t\tchartVisiable2: false,\n\t\t\t\tchartVisiable3: false,\n\t\t\t\tchartVisiable4: false,\n\t\t\t\tchartVisiable5: false,\n\t\t\t\taddOrUpdateFlag:false,\n\t\t\t\tlayouts: [\"total\",\"prev\",\"pager\",\"next\",\"sizes\",\"jumper\"],\n\t\t\t};\n\t\t},\n\t\tcreated() {\n\t\t\tthis.init();\n\t\t\tthis.getDataList();\n\t\t\tthis.contentStyleChange()\r\n\t\t},\n\t\tmounted() {\n\t\t},\n\t\tfilters: {\n\t\t\thtmlfilter: function (val) {\n\t\t\t\treturn val.replace(/<[^>]*>/g).replace(/undefined/g,'');\n\t\t\t}\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\ttablename(){\r\n\t\t\t\treturn this.$storage.get('sessionTable')\r\n\t\t\t},\r\n\t\t},\n\t\tcomponents: {\n\t\t\tAddOrUpdate,\n\t\t},\n\t\tmethods: {\r\n\t\t\tcontentStyleChange() {\n\t\t\t\tthis.contentPageStyleChange()\n\t\t\t},\n\t\t\t// 分页\n\t\t\tcontentPageStyleChange(){\n\t\t\t\tlet arr = []\n\n\t\t\t\t// if(this.contents.pageTotal) arr.push('total')\n\t\t\t\t// if(this.contents.pageSizes) arr.push('sizes')\n\t\t\t\t// if(this.contents.pagePrevNext){\n\t\t\t\t//   arr.push('prev')\n\t\t\t\t//   if(this.contents.pagePager) arr.push('pager')\n\t\t\t\t//   arr.push('next')\n\t\t\t\t// }\n\t\t\t\t// if(this.contents.pageJumper) arr.push('jumper')\n\t\t\t\t// this.layouts = arr.join()\n\t\t\t\t// this.contents.pageEachNum = 10\n\t\t\t},\n\n\n\n\n\n\n    init () {\n          this.$http({\n            url: `option/yuangong/gonghao`,\n            method: \"get\"\n          }).then(({ data }) => {\n            if (data && data.code === 0) {\n              this.gonghaoOptions = data.data;\n            } else {\n              this.$message.error(data.msg);\n            }\n          });\n    },\n    search() {\n      this.pageIndex = 1;\n      this.getDataList();\n    },\n\n    // 获取数据列表\n    getDataList() {\n      this.dataListLoading = true;\n      let params = {\n        page: this.pageIndex,\n        limit: this.pageSize,\n        sort: 'id',\n        order: 'desc',\n      }\n           if(this.searchForm.gonghao!='' && this.searchForm.gonghao!=undefined){\n            params['gonghao'] = this.searchForm.gonghao\n          }\r\n           if(this.searchForm.xingming!='' && this.searchForm.xingming!=undefined){\n            params['xingming'] = '%' + this.searchForm.xingming + '%'\n          }\n\t\t\tthis.$http({\n\t\t\t\turl: \"kaoqinxinxi/page\",\n\t\t\t\tmethod: \"get\",\n\t\t\t\tparams: params\n\t\t\t}).then(({ data }) => {\n\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\tthis.dataList = data.data.list;\n\t\t\t\t\tthis.totalPage = data.data.total;\n\t\t\t\t} else {\n\t\t\t\t\tthis.dataList = [];\n\t\t\t\t\tthis.totalPage = 0;\n\t\t\t\t}\n\t\t\t\tthis.dataListLoading = false;\n\t\t\t});\n    },\n    // 每页数\n    sizeChangeHandle(val) {\n      this.pageSize = val;\n      this.pageIndex = 1;\n      this.getDataList();\n    },\n    // 当前页\n    currentChangeHandle(val) {\n      this.pageIndex = val;\n      this.getDataList();\n    },\n    // 多选\n    selectionChangeHandler(val) {\n      this.dataListSelections = val;\n    },\n    // 添加/修改\n    addOrUpdateHandler(id,type) {\n      this.showFlag = false;\n      this.addOrUpdateFlag = true;\n      this.crossAddOrUpdateFlag = false;\n      if(type!='info'){\n        type = 'else';\n      }\n      this.$nextTick(() => {\n        this.$refs.addOrUpdate.init(id,type);\n      });\n    },\n    // 下载\n    download(file){\n      let arr = file.replace(new RegExp('upload/', \"g\"), \"\")\r\n      axios.get(this.$base.url + 'file/download?fileName=' + arr, {\r\n      \theaders: {\r\n      \t\ttoken: this.$storage.get('Token')\r\n      \t},\r\n      \tresponseType: \"blob\"\r\n      }).then(({\r\n      \tdata\r\n      }) => {\r\n      \tconst binaryData = [];\r\n      \tbinaryData.push(data);\r\n      \tconst objectUrl = window.URL.createObjectURL(new Blob(binaryData, {\r\n      \t\ttype: 'application/pdf;chartset=UTF-8'\r\n      \t}))\r\n      \tconst a = document.createElement('a')\r\n      \ta.href = objectUrl\r\n      \ta.download = arr\r\n      \t// a.click()\r\n      \t// 下面这个写法兼容火狐\r\n      \ta.dispatchEvent(new MouseEvent('click', {\r\n      \t\tbubbles: true,\r\n      \t\tcancelable: true,\r\n      \t\tview: window\r\n      \t}))\r\n      \twindow.URL.revokeObjectURL(data)\r\n      },err=>{\r\n\t\t  axios.get((location.href.split(this.$base.name).length>1 ? location.href.split(this.$base.name)[0] :'') + this.$base.name + '/file/download?fileName=' + arr, {\r\n\t\t  \theaders: {\r\n\t\t  \t\ttoken: this.$storage.get('Token')\r\n\t\t  \t},\r\n\t\t  \tresponseType: \"blob\"\r\n\t\t  }).then(({\r\n\t\t  \tdata\r\n\t\t  }) => {\r\n\t\t  \tconst binaryData = [];\r\n\t\t  \tbinaryData.push(data);\r\n\t\t  \tconst objectUrl = window.URL.createObjectURL(new Blob(binaryData, {\r\n\t\t  \t\ttype: 'application/pdf;chartset=UTF-8'\r\n\t\t  \t}))\r\n\t\t  \tconst a = document.createElement('a')\r\n\t\t  \ta.href = objectUrl\r\n\t\t  \ta.download = arr\r\n\t\t  \t// a.click()\r\n\t\t  \t// 下面这个写法兼容火狐\r\n\t\t  \ta.dispatchEvent(new MouseEvent('click', {\r\n\t\t  \t\tbubbles: true,\r\n\t\t  \t\tcancelable: true,\r\n\t\t  \t\tview: window\r\n\t\t  \t}))\r\n\t\t  \twindow.URL.revokeObjectURL(data)\r\n\t\t  })\r\n\t  })\n    },\r\n\t// 预览\r\n\tpreClick(file){\r\n\t\tif(!file){\r\n\t\t\treturn false\r\n\t\t}\r\n\t\twindow.open((location.href.split(this.$base.name).length>1 ? location.href.split(this.$base.name)[0] + this.$base.name + '/' + file :this.$base.url + file))\r\n\t},\r\n\tkaoqinxinxistatusChange(e,row){\r\n\t\tif(row.status==0){\r\n\t\t\trow.passwordwrongnum = 0\r\n\t\t}\r\n\t\tthis.$http({\r\n\t\t\turl:'kaoqinxinxi/update',\r\n\t\t\tmethod:'post',\r\n\t\t\tdata:row\r\n\t\t}).then(res=>{\r\n\t\t\tif(row.status==1){\r\n\t\t\t\tthis.$message.error('该用户已锁定')\r\n\t\t\t}else{\r\n\t\t\t\tthis.$message.success('该用户已解除锁定')\r\n\t\t\t}\r\n\t\t})\r\n\t},\r\n    // 删除\n    deleteHandler(id ) {\n      var ids = id\n        ? [Number(id)]\n        : this.dataListSelections.map(item => {\n            return Number(item.id);\n          });\n      this.$confirm(`确定进行[${id ? \"删除\" : \"批量删除\"}]操作?`, \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      }).then(() => {\n        this.$http({\n          url: \"kaoqinxinxi/delete\",\n          method: \"post\",\n          data: ids\n        }).then(({ data }) => {\n          if (data && data.code === 0) {\r\n\t\t\tthis.$message({\r\n\t\t\t  message: \"操作成功\",\r\n\t\t\t  type: \"success\",\r\n\t\t\t  duration: 1500,\r\n\t\t\t  onClose: () => {\r\n\t\t\t    this.search();\r\n\t\t\t  }\r\n\t\t\t});\r\n            \n          } else {\n            this.$message.error(data.msg);\n          }\n        });\n      });\n    },\n\n\n  }\n\n};\n</script>\n<style lang=\"scss\" scoped>\n\t\r\n\t.center-form-pv {\r\n\t  .el-date-editor.el-input {\r\n\t    width: auto;\r\n\t  }\r\n\t}\r\n\t\r\n\t.el-input {\r\n\t  width: auto;\r\n\t}\n\t\r\n\t// form\r\n\t.center-form-pv .el-input /deep/ .el-input__inner {\r\n\t\t\t\tborder: 1px solid rgba(167, 180, 201,.3) ;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 12px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #a7b4c9;\r\n\t\t\t\twidth: 170px;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.center-form-pv .el-select /deep/ .el-input__inner {\r\n\t\t\t\tborder: 1px solid rgba(167, 180, 201,.3) ;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 10px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #a7b4c9;\r\n\t\t\t\twidth: 170px;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.center-form-pv .el-date-editor /deep/ .el-input__inner {\r\n\t\t\t\tborder: 1px solid rgba(167, 180, 201,.3) ;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 10px 0 30px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #a7b4c9;\r\n\t\t\t\twidth: 170px;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.center-form-pv .search {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #2ddcd3;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.center-form-pv .search:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.center-form-pv .actions .add {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #ffa22b;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.center-form-pv .actions .add:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.center-form-pv .actions .del {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #ff382b;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.center-form-pv .actions .del:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.center-form-pv .actions .statis {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #05e6e6;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.center-form-pv .actions .statis:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.center-form-pv .actions .btn18 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #ff2b88;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.center-form-pv .actions .btn18:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t// table\r\n\t.el-table /deep/ .el-table__header-wrapper thead {\r\n\t\t\t\tcolor: #374254;\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t}\r\n\t\r\n\t.el-table /deep/ .el-table__header-wrapper thead tr {\r\n\t\t\t\tbackground: #fff;\r\n\t\t\t}\r\n\t\r\n\t.el-table /deep/ .el-table__header-wrapper thead tr th {\r\n\t\t\t\tpadding: 12px 0;\r\n\t\t\t\tborder-color: rgba(167, 180, 201,.3) ;\r\n\t\t\t\tborder-width: 2px 1px 1px 0;\r\n\t\t\t\tborder-style: solid;\r\n\t\t\t\ttext-align: left;\r\n\t\t\t}\r\n\r\n\t.el-table /deep/ .el-table__header-wrapper thead tr th .cell {\r\n\t\t\t\tpadding: 0 10px;\r\n\t\t\t\tword-wrap: normal;\r\n\t\t\t\tword-break: break-all;\r\n\t\t\t\twhite-space: normal;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tvertical-align: middle;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tline-height: 24px;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t}\r\n\r\n\t\r\n\t.el-table /deep/ .el-table__body-wrapper tbody {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t}\r\n\r\n\t.el-table /deep/ .el-table__body-wrapper tbody tr {\r\n\t\t\t\tbackground: #fff;\r\n\t\t\t}\r\n\t\r\n\t.el-table /deep/ .el-table__body-wrapper tbody tr td {\r\n\t\t\t\tpadding: 12px 0;\r\n\t\t\t\tcolor: #999;\r\n\t\t\t\tbackground: #fff;\r\n\t\t\t\tborder-color: rgba(167, 180, 201,.3) ;\r\n\t\t\t\tborder-width: 0 1px 1px 0;\r\n\t\t\t\tborder-style: solid;\r\n\t\t\t\ttext-align: left;\r\n\t\t\t}\r\n\t\r\n\t\t\r\n\t.el-table /deep/ .el-table__body-wrapper tbody tr:hover td {\r\n\t\t\t\tpadding: 12px 0;\r\n\t\t\t\tcolor: #374254;\r\n\t\t\t\tbackground: #f9faff;\r\n\t\t\t\tborder-color: #eee;\r\n\t\t\t\tborder-width: 0 1px 1px 0;\r\n\t\t\t\tborder-style: solid;\r\n\t\t\t\ttext-align: left;\r\n\t\t\t}\r\n\t\r\n\t.el-table /deep/ .el-table__body-wrapper tbody tr td {\r\n\t\t\t\tpadding: 12px 0;\r\n\t\t\t\tcolor: #999;\r\n\t\t\t\tbackground: #fff;\r\n\t\t\t\tborder-color: rgba(167, 180, 201,.3) ;\r\n\t\t\t\tborder-width: 0 1px 1px 0;\r\n\t\t\t\tborder-style: solid;\r\n\t\t\t\ttext-align: left;\r\n\t\t\t}\r\n\r\n\t.el-table /deep/ .el-table__body-wrapper tbody tr td .cell {\r\n\t\t\t\tpadding: 0 10px;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\tword-break: break-all;\r\n\t\t\t\twhite-space: normal;\r\n\t\t\t\tline-height: 24px;\r\n\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t}\r\n\t\r\n\t.el-table /deep/ .el-table__body-wrapper tbody tr td .view {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 0 10px 10px 0;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #2ddcd3;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 32px;\r\n\t\t\t}\r\n\t\r\n\t.el-table /deep/ .el-table__body-wrapper tbody tr td .view:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.el-table /deep/ .el-table__body-wrapper tbody tr td .add {\r\n\t\t\t}\r\n\t\r\n\t.el-table /deep/ .el-table__body-wrapper tbody tr td .add:hover {\r\n\t\t\t}\r\n\t\r\n\t.el-table /deep/ .el-table__body-wrapper tbody tr td .edit {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 0 10px 10px 0;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #4771db;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 32px;\r\n\t\t\t}\r\n\t\r\n\t.el-table /deep/ .el-table__body-wrapper tbody tr td .edit:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.el-table /deep/ .el-table__body-wrapper tbody tr td .del {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 0 10px 10px 0;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #ff382b;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 32px;\r\n\t\t\t}\r\n\t\r\n\t.el-table /deep/ .el-table__body-wrapper tbody tr td .del:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.el-table /deep/ .el-table__body-wrapper tbody tr td .btn8 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 0 10px 10px 0;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #ff2b88;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.el-table /deep/ .el-table__body-wrapper tbody tr td .btn8:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t// pagination\r\n\t.main-content .el-pagination /deep/ .el-pagination__total {\r\n\t\t\t\tmargin: 0 10px 0 0;\r\n\t\t\t\tcolor: #666;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tvertical-align: top;\r\n\t\t\t\tfont-size: 13px;\r\n\t\t\t\tline-height: 28px;\r\n\t\t\t\theight: 28px;\r\n\t\t\t}\r\n\t\r\n\t.main-content .el-pagination /deep/ .btn-prev {\r\n\t\t\t\tborder: none;\r\n\t\t\t\tborder-radius: 2px;\r\n\t\t\t\tpadding: 0;\r\n\t\t\t\tmargin: 0 5px;\r\n\t\t\t\tcolor: #666;\r\n\t\t\t\tbackground: #f4f4f5;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tvertical-align: top;\r\n\t\t\t\tfont-size: 13px;\r\n\t\t\t\tline-height: 28px;\r\n\t\t\t\tmin-width: 35px;\r\n\t\t\t\theight: 28px;\r\n\t\t\t}\r\n\t\r\n\t.main-content .el-pagination /deep/ .btn-next {\r\n\t\t\t\tborder: none;\r\n\t\t\t\tborder-radius: 2px;\r\n\t\t\t\tpadding: 0;\r\n\t\t\t\tmargin: 0 5px;\r\n\t\t\t\tcolor: #666;\r\n\t\t\t\tbackground: #f4f4f5;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tvertical-align: top;\r\n\t\t\t\tfont-size: 13px;\r\n\t\t\t\tline-height: 28px;\r\n\t\t\t\tmin-width: 35px;\r\n\t\t\t\theight: 28px;\r\n\t\t\t}\r\n\t\r\n\t.main-content .el-pagination /deep/ .btn-prev:disabled {\r\n\t\t\t\tborder: none;\r\n\t\t\t\tcursor: not-allowed;\r\n\t\t\t\tborder-radius: 2px;\r\n\t\t\t\tpadding: 0;\r\n\t\t\t\tmargin: 0 5px;\r\n\t\t\t\tcolor: #C0C4CC;\r\n\t\t\t\tbackground: #f4f4f5;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tvertical-align: top;\r\n\t\t\t\tfont-size: 13px;\r\n\t\t\t\tline-height: 28px;\r\n\t\t\t\theight: 28px;\r\n\t\t\t}\r\n\t\r\n\t.main-content .el-pagination /deep/ .btn-next:disabled {\r\n\t\t\t\tborder: none;\r\n\t\t\t\tcursor: not-allowed;\r\n\t\t\t\tborder-radius: 2px;\r\n\t\t\t\tpadding: 0;\r\n\t\t\t\tmargin: 0 5px;\r\n\t\t\t\tcolor: #C0C4CC;\r\n\t\t\t\tbackground: #f4f4f5;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tvertical-align: top;\r\n\t\t\t\tfont-size: 13px;\r\n\t\t\t\tline-height: 28px;\r\n\t\t\t\theight: 28px;\r\n\t\t\t}\r\n\r\n\t.main-content .el-pagination /deep/ .el-pager {\r\n\t\t\t\tpadding: 0;\r\n\t\t\t\tmargin: 0;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tvertical-align: top;\r\n\t\t\t}\r\n\r\n\t.main-content .el-pagination /deep/ .el-pager .number {\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tpadding: 0 4px;\r\n\t\t\t\tmargin: 0 5px;\r\n\t\t\t\tcolor: #666;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tvertical-align: top;\r\n\t\t\t\tfont-size: 13px;\r\n\t\t\t\tline-height: 28px;\r\n\t\t\t\tborder-radius: 2px;\r\n\t\t\t\tbackground: #f4f4f5;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tmin-width: 30px;\r\n\t\t\t\theight: 28px;\r\n\t\t\t}\r\n\t\r\n\t.main-content .el-pagination /deep/ .el-pager .number:hover {\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tpadding: 0 4px;\r\n\t\t\t\tmargin: 0 5px;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tvertical-align: top;\r\n\t\t\t\tfont-size: 13px;\r\n\t\t\t\tline-height: 28px;\r\n\t\t\t\tborder-radius: 2px;\r\n\t\t\t\tbackground: #2ddcd3;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tmin-width: 30px;\r\n\t\t\t\theight: 28px;\r\n\t\t\t}\r\n\t\r\n\t.main-content .el-pagination /deep/ .el-pager .number.active {\r\n\t\t\t\tcursor: default;\r\n\t\t\t\tpadding: 0 4px;\r\n\t\t\t\tmargin: 0 5px;\r\n\t\t\t\tcolor: #FFF;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tvertical-align: top;\r\n\t\t\t\tfont-size: 13px;\r\n\t\t\t\tline-height: 28px;\r\n\t\t\t\tborder-radius: 2px;\r\n\t\t\t\tbackground: #2ddcd3;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tmin-width: 30px;\r\n\t\t\t\theight: 28px;\r\n\t\t\t}\r\n\t\r\n\t.main-content .el-pagination /deep/ .el-pagination__sizes {\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tvertical-align: top;\r\n\t\t\t\tfont-size: 13px;\r\n\t\t\t\tline-height: 28px;\r\n\t\t\t\theight: 28px;\r\n\t\t\t}\r\n\t\r\n\t.main-content .el-pagination /deep/ .el-pagination__sizes .el-input {\r\n\t\t\t\tmargin: 0 5px;\r\n\t\t\t\twidth: 100px;\r\n\t\t\t\tposition: relative;\r\n\t\t\t}\r\n\t\r\n\t.main-content .el-pagination /deep/ .el-pagination__sizes .el-input .el-input__inner {\r\n\t\t\t\tborder: 1px solid #DCDFE6;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tpadding: 0 25px 0 8px;\r\n\t\t\t\tcolor: #606266;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tfont-size: 13px;\r\n\t\t\t\tline-height: 28px;\r\n\t\t\t\tborder-radius: 3px;\r\n\t\t\t\toutline: 0;\r\n\t\t\t\tbackground: #FFF;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\theight: 28px;\r\n\t\t\t}\r\n\t\r\n\t.main-content .el-pagination /deep/ .el-pagination__sizes .el-input span.el-input__suffix {\r\n\t\t\t\ttop: 0;\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tright: 0;\r\n\t\t\t\theight: 100%;\r\n\t\t\t}\r\n\t\r\n\t.main-content .el-pagination /deep/ .el-pagination__sizes .el-input .el-input__suffix .el-select__caret {\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tcolor: #C0C4CC;\r\n\t\t\t\twidth: 25px;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\tline-height: 28px;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t}\r\n\t\r\n\t.main-content .el-pagination /deep/ .el-pagination__jump {\r\n\t\t\t\tmargin: 0 0 0 24px;\r\n\t\t\t\tcolor: #606266;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tvertical-align: top;\r\n\t\t\t\tfont-size: 13px;\r\n\t\t\t\tline-height: 28px;\r\n\t\t\t\theight: 28px;\r\n\t\t\t}\r\n\t\r\n\t.main-content .el-pagination /deep/ .el-pagination__jump .el-input {\r\n\t\t\t\tborder-radius: 3px;\r\n\t\t\t\tpadding: 0 2px;\r\n\t\t\t\tmargin: 0 2px;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\twidth: 50px;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\tline-height: 18px;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\theight: 28px;\r\n\t\t\t}\r\n\t\r\n\t.main-content .el-pagination /deep/ .el-pagination__jump .el-input .el-input__inner {\r\n\t\t\t\tborder: 1px solid #DCDFE6;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tpadding: 0 3px;\r\n\t\t\t\tcolor: #606266;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\tline-height: 28px;\r\n\t\t\t\tborder-radius: 3px;\r\n\t\t\t\toutline: 0;\r\n\t\t\t\tbackground: #FFF;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\theight: 28px;\r\n\t\t\t}\r\n\t\r\n\t// list one\r\n\t.one .list1-view {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 15px;\r\n\t\t\t\tmargin: 0;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #2ddcd3;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.one .list1-view:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.one .list1-edit {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 15px;\r\n\t\t\t\tmargin: 0;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #4771db;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.one .list1-edit:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.one .list1-del {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 15px;\r\n\t\t\t\tmargin: 0;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #ff382b;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.one .list1-del:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.one .list1-btn8 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #ff2b88;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.one .list1-btn8:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.main-content .el-table .el-switch {\r\n\t\t\t\tdisplay: inline-flex;\r\n\t\t\t\tvertical-align: middle;\r\n\t\t\t\tline-height: 30px;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\theight: 30px;\r\n\t\t\t}\r\n\t.main-content .el-table .el-switch /deep/ .el-switch__label--left {\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tmargin: 0 10px 0 0;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tvertical-align: middle;\r\n\t\t\t\tfont-size: 16px;\r\n\t\t\t\ttransition: .2s;\r\n\t\t\t\theight: 30px;\r\n\t\t\t}\r\n\t.main-content .el-table .el-switch /deep/ .el-switch__label--right {\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tmargin: 0 0 0 10px;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tvertical-align: middle;\r\n\t\t\t\tfont-size: 16px;\r\n\t\t\t\ttransition: .2s;\r\n\t\t\t\theight: 30px;\r\n\t\t\t}\r\n\t.main-content .el-table .el-switch /deep/ .el-switch__core {\r\n\t\t\t\tborder: 1px solid #0000ff;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 15px;\r\n\t\t\t\tmargin: 0;\r\n\t\t\t\toutline: 0;\r\n\t\t\t\tbackground: #0000ff;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\twidth: 60px;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\ttransition: border-color .3s,background-color .3s;\r\n\t\t\t\theight: 30px;\r\n\t\t\t}\r\n\t.main-content .el-table .el-switch /deep/ .el-switch__core::after {\r\n\t\t\t\tborder-radius: 100%;\r\n\t\t\t\ttop: 1px;\r\n\t\t\t\tleft: 1px;\r\n\t\t\t\tbackground: #FFF;\r\n\t\t\t\twidth: 26px;\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttransition: all .3s;\r\n\t\t\t\theight: 26px;\r\n\t\t\t}\r\n\t.main-content .el-table .el-switch.is-checked /deep/ .el-switch__core::after {\r\n\t\t\t\tmargin: 0 0 0 -27px;\r\n\t\t\t\tleft: 100%;\r\n\t\t\t}\r\n\t\r\n\t.main-content .el-table .el-rate /deep/ .el-rate__item {\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tvertical-align: middle;\r\n\t\t\t\tfont-size: 0;\r\n\t\t\t\tposition: relative;\r\n\t\t\t}\r\n\t.main-content .el-table .el-rate /deep/ .el-rate__item .el-rate__icon {\r\n\t\t\t\tmargin: 0 3px;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tfont-size: 18px;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: .3s;\r\n\t\t\t}\r\n</style>\n"]}]}