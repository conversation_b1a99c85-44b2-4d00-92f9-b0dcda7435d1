{"remainingRequest": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\src\\components\\common\\BreadCrumbs.vue?vue&type=style&index=0&id=b290fa88&lang=scss&scoped=true", "dependencies": [{"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\src\\components\\common\\BreadCrumbs.vue", "mtime": 1711079474000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5lbC1icmVhZGNydW1iIHsKCSYgL2RlZXAvIC5lbC1icmVhZGNydW1iX19zZXBhcmF0b3IgewoJICAJCSAgbWFyZ2luOiAwIDlweDsKCSAgCQkgIGNvbG9yOiAjY2NjOwoJICAJCSAgZm9udC13ZWlnaHQ6IDUwMDsKCSAgCQl9CgkKCSYgL2RlZXAvIC5lbC1icmVhZGNydW1iX19pbm5lciBhIHsKCSAgCQkgIGNvbG9yOiAjMzc0MjU0OwoJICAJCSAgZm9udC13ZWlnaHQ6IDYwMDsKCSAgCQkgIGRpc3BsYXk6IGlubGluZS1ibG9jazsKCSAgCQkgIGZvbnQtc2l6ZTogMjVweDsKCSAgCQl9CgkKCSYgL2RlZXAvIC5lbC1icmVhZGNydW1iX19pbm5lciB7CgkgIAkJICBjb2xvcjogIzM3NDI1NDsKCSAgCQkgIGxldHRlci1zcGFjaW5nOiAxcHg7CgkgIAkJICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7CgkgIAkJICBmb250LXNpemU6IDI1cHg7CgkgIAkJfQp9Cg=="}, {"version": 3, "sources": ["BreadCrumbs.vue"], "names": [], "mappings": ";AAgGA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "BreadCrumbs.vue", "sourceRoot": "src/components/common", "sourcesContent": ["<template>\r\n\t<div class=\"breadcrumb-preview\">\r\n\t\t<el-breadcrumb :style='{\"fontSize\":\"14px\",\"lineHeight\":\"1\"}' separator=\"Ξ\">\r\n\t\t\t<transition-group name=\"breadcrumb\" class=\"box\">\r\n\t\t\t\t<el-breadcrumb-item v-for=\"(item,index) in levelList\" :key=\"item.path\">\r\n\t\t\t\t\t<span v-if=\"item.redirect==='noRedirect'||index==levelList.length-1\" class=\"no-redirect\">{{ item.name }}</span>\r\n\t\t\t\t\t<a v-else @click.prevent=\"handleLink(item)\">\r\n\t\t\t\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"letterSpacing\":\"1px\",\"margin\":\"0 2px\",\"lineHeight\":\"1\",\"fontSize\":\"25px\",\"color\":\"#374254\"}'></span>首页\r\n\t\t\t\t\t</a>\r\n\t\t\t\t</el-breadcrumb-item>\r\n\t\t\t</transition-group>\r\n\t\t</el-breadcrumb>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nimport pathToRegexp from 'path-to-regexp'\r\nimport { generateTitle } from '@/utils/i18n'\r\nexport default {\r\n  data() {\r\n    return {\r\n      levelList: null\r\n    }\r\n  },\r\n  watch: {\r\n    $route() {\r\n      this.getBreadcrumb()\r\n    }\r\n  },\r\n  created() {\r\n    this.getBreadcrumb()\r\n  },\r\n  methods: {\r\n    generateTitle,\r\n    getBreadcrumb() {\r\n      // only show routes with meta.title\r\n      let route = this.$route\r\n      let matched = route.matched.filter(item => item.meta)\r\n      const first = matched[0]\r\n      matched = [{ path: '/index' }].concat(matched)\r\n\r\n      this.levelList = matched.filter(item => item.meta)\r\n    },\r\n    isDashboard(route) {\r\n      const name = route && route.name\r\n      if (!name) {\r\n        return false\r\n      }\r\n      return name.trim().toLocaleLowerCase() === 'Index'.toLocaleLowerCase()\r\n    },\r\n    pathCompile(path) {\r\n      // To solve this problem https://github.com/PanJiaChen/vue-element-admin/issues/561\r\n      const { params } = this.$route\r\n      var toPath = pathToRegexp.compile(path)\r\n      return toPath(params)\r\n    },\r\n    handleLink(item) {\r\n      const { redirect, path } = item\r\n      if (redirect) {\r\n        this.$router.push(redirect)\r\n        return\r\n      }\r\n      if(path){\r\n      \t\t  this.$router.push(path)\r\n      }else{\r\n      \t\t  this.$router.push('/')\r\n      }\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.el-breadcrumb {\r\n\t\t& /deep/ .el-breadcrumb__separator {\r\n\t\t  \t\t  margin: 0 9px;\r\n\t\t  \t\t  color: #ccc;\r\n\t\t  \t\t  font-weight: 500;\r\n\t\t  \t\t}\r\n\t\t\r\n\t\t& /deep/ .el-breadcrumb__inner a {\r\n\t\t  \t\t  color: #374254;\r\n\t\t  \t\t  font-weight: 600;\r\n\t\t  \t\t  display: inline-block;\r\n\t\t  \t\t  font-size: 25px;\r\n\t\t  \t\t}\r\n\t\t\r\n\t\t& /deep/ .el-breadcrumb__inner {\r\n\t\t  \t\t  color: #374254;\r\n\t\t  \t\t  letter-spacing: 1px;\r\n\t\t  \t\t  display: inline-block;\r\n\t\t  \t\t  font-size: 25px;\r\n\t\t  \t\t}\r\n\t}\r\n</style>\r\n"]}]}