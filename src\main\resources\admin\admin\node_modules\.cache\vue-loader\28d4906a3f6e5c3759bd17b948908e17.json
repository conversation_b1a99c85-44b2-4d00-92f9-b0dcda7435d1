{"remainingRequest": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\src\\App.vue?vue&type=style&index=0&id=7ba5bd90&lang=scss", "dependencies": [{"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\src\\App.vue", "mtime": 1711079474000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Cip7CiAgcGFkZGluZzogMDsKICBtYXJnaW46MDsNCiAgYm94LXNpemluZzogYm9yZGVyLWJveDsKfQpodG1sLGJvZHl7CiAgd2lkdGg6IDEwMCU7CiAgaGVpZ2h0OiAxMDAlOwp9CiNhcHB7CiAgaGVpZ2h0OjEwMCU7Cn0KYm9keSB7CiAgcGFkZGluZzogMDsKICBtYXJnaW46IDA7CiAgCn0K"}, {"version": 3, "sources": ["App.vue"], "names": [], "mappings": ";AAaA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA", "file": "App.vue", "sourceRoot": "src", "sourcesContent": ["<template>\n  <div id=\"app\" class=\"\">\n    <router-view></router-view>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"app\",\n};\n</script>\n\n<style lang=\"scss\">\n*{\n  padding: 0;\n  margin:0;\r\n  box-sizing: border-box;\n}\nhtml,body{\n  width: 100%;\n  height: 100%;\n}\n#app{\n  height:100%;\n}\nbody {\n  padding: 0;\n  margin: 0;\n  \n}\n</style>\n"]}]}