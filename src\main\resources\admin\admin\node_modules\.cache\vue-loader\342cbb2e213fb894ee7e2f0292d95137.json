{"remainingRequest": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\src\\components\\common\\BreadCrumbs.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\src\\components\\common\\BreadCrumbs.vue", "mtime": 1711079474000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgcGF0aFRvUmVnZXhwIGZyb20gJ3BhdGgtdG8tcmVnZXhwJw0KaW1wb3J0IHsgZ2VuZXJhdGVUaXRsZSB9IGZyb20gJ0AvdXRpbHMvaTE4bicNCmV4cG9ydCBkZWZhdWx0IHsNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgbGV2ZWxMaXN0OiBudWxsDQogICAgfQ0KICB9LA0KICB3YXRjaDogew0KICAgICRyb3V0ZSgpIHsNCiAgICAgIHRoaXMuZ2V0QnJlYWRjcnVtYigpDQogICAgfQ0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0QnJlYWRjcnVtYigpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBnZW5lcmF0ZVRpdGxlLA0KICAgIGdldEJyZWFkY3J1bWIoKSB7DQogICAgICAvLyBvbmx5IHNob3cgcm91dGVzIHdpdGggbWV0YS50aXRsZQ0KICAgICAgbGV0IHJvdXRlID0gdGhpcy4kcm91dGUNCiAgICAgIGxldCBtYXRjaGVkID0gcm91dGUubWF0Y2hlZC5maWx0ZXIoaXRlbSA9PiBpdGVtLm1ldGEpDQogICAgICBjb25zdCBmaXJzdCA9IG1hdGNoZWRbMF0NCiAgICAgIG1hdGNoZWQgPSBbeyBwYXRoOiAnL2luZGV4JyB9XS5jb25jYXQobWF0Y2hlZCkNCg0KICAgICAgdGhpcy5sZXZlbExpc3QgPSBtYXRjaGVkLmZpbHRlcihpdGVtID0+IGl0ZW0ubWV0YSkNCiAgICB9LA0KICAgIGlzRGFzaGJvYXJkKHJvdXRlKSB7DQogICAgICBjb25zdCBuYW1lID0gcm91dGUgJiYgcm91dGUubmFtZQ0KICAgICAgaWYgKCFuYW1lKSB7DQogICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIG5hbWUudHJpbSgpLnRvTG9jYWxlTG93ZXJDYXNlKCkgPT09ICdJbmRleCcudG9Mb2NhbGVMb3dlckNhc2UoKQ0KICAgIH0sDQogICAgcGF0aENvbXBpbGUocGF0aCkgew0KICAgICAgLy8gVG8gc29sdmUgdGhpcyBwcm9ibGVtIGh0dHBzOi8vZ2l0aHViLmNvbS9QYW5KaWFDaGVuL3Z1ZS1lbGVtZW50LWFkbWluL2lzc3Vlcy81NjENCiAgICAgIGNvbnN0IHsgcGFyYW1zIH0gPSB0aGlzLiRyb3V0ZQ0KICAgICAgdmFyIHRvUGF0aCA9IHBhdGhUb1JlZ2V4cC5jb21waWxlKHBhdGgpDQogICAgICByZXR1cm4gdG9QYXRoKHBhcmFtcykNCiAgICB9LA0KICAgIGhhbmRsZUxpbmsoaXRlbSkgew0KICAgICAgY29uc3QgeyByZWRpcmVjdCwgcGF0aCB9ID0gaXRlbQ0KICAgICAgaWYgKHJlZGlyZWN0KSB7DQogICAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHJlZGlyZWN0KQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIGlmKHBhdGgpew0KICAgICAgCQkgIHRoaXMuJHJvdXRlci5wdXNoKHBhdGgpDQogICAgICB9ZWxzZXsNCiAgICAgIAkJICB0aGlzLiRyb3V0ZXIucHVzaCgnLycpDQogICAgICB9DQogICAgfSwNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["BreadCrumbs.vue"], "names": [], "mappings": ";AAgBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "BreadCrumbs.vue", "sourceRoot": "src/components/common", "sourcesContent": ["<template>\r\n\t<div class=\"breadcrumb-preview\">\r\n\t\t<el-breadcrumb :style='{\"fontSize\":\"14px\",\"lineHeight\":\"1\"}' separator=\"Ξ\">\r\n\t\t\t<transition-group name=\"breadcrumb\" class=\"box\">\r\n\t\t\t\t<el-breadcrumb-item v-for=\"(item,index) in levelList\" :key=\"item.path\">\r\n\t\t\t\t\t<span v-if=\"item.redirect==='noRedirect'||index==levelList.length-1\" class=\"no-redirect\">{{ item.name }}</span>\r\n\t\t\t\t\t<a v-else @click.prevent=\"handleLink(item)\">\r\n\t\t\t\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"letterSpacing\":\"1px\",\"margin\":\"0 2px\",\"lineHeight\":\"1\",\"fontSize\":\"25px\",\"color\":\"#374254\"}'></span>首页\r\n\t\t\t\t\t</a>\r\n\t\t\t\t</el-breadcrumb-item>\r\n\t\t\t</transition-group>\r\n\t\t</el-breadcrumb>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\nimport pathToRegexp from 'path-to-regexp'\r\nimport { generateTitle } from '@/utils/i18n'\r\nexport default {\r\n  data() {\r\n    return {\r\n      levelList: null\r\n    }\r\n  },\r\n  watch: {\r\n    $route() {\r\n      this.getBreadcrumb()\r\n    }\r\n  },\r\n  created() {\r\n    this.getBreadcrumb()\r\n  },\r\n  methods: {\r\n    generateTitle,\r\n    getBreadcrumb() {\r\n      // only show routes with meta.title\r\n      let route = this.$route\r\n      let matched = route.matched.filter(item => item.meta)\r\n      const first = matched[0]\r\n      matched = [{ path: '/index' }].concat(matched)\r\n\r\n      this.levelList = matched.filter(item => item.meta)\r\n    },\r\n    isDashboard(route) {\r\n      const name = route && route.name\r\n      if (!name) {\r\n        return false\r\n      }\r\n      return name.trim().toLocaleLowerCase() === 'Index'.toLocaleLowerCase()\r\n    },\r\n    pathCompile(path) {\r\n      // To solve this problem https://github.com/PanJiaChen/vue-element-admin/issues/561\r\n      const { params } = this.$route\r\n      var toPath = pathToRegexp.compile(path)\r\n      return toPath(params)\r\n    },\r\n    handleLink(item) {\r\n      const { redirect, path } = item\r\n      if (redirect) {\r\n        this.$router.push(redirect)\r\n        return\r\n      }\r\n      if(path){\r\n      \t\t  this.$router.push(path)\r\n      }else{\r\n      \t\t  this.$router.push('/')\r\n      }\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.el-breadcrumb {\r\n\t\t& /deep/ .el-breadcrumb__separator {\r\n\t\t  \t\t  margin: 0 9px;\r\n\t\t  \t\t  color: #ccc;\r\n\t\t  \t\t  font-weight: 500;\r\n\t\t  \t\t}\r\n\t\t\r\n\t\t& /deep/ .el-breadcrumb__inner a {\r\n\t\t  \t\t  color: #374254;\r\n\t\t  \t\t  font-weight: 600;\r\n\t\t  \t\t  display: inline-block;\r\n\t\t  \t\t  font-size: 25px;\r\n\t\t  \t\t}\r\n\t\t\r\n\t\t& /deep/ .el-breadcrumb__inner {\r\n\t\t  \t\t  color: #374254;\r\n\t\t  \t\t  letter-spacing: 1px;\r\n\t\t  \t\t  display: inline-block;\r\n\t\t  \t\t  font-size: 25px;\r\n\t\t  \t\t}\r\n\t}\r\n</style>\r\n"]}]}