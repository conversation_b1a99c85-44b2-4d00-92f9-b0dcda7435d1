{"remainingRequest": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\src\\views\\center.vue?vue&type=style&index=0&id=288a5f22&lang=scss&scoped=true", "dependencies": [{"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\src\\views\\center.vue", "mtime": 1754737848836}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["center.vue"], "names": [], "mappings": ";AAuUA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "center.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div :style='{\"padding\":\"30px\",\"margin\":\"0\"}'>\r\n    <el-form\r\n\t  :style='{\"border\":\"1px solid rgba(167, 180, 201,.3)  \",\"padding\":\"30px\",\"borderRadius\":\"6px\",\"background\":\"#fff\"}'\r\n      class=\"add-update-preview\"\r\n      ref=\"ruleForm\"\r\n      :model=\"ruleForm\"\r\n      label-width=\"80px\"\r\n    >  \r\n     <el-row>\r\n        <el-form-item :style='{\"margin\":\"0 0 20px 0\"}'   v-if=\"flag=='yuangong'\"  label=\"工号\" prop=\"gonghao\">\r\n          <el-input v-model=\"ruleForm.gonghao\" readonly              placeholder=\"工号\" clearable></el-input>\r\n        </el-form-item>\r\n        <el-form-item :style='{\"margin\":\"0 0 20px 0\"}'   v-if=\"flag=='yuangong'\"  label=\"姓名\" prop=\"xingming\">\r\n          <el-input v-model=\"ruleForm.xingming\"               placeholder=\"姓名\" clearable></el-input>\r\n        </el-form-item>\r\n        <el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-if=\"flag=='yuangong'\" label=\"头像\" prop=\"touxiang\">\r\n          <file-upload\r\n          tip=\"点击上传头像\"\r\n          action=\"file/upload\"\r\n          :limit=\"3\"\r\n          :multiple=\"true\"\r\n          :fileUrls=\"ruleForm.touxiang?ruleForm.touxiang:''\"\r\n          @change=\"yuangongtouxiangUploadChange\"\r\n          ></file-upload>\r\n        </el-form-item>\r\n        <el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-if=\"flag=='yuangong'\"  label=\"性别\" prop=\"xingbie\">\r\n          <el-select v-model=\"ruleForm.xingbie\"  placeholder=\"请选择性别\">\r\n            <el-option\r\n                v-for=\"(item,index) in yuangongxingbieOptions\"\r\n                v-bind:key=\"index\"\r\n                :label=\"item\"\r\n                :value=\"item\">\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item :style='{\"margin\":\"0 0 20px 0\"}'   v-if=\"flag=='yuangong'\"  label=\"联系电话\" prop=\"lianxidianhua\">\r\n          <el-input v-model=\"ruleForm.lianxidianhua\"               placeholder=\"联系电话\" clearable></el-input>\r\n        </el-form-item>\r\n        <el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-if=\"flag=='yuangong'\"  label=\"部门\" prop=\"bumen\">\r\n          <el-select v-model=\"ruleForm.bumen\" :disabled=\"true\" placeholder=\"请选择部门\">\r\n            <el-option\r\n                v-for=\"(item,index) in yuangongbumenOptions\"\r\n                v-bind:key=\"index\"\r\n                :label=\"item\"\r\n                :value=\"item\">\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-if=\"flag=='yuangong'\"  label=\"职位\" prop=\"zhiwei\">\r\n          <el-select v-model=\"ruleForm.zhiwei\" :disabled=\"true\" placeholder=\"请选择职位\">\r\n            <el-option\r\n                v-for=\"(item,index) in yuangongzhiweiOptions\"\r\n                v-bind:key=\"index\"\r\n                :label=\"item\"\r\n                :value=\"item\">\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-if=\"flag=='users'\" label=\"用户名\" prop=\"username\">\r\n\t\t\t<el-input v-model=\"ruleForm.username\" placeholder=\"用户名\"></el-input>\r\n\t\t</el-form-item>\r\n\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-if=\"flag=='users'\" label=\"头像\" prop=\"image\">\r\n\t\t  <file-upload\r\n\t\t  tip=\"点击上传头像\"\r\n\t\t  action=\"file/upload\"\r\n\t\t  :limit=\"1\"\r\n\t\t  :multiple=\"false\"\r\n\t\t  :fileUrls=\"ruleForm.image?ruleForm.image:''\"\r\n\t\t  @change=\"usersimageUploadChange\"\r\n\t\t  ></file-upload>\r\n\t\t</el-form-item>\r\n\t\t<el-form-item :style='{\"padding\":\"0\",\"margin\":\"0\"}'>\r\n\t\t\t<el-button class=\"btn3\" :style='{\"border\":\"0\",\"cursor\":\"pointer\",\"padding\":\"0 24px\",\"margin\":\"4px\",\"outline\":\"none\",\"color\":\"#fff\",\"borderRadius\":\"4px\",\"background\":\"#ff2b88\",\"width\":\"auto\",\"fontSize\":\"14px\",\"height\":\"40px\"}' type=\"primary\" @click=\"onUpdateHandler\">\r\n\t\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"margin\":\"0 2px\",\"fontSize\":\"14px\",\"color\":\"#fff\",\"height\":\"40px\"}'></span>\r\n\t\t\t\t提交\r\n\t\t\t</el-button>\r\n\t\t</el-form-item>\r\n      </el-row>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\n// 数字，邮件，手机，url，身份证校验\r\nimport { isNumber,isIntNumer,isEmail,isMobile,isPhone,isURL,checkIdCard } from \"@/utils/validate\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      ruleForm: {},\r\n      flag: '',\r\n      usersFlag: false,\r\n      yuangongxingbieOptions: [],\r\n      yuangongbumenOptions: [],\r\n      yuangongzhiweiOptions: [],\r\n    };\r\n  },\r\n  mounted() {\r\n    var table = this.$storage.get(\"sessionTable\");\r\n    this.flag = table;\r\n    this.$http({\r\n      url: `${this.$storage.get(\"sessionTable\")}/session`,\r\n      method: \"get\"\r\n    }).then(({ data }) => {\r\n      if (data && data.code === 0) {\r\n        this.ruleForm = data.data;\r\n      } else {\r\n        this.$message.error(data.msg);\r\n      }\r\n    });\r\n    this.yuangongxingbieOptions = \"男,女\".split(',')\r\n    this.$http({\r\n      url: `option/bumen/bumen`,\r\n      method: \"get\"\r\n    }).then(({ data }) => {\r\n      if (data && data.code === 0) {\r\n        this.yuangongbumenOptions = data.data;\r\n      } else {\r\n        this.$message.error(data.msg);\r\n      }\r\n    });\r\n    this.$http({\r\n      url: `option/zhiwei/zhiwei`,\r\n      method: \"get\"\r\n    }).then(({ data }) => {\r\n      if (data && data.code === 0) {\r\n        this.yuangongzhiweiOptions = data.data;\r\n      } else {\r\n        this.$message.error(data.msg);\r\n      }\r\n    });\r\n  },\r\n  methods: {\r\n    yuangongtouxiangUploadChange(fileUrls) {\r\n        this.ruleForm.touxiang = fileUrls;\r\n    },\r\n\tusersimageUploadChange(fileUrls) {\r\n\t\tthis.ruleForm.image = fileUrls;\r\n\t},\r\n    onUpdateHandler() {\r\n      if((!this.ruleForm.gonghao)&& 'yuangong'==this.flag){\r\n        this.$message.error('工号不能为空');\r\n        return\r\n      }\r\n\r\n\r\n      if((!this.ruleForm.mima)&& 'yuangong'==this.flag){\r\n        this.$message.error('密码不能为空');\r\n        return\r\n      }\r\n\r\n\r\n      if((!this.ruleForm.xingming)&& 'yuangong'==this.flag){\r\n        this.$message.error('姓名不能为空');\r\n        return\r\n      }\r\n\r\n\r\n\r\n\r\n        if(this.ruleForm.touxiang!=null) {\r\n                this.ruleForm.touxiang = this.ruleForm.touxiang.replace(new RegExp(this.$base.url,\"g\"),\"\");\r\n        }\r\n\r\n\r\n\r\n\r\n      // if( 'yuangong' ==this.flag && this.ruleForm.lianxidianhua&&(!isMobile(this.ruleForm.lianxidianhua))){\r\n      //   this.$message.error(`联系电话应输入手机格式`);\r\n      //   return\r\n      // }\r\n\r\n\r\n\r\n\r\n      if('users'==this.flag && this.ruleForm.username.trim().length<1) {\r\n\tthis.$message.error(`用户名不能为空`);\r\n        return\t\r\n      }\r\n\t  if(this.flag=='users'){\r\n\t  \tthis.ruleForm.image = this.ruleForm.image.replace(new RegExp(this.$base.url,\"g\"),\"\")\r\n\t  }\r\n      this.$http({\r\n        url: `${this.$storage.get(\"sessionTable\")}/update`,\r\n        method: \"post\",\r\n        data: this.ruleForm\r\n      }).then(({ data }) => {\r\n        if (data && data.code === 0) {\r\n          this.$message({\r\n            message: \"修改信息成功\",\r\n            type: \"success\",\r\n            duration: 1500,\r\n            onClose: () => {\r\n\t\t\t\tif(this.flag=='users'){\r\n\t\t\t\t\tthis.$storage.set('headportrait',this.ruleForm.image)\r\n\t\t\t\t}\r\n            }\r\n          });\r\n        } else {\r\n          this.$message.error(data.msg);\r\n        }\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\t.el-date-editor.el-input {\r\n\t\twidth: auto;\r\n\t}\r\n\t\r\n\t.add-update-preview .el-form-item /deep/ .el-form-item__label {\r\n\t  \t  padding: 0 10px 0 0;\r\n\t  \t  color: #374254;\r\n\t  \t  font-weight: 600;\r\n\t  \t  width: 80px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  line-height: 40px;\r\n\t  \t  text-align: right;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .el-form-item /deep/ .el-form-item__content {\r\n\t  margin-left: 80px;\r\n\t}\r\n\t\r\n\t.add-update-preview .el-input /deep/ .el-input__inner {\r\n\t  \t  border: 1px solid rgba(167, 180, 201,.3);\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 12px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .el-select /deep/ .el-input__inner {\r\n\t  \t  border:  1px solid rgba(167, 180, 201,.3) ;\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 10px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .el-date-editor /deep/ .el-input__inner {\r\n\t  \t  border:  1px solid rgba(167, 180, 201,.3) ;\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 10px 0 30px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview /deep/ .el-upload--picture-card {\r\n\t\tbackground: transparent;\r\n\t\tborder: 0;\r\n\t\tborder-radius: 0;\r\n\t\twidth: auto;\r\n\t\theight: auto;\r\n\t\tline-height: initial;\r\n\t\tvertical-align: middle;\r\n\t}\r\n\t\r\n\t.add-update-preview /deep/ .el-upload-list .el-upload-list__item {\r\n\t  \t  border: 2px dashed rgba(167, 180, 201,.3) ;\r\n\t  \t  cursor: pointer;\r\n\t  \t  border-radius: 6px;\r\n\t  \t  color: #a7b4c9  ;\r\n\t  \t  width: 200px;\r\n\t  \t  font-size: 32px;\r\n\t  \t  line-height: 200px;\r\n\t  \t  text-align: center;\r\n\t  \t  height: 200px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview /deep/ .el-upload .el-icon-plus {\r\n\t  \t  border: 2px dashed rgba(167, 180, 201,.3) ;\r\n\t  \t  cursor: pointer;\r\n\t  \t  border-radius: 6px;\r\n\t  \t  color: #a7b4c9  ;\r\n\t  \t  width: 200px;\r\n\t  \t  font-size: 32px;\r\n\t  \t  line-height: 200px;\r\n\t  \t  text-align: center;\r\n\t  \t  height: 200px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .el-textarea /deep/ .el-textarea__inner {\r\n\t  \t  border: 1px solid rgba(167, 180, 201,.3)     ;\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 12px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9 ;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 120px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .btn3 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #ff2b88;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn3:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.editor>.avatar-uploader {\r\n\t\tline-height: 0;\r\n\t\theight: 0;\r\n\t}\r\n</style>\r\n"]}]}