{"remainingRequest": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\src\\views\\home.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\src\\views\\home.vue", "mtime": 1755434670143}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQovLzMNCmltcG9ydCByb3V0ZXIgZnJvbSAnQC9yb3V0ZXIvcm91dGVyLXN0YXRpYycNCmltcG9ydCAqIGFzIGVjaGFydHMgZnJvbSAnZWNoYXJ0cycNCmV4cG9ydCBkZWZhdWx0IHsNCglkYXRhKCkgew0KCQlyZXR1cm4gew0KICAgICAgICAgICAgY2Fpd3V4aW54aUNvdW50OiAwLA0KCQkJbGluZTogeyJiYWNrZ3JvdW5kQ29sb3IiOiJ0cmFuc3BhcmVudCIsInlBeGlzIjp7ImF4aXNMYWJlbCI6eyJib3JkZXJUeXBlIjoic29saWQiLCJyb3RhdGUiOjAsInBhZGRpbmciOjAsInNoYWRvd09mZnNldFgiOjAsIm1hcmdpbiI6MTUsImJhY2tncm91bmRDb2xvciI6InRyYW5zcGFyZW50IiwiYm9yZGVyQ29sb3IiOiIjMDAwIiwic2hhZG93T2Zmc2V0WSI6MCwiY29sb3IiOiIjZmZmIiwic2hhZG93Qmx1ciI6MCwic2hvdyI6dHJ1ZSwiaW5zaWRlIjpmYWxzZSwiZWxsaXBzaXMiOiIuLi4iLCJvdmVyZmxvdyI6Im5vbmUiLCJib3JkZXJSYWRpdXMiOjAsImJvcmRlcldpZHRoIjowLCJ3aWR0aCI6IiIsImZvbnRTaXplIjoxMiwibGluZUhlaWdodCI6MjQsInNoYWRvd0NvbG9yIjoidHJhbnNwYXJlbnQiLCJmb250V2VpZ2h0Ijoibm9ybWFsIiwiaGVpZ2h0IjoiIn0sImF4aXNUaWNrIjp7InNob3ciOnRydWUsImxlbmd0aCI6NSwibGluZVN0eWxlIjp7InNoYWRvd09mZnNldFgiOjAsInNoYWRvd09mZnNldFkiOjAsImNhcCI6ImJ1dHQiLCJjb2xvciI6IiNmZmYiLCJzaGFkb3dCbHVyIjowLCJ3aWR0aCI6MSwidHlwZSI6InNvbGlkIiwib3BhY2l0eSI6MSwic2hhZG93Q29sb3IiOiJyZ2JhKDAsMCwwLC41KSJ9LCJpbnNpZGUiOmZhbHNlfSwic3BsaXRMaW5lIjp7ImxpbmVTdHlsZSI6eyJzaGFkb3dPZmZzZXRYIjowLCJzaGFkb3dPZmZzZXRZIjowLCJjYXAiOiJidXR0IiwiY29sb3IiOiIjY2NjIiwic2hhZG93Qmx1ciI6MCwid2lkdGgiOjEsInR5cGUiOiJzb2xpZCIsIm9wYWNpdHkiOjEsInNoYWRvd0NvbG9yIjoicmdiYSgwLDAsMCwuNSkifSwic2hvdyI6dHJ1ZX0sImF4aXNMaW5lIjp7ImxpbmVTdHlsZSI6eyJzaGFkb3dPZmZzZXRYIjowLCJzaGFkb3dPZmZzZXRZIjowLCJjYXAiOiJidXR0IiwiY29sb3IiOiIjZmZmIiwic2hhZG93Qmx1ciI6MCwid2lkdGgiOjEsInR5cGUiOiJzb2xpZCIsIm9wYWNpdHkiOjEsInNoYWRvd0NvbG9yIjoicmdiYSgwLDAsMCwuNSkifSwic2hvdyI6dHJ1ZX0sInNwbGl0QXJlYSI6eyJzaG93IjpmYWxzZSwiYXJlYVN0eWxlIjp7InNoYWRvd09mZnNldFgiOjAsInNoYWRvd09mZnNldFkiOjAsImNvbG9yIjoicmdiYSgyNTAsMjUwLDI1MCwwLjMpIiwib3BhY2l0eSI6MSwic2hhZG93Qmx1ciI6MTAsInNoYWRvd0NvbG9yIjoicmdiYSgwLDAsMCwuNSkifX19LCJ4QXhpcyI6eyJheGlzTGFiZWwiOnsiYm9yZGVyVHlwZSI6InNvbGlkIiwicm90YXRlIjowLCJwYWRkaW5nIjowLCJzaGFkb3dPZmZzZXRYIjowLCJtYXJnaW4iOjQsImJhY2tncm91bmRDb2xvciI6InRyYW5zcGFyZW50IiwiYm9yZGVyQ29sb3IiOiIjMDAwIiwic2hhZG93T2Zmc2V0WSI6MCwiY29sb3IiOiIjZmZmIiwic2hhZG93Qmx1ciI6MCwic2hvdyI6dHJ1ZSwiaW5zaWRlIjpmYWxzZSwiZWxsaXBzaXMiOiIuLi4iLCJvdmVyZmxvdyI6Im5vbmUiLCJib3JkZXJSYWRpdXMiOjAsImJvcmRlcldpZHRoIjowLCJ3aWR0aCI6IiIsImZvbnRTaXplIjoxMiwibGluZUhlaWdodCI6MjQsInNoYWRvd0NvbG9yIjoidHJhbnNwYXJlbnQiLCJmb250V2VpZ2h0Ijoibm9ybWFsIiwiaGVpZ2h0IjoiIn0sImF4aXNUaWNrIjp7InNob3ciOnRydWUsImxlbmd0aCI6NSwibGluZVN0eWxlIjp7InNoYWRvd09mZnNldFgiOjAsInNoYWRvd09mZnNldFkiOjAsImNhcCI6ImJ1dHQiLCJjb2xvciI6IiNmZmYiLCJzaGFkb3dCbHVyIjowLCJ3aWR0aCI6MSwidHlwZSI6InNvbGlkIiwib3BhY2l0eSI6MSwic2hhZG93Q29sb3IiOiJyZ2JhKDAsMCwwLC41KSJ9LCJpbnNpZGUiOmZhbHNlfSwic3BsaXRMaW5lIjp7ImxpbmVTdHlsZSI6eyJzaGFkb3dPZmZzZXRYIjowLCJzaGFkb3dPZmZzZXRZIjowLCJjYXAiOiJidXR0IiwiY29sb3IiOiIjZmZmIiwic2hhZG93Qmx1ciI6MCwid2lkdGgiOjEsInR5cGUiOiJzb2xpZCIsIm9wYWNpdHkiOjEsInNoYWRvd0NvbG9yIjoicmdiYSgwLDAsMCwuNSkifSwic2hvdyI6ZmFsc2V9LCJheGlzTGluZSI6eyJsaW5lU3R5bGUiOnsic2hhZG93T2Zmc2V0WCI6MCwic2hhZG93T2Zmc2V0WSI6MCwiY2FwIjoiYnV0dCIsImNvbG9yIjoiI2ZmZiIsInNoYWRvd0JsdXIiOjAsIndpZHRoIjoxLCJ0eXBlIjoic29saWQiLCJvcGFjaXR5IjoxLCJzaGFkb3dDb2xvciI6InJnYmEoMCwwLDAsLjUpIn0sInNob3ciOnRydWV9LCJzcGxpdEFyZWEiOnsic2hvdyI6ZmFsc2UsImFyZWFTdHlsZSI6eyJzaGFkb3dPZmZzZXRYIjowLCJzaGFkb3dPZmZzZXRZIjowLCJjb2xvciI6InJnYmEoMjU1LDI1NSwyNTUsLjMpIiwib3BhY2l0eSI6MSwic2hhZG93Qmx1ciI6MTAsInNoYWRvd0NvbG9yIjoicmdiYSgwLDAsMCwuNSkifX19LCJjb2xvciI6WyIjNTQ3MGM2IiwiIzkxY2M3NSIsIiNmYWM4NTgiLCIjZWU2NjY2IiwiIzczYzBkZSIsIiMzYmEyNzIiLCIjZmM4NDUyIiwiIzlhNjBiNCIsIiNlYTdjY2MiXSwibGVnZW5kIjp7InBhZGRpbmciOjUsIml0ZW1HYXAiOjEwLCJzaGFkb3dPZmZzZXRYIjowLCJiYWNrZ3JvdW5kQ29sb3IiOiJ0cmFuc3BhcmVudCIsImJvcmRlckNvbG9yIjoiI2NjYyIsInNoYWRvd09mZnNldFkiOjAsIm9yaWVudCI6Imhvcml6b250YWwiLCJzaGFkb3dCbHVyIjowLCJib3R0b20iOiJhdXRvIiwiaXRlbUhlaWdodCI6MTQsInNob3ciOnRydWUsImljb24iOiJyb3VuZFJlY3QiLCJpdGVtU3R5bGUiOnsiYm9yZGVyVHlwZSI6InNvbGlkIiwic2hhZG93T2Zmc2V0WCI6MCwiYm9yZGVyQ29sb3IiOiJpbmhlcml0Iiwic2hhZG93T2Zmc2V0WSI6MCwiY29sb3IiOiJyZ2IoMjU1LDI1NSwyNTUpIiwic2hhZG93Qmx1ciI6MCwiYm9yZGVyV2lkdGgiOjAsIm9wYWNpdHkiOjEsInNoYWRvd0NvbG9yIjoidHJhbnNwYXJlbnQifSwicmlnaHQiOiJhdXRvIiwidG9wIjoiYXV0byIsImJvcmRlclJhZGl1cyI6MCwibGluZVN0eWxlIjp7InNoYWRvd09mZnNldFgiOjAsInNoYWRvd09mZnNldFkiOjAsImNvbG9yIjoiaW5oZXJpdCIsInNoYWRvd0JsdXIiOjAsIndpZHRoIjoiYXV0byIsInR5cGUiOiJpbmhlcml0Iiwib3BhY2l0eSI6MSwic2hhZG93Q29sb3IiOiJ0cmFuc3BhcmVudCJ9LCJsZWZ0IjoicmlnaHQiLCJib3JkZXJXaWR0aCI6MCwid2lkdGgiOiJhdXRvIiwiaXRlbVdpZHRoIjoyNSwidGV4dFN0eWxlIjp7InRleHRCb3JkZXJXaWR0aCI6MCwiY29sb3IiOiIjZmZmIiwidGV4dFNoYWRvd0NvbG9yIjoidHJhbnNwYXJlbnQiLCJlbGxpcHNpcyI6Ii4uLiIsIm92ZXJmbG93Ijoibm9uZSIsImZvbnRTaXplIjoxMiwibGluZUhlaWdodCI6MjQsInRleHRTaGFkb3dPZmZzZXRYIjowLCJ0ZXh0U2hhZG93T2Zmc2V0WSI6MCwidGV4dEJvcmRlclR5cGUiOiJzb2xpZCIsImZvbnRXZWlnaHQiOjUwMCwidGV4dEJvcmRlckNvbG9yIjoidHJhbnNwYXJlbnQiLCJ0ZXh0U2hhZG93Qmx1ciI6MH0sInNoYWRvd0NvbG9yIjoicmdiYSgwLDAsMCwuMykiLCJoZWlnaHQiOiJhdXRvIn0sInNlcmllcyI6eyJzeW1ib2wiOiJlbXB0eUNpcmNsZSIsIml0ZW1TdHlsZSI6eyJib3JkZXJUeXBlIjoic29saWQiLCJzaGFkb3dPZmZzZXRYIjowLCJib3JkZXJDb2xvciI6IiNmZmYiLCJzaGFkb3dPZmZzZXRZIjowLCJjb2xvciI6IiIsInNoYWRvd0JsdXIiOjAsImJvcmRlcldpZHRoIjowLCJvcGFjaXR5IjoxLCJzaGFkb3dDb2xvciI6IiMwMDAifSwic2hvd1N5bWJvbCI6dHJ1ZSwibGluZVN0eWxlIjp7InNoYWRvd09mZnNldFgiOjAsInNoYWRvd09mZnNldFkiOjAsImNvbG9yIjoiIiwic2hhZG93Qmx1ciI6MCwid2lkdGgiOjIsInR5cGUiOiJzb2xpZCIsIm9wYWNpdHkiOjEsInNoYWRvd0NvbG9yIjoiIzAwMCJ9LCJzeW1ib2xTaXplIjo0fSwidG9vbHRpcCI6eyJiYWNrZ3JvdW5kQ29sb3IiOiIjMTIzIiwidGV4dFN0eWxlIjp7ImNvbG9yIjoiI2ZmZiJ9fSwidGl0bGUiOnsiYm9yZGVyVHlwZSI6InNvbGlkIiwicGFkZGluZyI6Miwic2hhZG93T2Zmc2V0WCI6MCwiYmFja2dyb3VuZENvbG9yIjoidHJhbnNwYXJlbnQiLCJib3JkZXJDb2xvciI6IiNjY2MiLCJzaGFkb3dPZmZzZXRZIjowLCJzaGFkb3dCbHVyIjowLCJib3R0b20iOiJhdXRvIiwic2hvdyI6dHJ1ZSwicmlnaHQiOiJhdXRvIiwidG9wIjoiYXV0byIsImJvcmRlclJhZGl1cyI6MCwibGVmdCI6ImxlZnQiLCJib3JkZXJXaWR0aCI6MCwidGV4dFN0eWxlIjp7InRleHRCb3JkZXJXaWR0aCI6MCwiY29sb3IiOiIjZmZmIiwidGV4dFNoYWRvd0NvbG9yIjoidHJhbnNwYXJlbnQiLCJmb250U2l6ZSI6MTQsImxpbmVIZWlnaHQiOjI0LCJ0ZXh0U2hhZG93T2Zmc2V0WCI6MCwidGV4dFNoYWRvd09mZnNldFkiOjAsInRleHRCb3JkZXJUeXBlIjoic29saWQiLCJmb250V2VpZ2h0Ijo1MDAsInRleHRCb3JkZXJDb2xvciI6IiNjY2MiLCJ0ZXh0U2hhZG93Qmx1ciI6MH0sInNoYWRvd0NvbG9yIjoidHJhbnNwYXJlbnQifX0sDQoJCQliYXI6IHsiYmFja2dyb3VuZENvbG9yIjoidHJhbnNwYXJlbnQiLCJ5QXhpcyI6eyJheGlzTGFiZWwiOnsiYm9yZGVyVHlwZSI6InNvbGlkIiwicm90YXRlIjowLCJwYWRkaW5nIjowLCJzaGFkb3dPZmZzZXRYIjowLCJtYXJnaW4iOjEyLCJiYWNrZ3JvdW5kQ29sb3IiOiJ0cmFuc3BhcmVudCIsImJvcmRlckNvbG9yIjoiIzAwMCIsInNoYWRvd09mZnNldFkiOjAsImNvbG9yIjoiI2ZmZiIsInNoYWRvd0JsdXIiOjAsInNob3ciOnRydWUsImluc2lkZSI6ZmFsc2UsImVsbGlwc2lzIjoiLi4uIiwib3ZlcmZsb3ciOiJub25lIiwiYm9yZGVyUmFkaXVzIjowLCJib3JkZXJXaWR0aCI6MCwid2lkdGgiOiIiLCJmb250U2l6ZSI6MTIsImxpbmVIZWlnaHQiOjI0LCJzaGFkb3dDb2xvciI6InRyYW5zcGFyZW50IiwiZm9udFdlaWdodCI6Im5vcm1hbCIsImhlaWdodCI6IiJ9LCJheGlzVGljayI6eyJzaG93Ijp0cnVlLCJsZW5ndGgiOjUsImxpbmVTdHlsZSI6eyJzaGFkb3dPZmZzZXRYIjowLCJzaGFkb3dPZmZzZXRZIjowLCJjYXAiOiJidXR0IiwiY29sb3IiOiIjZmZmIiwic2hhZG93Qmx1ciI6MCwid2lkdGgiOjEsInR5cGUiOiJzb2xpZCIsIm9wYWNpdHkiOjEsInNoYWRvd0NvbG9yIjoicmdiYSgwLDAsMCwuNSkifSwiaW5zaWRlIjpmYWxzZX0sInNwbGl0TGluZSI6eyJsaW5lU3R5bGUiOnsic2hhZG93T2Zmc2V0WCI6MCwic2hhZG93T2Zmc2V0WSI6MCwiY2FwIjoiYnV0dCIsImNvbG9yIjoiI2NjYyIsInNoYWRvd0JsdXIiOjAsIndpZHRoIjoxLCJ0eXBlIjoic29saWQiLCJvcGFjaXR5IjoxLCJzaGFkb3dDb2xvciI6InJnYmEoMCwwLDAsLjUpIn0sInNob3ciOnRydWV9LCJheGlzTGluZSI6eyJsaW5lU3R5bGUiOnsic2hhZG93T2Zmc2V0WCI6MCwic2hhZG93T2Zmc2V0WSI6MCwiY2FwIjoiYnV0dCIsImNvbG9yIjoiI2ZmZiIsInNoYWRvd0JsdXIiOjAsIndpZHRoIjoxLCJ0eXBlIjoic29saWQiLCJvcGFjaXR5IjoxLCJzaGFkb3dDb2xvciI6InJnYmEoMCwwLDAsLjUpIn0sInNob3ciOnRydWV9LCJzcGxpdEFyZWEiOnsic2hvdyI6ZmFsc2UsImFyZWFTdHlsZSI6eyJzaGFkb3dPZmZzZXRYIjowLCJzaGFkb3dPZmZzZXRZIjowLCJjb2xvciI6InJnYmEoMjUwLDI1MCwyNTAsMC4zKSIsIm9wYWNpdHkiOjEsInNoYWRvd0JsdXIiOjEwLCJzaGFkb3dDb2xvciI6InJnYmEoMCwwLDAsLjUpIn19fSwieEF4aXMiOnsiYXhpc0xhYmVsIjp7ImJvcmRlclR5cGUiOiJzb2xpZCIsInJvdGF0ZSI6MCwicGFkZGluZyI6MCwic2hhZG93T2Zmc2V0WCI6MCwibWFyZ2luIjo0LCJiYWNrZ3JvdW5kQ29sb3IiOiJ0cmFuc3BhcmVudCIsImJvcmRlckNvbG9yIjoiIzAwMCIsInNoYWRvd09mZnNldFkiOjAsImNvbG9yIjoiI2ZmZiIsInNoYWRvd0JsdXIiOjAsInNob3ciOnRydWUsImluc2lkZSI6ZmFsc2UsImVsbGlwc2lzIjoiLi4uIiwib3ZlcmZsb3ciOiJub25lIiwiYm9yZGVyUmFkaXVzIjowLCJib3JkZXJXaWR0aCI6MCwid2lkdGgiOiIiLCJmb250U2l6ZSI6MTIsImxpbmVIZWlnaHQiOjI0LCJzaGFkb3dDb2xvciI6InRyYW5zcGFyZW50IiwiZm9udFdlaWdodCI6Im5vcm1hbCIsImhlaWdodCI6IiJ9LCJheGlzVGljayI6eyJzaG93Ijp0cnVlLCJsZW5ndGgiOjUsImxpbmVTdHlsZSI6eyJzaGFkb3dPZmZzZXRYIjowLCJzaGFkb3dPZmZzZXRZIjowLCJjYXAiOiJidXR0IiwiY29sb3IiOiIjZmZmIiwic2hhZG93Qmx1ciI6MCwid2lkdGgiOjEsInR5cGUiOiJzb2xpZCIsIm9wYWNpdHkiOjEsInNoYWRvd0NvbG9yIjoicmdiYSgwLDAsMCwuNSkifSwiaW5zaWRlIjpmYWxzZX0sInNwbGl0TGluZSI6eyJsaW5lU3R5bGUiOnsic2hhZG93T2Zmc2V0WCI6MCwic2hhZG93T2Zmc2V0WSI6MCwiY2FwIjoiYnV0dCIsImNvbG9yIjoiI2ZmZiIsInNoYWRvd0JsdXIiOjAsIndpZHRoIjoxLCJ0eXBlIjoic29saWQiLCJvcGFjaXR5IjoxLCJzaGFkb3dDb2xvciI6InJnYmEoMCwwLDAsLjUpIn0sInNob3ciOmZhbHNlfSwiYXhpc0xpbmUiOnsibGluZVN0eWxlIjp7InNoYWRvd09mZnNldFgiOjAsInNoYWRvd09mZnNldFkiOjAsImNhcCI6ImJ1dHQiLCJjb2xvciI6IiNmZmYiLCJzaGFkb3dCbHVyIjowLCJ3aWR0aCI6MSwidHlwZSI6InNvbGlkIiwib3BhY2l0eSI6MSwic2hhZG93Q29sb3IiOiJyZ2JhKDAsMCwwLC41KSJ9LCJzaG93Ijp0cnVlfSwic3BsaXRBcmVhIjp7InNob3ciOmZhbHNlLCJhcmVhU3R5bGUiOnsic2hhZG93T2Zmc2V0WCI6MCwic2hhZG93T2Zmc2V0WSI6MCwiY29sb3IiOiJyZ2JhKDI1NSwyNTUsMjU1LC4zKSIsIm9wYWNpdHkiOjEsInNoYWRvd0JsdXIiOjEwLCJzaGFkb3dDb2xvciI6InJnYmEoMCwwLDAsLjUpIn19fSwiY29sb3IiOlsiIzAwZmYwMCIsIiM5MWNjNzUiLCIjZmFjODU4IiwiI2VlNjY2NiIsIiM3M2MwZGUiLCIjM2JhMjcyIiwiI2ZjODQ1MiIsIiM5YTYwYjQiLCIjZWE3Y2NjIl0sImxlZ2VuZCI6eyJwYWRkaW5nIjo1LCJpdGVtR2FwIjoxMCwic2hhZG93T2Zmc2V0WCI6MCwiYmFja2dyb3VuZENvbG9yIjoidHJhbnNwYXJlbnQiLCJib3JkZXJDb2xvciI6IiNjY2MiLCJzaGFkb3dPZmZzZXRZIjowLCJvcmllbnQiOiJob3Jpem9udGFsIiwic2hhZG93Qmx1ciI6MCwiYm90dG9tIjoiYXV0byIsIml0ZW1IZWlnaHQiOjE0LCJzaG93Ijp0cnVlLCJpY29uIjoicm91bmRSZWN0IiwiaXRlbVN0eWxlIjp7ImJvcmRlclR5cGUiOiJzb2xpZCIsInNoYWRvd09mZnNldFgiOjAsImJvcmRlckNvbG9yIjoiaW5oZXJpdCIsInNoYWRvd09mZnNldFkiOjAsImNvbG9yIjoicmdiKDI1NSwyNTUsMjU1KSIsInNoYWRvd0JsdXIiOjAsImJvcmRlcldpZHRoIjowLCJvcGFjaXR5IjoxLCJzaGFkb3dDb2xvciI6InRyYW5zcGFyZW50In0sInJpZ2h0IjoiYXV0byIsInRvcCI6ImF1dG8iLCJib3JkZXJSYWRpdXMiOjAsImxpbmVTdHlsZSI6eyJzaGFkb3dPZmZzZXRYIjowLCJzaGFkb3dPZmZzZXRZIjowLCJjb2xvciI6ImluaGVyaXQiLCJzaGFkb3dCbHVyIjowLCJ3aWR0aCI6ImF1dG8iLCJ0eXBlIjoiaW5oZXJpdCIsIm9wYWNpdHkiOjEsInNoYWRvd0NvbG9yIjoidHJhbnNwYXJlbnQifSwibGVmdCI6InJpZ2h0IiwiYm9yZGVyV2lkdGgiOjAsIndpZHRoIjoiYXV0byIsIml0ZW1XaWR0aCI6MjUsInRleHRTdHlsZSI6eyJ0ZXh0Qm9yZGVyV2lkdGgiOjAsImNvbG9yIjoiI2ZmZiIsInRleHRTaGFkb3dDb2xvciI6InRyYW5zcGFyZW50IiwiZWxsaXBzaXMiOiIuLi4iLCJvdmVyZmxvdyI6Im5vbmUiLCJmb250U2l6ZSI6MTIsImxpbmVIZWlnaHQiOjI0LCJ0ZXh0U2hhZG93T2Zmc2V0WCI6MCwidGV4dFNoYWRvd09mZnNldFkiOjAsInRleHRCb3JkZXJUeXBlIjoic29saWQiLCJmb250V2VpZ2h0Ijo1MDAsInRleHRCb3JkZXJDb2xvciI6InRyYW5zcGFyZW50IiwidGV4dFNoYWRvd0JsdXIiOjB9LCJzaGFkb3dDb2xvciI6InJnYmEoMCwwLDAsLjMpIiwiaGVpZ2h0IjoiYXV0byJ9LCJzZXJpZXMiOnsiYmFyV2lkdGgiOiJhdXRvIiwiaXRlbVN0eWxlIjp7ImJvcmRlclR5cGUiOiJzb2xpZCIsInNoYWRvd09mZnNldFgiOjAsImJvcmRlckNvbG9yIjoiI2ZmZiIsInNoYWRvd09mZnNldFkiOjAsImNvbG9yIjoiIiwic2hhZG93Qmx1ciI6MCwiYm9yZGVyV2lkdGgiOjAsIm9wYWNpdHkiOjEsInNoYWRvd0NvbG9yIjoiIzAwMCJ9LCJjb2xvckJ5IjoiZGF0YSIsImJhckNhdGVnb3J5R2FwIjoiMjAlIn0sInRvb2x0aXAiOnsiYmFja2dyb3VuZENvbG9yIjoiIzEyMyIsInRleHRTdHlsZSI6eyJjb2xvciI6IiNmZmYifX0sInRpdGxlIjp7ImJvcmRlclR5cGUiOiJzb2xpZCIsInBhZGRpbmciOjIsInNoYWRvd09mZnNldFgiOjAsImJhY2tncm91bmRDb2xvciI6InRyYW5zcGFyZW50IiwiYm9yZGVyQ29sb3IiOiIjY2NjIiwic2hhZG93T2Zmc2V0WSI6MCwic2hhZG93Qmx1ciI6MCwiYm90dG9tIjoiYXV0byIsInNob3ciOnRydWUsInJpZ2h0IjoiYXV0byIsInRvcCI6ImF1dG8iLCJib3JkZXJSYWRpdXMiOjAsImxlZnQiOiJsZWZ0IiwiYm9yZGVyV2lkdGgiOjAsInRleHRTdHlsZSI6eyJ0ZXh0Qm9yZGVyV2lkdGgiOjAsImNvbG9yIjoiI2ZmZiIsInRleHRTaGFkb3dDb2xvciI6InRyYW5zcGFyZW50IiwiZm9udFNpemUiOjE0LCJsaW5lSGVpZ2h0IjoyNCwidGV4dFNoYWRvd09mZnNldFgiOjAsInRleHRTaGFkb3dPZmZzZXRZIjowLCJ0ZXh0Qm9yZGVyVHlwZSI6InNvbGlkIiwiZm9udFdlaWdodCI6NTAwLCJ0ZXh0Qm9yZGVyQ29sb3IiOiIjY2NjIiwidGV4dFNoYWRvd0JsdXIiOjB9LCJzaGFkb3dDb2xvciI6InRyYW5zcGFyZW50In0sImJhc2UiOnsiYW5pbWF0ZSI6ZmFsc2UsImludGVydmFsIjoyMDAwfX0sDQoJCQlwaWU6IHsidG9vbHRpcCI6eyJiYWNrZ3JvdW5kQ29sb3IiOiIjMTIzIiwidGV4dFN0eWxlIjp7ImNvbG9yIjoiI2ZmZiJ9fSwiYmFja2dyb3VuZENvbG9yIjoidHJhbnNwYXJlbnQiLCJjb2xvciI6WyIjNTQ3MGM2IiwiIzkxY2M3NSIsIiNmYWM4NTgiLCIjZWU2NjY2IiwiIzczYzBkZSIsIiMzYmEyNzIiLCIjZmM4NDUyIiwiIzlhNjBiNCIsIiNlYTdjY2MiXSwidGl0bGUiOnsiYm9yZGVyVHlwZSI6InNvbGlkIiwicGFkZGluZyI6Miwic2hhZG93T2Zmc2V0WCI6MCwiYmFja2dyb3VuZENvbG9yIjoidHJhbnNwYXJlbnQiLCJib3JkZXJDb2xvciI6IiNjY2MiLCJzaGFkb3dPZmZzZXRZIjowLCJzaGFkb3dCbHVyIjowLCJib3R0b20iOiJhdXRvIiwic2hvdyI6dHJ1ZSwicmlnaHQiOiJhdXRvIiwidG9wIjoiYXV0byIsImJvcmRlclJhZGl1cyI6MCwibGVmdCI6ImxlZnQiLCJib3JkZXJXaWR0aCI6MCwidGV4dFN0eWxlIjp7InRleHRCb3JkZXJXaWR0aCI6MCwiY29sb3IiOiIjZmZmIiwidGV4dFNoYWRvd0NvbG9yIjoidHJhbnNwYXJlbnQiLCJmb250U2l6ZSI6MTQsImxpbmVIZWlnaHQiOjI0LCJ0ZXh0U2hhZG93T2Zmc2V0WCI6MCwidGV4dFNoYWRvd09mZnNldFkiOjAsInRleHRCb3JkZXJUeXBlIjoic29saWQiLCJmb250V2VpZ2h0Ijo1MDAsInRleHRCb3JkZXJDb2xvciI6IiNjY2MiLCJ0ZXh0U2hhZG93Qmx1ciI6MH0sInNoYWRvd0NvbG9yIjoidHJhbnNwYXJlbnQifSwibGVnZW5kIjp7InBhZGRpbmciOjUsIml0ZW1HYXAiOjEwLCJzaGFkb3dPZmZzZXRYIjowLCJiYWNrZ3JvdW5kQ29sb3IiOiJ0cmFuc3BhcmVudCIsImJvcmRlckNvbG9yIjoiI2NjYyIsInNoYWRvd09mZnNldFkiOjAsIm9yaWVudCI6Imhvcml6b250YWwiLCJzaGFkb3dCbHVyIjowLCJib3R0b20iOiJhdXRvIiwiaXRlbUhlaWdodCI6MTQsInNob3ciOnRydWUsImljb24iOiJyb3VuZFJlY3QiLCJpdGVtU3R5bGUiOnsiYm9yZGVyVHlwZSI6InNvbGlkIiwic2hhZG93T2Zmc2V0WCI6MCwiYm9yZGVyQ29sb3IiOiJpbmhlcml0Iiwic2hhZG93T2Zmc2V0WSI6MCwiY29sb3IiOiJpbmhlcml0Iiwic2hhZG93Qmx1ciI6MCwiYm9yZGVyV2lkdGgiOjAsIm9wYWNpdHkiOjEsInNoYWRvd0NvbG9yIjoidHJhbnNwYXJlbnQifSwicmlnaHQiOiJhdXRvIiwidG9wIjoiYXV0byIsImJvcmRlclJhZGl1cyI6MCwibGluZVN0eWxlIjp7InNoYWRvd09mZnNldFgiOjAsInNoYWRvd09mZnNldFkiOjAsImNvbG9yIjoiaW5oZXJpdCIsInNoYWRvd0JsdXIiOjAsIndpZHRoIjoiYXV0byIsInR5cGUiOiJpbmhlcml0Iiwib3BhY2l0eSI6MSwic2hhZG93Q29sb3IiOiJ0cmFuc3BhcmVudCJ9LCJsZWZ0IjoicmlnaHQiLCJib3JkZXJXaWR0aCI6MCwid2lkdGgiOiJhdXRvIiwiaXRlbVdpZHRoIjoyNSwidGV4dFN0eWxlIjp7InRleHRCb3JkZXJXaWR0aCI6MCwiY29sb3IiOiIjZmZmIiwidGV4dFNoYWRvd0NvbG9yIjoidHJhbnNwYXJlbnQiLCJlbGxpcHNpcyI6Ii4uLiIsIm92ZXJmbG93Ijoibm9uZSIsImZvbnRTaXplIjoxMiwibGluZUhlaWdodCI6MjQsInRleHRTaGFkb3dPZmZzZXRYIjowLCJ0ZXh0U2hhZG93T2Zmc2V0WSI6MCwidGV4dEJvcmRlclR5cGUiOiJzb2xpZCIsImZvbnRXZWlnaHQiOjUwMCwidGV4dEJvcmRlckNvbG9yIjoidHJhbnNwYXJlbnQiLCJ0ZXh0U2hhZG93Qmx1ciI6MH0sInNoYWRvd0NvbG9yIjoicmdiYSgwLDAsMCwuMykiLCJoZWlnaHQiOiJhdXRvIn0sInNlcmllcyI6eyJpdGVtU3R5bGUiOnsiYm9yZGVyVHlwZSI6InNvbGlkIiwic2hhZG93T2Zmc2V0WCI6MCwiYm9yZGVyQ29sb3IiOiIjZmZmIiwic2hhZG93T2Zmc2V0WSI6MCwiY29sb3IiOiIiLCJzaGFkb3dCbHVyIjowLCJib3JkZXJXaWR0aCI6MCwib3BhY2l0eSI6MSwic2hhZG93Q29sb3IiOiIjMDAwIn0sImxhYmVsIjp7ImJvcmRlclR5cGUiOiJzb2xpZCIsInJvdGF0ZSI6MCwicGFkZGluZyI6MCwidGV4dEJvcmRlcldpZHRoIjowLCJiYWNrZ3JvdW5kQ29sb3IiOiJ0cmFuc3BhcmVudCIsImJvcmRlckNvbG9yIjoiI2ZmZiIsImNvbG9yIjoiI2ZmZiIsInNob3ciOnRydWUsInRleHRTaGFkb3dDb2xvciI6InRyYW5zcGFyZW50IiwiZGlzdGFuY2VUb0xhYmVsTGluZSI6NSwiZWxsaXBzaXMiOiIuLi4iLCJvdmVyZmxvdyI6Im5vbmUiLCJib3JkZXJSYWRpdXMiOjAsImJvcmRlcldpZHRoIjowLCJmb250U2l6ZSI6MTIsImxpbmVIZWlnaHQiOjE4LCJ0ZXh0U2hhZG93T2Zmc2V0WCI6MCwicG9zaXRpb24iOiJvdXRzaWRlIiwidGV4dFNoYWRvd09mZnNldFkiOjAsInRleHRCb3JkZXJUeXBlIjoic29saWQiLCJ0ZXh0Qm9yZGVyQ29sb3IiOiIjZmZmIiwidGV4dFNoYWRvd0JsdXIiOjB9LCJsYWJlbExpbmUiOnsic2hvdyI6dHJ1ZSwibGVuZ3RoIjoxMCwibGluZVN0eWxlIjp7InNoYWRvd09mZnNldFgiOjAsInNoYWRvd09mZnNldFkiOjAsImNvbG9yIjoiI2ZmZiIsInNoYWRvd0JsdXIiOjAsIndpZHRoIjoxLCJ0eXBlIjoic29saWQiLCJvcGFjaXR5IjoxLCJzaGFkb3dDb2xvciI6IiMwMDAifSwibGVuZ3RoMiI6MTQsInNtb290aCI6ZmFsc2V9fX0sDQoJCQlmdW5uZWw6IHsidG9vbHRpcCI6eyJiYWNrZ3JvdW5kQ29sb3IiOiIjMTIzIiwidGV4dFN0eWxlIjp7ImNvbG9yIjoiI2ZmZiJ9fSwiYmFja2dyb3VuZENvbG9yIjoidHJhbnNwYXJlbnQiLCJjb2xvciI6WyIjNTQ3MGM2IiwiIzkxY2M3NSIsIiNmYWM4NTgiLCIjZWU2NjY2IiwiIzczYzBkZSIsIiMzYmEyNzIiLCIjZmM4NDUyIiwiIzlhNjBiNCIsIiNlYTdjY2MiXSwidGl0bGUiOnsiYm9yZGVyVHlwZSI6InNvbGlkIiwicGFkZGluZyI6Miwic2hhZG93T2Zmc2V0WCI6MCwiYmFja2dyb3VuZENvbG9yIjoidHJhbnNwYXJlbnQiLCJib3JkZXJDb2xvciI6IiNjY2MiLCJzaGFkb3dPZmZzZXRZIjowLCJzaGFkb3dCbHVyIjowLCJib3R0b20iOiJhdXRvIiwic2hvdyI6dHJ1ZSwicmlnaHQiOiJhdXRvIiwidG9wIjoiYXV0byIsImJvcmRlclJhZGl1cyI6MCwibGVmdCI6ImNlbnRlciIsImJvcmRlcldpZHRoIjoxLCJ0ZXh0U3R5bGUiOnsidGV4dEJvcmRlcldpZHRoIjowLCJjb2xvciI6IiNmZmYiLCJ0ZXh0U2hhZG93Q29sb3IiOiJ0cmFuc3BhcmVudCIsImZvbnRTaXplIjoxNCwibGluZUhlaWdodCI6MjQsInRleHRTaGFkb3dPZmZzZXRYIjowLCJ0ZXh0U2hhZG93T2Zmc2V0WSI6MCwidGV4dEJvcmRlclR5cGUiOiJzb2xpZCIsImZvbnRXZWlnaHQiOjUwMCwidGV4dEJvcmRlckNvbG9yIjoiI2NjYyIsInRleHRTaGFkb3dCbHVyIjowfSwic2hhZG93Q29sb3IiOiJ0cmFuc3BhcmVudCJ9LCJsZWdlbmQiOnsicGFkZGluZyI6NSwiaXRlbUdhcCI6MTAsInNoYWRvd09mZnNldFgiOjAsImJhY2tncm91bmRDb2xvciI6InRyYW5zcGFyZW50IiwiYm9yZGVyQ29sb3IiOiIjY2NjIiwic2hhZG93T2Zmc2V0WSI6MCwib3JpZW50IjoidmVydGljYWwiLCJzaGFkb3dCbHVyIjowLCJib3R0b20iOiJhdXRvIiwiaXRlbUhlaWdodCI6MTQsInNob3ciOnRydWUsImljb24iOiJyb3VuZFJlY3QiLCJpdGVtU3R5bGUiOnsiYm9yZGVyVHlwZSI6InNvbGlkIiwic2hhZG93T2Zmc2V0WCI6MCwiYm9yZGVyQ29sb3IiOiJpbmhlcml0Iiwic2hhZG93T2Zmc2V0WSI6MCwiY29sb3IiOiJpbmhlcml0Iiwic2hhZG93Qmx1ciI6MCwiYm9yZGVyV2lkdGgiOjAsIm9wYWNpdHkiOjEsInNoYWRvd0NvbG9yIjoidHJhbnNwYXJlbnQifSwidG9wIjoiYXV0byIsImJvcmRlclJhZGl1cyI6MCwibGluZVN0eWxlIjp7InNoYWRvd09mZnNldFgiOjAsInNoYWRvd09mZnNldFkiOjAsImNvbG9yIjoiaW5oZXJpdCIsInNoYWRvd0JsdXIiOjAsIndpZHRoIjoiYXV0byIsInR5cGUiOiJpbmhlcml0Iiwib3BhY2l0eSI6MSwic2hhZG93Q29sb3IiOiJ0cmFuc3BhcmVudCJ9LCJsZWZ0IjoibGVmdCIsImJvcmRlcldpZHRoIjowLCJ3aWR0aCI6ImF1dG8iLCJpdGVtV2lkdGgiOjI1LCJ0ZXh0U3R5bGUiOnsidGV4dEJvcmRlcldpZHRoIjowLCJjb2xvciI6IiNmZmYiLCJ0ZXh0U2hhZG93Q29sb3IiOiJ0cmFuc3BhcmVudCIsImVsbGlwc2lzIjoiLi4uIiwib3ZlcmZsb3ciOiJub25lIiwiZm9udFNpemUiOjEyLCJsaW5lSGVpZ2h0IjoyNCwidGV4dFNoYWRvd09mZnNldFgiOjAsInRleHRTaGFkb3dPZmZzZXRZIjowLCJ0ZXh0Qm9yZGVyVHlwZSI6InNvbGlkIiwiZm9udFdlaWdodCI6NTAwLCJ0ZXh0Qm9yZGVyQ29sb3IiOiJ0cmFuc3BhcmVudCIsInRleHRTaGFkb3dCbHVyIjowfSwic2hhZG93Q29sb3IiOiJyZ2JhKDAsMCwwLC4zKSIsImhlaWdodCI6ImF1dG8ifSwic2VyaWVzIjp7Iml0ZW1TdHlsZSI6eyJib3JkZXJUeXBlIjoic29saWQiLCJzaGFkb3dPZmZzZXRYIjowLCJib3JkZXJDb2xvciI6IiMwMDAiLCJzaGFkb3dPZmZzZXRZIjowLCJjb2xvciI6IiIsInNoYWRvd0JsdXIiOjAsImJvcmRlcldpZHRoIjowLCJvcGFjaXR5IjoxLCJzaGFkb3dDb2xvciI6IiMwMDAifSwibGFiZWwiOnsiYm9yZGVyVHlwZSI6InNvbGlkIiwicm90YXRlIjowLCJwYWRkaW5nIjowLCJ0ZXh0Qm9yZGVyV2lkdGgiOjAsImJhY2tncm91bmRDb2xvciI6InRyYW5zcGFyZW50IiwiYm9yZGVyQ29sb3IiOiIjZmZmIiwiY29sb3IiOiIiLCJzaG93Ijp0cnVlLCJ0ZXh0U2hhZG93Q29sb3IiOiJ0cmFuc3BhcmVudCIsImRpc3RhbmNlVG9MYWJlbExpbmUiOjUsImVsbGlwc2lzIjoiLi4uIiwib3ZlcmZsb3ciOiJub25lIiwiYm9yZGVyUmFkaXVzIjowLCJib3JkZXJXaWR0aCI6MCwiZm9udFNpemUiOjEyLCJsaW5lSGVpZ2h0IjoxOCwidGV4dFNoYWRvd09mZnNldFgiOjAsInBvc2l0aW9uIjoib3V0c2lkZSIsInRleHRTaGFkb3dPZmZzZXRZIjowLCJ0ZXh0Qm9yZGVyVHlwZSI6InNvbGlkIiwidGV4dEJvcmRlckNvbG9yIjoiI2ZmZiIsInRleHRTaGFkb3dCbHVyIjowfSwibGFiZWxMaW5lIjp7InNob3ciOnRydWUsImxlbmd0aCI6MTAsImxpbmVTdHlsZSI6eyJzaGFkb3dPZmZzZXRYIjowLCJzaGFkb3dPZmZzZXRZIjowLCJzaGFkb3dCbHVyIjowLCJ3aWR0aCI6MSwidHlwZSI6InNvbGlkIiwib3BhY2l0eSI6MSwic2hhZG93Q29sb3IiOiIjMDAwIn0sImxlbmd0aDIiOjE0LCJzbW9vdGgiOmZhbHNlfX19LA0KCQkJYm9hcmRCYXNlOiB7ImZ1bm5lbE51bSI6OCwibGluZU51bSI6OCwiZ2F1Z2VOdW0iOjgsImJhck51bSI6OCwicGllTnVtIjo4fSwNCgkJCWdhdWdlOiB7InRvb2x0aXAiOnsiYmFja2dyb3VuZENvbG9yIjoiIzEyMyIsInRleHRTdHlsZSI6eyJjb2xvciI6IiNmZmYifX0sImJhY2tncm91bmRDb2xvciI6InRyYW5zcGFyZW50IiwiY29sb3IiOlsiIzU0NzBjNiIsIiM5MWNjNzUiLCIjZmFjODU4IiwiI2VlNjY2NiIsIiM3M2MwZGUiLCIjM2JhMjcyIiwiI2ZjODQ1MiIsIiM5YTYwYjQiLCIjZWE3Y2NjIl0sInRpdGxlIjp7InRvcCI6ImJvdHRvbSIsImxlZnQiOiJsZWZ0In0sInNlcmllcyI6eyJwb2ludGVyIjp7Im9mZnNldENlbnRlciI6WzAsIjEwJSJdLCJpY29uIjoicGF0aDovL00yLjksMC43TDIuOSwwLjdjMS40LDAsMi42LDEuMiwyLjYsMi42djExNWMwLDEuNC0xLjIsMi42LTIuNiwyLjZsMCwwYy0xLjQsMC0yLjYtMS4yLTIuNi0yLjZWMy4zQzAuMywxLjksMS40LDAuNywyLjksMC43eiIsIndpZHRoIjo4LCJsZW5ndGgiOiI4MCUifSwiYXhpc0xpbmUiOnsibGluZVN0eWxlIjp7InNoYWRvd09mZnNldFgiOjAsInNoYWRvd09mZnNldFkiOjAsIm9wYWNpdHkiOjAuNSwic2hhZG93Qmx1ciI6MSwic2hhZG93Q29sb3IiOiIjMDAwIn0sInJvdW5kQ2FwIjp0cnVlfSwiYW5jaG9yIjp7InNob3ciOnRydWUsIml0ZW1TdHlsZSI6eyJjb2xvciI6ImluaGVyaXQifSwic2l6ZSI6MTgsInNob3dBYm92ZSI6dHJ1ZX0sImVtcGhhc2lzIjp7ImRpc2FibGVkIjpmYWxzZX0sInByb2dyZXNzIjp7InNob3ciOnRydWUsInJvdW5kQ2FwIjp0cnVlLCJvdmVybGFwIjp0cnVlfSwic3BsaXROdW1iZXIiOjI1LCJkZXRhaWwiOnsiZm9ybWF0dGVyIjoie3ZhbHVlfSIsImJhY2tncm91bmRDb2xvciI6ImluaGVyaXQiLCJjb2xvciI6IiNmZmYiLCJib3JkZXJSYWRpdXMiOjMsIndpZHRoIjoyMCwiZm9udFNpemUiOjE0LCJoZWlnaHQiOjE2fSwidGl0bGUiOnsiZm9udFNpemUiOjE0fSwiYW5pbWF0aW9uIjp0cnVlfX0sDQoJCX07DQoJfSwNCgltb3VudGVkKCl7DQoJCXRoaXMuaW5pdCgpOw0KCQl0aGlzLmdldGNhaXd1eGlueGlDb3VudCgpOw0KCQl0aGlzLmNhaXd1eGlueGlDaGF0MSgpOw0KCQl0aGlzLmNhaXd1eGlueGlDaGF0MigpOw0KCQl0aGlzLmNhaXd1eGlueGlDaGF0MygpOw0KCX0sDQoJbWV0aG9kczp7DQoJCS8vIOivjeS6kQ0KCQl3b3JkY2xvdWRzKHdvcmRjbG91ZERhdGEsZWNoYXJ0c0lkKSB7DQoJCQlsZXQgd29yZGNsb3VkID0gJHRlbXBsYXRlMi5iYWNrLmJvYXJkLndvcmRjbG91ZA0KCQkJd29yZGNsb3VkID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeSh3b3JkY2xvdWQpLCAoaywgdikgPT4gew0KCQkJICBpZih0eXBlb2YgdiA9PSAnc3RyaW5nJyAmJiB2LmluZGV4T2YoJ2Z1bmN0aW9uJykgPiAtMSl7DQoJCQkJcmV0dXJuIGV2YWwoIihmdW5jdGlvbigpe3JldHVybiAiK3YrIiB9KSgpIikNCgkJCSAgfQ0KCQkJICByZXR1cm4gdjsNCgkJCX0pDQoJCQl3b3JkY2xvdWQub3B0aW9uLnNlcmllc1swXS5kYXRhPXdvcmRjbG91ZERhdGE7DQoJCQkNCgkJCXRoaXMubXlDaGFydDAgPSBlY2hhcnRzLmluaXQoZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoZWNoYXJ0c0lkKSk7DQoJCQlsZXQgbXlDaGFydCA9IHRoaXMubXlDaGFydDANCgkJCWxldCBpbWcgPSB3b3JkY2xvdWQubWFza0ltYWdlDQoJCQ0KCQkJaWYgKGltZykgew0KCQkJCXZhciBtYXNrSW1hZ2UgPSBuZXcgSW1hZ2UoKTsNCgkJCQltYXNrSW1hZ2Uuc3JjID0gaW1nDQoJCQkJbWFza0ltYWdlLm9ubG9hZCA9IGZ1bmN0aW9uKCkgew0KCQkJCQl3b3JkY2xvdWQub3B0aW9uLnNlcmllc1swXS5tYXNrSW1hZ2UgPSBtYXNrSW1hZ2UNCgkJCQkJbXlDaGFydC5jbGVhcigpDQoJCQkJCW15Q2hhcnQuc2V0T3B0aW9uKHdvcmRjbG91ZC5vcHRpb24pDQoJCQkJfQ0KCQkJfSBlbHNlIHsNCgkJCQlkZWxldGUgd29yZGNsb3VkLm9wdGlvbi5zZXJpZXNbMF0ubWFza0ltYWdlDQoJCQkJbXlDaGFydC5jbGVhcigpDQoJCQkJbXlDaGFydC5zZXRPcHRpb24od29yZGNsb3VkLm9wdGlvbikNCgkJCX0NCgkJfSwNCgkJLy8g57uf6K6h5Zu+5Yqo55S7DQoJCW15Q2hhcnRJbnRlcnZhbCh0eXBlLCB4QXhpc0RhdGEsIHNlcmllc0RhdGEsIG15Q2hhcnQpIHsNCgkJCXRoaXMuJG5leHRUaWNrKCgpID0+IHsNCgkJCQlzZXRJbnRlcnZhbCgoKSA9PiB7DQoJCQkJCWxldCB4QXhpcyA9IHhBeGlzRGF0YS5zaGlmdCgpDQoJCQkJCXhBeGlzRGF0YS5wdXNoKHhBeGlzKQ0KCQkJCQlsZXQgc2VyaWVzID0gc2VyaWVzRGF0YS5zaGlmdCgpDQoJCQkJCXNlcmllc0RhdGEucHVzaChzZXJpZXMpDQoJCQkJDQoJCQkJCWlmICh0eXBlID09IDEpIHsNCgkJCQkJCW15Q2hhcnQuc2V0T3B0aW9uKHsNCgkJCQkJCQl4QXhpczogW3sNCgkJCQkJCQkJZGF0YTogeEF4aXNEYXRhDQoJCQkJCQkJfV0sDQoJCQkJCQkJc2VyaWVzOiBbew0KCQkJCQkJCQlkYXRhOiBzZXJpZXNEYXRhDQoJCQkJCQkJfV0NCgkJCQkJCX0pOw0KCQkJCQl9DQoJCQkJCWlmICh0eXBlID09IDIpIHsNCgkJCQkJCW15Q2hhcnQuc2V0T3B0aW9uKHsNCgkJCQkJCQl5QXhpczogW3sNCgkJCQkJCQkJZGF0YTogeEF4aXNEYXRhDQoJCQkJCQkJfV0sDQoJCQkJCQkJc2VyaWVzOiBbew0KCQkJCQkJCQlkYXRhOiBzZXJpZXNEYXRhDQoJCQkJCQkJfV0NCgkJCQkJCX0pOw0KCQkJCQl9DQoJCQkJfSwgJHRlbXBsYXRlMi5iYWNrLmJvYXJkLmJhci5iYXNlLmludGVydmFsKTsNCgkJCX0pDQoJCX0sDQoJCWluaXQoKXsNCgkJCWlmKHRoaXMuJHN0b3JhZ2UuZ2V0KCdUb2tlbicpKXsNCgkJCXRoaXMuJGh0dHAoew0KCQkJCXVybDogYCR7dGhpcy4kc3RvcmFnZS5nZXQoJ3Nlc3Npb25UYWJsZScpfS9zZXNzaW9uYCwNCgkJCQltZXRob2Q6ICJnZXQiDQoJCQl9KS50aGVuKCh7IGRhdGEgfSkgPT4gew0KCQkJCWlmIChkYXRhICYmIGRhdGEuY29kZSAhPSAwKSB7DQoJCQkJcm91dGVyLnB1c2goeyBuYW1lOiAnbG9naW4nIH0pDQoJCQkJfQ0KCQkJfSk7DQoJCQl9ZWxzZXsNCgkJCQlyb3V0ZXIucHVzaCh7IG5hbWU6ICdsb2dpbicgfSkNCgkJCX0NCgkJfSwNCgkJZ2V0Y2Fpd3V4aW54aUNvdW50KCkgew0KCQkJdGhpcy4kaHR0cCh7DQoJCQkJdXJsOiBgY2Fpd3V4aW54aS9jb3VudGAsDQoJCQkJbWV0aG9kOiAiZ2V0Ig0KCQkJfSkudGhlbigoew0KCQkJCWRhdGENCgkJCX0pID0+IHsNCgkJCQlpZiAoZGF0YSAmJiBkYXRhLmNvZGUgPT0gMCkgew0KCQkJCQl0aGlzLmNhaXd1eGlueGlDb3VudCA9IGRhdGEuZGF0YQ0KCQkJCX0NCgkJCX0pDQoJCX0sDQoJCWNhaXd1eGlueGlDaGF0MSgpIHsNCgkJCXRoaXMuJG5leHRUaWNrKCgpPT57DQoNCiAgICAgICAgdmFyIGNhaXd1eGlueGlDaGFydDEgPSBlY2hhcnRzLmluaXQoZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoImNhaXd1eGlueGlDaGFydDEiKSwnbWFjYXJvbnMnKTsNCiAgICAgICAgdGhpcy4kaHR0cCh7DQogICAgICAgICAgICB1cmw6IGBjYWl3dXhpbnhpL3ZhbHVlL2RlbmdqaXJpcWkvc2hvdXJ1amluZS/lubRgLA0KICAgICAgICAgICAgbWV0aG9kOiAiZ2V0IiwNCiAgICAgICAgfSkudGhlbigoeyBkYXRhIH0pID0+IHsNCiAgICAgICAgICAgIGlmIChkYXRhICYmIGRhdGEuY29kZSA9PT0gMCkgew0KICAgICAgICAgICAgICAgIGxldCByZXMgPSBkYXRhLmRhdGE7DQogICAgICAgICAgICAgICAgbGV0IHhBeGlzID0gW107DQogICAgICAgICAgICAgICAgbGV0IHlBeGlzID0gW107DQogICAgICAgICAgICAgICAgbGV0IHBBcnJheSA9IFtdDQogICAgICAgICAgICAgICAgZm9yKGxldCBpPTA7aTxyZXMubGVuZ3RoO2krKyl7DQoJCQkJCWlmKHRoaXMuYm9hcmRCYXNlJiZpPT10aGlzLmJvYXJkQmFzZS5mdW5uZWxOdW0pew0KCQkJCQkJYnJlYWs7DQoJCQkJCX0NCiAgICAgICAgICAgICAgICAgICAgeEF4aXMucHVzaChyZXNbaV0uZGVuZ2ppcmlxaSk7DQogICAgICAgICAgICAgICAgICAgIHlBeGlzLnB1c2gocGFyc2VGbG9hdCgocmVzW2ldLnRvdGFsKSkpOw0KICAgICAgICAgICAgICAgICAgICBwQXJyYXkucHVzaCh7DQogICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogcGFyc2VGbG9hdCgocmVzW2ldLnRvdGFsKSksDQogICAgICAgICAgICAgICAgICAgICAgICBuYW1lOiByZXNbaV0uZGVuZ2ppcmlxaQ0KICAgICAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB2YXIgb3B0aW9uID0ge307DQoJCQkJbGV0IHRpdGxlT2JqID0gdGhpcy5mdW5uZWwudGl0bGUNCgkJCQl0aXRsZU9iai50ZXh0ID0gJ+aUtuWFpee7n+iuoScNCgkJCQkNCgkJCQlsZXQgbGVnZW5kT2JqID0gew0KCQkJCQlkYXRhOiB4QXhpcywNCgkJCQl9DQoJCQkJbGVnZW5kT2JqID0gT2JqZWN0LmFzc2lnbihsZWdlbmRPYmogLCB0aGlzLmZ1bm5lbC5sZWdlbmQpDQoJCQkJbGV0IHRvb2x0aXBPYmogPSB7dHJpZ2dlcjogJ2l0ZW0nLGZvcm1hdHRlcjogJ3tifSA6IHtjfSd9DQoJCQkJdG9vbHRpcE9iaiA9IE9iamVjdC5hc3NpZ24odG9vbHRpcE9iaiAsIHRoaXMuZnVubmVsLnRvb2x0aXA/dGhpcy5mdW5uZWwudG9vbHRpcDp7fSkNCgkJCQlsZXQgc2VyaWVzT2JqID0gew0KCQkJCQluYW1lOiAn5pS25YWl57uf6K6hJywNCgkJCQkJZGF0YTogcEFycmF5LA0KCQkJCQl0eXBlOiAnZnVubmVsJywNCgkJCQkJbGVmdDogJzEwJScsDQoJCQkJCXRvcDogNjAsDQoJCQkJCWJvdHRvbTogNjAsDQoJCQkJCXdpZHRoOiAnODAlJywNCgkJCQkJbWluU2l6ZTogJzAlJywNCgkJCQkJbWF4U2l6ZTogJzEwMCUnLA0KCQkJCX0NCgkJCQlzZXJpZXNPYmogPSBPYmplY3QuYXNzaWduKHNlcmllc09iaiAsIHRoaXMuZnVubmVsLnNlcmllcykNCgkJCQlvcHRpb24gPSB7DQoJCQkJCWJhY2tncm91bmRDb2xvcjogdGhpcy5mdW5uZWwuYmFja2dyb3VuZENvbG9yLA0KCQkJCQljb2xvcjogdGhpcy5mdW5uZWwuY29sb3IsDQoJCQkJICAgIHRpdGxlOiB0aXRsZU9iaiwNCgkJCQkgICAgbGVnZW5kOiBsZWdlbmRPYmosDQoJCQkJICAgIHRvb2x0aXA6IHRvb2x0aXBPYmosDQoJCQkJICAgIHNlcmllczogc2VyaWVzT2JqLA0KCQkJCX0NCiAgICAgICAgICAgICAgICAvLyDkvb/nlKjliJrmjIflrprnmoTphY3nva7pobnlkozmlbDmja7mmL7npLrlm77ooajjgIINCiAgICAgICAgICAgICAgICBjYWl3dXhpbnhpQ2hhcnQxLnNldE9wdGlvbihvcHRpb24pOw0KCQkJCQ0KICAgICAgICAgICAgICAgICAgLy/moLnmja7nqpflj6PnmoTlpKflsI/lj5jliqjlm77ooagNCiAgICAgICAgICAgICAgICB3aW5kb3cub25yZXNpemUgPSBmdW5jdGlvbigpIHsNCiAgICAgICAgICAgICAgICAgICAgY2Fpd3V4aW54aUNoYXJ0MS5yZXNpemUoKTsNCiAgICAgICAgICAgICAgICB9Ow0KICAgICAgICAgICAgfQ0KICAgICAgICB9KTsNCiAgICAgIH0pDQogICAgfSwNCg0KICAgIGNhaXd1eGlueGlDaGF0MigpIHsNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpPT57DQoNCiAgICAgICAgdmFyIGNhaXd1eGlueGlDaGFydDIgPSBlY2hhcnRzLmluaXQoZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoImNhaXd1eGlueGlDaGFydDIiKSwnbWFjYXJvbnMnKTsNCiAgICAgICAgdGhpcy4kaHR0cCh7DQogICAgICAgICAgICB1cmw6IGBjYWl3dXhpbnhpL3ZhbHVlL2RlbmdqaXJpcWkvemhpY2h1amluZS/lubRgLA0KICAgICAgICAgICAgbWV0aG9kOiAiZ2V0IiwNCiAgICAgICAgfSkudGhlbigoeyBkYXRhIH0pID0+IHsNCiAgICAgICAgICAgIGlmIChkYXRhICYmIGRhdGEuY29kZSA9PT0gMCkgew0KICAgICAgICAgICAgICAgIGxldCByZXMgPSBkYXRhLmRhdGE7DQogICAgICAgICAgICAgICAgbGV0IHhBeGlzID0gW107DQogICAgICAgICAgICAgICAgbGV0IHlBeGlzID0gW107DQogICAgICAgICAgICAgICAgbGV0IHBBcnJheSA9IFtdDQogICAgICAgICAgICAgICAgZm9yKGxldCBpPTA7aTxyZXMubGVuZ3RoO2krKyl7DQoJCQkJCWlmKHRoaXMuYm9hcmRCYXNlJiZpPT10aGlzLmJvYXJkQmFzZS5iYXJOdW0pew0KCQkJCQkJYnJlYWs7DQoJCQkJCX0NCiAgICAgICAgICAgICAgICAgICAgeEF4aXMucHVzaChyZXNbaV0uZGVuZ2ppcmlxaSk7DQogICAgICAgICAgICAgICAgICAgIHlBeGlzLnB1c2gocGFyc2VGbG9hdCgocmVzW2ldLnRvdGFsKSkpOw0KICAgICAgICAgICAgICAgICAgICBwQXJyYXkucHVzaCh7DQogICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogcGFyc2VGbG9hdCgocmVzW2ldLnRvdGFsKSksDQogICAgICAgICAgICAgICAgICAgICAgICBuYW1lOiByZXNbaV0uZGVuZ2ppcmlxaQ0KICAgICAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB2YXIgb3B0aW9uID0ge307DQogICAgICAgICAgICAgICAgbGV0IHRpdGxlT2JqID0gdGhpcy5iYXIudGl0bGUNCgkJCQl0aXRsZU9iai50ZXh0ID0gJ+aUr+WHuue7n+iuoScNCgkJCQkNCgkJCQljb25zdCBsZWdlbmRPYmogPSB0aGlzLmJhci5sZWdlbmQNCgkJCQlsZXQgdG9vbHRpcE9iaiA9IHt0cmlnZ2VyOiAnaXRlbScsZm9ybWF0dGVyOiAne2J9IDoge2N9J30NCgkJCQl0b29sdGlwT2JqID0gT2JqZWN0LmFzc2lnbih0b29sdGlwT2JqICwgdGhpcy5iYXIudG9vbHRpcD90aGlzLmJhci50b29sdGlwOnt9KQ0KCQkJCQ0KCQkJCWxldCB4QXhpc09iaiA9IHRoaXMuYmFyLnhBeGlzDQoJCQkJeEF4aXNPYmoudHlwZSA9ICdjYXRlZ29yeScNCgkJCQl4QXhpc09iai5kYXRhID0geEF4aXMNCiAgICAgICAgICAgICAgICB4QXhpc09iai5heGlzTGFiZWwucm90YXRlPTQwDQoJCQkJDQoJCQkJbGV0IHlBeGlzT2JqID0gdGhpcy5iYXIueUF4aXMNCgkJCQl5QXhpc09iai50eXBlID0gJ3ZhbHVlJw0KCQkJCWxldCBzZXJpZXNPYmogPSB7DQoJCQkJCQlkYXRhOiB5QXhpcywNCgkJCQkJCXR5cGU6ICdiYXInDQoJCQkJCX0NCgkJCQlzZXJpZXNPYmogPSBPYmplY3QuYXNzaWduKHNlcmllc09iaiAsIHRoaXMuYmFyLnNlcmllcykNCg0KCQkJCW9wdGlvbiA9IHsNCgkJCQkJYmFja2dyb3VuZENvbG9yOiB0aGlzLmJhci5iYWNrZ3JvdW5kQ29sb3IsDQoJCQkJCWNvbG9yOiB0aGlzLmJhci5jb2xvciwNCgkJCQkJdGl0bGU6IHRpdGxlT2JqLA0KCQkJCQlsZWdlbmQ6IGxlZ2VuZE9iaiwNCgkJCQkJdG9vbHRpcDogdG9vbHRpcE9iaiwNCgkJCQkJeEF4aXM6IHhBeGlzT2JqLA0KCQkJCQl5QXhpczogeUF4aXNPYmosDQoJCQkJCXNlcmllczogW3Nlcmllc09ial0NCgkJCQl9Ow0KICAgICAgICAgICAgICAgIC8vIOS9v+eUqOWImuaMh+WumueahOmFjee9rumhueWSjOaVsOaNruaYvuekuuWbvuihqOOAgg0KICAgICAgICAgICAgICAgIGNhaXd1eGlueGlDaGFydDIuc2V0T3B0aW9uKG9wdGlvbik7DQoJCQkJDQogICAgICAgICAgICAgICAgICAvL+agueaNrueql+WPo+eahOWkp+Wwj+WPmOWKqOWbvuihqA0KICAgICAgICAgICAgICAgIHdpbmRvdy5vbnJlc2l6ZSA9IGZ1bmN0aW9uKCkgew0KICAgICAgICAgICAgICAgICAgICBjYWl3dXhpbnhpQ2hhcnQyLnJlc2l6ZSgpOw0KICAgICAgICAgICAgICAgIH07DQogICAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgfSkNCiAgICB9LA0KDQogICAgY2Fpd3V4aW54aUNoYXQzKCkgew0KICAgICAgdGhpcy4kbmV4dFRpY2soKCk9PnsNCg0KICAgICAgICB2YXIgY2Fpd3V4aW54aUNoYXJ0MyA9IGVjaGFydHMuaW5pdChkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgiY2Fpd3V4aW54aUNoYXJ0MyIpLCdtYWNhcm9ucycpOw0KICAgICAgICB0aGlzLiRodHRwKHsNCiAgICAgICAgICAgIHVybDogYGNhaXd1eGlueGkvdmFsdWUvZGVuZ2ppcmlxaS9saXJ1bi/lubRgLA0KICAgICAgICAgICAgbWV0aG9kOiAiZ2V0IiwNCiAgICAgICAgfSkudGhlbigoeyBkYXRhIH0pID0+IHsNCiAgICAgICAgICAgIGlmIChkYXRhICYmIGRhdGEuY29kZSA9PT0gMCkgew0KICAgICAgICAgICAgICAgIGxldCByZXMgPSBkYXRhLmRhdGE7DQogICAgICAgICAgICAgICAgbGV0IHhBeGlzID0gW107DQogICAgICAgICAgICAgICAgbGV0IHlBeGlzID0gW107DQogICAgICAgICAgICAgICAgbGV0IHBBcnJheSA9IFtdDQogICAgICAgICAgICAgICAgZm9yKGxldCBpPTA7aTxyZXMubGVuZ3RoO2krKyl7DQoJCQkJCWlmKHRoaXMuYm9hcmRCYXNlJiZpPT10aGlzLmJvYXJkQmFzZS5saW5lTnVtKXsNCgkJCQkJCWJyZWFrOw0KCQkJCQl9DQogICAgICAgICAgICAgICAgICAgIHhBeGlzLnB1c2gocmVzW2ldLmRlbmdqaXJpcWkpOw0KICAgICAgICAgICAgICAgICAgICB5QXhpcy5wdXNoKHBhcnNlRmxvYXQoKHJlc1tpXS50b3RhbCkpKTsNCiAgICAgICAgICAgICAgICAgICAgcEFycmF5LnB1c2goew0KICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IHBhcnNlRmxvYXQoKHJlc1tpXS50b3RhbCkpLA0KICAgICAgICAgICAgICAgICAgICAgICAgbmFtZTogcmVzW2ldLmRlbmdqaXJpcWkNCiAgICAgICAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgdmFyIG9wdGlvbiA9IHt9Ow0KICAgICAgICAgICAgICAgIGxldCB0aXRsZU9iaiA9IHRoaXMubGluZS50aXRsZQ0KCQkJCXRpdGxlT2JqLnRleHQgPSAn5Yip5ram57uf6K6hJw0KCQkJCQ0KCQkJCWNvbnN0IGxlZ2VuZE9iaiA9IHRoaXMubGluZS5sZWdlbmQNCgkJCQlsZXQgdG9vbHRpcE9iaiA9IHsgdHJpZ2dlcjogJ2l0ZW0nLGZvcm1hdHRlcjogJ3tifSA6IHtjfSd9DQoJCQkJdG9vbHRpcE9iaiA9IE9iamVjdC5hc3NpZ24odG9vbHRpcE9iaiAsIHRoaXMubGluZS50b29sdGlwP3RoaXMubGluZS50b29sdGlwOnt9KQ0KCQkJCQ0KCQkJCWxldCB4QXhpc09iaiA9IHRoaXMubGluZS54QXhpcw0KCQkJCXhBeGlzT2JqLnR5cGUgPSAnY2F0ZWdvcnknDQoJCQkJeEF4aXNPYmouYm91bmRhcnlHYXAgPSBmYWxzZQ0KCQkJCXhBeGlzT2JqLmRhdGEgPSB4QXhpcw0KICAgICAgICAgICAgICAgIHhBeGlzT2JqLmF4aXNMYWJlbC5yb3RhdGU9NzANCgkJCQkNCgkJCQlsZXQgeUF4aXNPYmogPSB0aGlzLmxpbmUueUF4aXMNCgkJCQl5QXhpc09iai50eXBlID0gJ3ZhbHVlJw0KCQkJCQ0KCQkJCWxldCBzZXJpZXNPYmogPSB7DQoJCQkJCWRhdGE6IHlBeGlzLA0KCQkJCQl0eXBlOiAnbGluZScsDQoJCQkJfQ0KCQkJCXNlcmllc09iaiA9IE9iamVjdC5hc3NpZ24oc2VyaWVzT2JqICwgdGhpcy5saW5lLnNlcmllcykNCgkJCQkNCgkJCQlvcHRpb24gPSB7DQoJCQkJCWJhY2tncm91bmRDb2xvcjogdGhpcy5saW5lLmJhY2tncm91bmRDb2xvciwNCgkJCQkJY29sb3I6IHRoaXMubGluZS5jb2xvciwNCgkJCQkJdGl0bGU6IHRpdGxlT2JqLA0KCQkJCQlsZWdlbmQ6IGxlZ2VuZE9iaiwNCgkJCQkJdG9vbHRpcDogdG9vbHRpcE9iaiwNCgkJCQkJeEF4aXM6IHhBeGlzT2JqLA0KCQkJCQl5QXhpczogeUF4aXNPYmosDQoJCQkJCXNlcmllczogW3Nlcmllc09ial0NCgkJCQl9Ow0KICAgICAgICAgICAgICAgIC8vIOS9v+eUqOWImuaMh+WumueahOmFjee9rumhueWSjOaVsOaNruaYvuekuuWbvuihqOOAgg0KICAgICAgICAgICAgICAgIGNhaXd1eGlueGlDaGFydDMuc2V0T3B0aW9uKG9wdGlvbik7DQogICAgICAgICAgICAgICAgICAvL+agueaNrueql+WPo+eahOWkp+Wwj+WPmOWKqOWbvuihqA0KICAgICAgICAgICAgICAgIHdpbmRvdy5vbnJlc2l6ZSA9IGZ1bmN0aW9uKCkgew0KICAgICAgICAgICAgICAgICAgICBjYWl3dXhpbnhpQ2hhcnQzLnJlc2l6ZSgpOw0KICAgICAgICAgICAgICAgIH07DQogICAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgfSkNCiAgICB9LA0KICB9DQp9Ow0K"}, {"version": 3, "sources": ["home.vue"], "names": [], "mappings": ";AA+BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "home.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n<div class=\"content\" :style='{\"padding\":\"30px\"}'>\r\n\t<!-- notice -->\r\n\t<!-- title -->\r\n\t<div class=\"text\" :style='{\"margin\":\"20px auto\",\"fontSize\":\"24px\",\"color\":\" #374254\",\"textAlign\":\"center\",\"fontWeight\":\"bold\"}'>欢迎使用 {{this.$project.projectName}}</div>\r\n\t<!-- statis -->\r\n\t<div :style='{\"width\":\"100%\",\"margin\":\"0 0 20px 0\",\"alignItems\":\"center\",\"flexWrap\":\"wrap\",\"justifyContent\":\"center\",\"display\":\"flex\"}'>\r\n\t\t<div :style='{\"border\":\" 1px solid rgba(167, 180, 201,.3)    \",\"margin\":\"0 10px\",\"borderRadius\":\"4px\",\"background\":\"#fff\",\"display\":\"flex\"}' v-if=\"isAuth('caiwuxinxi','首页总数')\">\r\n\t\t\t<div :style='{\"alignItems\":\"center\",\"background\":\"#00e682\",\"display\":\"flex\",\"width\":\"80px\",\"justifyContent\":\"center\",\"height\":\"80px\",\"order\":\"2\"}'>\r\n\t\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"color\":\"#fff\",\"fontSize\":\"24px\"}'></span>\r\n\t\t\t</div>\r\n\t\t\t<div :style='{\"width\":\"120px\",\"alignItems\":\"center\",\"flexDirection\":\"column\",\"justifyContent\":\"center\",\"display\":\"flex\",\"order\":\"1\"}'>\r\n\t\t\t\t<div :style='{\"margin\":\"5px 0\",\"lineHeight\":\"24px\",\"fontSize\":\"20px\",\"color\":\"1\",\"fontWeight\":\"bold\",\"height\":\"24px\"}'>{{caiwuxinxiCount}}</div>\r\n\t\t\t\t<div :style='{\"margin\":\"5px 0\",\"lineHeight\":\"24px\",\"fontSize\":\"14px\",\"color\":\" #a3b1c9\",\"height\":\"24px\"}'>财物信息总数</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t</div>\r\n\t<!-- statis -->\r\n\t\r\n\r\n\t\r\n\t<!-- echarts -->\r\n\t<!-- 3 -->\r\n\t<div class=\"type3\" :style='{\"alignContent\":\"flex-start\",\"padding\":\"10px 20px\",\"flexWrap\":\"wrap\",\"background\":\"rebeccapurple\",\"display\":\"flex\",\"width\":\"100%\",\"position\":\"relative\",\"justifyContent\":\"space-between\",\"height\":\"auto\"}'>\r\n\t\t<div id=\"caiwuxinxiChart1\" class=\"echarts1\" v-if=\"isAuth('caiwuxinxi','首页统计')\"></div>\r\n\t\t<div id=\"caiwuxinxiChart2\" class=\"echarts2\" v-if=\"isAuth('caiwuxinxi','首页统计')\"></div>\r\n\t\t<div id=\"caiwuxinxiChart3\" class=\"echarts3\" v-if=\"isAuth('caiwuxinxi','首页统计')\"></div>\r\n\t</div>\r\n</div>\r\n</template>\r\n<script>\r\n//3\r\nimport router from '@/router/router-static'\r\nimport * as echarts from 'echarts'\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n            caiwuxinxiCount: 0,\r\n\t\t\tline: {\"backgroundColor\":\"transparent\",\"yAxis\":{\"axisLabel\":{\"borderType\":\"solid\",\"rotate\":0,\"padding\":0,\"shadowOffsetX\":0,\"margin\":15,\"backgroundColor\":\"transparent\",\"borderColor\":\"#000\",\"shadowOffsetY\":0,\"color\":\"#fff\",\"shadowBlur\":0,\"show\":true,\"inside\":false,\"ellipsis\":\"...\",\"overflow\":\"none\",\"borderRadius\":0,\"borderWidth\":0,\"width\":\"\",\"fontSize\":12,\"lineHeight\":24,\"shadowColor\":\"transparent\",\"fontWeight\":\"normal\",\"height\":\"\"},\"axisTick\":{\"show\":true,\"length\":5,\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"cap\":\"butt\",\"color\":\"#fff\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"rgba(0,0,0,.5)\"},\"inside\":false},\"splitLine\":{\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"cap\":\"butt\",\"color\":\"#ccc\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"rgba(0,0,0,.5)\"},\"show\":true},\"axisLine\":{\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"cap\":\"butt\",\"color\":\"#fff\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"rgba(0,0,0,.5)\"},\"show\":true},\"splitArea\":{\"show\":false,\"areaStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"color\":\"rgba(250,250,250,0.3)\",\"opacity\":1,\"shadowBlur\":10,\"shadowColor\":\"rgba(0,0,0,.5)\"}}},\"xAxis\":{\"axisLabel\":{\"borderType\":\"solid\",\"rotate\":0,\"padding\":0,\"shadowOffsetX\":0,\"margin\":4,\"backgroundColor\":\"transparent\",\"borderColor\":\"#000\",\"shadowOffsetY\":0,\"color\":\"#fff\",\"shadowBlur\":0,\"show\":true,\"inside\":false,\"ellipsis\":\"...\",\"overflow\":\"none\",\"borderRadius\":0,\"borderWidth\":0,\"width\":\"\",\"fontSize\":12,\"lineHeight\":24,\"shadowColor\":\"transparent\",\"fontWeight\":\"normal\",\"height\":\"\"},\"axisTick\":{\"show\":true,\"length\":5,\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"cap\":\"butt\",\"color\":\"#fff\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"rgba(0,0,0,.5)\"},\"inside\":false},\"splitLine\":{\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"cap\":\"butt\",\"color\":\"#fff\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"rgba(0,0,0,.5)\"},\"show\":false},\"axisLine\":{\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"cap\":\"butt\",\"color\":\"#fff\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"rgba(0,0,0,.5)\"},\"show\":true},\"splitArea\":{\"show\":false,\"areaStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"color\":\"rgba(255,255,255,.3)\",\"opacity\":1,\"shadowBlur\":10,\"shadowColor\":\"rgba(0,0,0,.5)\"}}},\"color\":[\"#5470c6\",\"#91cc75\",\"#fac858\",\"#ee6666\",\"#73c0de\",\"#3ba272\",\"#fc8452\",\"#9a60b4\",\"#ea7ccc\"],\"legend\":{\"padding\":5,\"itemGap\":10,\"shadowOffsetX\":0,\"backgroundColor\":\"transparent\",\"borderColor\":\"#ccc\",\"shadowOffsetY\":0,\"orient\":\"horizontal\",\"shadowBlur\":0,\"bottom\":\"auto\",\"itemHeight\":14,\"show\":true,\"icon\":\"roundRect\",\"itemStyle\":{\"borderType\":\"solid\",\"shadowOffsetX\":0,\"borderColor\":\"inherit\",\"shadowOffsetY\":0,\"color\":\"rgb(255,255,255)\",\"shadowBlur\":0,\"borderWidth\":0,\"opacity\":1,\"shadowColor\":\"transparent\"},\"right\":\"auto\",\"top\":\"auto\",\"borderRadius\":0,\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"color\":\"inherit\",\"shadowBlur\":0,\"width\":\"auto\",\"type\":\"inherit\",\"opacity\":1,\"shadowColor\":\"transparent\"},\"left\":\"right\",\"borderWidth\":0,\"width\":\"auto\",\"itemWidth\":25,\"textStyle\":{\"textBorderWidth\":0,\"color\":\"#fff\",\"textShadowColor\":\"transparent\",\"ellipsis\":\"...\",\"overflow\":\"none\",\"fontSize\":12,\"lineHeight\":24,\"textShadowOffsetX\":0,\"textShadowOffsetY\":0,\"textBorderType\":\"solid\",\"fontWeight\":500,\"textBorderColor\":\"transparent\",\"textShadowBlur\":0},\"shadowColor\":\"rgba(0,0,0,.3)\",\"height\":\"auto\"},\"series\":{\"symbol\":\"emptyCircle\",\"itemStyle\":{\"borderType\":\"solid\",\"shadowOffsetX\":0,\"borderColor\":\"#fff\",\"shadowOffsetY\":0,\"color\":\"\",\"shadowBlur\":0,\"borderWidth\":0,\"opacity\":1,\"shadowColor\":\"#000\"},\"showSymbol\":true,\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"color\":\"\",\"shadowBlur\":0,\"width\":2,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"#000\"},\"symbolSize\":4},\"tooltip\":{\"backgroundColor\":\"#123\",\"textStyle\":{\"color\":\"#fff\"}},\"title\":{\"borderType\":\"solid\",\"padding\":2,\"shadowOffsetX\":0,\"backgroundColor\":\"transparent\",\"borderColor\":\"#ccc\",\"shadowOffsetY\":0,\"shadowBlur\":0,\"bottom\":\"auto\",\"show\":true,\"right\":\"auto\",\"top\":\"auto\",\"borderRadius\":0,\"left\":\"left\",\"borderWidth\":0,\"textStyle\":{\"textBorderWidth\":0,\"color\":\"#fff\",\"textShadowColor\":\"transparent\",\"fontSize\":14,\"lineHeight\":24,\"textShadowOffsetX\":0,\"textShadowOffsetY\":0,\"textBorderType\":\"solid\",\"fontWeight\":500,\"textBorderColor\":\"#ccc\",\"textShadowBlur\":0},\"shadowColor\":\"transparent\"}},\r\n\t\t\tbar: {\"backgroundColor\":\"transparent\",\"yAxis\":{\"axisLabel\":{\"borderType\":\"solid\",\"rotate\":0,\"padding\":0,\"shadowOffsetX\":0,\"margin\":12,\"backgroundColor\":\"transparent\",\"borderColor\":\"#000\",\"shadowOffsetY\":0,\"color\":\"#fff\",\"shadowBlur\":0,\"show\":true,\"inside\":false,\"ellipsis\":\"...\",\"overflow\":\"none\",\"borderRadius\":0,\"borderWidth\":0,\"width\":\"\",\"fontSize\":12,\"lineHeight\":24,\"shadowColor\":\"transparent\",\"fontWeight\":\"normal\",\"height\":\"\"},\"axisTick\":{\"show\":true,\"length\":5,\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"cap\":\"butt\",\"color\":\"#fff\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"rgba(0,0,0,.5)\"},\"inside\":false},\"splitLine\":{\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"cap\":\"butt\",\"color\":\"#ccc\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"rgba(0,0,0,.5)\"},\"show\":true},\"axisLine\":{\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"cap\":\"butt\",\"color\":\"#fff\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"rgba(0,0,0,.5)\"},\"show\":true},\"splitArea\":{\"show\":false,\"areaStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"color\":\"rgba(250,250,250,0.3)\",\"opacity\":1,\"shadowBlur\":10,\"shadowColor\":\"rgba(0,0,0,.5)\"}}},\"xAxis\":{\"axisLabel\":{\"borderType\":\"solid\",\"rotate\":0,\"padding\":0,\"shadowOffsetX\":0,\"margin\":4,\"backgroundColor\":\"transparent\",\"borderColor\":\"#000\",\"shadowOffsetY\":0,\"color\":\"#fff\",\"shadowBlur\":0,\"show\":true,\"inside\":false,\"ellipsis\":\"...\",\"overflow\":\"none\",\"borderRadius\":0,\"borderWidth\":0,\"width\":\"\",\"fontSize\":12,\"lineHeight\":24,\"shadowColor\":\"transparent\",\"fontWeight\":\"normal\",\"height\":\"\"},\"axisTick\":{\"show\":true,\"length\":5,\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"cap\":\"butt\",\"color\":\"#fff\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"rgba(0,0,0,.5)\"},\"inside\":false},\"splitLine\":{\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"cap\":\"butt\",\"color\":\"#fff\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"rgba(0,0,0,.5)\"},\"show\":false},\"axisLine\":{\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"cap\":\"butt\",\"color\":\"#fff\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"rgba(0,0,0,.5)\"},\"show\":true},\"splitArea\":{\"show\":false,\"areaStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"color\":\"rgba(255,255,255,.3)\",\"opacity\":1,\"shadowBlur\":10,\"shadowColor\":\"rgba(0,0,0,.5)\"}}},\"color\":[\"#00ff00\",\"#91cc75\",\"#fac858\",\"#ee6666\",\"#73c0de\",\"#3ba272\",\"#fc8452\",\"#9a60b4\",\"#ea7ccc\"],\"legend\":{\"padding\":5,\"itemGap\":10,\"shadowOffsetX\":0,\"backgroundColor\":\"transparent\",\"borderColor\":\"#ccc\",\"shadowOffsetY\":0,\"orient\":\"horizontal\",\"shadowBlur\":0,\"bottom\":\"auto\",\"itemHeight\":14,\"show\":true,\"icon\":\"roundRect\",\"itemStyle\":{\"borderType\":\"solid\",\"shadowOffsetX\":0,\"borderColor\":\"inherit\",\"shadowOffsetY\":0,\"color\":\"rgb(255,255,255)\",\"shadowBlur\":0,\"borderWidth\":0,\"opacity\":1,\"shadowColor\":\"transparent\"},\"right\":\"auto\",\"top\":\"auto\",\"borderRadius\":0,\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"color\":\"inherit\",\"shadowBlur\":0,\"width\":\"auto\",\"type\":\"inherit\",\"opacity\":1,\"shadowColor\":\"transparent\"},\"left\":\"right\",\"borderWidth\":0,\"width\":\"auto\",\"itemWidth\":25,\"textStyle\":{\"textBorderWidth\":0,\"color\":\"#fff\",\"textShadowColor\":\"transparent\",\"ellipsis\":\"...\",\"overflow\":\"none\",\"fontSize\":12,\"lineHeight\":24,\"textShadowOffsetX\":0,\"textShadowOffsetY\":0,\"textBorderType\":\"solid\",\"fontWeight\":500,\"textBorderColor\":\"transparent\",\"textShadowBlur\":0},\"shadowColor\":\"rgba(0,0,0,.3)\",\"height\":\"auto\"},\"series\":{\"barWidth\":\"auto\",\"itemStyle\":{\"borderType\":\"solid\",\"shadowOffsetX\":0,\"borderColor\":\"#fff\",\"shadowOffsetY\":0,\"color\":\"\",\"shadowBlur\":0,\"borderWidth\":0,\"opacity\":1,\"shadowColor\":\"#000\"},\"colorBy\":\"data\",\"barCategoryGap\":\"20%\"},\"tooltip\":{\"backgroundColor\":\"#123\",\"textStyle\":{\"color\":\"#fff\"}},\"title\":{\"borderType\":\"solid\",\"padding\":2,\"shadowOffsetX\":0,\"backgroundColor\":\"transparent\",\"borderColor\":\"#ccc\",\"shadowOffsetY\":0,\"shadowBlur\":0,\"bottom\":\"auto\",\"show\":true,\"right\":\"auto\",\"top\":\"auto\",\"borderRadius\":0,\"left\":\"left\",\"borderWidth\":0,\"textStyle\":{\"textBorderWidth\":0,\"color\":\"#fff\",\"textShadowColor\":\"transparent\",\"fontSize\":14,\"lineHeight\":24,\"textShadowOffsetX\":0,\"textShadowOffsetY\":0,\"textBorderType\":\"solid\",\"fontWeight\":500,\"textBorderColor\":\"#ccc\",\"textShadowBlur\":0},\"shadowColor\":\"transparent\"},\"base\":{\"animate\":false,\"interval\":2000}},\r\n\t\t\tpie: {\"tooltip\":{\"backgroundColor\":\"#123\",\"textStyle\":{\"color\":\"#fff\"}},\"backgroundColor\":\"transparent\",\"color\":[\"#5470c6\",\"#91cc75\",\"#fac858\",\"#ee6666\",\"#73c0de\",\"#3ba272\",\"#fc8452\",\"#9a60b4\",\"#ea7ccc\"],\"title\":{\"borderType\":\"solid\",\"padding\":2,\"shadowOffsetX\":0,\"backgroundColor\":\"transparent\",\"borderColor\":\"#ccc\",\"shadowOffsetY\":0,\"shadowBlur\":0,\"bottom\":\"auto\",\"show\":true,\"right\":\"auto\",\"top\":\"auto\",\"borderRadius\":0,\"left\":\"left\",\"borderWidth\":0,\"textStyle\":{\"textBorderWidth\":0,\"color\":\"#fff\",\"textShadowColor\":\"transparent\",\"fontSize\":14,\"lineHeight\":24,\"textShadowOffsetX\":0,\"textShadowOffsetY\":0,\"textBorderType\":\"solid\",\"fontWeight\":500,\"textBorderColor\":\"#ccc\",\"textShadowBlur\":0},\"shadowColor\":\"transparent\"},\"legend\":{\"padding\":5,\"itemGap\":10,\"shadowOffsetX\":0,\"backgroundColor\":\"transparent\",\"borderColor\":\"#ccc\",\"shadowOffsetY\":0,\"orient\":\"horizontal\",\"shadowBlur\":0,\"bottom\":\"auto\",\"itemHeight\":14,\"show\":true,\"icon\":\"roundRect\",\"itemStyle\":{\"borderType\":\"solid\",\"shadowOffsetX\":0,\"borderColor\":\"inherit\",\"shadowOffsetY\":0,\"color\":\"inherit\",\"shadowBlur\":0,\"borderWidth\":0,\"opacity\":1,\"shadowColor\":\"transparent\"},\"right\":\"auto\",\"top\":\"auto\",\"borderRadius\":0,\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"color\":\"inherit\",\"shadowBlur\":0,\"width\":\"auto\",\"type\":\"inherit\",\"opacity\":1,\"shadowColor\":\"transparent\"},\"left\":\"right\",\"borderWidth\":0,\"width\":\"auto\",\"itemWidth\":25,\"textStyle\":{\"textBorderWidth\":0,\"color\":\"#fff\",\"textShadowColor\":\"transparent\",\"ellipsis\":\"...\",\"overflow\":\"none\",\"fontSize\":12,\"lineHeight\":24,\"textShadowOffsetX\":0,\"textShadowOffsetY\":0,\"textBorderType\":\"solid\",\"fontWeight\":500,\"textBorderColor\":\"transparent\",\"textShadowBlur\":0},\"shadowColor\":\"rgba(0,0,0,.3)\",\"height\":\"auto\"},\"series\":{\"itemStyle\":{\"borderType\":\"solid\",\"shadowOffsetX\":0,\"borderColor\":\"#fff\",\"shadowOffsetY\":0,\"color\":\"\",\"shadowBlur\":0,\"borderWidth\":0,\"opacity\":1,\"shadowColor\":\"#000\"},\"label\":{\"borderType\":\"solid\",\"rotate\":0,\"padding\":0,\"textBorderWidth\":0,\"backgroundColor\":\"transparent\",\"borderColor\":\"#fff\",\"color\":\"#fff\",\"show\":true,\"textShadowColor\":\"transparent\",\"distanceToLabelLine\":5,\"ellipsis\":\"...\",\"overflow\":\"none\",\"borderRadius\":0,\"borderWidth\":0,\"fontSize\":12,\"lineHeight\":18,\"textShadowOffsetX\":0,\"position\":\"outside\",\"textShadowOffsetY\":0,\"textBorderType\":\"solid\",\"textBorderColor\":\"#fff\",\"textShadowBlur\":0},\"labelLine\":{\"show\":true,\"length\":10,\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"color\":\"#fff\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"#000\"},\"length2\":14,\"smooth\":false}}},\r\n\t\t\tfunnel: {\"tooltip\":{\"backgroundColor\":\"#123\",\"textStyle\":{\"color\":\"#fff\"}},\"backgroundColor\":\"transparent\",\"color\":[\"#5470c6\",\"#91cc75\",\"#fac858\",\"#ee6666\",\"#73c0de\",\"#3ba272\",\"#fc8452\",\"#9a60b4\",\"#ea7ccc\"],\"title\":{\"borderType\":\"solid\",\"padding\":2,\"shadowOffsetX\":0,\"backgroundColor\":\"transparent\",\"borderColor\":\"#ccc\",\"shadowOffsetY\":0,\"shadowBlur\":0,\"bottom\":\"auto\",\"show\":true,\"right\":\"auto\",\"top\":\"auto\",\"borderRadius\":0,\"left\":\"center\",\"borderWidth\":1,\"textStyle\":{\"textBorderWidth\":0,\"color\":\"#fff\",\"textShadowColor\":\"transparent\",\"fontSize\":14,\"lineHeight\":24,\"textShadowOffsetX\":0,\"textShadowOffsetY\":0,\"textBorderType\":\"solid\",\"fontWeight\":500,\"textBorderColor\":\"#ccc\",\"textShadowBlur\":0},\"shadowColor\":\"transparent\"},\"legend\":{\"padding\":5,\"itemGap\":10,\"shadowOffsetX\":0,\"backgroundColor\":\"transparent\",\"borderColor\":\"#ccc\",\"shadowOffsetY\":0,\"orient\":\"vertical\",\"shadowBlur\":0,\"bottom\":\"auto\",\"itemHeight\":14,\"show\":true,\"icon\":\"roundRect\",\"itemStyle\":{\"borderType\":\"solid\",\"shadowOffsetX\":0,\"borderColor\":\"inherit\",\"shadowOffsetY\":0,\"color\":\"inherit\",\"shadowBlur\":0,\"borderWidth\":0,\"opacity\":1,\"shadowColor\":\"transparent\"},\"top\":\"auto\",\"borderRadius\":0,\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"color\":\"inherit\",\"shadowBlur\":0,\"width\":\"auto\",\"type\":\"inherit\",\"opacity\":1,\"shadowColor\":\"transparent\"},\"left\":\"left\",\"borderWidth\":0,\"width\":\"auto\",\"itemWidth\":25,\"textStyle\":{\"textBorderWidth\":0,\"color\":\"#fff\",\"textShadowColor\":\"transparent\",\"ellipsis\":\"...\",\"overflow\":\"none\",\"fontSize\":12,\"lineHeight\":24,\"textShadowOffsetX\":0,\"textShadowOffsetY\":0,\"textBorderType\":\"solid\",\"fontWeight\":500,\"textBorderColor\":\"transparent\",\"textShadowBlur\":0},\"shadowColor\":\"rgba(0,0,0,.3)\",\"height\":\"auto\"},\"series\":{\"itemStyle\":{\"borderType\":\"solid\",\"shadowOffsetX\":0,\"borderColor\":\"#000\",\"shadowOffsetY\":0,\"color\":\"\",\"shadowBlur\":0,\"borderWidth\":0,\"opacity\":1,\"shadowColor\":\"#000\"},\"label\":{\"borderType\":\"solid\",\"rotate\":0,\"padding\":0,\"textBorderWidth\":0,\"backgroundColor\":\"transparent\",\"borderColor\":\"#fff\",\"color\":\"\",\"show\":true,\"textShadowColor\":\"transparent\",\"distanceToLabelLine\":5,\"ellipsis\":\"...\",\"overflow\":\"none\",\"borderRadius\":0,\"borderWidth\":0,\"fontSize\":12,\"lineHeight\":18,\"textShadowOffsetX\":0,\"position\":\"outside\",\"textShadowOffsetY\":0,\"textBorderType\":\"solid\",\"textBorderColor\":\"#fff\",\"textShadowBlur\":0},\"labelLine\":{\"show\":true,\"length\":10,\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"#000\"},\"length2\":14,\"smooth\":false}}},\r\n\t\t\tboardBase: {\"funnelNum\":8,\"lineNum\":8,\"gaugeNum\":8,\"barNum\":8,\"pieNum\":8},\r\n\t\t\tgauge: {\"tooltip\":{\"backgroundColor\":\"#123\",\"textStyle\":{\"color\":\"#fff\"}},\"backgroundColor\":\"transparent\",\"color\":[\"#5470c6\",\"#91cc75\",\"#fac858\",\"#ee6666\",\"#73c0de\",\"#3ba272\",\"#fc8452\",\"#9a60b4\",\"#ea7ccc\"],\"title\":{\"top\":\"bottom\",\"left\":\"left\"},\"series\":{\"pointer\":{\"offsetCenter\":[0,\"10%\"],\"icon\":\"path://M2.9,0.7L2.9,0.7c1.4,0,2.6,1.2,2.6,2.6v115c0,1.4-1.2,2.6-2.6,2.6l0,0c-1.4,0-2.6-1.2-2.6-2.6V3.3C0.3,1.9,1.4,0.7,2.9,0.7z\",\"width\":8,\"length\":\"80%\"},\"axisLine\":{\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"opacity\":0.5,\"shadowBlur\":1,\"shadowColor\":\"#000\"},\"roundCap\":true},\"anchor\":{\"show\":true,\"itemStyle\":{\"color\":\"inherit\"},\"size\":18,\"showAbove\":true},\"emphasis\":{\"disabled\":false},\"progress\":{\"show\":true,\"roundCap\":true,\"overlap\":true},\"splitNumber\":25,\"detail\":{\"formatter\":\"{value}\",\"backgroundColor\":\"inherit\",\"color\":\"#fff\",\"borderRadius\":3,\"width\":20,\"fontSize\":14,\"height\":16},\"title\":{\"fontSize\":14},\"animation\":true}},\r\n\t\t};\r\n\t},\r\n\tmounted(){\r\n\t\tthis.init();\r\n\t\tthis.getcaiwuxinxiCount();\r\n\t\tthis.caiwuxinxiChat1();\r\n\t\tthis.caiwuxinxiChat2();\r\n\t\tthis.caiwuxinxiChat3();\r\n\t},\r\n\tmethods:{\r\n\t\t// 词云\r\n\t\twordclouds(wordcloudData,echartsId) {\r\n\t\t\tlet wordcloud = $template2.back.board.wordcloud\r\n\t\t\twordcloud = JSON.parse(JSON.stringify(wordcloud), (k, v) => {\r\n\t\t\t  if(typeof v == 'string' && v.indexOf('function') > -1){\r\n\t\t\t\treturn eval(\"(function(){return \"+v+\" })()\")\r\n\t\t\t  }\r\n\t\t\t  return v;\r\n\t\t\t})\r\n\t\t\twordcloud.option.series[0].data=wordcloudData;\r\n\t\t\t\r\n\t\t\tthis.myChart0 = echarts.init(document.getElementById(echartsId));\r\n\t\t\tlet myChart = this.myChart0\r\n\t\t\tlet img = wordcloud.maskImage\r\n\t\t\r\n\t\t\tif (img) {\r\n\t\t\t\tvar maskImage = new Image();\r\n\t\t\t\tmaskImage.src = img\r\n\t\t\t\tmaskImage.onload = function() {\r\n\t\t\t\t\twordcloud.option.series[0].maskImage = maskImage\r\n\t\t\t\t\tmyChart.clear()\r\n\t\t\t\t\tmyChart.setOption(wordcloud.option)\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tdelete wordcloud.option.series[0].maskImage\r\n\t\t\t\tmyChart.clear()\r\n\t\t\t\tmyChart.setOption(wordcloud.option)\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 统计图动画\r\n\t\tmyChartInterval(type, xAxisData, seriesData, myChart) {\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tsetInterval(() => {\r\n\t\t\t\t\tlet xAxis = xAxisData.shift()\r\n\t\t\t\t\txAxisData.push(xAxis)\r\n\t\t\t\t\tlet series = seriesData.shift()\r\n\t\t\t\t\tseriesData.push(series)\r\n\t\t\t\t\r\n\t\t\t\t\tif (type == 1) {\r\n\t\t\t\t\t\tmyChart.setOption({\r\n\t\t\t\t\t\t\txAxis: [{\r\n\t\t\t\t\t\t\t\tdata: xAxisData\r\n\t\t\t\t\t\t\t}],\r\n\t\t\t\t\t\t\tseries: [{\r\n\t\t\t\t\t\t\t\tdata: seriesData\r\n\t\t\t\t\t\t\t}]\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (type == 2) {\r\n\t\t\t\t\t\tmyChart.setOption({\r\n\t\t\t\t\t\t\tyAxis: [{\r\n\t\t\t\t\t\t\t\tdata: xAxisData\r\n\t\t\t\t\t\t\t}],\r\n\t\t\t\t\t\t\tseries: [{\r\n\t\t\t\t\t\t\t\tdata: seriesData\r\n\t\t\t\t\t\t\t}]\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t}, $template2.back.board.bar.base.interval);\r\n\t\t\t})\r\n\t\t},\r\n\t\tinit(){\r\n\t\t\tif(this.$storage.get('Token')){\r\n\t\t\tthis.$http({\r\n\t\t\t\turl: `${this.$storage.get('sessionTable')}/session`,\r\n\t\t\t\tmethod: \"get\"\r\n\t\t\t}).then(({ data }) => {\r\n\t\t\t\tif (data && data.code != 0) {\r\n\t\t\t\trouter.push({ name: 'login' })\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t}else{\r\n\t\t\t\trouter.push({ name: 'login' })\r\n\t\t\t}\r\n\t\t},\r\n\t\tgetcaiwuxinxiCount() {\r\n\t\t\tthis.$http({\r\n\t\t\t\turl: `caiwuxinxi/count`,\r\n\t\t\t\tmethod: \"get\"\r\n\t\t\t}).then(({\r\n\t\t\t\tdata\r\n\t\t\t}) => {\r\n\t\t\t\tif (data && data.code == 0) {\r\n\t\t\t\t\tthis.caiwuxinxiCount = data.data\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tcaiwuxinxiChat1() {\r\n\t\t\tthis.$nextTick(()=>{\r\n\r\n        var caiwuxinxiChart1 = echarts.init(document.getElementById(\"caiwuxinxiChart1\"),'macarons');\r\n        this.$http({\r\n            url: `caiwuxinxi/value/dengjiriqi/shourujine/年`,\r\n            method: \"get\",\r\n        }).then(({ data }) => {\r\n            if (data && data.code === 0) {\r\n                let res = data.data;\r\n                let xAxis = [];\r\n                let yAxis = [];\r\n                let pArray = []\r\n                for(let i=0;i<res.length;i++){\r\n\t\t\t\t\tif(this.boardBase&&i==this.boardBase.funnelNum){\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n                    xAxis.push(res[i].dengjiriqi);\r\n                    yAxis.push(parseFloat((res[i].total)));\r\n                    pArray.push({\r\n                        value: parseFloat((res[i].total)),\r\n                        name: res[i].dengjiriqi\r\n                    })\r\n                }\r\n                var option = {};\r\n\t\t\t\tlet titleObj = this.funnel.title\r\n\t\t\t\ttitleObj.text = '收入统计'\r\n\t\t\t\t\r\n\t\t\t\tlet legendObj = {\r\n\t\t\t\t\tdata: xAxis,\r\n\t\t\t\t}\r\n\t\t\t\tlegendObj = Object.assign(legendObj , this.funnel.legend)\r\n\t\t\t\tlet tooltipObj = {trigger: 'item',formatter: '{b} : {c}'}\r\n\t\t\t\ttooltipObj = Object.assign(tooltipObj , this.funnel.tooltip?this.funnel.tooltip:{})\r\n\t\t\t\tlet seriesObj = {\r\n\t\t\t\t\tname: '收入统计',\r\n\t\t\t\t\tdata: pArray,\r\n\t\t\t\t\ttype: 'funnel',\r\n\t\t\t\t\tleft: '10%',\r\n\t\t\t\t\ttop: 60,\r\n\t\t\t\t\tbottom: 60,\r\n\t\t\t\t\twidth: '80%',\r\n\t\t\t\t\tminSize: '0%',\r\n\t\t\t\t\tmaxSize: '100%',\r\n\t\t\t\t}\r\n\t\t\t\tseriesObj = Object.assign(seriesObj , this.funnel.series)\r\n\t\t\t\toption = {\r\n\t\t\t\t\tbackgroundColor: this.funnel.backgroundColor,\r\n\t\t\t\t\tcolor: this.funnel.color,\r\n\t\t\t\t    title: titleObj,\r\n\t\t\t\t    legend: legendObj,\r\n\t\t\t\t    tooltip: tooltipObj,\r\n\t\t\t\t    series: seriesObj,\r\n\t\t\t\t}\r\n                // 使用刚指定的配置项和数据显示图表。\r\n                caiwuxinxiChart1.setOption(option);\r\n\t\t\t\t\r\n                  //根据窗口的大小变动图表\r\n                window.onresize = function() {\r\n                    caiwuxinxiChart1.resize();\r\n                };\r\n            }\r\n        });\r\n      })\r\n    },\r\n\r\n    caiwuxinxiChat2() {\r\n      this.$nextTick(()=>{\r\n\r\n        var caiwuxinxiChart2 = echarts.init(document.getElementById(\"caiwuxinxiChart2\"),'macarons');\r\n        this.$http({\r\n            url: `caiwuxinxi/value/dengjiriqi/zhichujine/年`,\r\n            method: \"get\",\r\n        }).then(({ data }) => {\r\n            if (data && data.code === 0) {\r\n                let res = data.data;\r\n                let xAxis = [];\r\n                let yAxis = [];\r\n                let pArray = []\r\n                for(let i=0;i<res.length;i++){\r\n\t\t\t\t\tif(this.boardBase&&i==this.boardBase.barNum){\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n                    xAxis.push(res[i].dengjiriqi);\r\n                    yAxis.push(parseFloat((res[i].total)));\r\n                    pArray.push({\r\n                        value: parseFloat((res[i].total)),\r\n                        name: res[i].dengjiriqi\r\n                    })\r\n                }\r\n                var option = {};\r\n                let titleObj = this.bar.title\r\n\t\t\t\ttitleObj.text = '支出统计'\r\n\t\t\t\t\r\n\t\t\t\tconst legendObj = this.bar.legend\r\n\t\t\t\tlet tooltipObj = {trigger: 'item',formatter: '{b} : {c}'}\r\n\t\t\t\ttooltipObj = Object.assign(tooltipObj , this.bar.tooltip?this.bar.tooltip:{})\r\n\t\t\t\t\r\n\t\t\t\tlet xAxisObj = this.bar.xAxis\r\n\t\t\t\txAxisObj.type = 'category'\r\n\t\t\t\txAxisObj.data = xAxis\r\n                xAxisObj.axisLabel.rotate=40\r\n\t\t\t\t\r\n\t\t\t\tlet yAxisObj = this.bar.yAxis\r\n\t\t\t\tyAxisObj.type = 'value'\r\n\t\t\t\tlet seriesObj = {\r\n\t\t\t\t\t\tdata: yAxis,\r\n\t\t\t\t\t\ttype: 'bar'\r\n\t\t\t\t\t}\r\n\t\t\t\tseriesObj = Object.assign(seriesObj , this.bar.series)\r\n\r\n\t\t\t\toption = {\r\n\t\t\t\t\tbackgroundColor: this.bar.backgroundColor,\r\n\t\t\t\t\tcolor: this.bar.color,\r\n\t\t\t\t\ttitle: titleObj,\r\n\t\t\t\t\tlegend: legendObj,\r\n\t\t\t\t\ttooltip: tooltipObj,\r\n\t\t\t\t\txAxis: xAxisObj,\r\n\t\t\t\t\tyAxis: yAxisObj,\r\n\t\t\t\t\tseries: [seriesObj]\r\n\t\t\t\t};\r\n                // 使用刚指定的配置项和数据显示图表。\r\n                caiwuxinxiChart2.setOption(option);\r\n\t\t\t\t\r\n                  //根据窗口的大小变动图表\r\n                window.onresize = function() {\r\n                    caiwuxinxiChart2.resize();\r\n                };\r\n            }\r\n        });\r\n      })\r\n    },\r\n\r\n    caiwuxinxiChat3() {\r\n      this.$nextTick(()=>{\r\n\r\n        var caiwuxinxiChart3 = echarts.init(document.getElementById(\"caiwuxinxiChart3\"),'macarons');\r\n        this.$http({\r\n            url: `caiwuxinxi/value/dengjiriqi/lirun/年`,\r\n            method: \"get\",\r\n        }).then(({ data }) => {\r\n            if (data && data.code === 0) {\r\n                let res = data.data;\r\n                let xAxis = [];\r\n                let yAxis = [];\r\n                let pArray = []\r\n                for(let i=0;i<res.length;i++){\r\n\t\t\t\t\tif(this.boardBase&&i==this.boardBase.lineNum){\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n                    xAxis.push(res[i].dengjiriqi);\r\n                    yAxis.push(parseFloat((res[i].total)));\r\n                    pArray.push({\r\n                        value: parseFloat((res[i].total)),\r\n                        name: res[i].dengjiriqi\r\n                    })\r\n                }\r\n                var option = {};\r\n                let titleObj = this.line.title\r\n\t\t\t\ttitleObj.text = '利润统计'\r\n\t\t\t\t\r\n\t\t\t\tconst legendObj = this.line.legend\r\n\t\t\t\tlet tooltipObj = { trigger: 'item',formatter: '{b} : {c}'}\r\n\t\t\t\ttooltipObj = Object.assign(tooltipObj , this.line.tooltip?this.line.tooltip:{})\r\n\t\t\t\t\r\n\t\t\t\tlet xAxisObj = this.line.xAxis\r\n\t\t\t\txAxisObj.type = 'category'\r\n\t\t\t\txAxisObj.boundaryGap = false\r\n\t\t\t\txAxisObj.data = xAxis\r\n                xAxisObj.axisLabel.rotate=70\r\n\t\t\t\t\r\n\t\t\t\tlet yAxisObj = this.line.yAxis\r\n\t\t\t\tyAxisObj.type = 'value'\r\n\t\t\t\t\r\n\t\t\t\tlet seriesObj = {\r\n\t\t\t\t\tdata: yAxis,\r\n\t\t\t\t\ttype: 'line',\r\n\t\t\t\t}\r\n\t\t\t\tseriesObj = Object.assign(seriesObj , this.line.series)\r\n\t\t\t\t\r\n\t\t\t\toption = {\r\n\t\t\t\t\tbackgroundColor: this.line.backgroundColor,\r\n\t\t\t\t\tcolor: this.line.color,\r\n\t\t\t\t\ttitle: titleObj,\r\n\t\t\t\t\tlegend: legendObj,\r\n\t\t\t\t\ttooltip: tooltipObj,\r\n\t\t\t\t\txAxis: xAxisObj,\r\n\t\t\t\t\tyAxis: yAxisObj,\r\n\t\t\t\t\tseries: [seriesObj]\r\n\t\t\t\t};\r\n                // 使用刚指定的配置项和数据显示图表。\r\n                caiwuxinxiChart3.setOption(option);\r\n                  //根据窗口的大小变动图表\r\n                window.onresize = function() {\r\n                    caiwuxinxiChart3.resize();\r\n                };\r\n            }\r\n        });\r\n      })\r\n    },\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n    .cardView {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        width: 100%;\r\n\r\n        .cards {\r\n            display: flex;\r\n            align-items: center;\r\n            width: 100%;\r\n            margin-bottom: 10px;\r\n            justify-content: center;\r\n            .card {\r\n                width: calc(25% - 20px);\r\n                margin: 0 10px;\r\n                /deep/.el-card__body{\r\n                    padding: 0;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\t\r\n\t// 日历\r\n\t.calendar td .text {\r\n\t\t\t\tborder-radius: 12px;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\tbackground: #fff;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\theight: 100%;\r\n\t\t\t}\r\n\t.calendar td .text:hover {\r\n\t\t\t\tbackground: rgba(78,110,242,.1);\r\n\t\t\t}\r\n\t.calendar td .text .new {\r\n\t\t\t\tcolor: #000;\r\n\t\t\t\tfont-size: 24px;\r\n\t\t\t\tline-height: 1.4;\r\n\t\t\t}\r\n\t.calendar td .text .old {\r\n\t\t\t\tcolor: #666;\r\n\t\t\t\tfont-size: 16px;\r\n\t\t\t\tline-height: 1.4;\r\n\t\t\t}\r\n\t.calendar td.festival .text {\r\n\t\t\t\tborder-radius: 12px;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\tbackground: rgba(235,51,51,.05);\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\theight: 100%;\r\n\t\t\t}\r\n\t.calendar td.festival .text:hover {\r\n\t\t\t\tbackground: rgba(78,110,242,.1);\r\n\t\t\t}\r\n\t.calendar td.festival .text .new {\r\n\t\t\t\tcolor: #000;\r\n\t\t\t\tfont-size: 24px;\r\n\t\t\t\tline-height: 1.4;\r\n\t\t\t}\r\n\t.calendar td.festival .text .old {\r\n\t\t\t\tcolor: #666;\r\n\t\t\t\tfont-size: 16px;\r\n\t\t\t\tline-height: 1.4;\r\n\t\t\t}\r\n\t.calendar td.other .text {\r\n\t\t\t\tborder-radius: 12px;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\tbackground: #fff;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\topacity: 0.3;\r\n\t\t\t\theight: 100%;\r\n\t\t\t}\r\n\t.calendar td.other .text:hover {\r\n\t\t\t\tbackground: rgba(78,110,242,.1);\r\n\t\t\t}\r\n\t.calendar td.other .text .new {\r\n\t\t\t\tcolor: #000;\r\n\t\t\t\tfont-size: 24px;\r\n\t\t\t\tline-height: 1.4;\r\n\t\t\t}\r\n\t.calendar td.other .text .old {\r\n\t\t\t\tcolor: #666;\r\n\t\t\t\tfont-size: 16px;\r\n\t\t\t\tline-height: 1.4;\r\n\t\t\t}\r\n\t.calendar td.today .text {\r\n\t\t\t\tborder-radius: 12px;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #2ddcd3;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\theight: 100%;\r\n\t\t\t}\r\n\t.calendar td.today .text:hover {\r\n\t\t\t\tbackground: rgba(64, 158, 255,.5);\r\n\t\t\t}\r\n\t.calendar td.today .text .new {\r\n\t\t\t\tcolor: inherit;\r\n\t\t\t\tfont-size: 24px;\r\n\t\t\t\tline-height: 1.4;\r\n\t\t\t}\r\n\t.calendar td.today .text .old {\r\n\t\t\t\tcolor: inherit;\r\n\t\t\t\tfont-size: 16px;\r\n\t\t\t\tline-height: 1.4;\r\n\t\t\t}\r\n\t\r\n\t// echarts1\r\n\t.type1 .echarts1 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: auto;\r\n\t\t\t}\r\n\t.type1 .echarts1:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t// echarts2\r\n\t.type2 .echarts1 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 49%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type2 .echarts1:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t.type2 .echarts2 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 49%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type2 .echarts2:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t// echarts3\r\n\t.type3 .echarts1 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type3 .echarts1:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t.type3 .echarts2 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 49%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type3 .echarts2:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t.type3 .echarts3 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 49%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type3 .echarts3:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t// echarts4\r\n\t.type4 .echarts1 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 49%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type4 .echarts1:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t.type4 .echarts2 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 49%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type4 .echarts2:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t.type4 .echarts3 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 49%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type4 .echarts3:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t.type4 .echarts4 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 49%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type4 .echarts4:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t// echarts5\r\n\t.type5 .echarts1 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type5 .echarts1:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t.type5 .echarts2 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 49%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type5 .echarts2:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t.type5 .echarts3 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 49%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type5 .echarts3:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t.type5 .echarts4 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 49%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type5 .echarts4:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t.type5 .echarts5 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 49%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type5 .echarts5:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t\r\n\t.echarts-flag-2 {\r\n\t  display: flex;\r\n\t  flex-wrap: wrap;\r\n\t  justify-content: space-between;\r\n\t  padding: 10px 20px;\r\n\t  background: rebeccapurple;\r\n\t\r\n\t  &>div {\r\n\t    width: 32%;\r\n\t    height: 300px;\r\n\t    margin: 10px 0;\r\n\t    background: rgba(255,255,255,.1);\r\n\t    border-radius: 8px;\r\n\t    padding: 10px 20px;\r\n\t  }\r\n\t}\r\n</style>\r\n"]}]}