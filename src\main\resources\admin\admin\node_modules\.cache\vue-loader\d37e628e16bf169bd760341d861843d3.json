{"remainingRequest": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\src\\views\\modules\\yuangongdangan\\add-or-update.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\src\\views\\modules\\yuangongdangan\\add-or-update.vue", "mtime": 1755434650291}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["add-or-update.vue"], "names": [], "mappings": ";AAsHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAWA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;;;;;;;;;AASA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "add-or-update.vue", "sourceRoot": "src/views/modules/yuangongdangan", "sourcesContent": ["<template>\r\n\t<div class=\"addEdit-block\" :style='{\"padding\":\"30px\",\"margin\":\"0\"}'>\r\n\t\t<el-form\r\n\t\t\t:style='{\"border\":\"1px solid rgba(167, 180, 201,.3)  \",\"padding\":\"30px\",\"borderRadius\":\"6px\",\"background\":\"#fff\"}'\r\n\t\t\tclass=\"add-update-preview\"\r\n\t\t\tref=\"ruleForm\"\r\n\t\t\t:model=\"ruleForm\"\r\n\t\t\t:rules=\"rules\"\r\n\t\t\tlabel-width=\"80px\"\r\n\t\t>\r\n\t\t\t<template >\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"select\" v-if=\"type!='info'\" label=\"工号\" prop=\"gonghao\">\r\n\t\t\t\t\t<el-select :disabled=\"ro.gonghao\" @change=\"gonghaoChange\" v-model=\"ruleForm.gonghao\" placeholder=\"请选择工号\">\r\n\t\t\t\t\t\t<el-option\r\n\t\t\t\t\t\t\tv-for=\"(item,index) in gonghaoOptions\"\r\n\t\t\t\t\t\t\tv-bind:key=\"index\"\r\n\t\t\t\t\t\t\t:label=\"item\"\r\n\t\t\t\t\t\t\t:value=\"item\">\r\n\t\t\t\t\t\t</el-option>\r\n\t\t\t\t\t</el-select>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-else-if=\"ruleForm.gonghao\" label=\"工号\" prop=\"gonghao\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.gonghao\" placeholder=\"工号\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"姓名\" prop=\"xingming\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.xingming\" placeholder=\"姓名\" clearable  :readonly=\"ro.xingming\"></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"姓名\" prop=\"xingming\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.xingming\" placeholder=\"姓名\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"性别\" prop=\"xingbie\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.xingbie\" placeholder=\"性别\" clearable  :readonly=\"ro.xingbie\"></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"性别\" prop=\"xingbie\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.xingbie\" placeholder=\"性别\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"联系电话\" prop=\"lianxidianhua\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.lianxidianhua\" placeholder=\"联系电话\" clearable  :readonly=\"ro.lianxidianhua\"></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"联系电话\" prop=\"lianxidianhua\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.lianxidianhua\" placeholder=\"联系电话\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"部门\" prop=\"bumen\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.bumen\" placeholder=\"部门\" clearable  :readonly=\"ro.bumen\"></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"部门\" prop=\"bumen\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.bumen\" placeholder=\"部门\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"职位\" prop=\"zhiwei\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.zhiwei\" placeholder=\"职位\" clearable  :readonly=\"ro.zhiwei\"></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"职位\" prop=\"zhiwei\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.zhiwei\" placeholder=\"职位\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"select\" v-if=\"type!='info'\"  label=\"人员状态\" prop=\"yuangongzhuangtai\">\r\n\t\t\t\t\t<el-select :disabled=\"ro.yuangongzhuangtai\" v-model=\"ruleForm.yuangongzhuangtai\" placeholder=\"请选择人员状态\" >\r\n\t\t\t\t\t\t<el-option\r\n\t\t\t\t\t\t\tv-for=\"(item,index) in yuangongzhuangtaiOptions\"\r\n\t\t\t\t\t\t\tv-bind:key=\"index\"\r\n\t\t\t\t\t\t\t:label=\"item\"\r\n\t\t\t\t\t\t\t:value=\"item\">\r\n\t\t\t\t\t\t</el-option>\r\n\t\t\t\t\t</el-select>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"人员状态\" prop=\"yuangongzhuangtai\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.yuangongzhuangtai\"\r\n\t\t\t\t\t\tplaceholder=\"人员状态\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"upload\" v-if=\"type!='info'&& !ro.yuangongdangan\" label=\"人员档案\" prop=\"yuangongdangan\">\r\n\t\t\t\t\t<file-upload\r\n\t\t\t\t\t\ttip=\"点击上传人员档案\"\r\n\t\t\t\t\t\taction=\"file/upload\"\r\n\t\t\t\t\t\t:limit=\"1\"\r\n\t\t\t\t\t\t:multiple=\"true\"\r\n\t\t\t\t\t\t:fileUrls=\"ruleForm.yuangongdangan?ruleForm.yuangongdangan:''\"\r\n\t\t\t\t\t\t@change=\"yuangongdanganUploadChange\"\r\n\t\t\t\t\t></file-upload>\r\n\t\t\t\t</el-form-item>  \r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else-if=\"ruleForm.yuangongdangan\" label=\"人员档案\" prop=\"yuangongdangan\">\r\n\t\t\t\t\t<el-button :style='{\"border\":\"0\",\"cursor\":\"pointer\",\"padding\":\"0 15px\",\"margin\":\"0 20px 0 0\",\"outline\":\"none\",\"color\":\"rgba(255, 255, 255, 1)\",\"borderRadius\":\"4px\",\"background\":\"#18c1b9\",\"width\":\"auto\",\"lineHeight\":\"40px\",\"fontSize\":\"14px\",\"height\":\"40px\"}' type=\"text\" size=\"small\" @click=\"download($base.url+ruleForm.yuangongdangan)\">下载</el-button>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else-if=\"!ruleForm.yuangongdangan\" label=\"人员档案\" prop=\"yuangongdangan\">\r\n\t\t\t\t\t<el-button :style='{\"border\":\"0\",\"cursor\":\"pointer\",\"padding\":\"0 15px\",\"margin\":\"0 20px 0 0\",\"outline\":\"none\",\"color\":\"rgba(255, 255, 255, 1)\",\"borderRadius\":\"4px\",\"background\":\"#18c1b9\",\"width\":\"auto\",\"lineHeight\":\"40px\",\"fontSize\":\"14px\",\"height\":\"40px\"}' type=\"text\" size=\"small\">无</el-button>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"date\" v-if=\"type!='info'\" label=\"入职日期\" prop=\"ruzhiriqi\">\r\n\t\t\t\t\t<el-date-picker\r\n\t\t\t\t\t\tformat=\"yyyy 年 MM 月 dd 日\"\r\n\t\t\t\t\t\tvalue-format=\"yyyy-MM-dd\"\r\n\t\t\t\t\t\tv-model=\"ruleForm.ruzhiriqi\" \r\n\t\t\t\t\t\ttype=\"date\"\r\n\t\t\t\t\t\t:readonly=\"ro.ruzhiriqi\"\r\n\t\t\t\t\t\tplaceholder=\"入职日期\"\r\n\t\t\t\t\t></el-date-picker> \r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-else-if=\"ruleForm.ruzhiriqi\" label=\"入职日期\" prop=\"ruzhiriqi\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.ruzhiriqi\" placeholder=\"入职日期\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t</template>\r\n\t\t\t<el-form-item :style='{\"padding\":\"0\",\"margin\":\"0\"}' class=\"btn\">\r\n\t\t\t\t<el-button class=\"btn3\"  v-if=\"type!='info'\" type=\"success\" @click=\"onSubmit\">\r\n\t\t\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"margin\":\"0 2px\",\"fontSize\":\"14px\",\"color\":\"#fff\",\"height\":\"40px\"}'></span>\r\n\t\t\t\t\t提交\r\n\t\t\t\t</el-button>\r\n\t\t\t\t<el-button class=\"btn4\" v-if=\"type!='info'\" type=\"success\" @click=\"back()\">\r\n\t\t\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"margin\":\"0 2px\",\"fontSize\":\"14px\",\"color\":\"#fff\",\"height\":\"40px\"}'></span>\r\n\t\t\t\t\t取消\r\n\t\t\t\t</el-button>\r\n\t\t\t\t<el-button class=\"btn5\" v-if=\"type=='info'\" type=\"success\" @click=\"back()\">\r\n\t\t\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"margin\":\"0 2px\",\"fontSize\":\"14px\",\"color\":\"#fff\",\"height\":\"40px\"}'></span>\r\n\t\t\t\t\t返回\r\n\t\t\t\t</el-button>\r\n\t\t\t</el-form-item>\r\n\t\t</el-form>\r\n    \r\n\r\n  </div>\r\n</template>\r\n<script>\r\n// 数字，邮件，手机，url，身份证校验\r\nimport { isNumber,isIntNumer,isEmail,isPhone, isMobile,isURL,checkIdCard } from \"@/utils/validate\";\r\nexport default {\r\n\tdata() {\r\n\t\tlet self = this\r\n\t\tvar validateIdCard = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!checkIdCard(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的身份证号码\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateUrl = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isURL(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的URL地址\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateMobile = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isMobile(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的手机号码\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validatePhone = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isPhone(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的电话号码\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateEmail = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isEmail(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的邮箱地址\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateNumber = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isNumber(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入数字\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateIntNumber = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isIntNumer(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入整数\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\treturn {\r\n\t\t\tid: '',\r\n\t\t\ttype: '',\r\n\t\t\t\r\n\t\t\t\r\n\t\t\tro:{\r\n\t\t\t\tgonghao : false,\r\n\t\t\t\txingming : false,\r\n\t\t\t\txingbie : false,\r\n\t\t\t\tlianxidianhua : false,\r\n\t\t\t\tbumen : false,\r\n\t\t\t\tzhiwei : false,\r\n\t\t\t\tyuangongzhuangtai : false,\r\n\t\t\t\tyuangongdangan : false,\r\n\t\t\t\truzhiriqi : false,\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t\r\n\t\t\truleForm: {\r\n\t\t\t\tgonghao: '',\r\n\t\t\t\txingming: '',\r\n\t\t\t\txingbie: '',\r\n\t\t\t\tlianxidianhua: '',\r\n\t\t\t\tbumen: '',\r\n\t\t\t\tzhiwei: '',\r\n\t\t\t\tyuangongzhuangtai: '',\r\n\t\t\t\tyuangongdangan: '',\r\n\t\t\t\truzhiriqi: '',\r\n\t\t\t},\r\n\t\t\r\n\t\t\tgonghaoOptions: [],\r\n\t\t\tyuangongzhuangtaiOptions: [],\r\n\r\n\t\t\t\r\n\t\t\trules: {\r\n\t\t\t\tgonghao: [\r\n\t\t\t\t],\r\n\t\t\t\txingming: [\r\n\t\t\t\t],\r\n\t\t\t\txingbie: [\r\n\t\t\t\t],\r\n\t\t\t\tlianxidianhua: [\r\n\t\t\t\t],\r\n\t\t\t\tbumen: [\r\n\t\t\t\t],\r\n\t\t\t\tzhiwei: [\r\n\t\t\t\t],\r\n\t\t\t\tyuangongzhuangtai: [\r\n\t\t\t\t\t{ required: true, message: '人员状态不能为空', trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\tyuangongdangan: [\r\n\t\t\t\t],\r\n\t\t\t\truzhiriqi: [\r\n\t\t\t\t],\r\n\t\t\t}\r\n\t\t};\r\n\t},\r\n\tprops: [\"parent\"],\r\n\tcomputed: {\r\n\r\n\r\n\r\n\t},\r\n    components: {\r\n    },\r\n\tcreated() {\r\n\t},\r\n\tmethods: {\r\n\t\t\r\n\t\t// 下载\r\n\t\tdownload(file){\r\n\t\t\twindow.open(`${file}`)\r\n\t\t},\r\n\t\t// 初始化\r\n\t\tinit(id,type) {\r\n\t\t\tif (id) {\r\n\t\t\t\tthis.id = id;\r\n\t\t\t\tthis.type = type;\r\n\t\t\t}\r\n\t\t\tif(this.type=='info'||this.type=='else'){\r\n\t\t\t\tthis.info(id);\r\n\t\t\t}else if(this.type=='logistics'){\r\n\t\t\t\tthis.logistics=false;\r\n\t\t\t\tthis.info(id);\r\n\t\t\t}else if(this.type=='cross'){\r\n\t\t\t\tvar obj = this.$storage.getObj('crossObj');\r\n\t\t\t\tfor (var o in obj){\r\n\t\t\t\t\t\tif(o=='gonghao'){\r\n\t\t\t\t\t\t\tthis.ruleForm.gonghao = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.gonghao = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='xingming'){\r\n\t\t\t\t\t\t\tthis.ruleForm.xingming = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.xingming = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='xingbie'){\r\n\t\t\t\t\t\t\tthis.ruleForm.xingbie = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.xingbie = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='lianxidianhua'){\r\n\t\t\t\t\t\t\tthis.ruleForm.lianxidianhua = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.lianxidianhua = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='bumen'){\r\n\t\t\t\t\t\t\tthis.ruleForm.bumen = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.bumen = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='zhiwei'){\r\n\t\t\t\t\t\t\tthis.ruleForm.zhiwei = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.zhiwei = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='yuangongzhuangtai'){\r\n\t\t\t\t\t\t\tthis.ruleForm.yuangongzhuangtai = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.yuangongzhuangtai = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='yuangongdangan'){\r\n\t\t\t\t\t\t\tthis.ruleForm.yuangongdangan = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.yuangongdangan = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='ruzhiriqi'){\r\n\t\t\t\t\t\t\tthis.ruleForm.ruzhiriqi = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.ruzhiriqi = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 获取用户信息\r\n\t\t\tthis.$http({\r\n\t\t\t\turl: `${this.$storage.get('sessionTable')}/session`,\r\n\t\t\t\tmethod: \"get\"\r\n\t\t\t}).then(({ data }) => {\r\n\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\t\r\n\t\t\t\t\tvar json = data.data;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t\r\n            this.$http({\r\n\t\t\t\turl: `option/yuangong/gonghao`,\r\n\t\t\t\tmethod: \"get\"\r\n            }).then(({ data }) => {\r\n\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\tthis.gonghaoOptions = data.data;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t\t}\r\n            });\r\n            this.yuangongzhuangtaiOptions = \"在职,离职\".split(',')\r\n\t\t\t\r\n\t\t},\r\n\t\t\t// 下二随\r\n\t\t\tgonghaoChange () {\r\n\t\t\t\tthis.$http({\r\n\t\t\t\t\turl: `follow/yuangong/gonghao?columnValue=`+ this.ruleForm.gonghao,\r\n\t\t\t\t\tmethod: \"get\"\r\n\t\t\t\t}).then(({ data }) => {\r\n\t\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\t\tif(data.data.xingming){\r\n\t\t\t\t\t\t\tthis.ruleForm.xingming = data.data.xingming\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(data.data.xingbie){\r\n\t\t\t\t\t\t\tthis.ruleForm.xingbie = data.data.xingbie\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(data.data.lianxidianhua){\r\n\t\t\t\t\t\t\tthis.ruleForm.lianxidianhua = data.data.lianxidianhua\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(data.data.bumen){\r\n\t\t\t\t\t\t\tthis.ruleForm.bumen = data.data.bumen\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(data.data.zhiwei){\r\n\t\t\t\t\t\t\tthis.ruleForm.zhiwei = data.data.zhiwei\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n    // 多级联动参数\r\n\r\n    info(id) {\r\n      this.$http({\r\n        url: `yuangongdangan/info/${id}`,\r\n        method: \"get\"\r\n      }).then(({ data }) => {\r\n        if (data && data.code === 0) {\r\n        this.ruleForm = data.data;\r\n        //解决前台上传图片后台不显示的问题\r\n        let reg=new RegExp('../../../upload','g')//g代表全部\r\n        } else {\r\n          this.$message.error(data.msg);\r\n        }\r\n      });\r\n    },\r\n\r\n\r\n    // 提交\r\n    onSubmit() {\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\tif(this.ruleForm.yuangongdangan!=null) {\r\n\t\tthis.ruleForm.yuangongdangan = this.ruleForm.yuangongdangan.replace(new RegExp(this.$base.url,\"g\"),\"\");\r\n\t}\r\n\r\n\r\nvar objcross = this.$storage.getObj('crossObj');\r\n      //更新跨表属性\r\n       var crossuserid;\r\n       var crossrefid;\r\n       var crossoptnum;\r\n       if(this.type=='cross'){\r\n                var statusColumnName = this.$storage.get('statusColumnName');\r\n                var statusColumnValue = this.$storage.get('statusColumnValue');\r\n                if(statusColumnName!='') {\r\n                        var obj = this.$storage.getObj('crossObj');\r\n                       if(statusColumnName && !statusColumnName.startsWith(\"[\")) {\r\n                               for (var o in obj){\r\n                                 if(o==statusColumnName){\r\n                                   obj[o] = statusColumnValue;\r\n                                 }\r\n                               }\r\n                               var table = this.$storage.get('crossTable');\r\n                             this.$http({\r\n                                 url: `${table}/update`,\r\n                                 method: \"post\",\r\n                                 data: obj\r\n                               }).then(({ data }) => {});\r\n                       } else {\r\n                               crossuserid=this.$storage.get('userid');\r\n                               crossrefid=obj['id'];\r\n                               crossoptnum=this.$storage.get('statusColumnName');\r\n                               crossoptnum=crossoptnum.replace(/\\[/,\"\").replace(/\\]/,\"\");\r\n                        }\r\n                }\r\n        }\r\n\t\tthis.$refs[\"ruleForm\"].validate(valid => {\r\n\t\t\tif (valid) {\r\n\t\t\t\tif(crossrefid && crossuserid) {\r\n\t\t\t\t\tthis.ruleForm.crossuserid = crossuserid;\r\n\t\t\t\t\tthis.ruleForm.crossrefid = crossrefid;\r\n\t\t\t\t\tlet params = { \r\n\t\t\t\t\t\tpage: 1, \r\n\t\t\t\t\t\tlimit: 10, \r\n\t\t\t\t\t\tcrossuserid:this.ruleForm.crossuserid,\r\n\t\t\t\t\t\tcrossrefid:this.ruleForm.crossrefid,\r\n\t\t\t\t\t} \r\n\t\t\t\tthis.$http({ \r\n\t\t\t\t\turl: \"yuangongdangan/page\", \r\n\t\t\t\t\tmethod: \"get\", \r\n\t\t\t\t\tparams: params \r\n\t\t\t\t}).then(({ \r\n\t\t\t\t\tdata \r\n\t\t\t\t}) => { \r\n\t\t\t\t\tif (data && data.code === 0) { \r\n\t\t\t\t\t\tif(data.data.total>=crossoptnum) {\r\n\t\t\t\t\t\t\tthis.$message.error(this.$storage.get('tips'));\r\n\t\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.$http({\r\n\t\t\t\t\t\t\t\turl: `yuangongdangan/${!this.ruleForm.id ? \"save\" : \"update\"}`,\r\n\t\t\t\t\t\t\t\tmethod: \"post\",\r\n\t\t\t\t\t\t\t\tdata: this.ruleForm\r\n\t\t\t\t\t\t\t}).then(({ data }) => {\r\n\t\t\t\t\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\t\t\t\tmessage: \"操作成功\",\r\n\t\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\t\tduration: 1500,\r\n\t\t\t\t\t\t\t\t\t\tonClose: () => {\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.showFlag = true;\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.addOrUpdateFlag = false;\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.yuangongdanganCrossAddOrUpdateFlag = false;\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.search();\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.contentStyleChange();\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else { \r\n\t\t\t\t} \r\n\t\t\t});\r\n\t\t} else {\r\n\t\t\tthis.$http({\r\n\t\t\t\turl: `yuangongdangan/${!this.ruleForm.id ? \"save\" : \"update\"}`,\r\n\t\t\t\tmethod: \"post\",\r\n\t\t\t   data: this.ruleForm\r\n\t\t\t}).then(({ data }) => {\r\n\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\tmessage: \"操作成功\",\r\n\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\tduration: 1500,\r\n\t\t\t\t\t\tonClose: () => {\r\n\t\t\t\t\t\t\tthis.parent.showFlag = true;\r\n\t\t\t\t\t\t\tthis.parent.addOrUpdateFlag = false;\r\n\t\t\t\t\t\t\tthis.parent.yuangongdanganCrossAddOrUpdateFlag = false;\r\n\t\t\t\t\t\t\tthis.parent.search();\r\n\t\t\t\t\t\t\tthis.parent.contentStyleChange();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t   }\r\n\t\t\t});\r\n\t\t }\r\n         }\r\n       });\r\n    },\r\n    // 获取uuid\r\n    getUUID () {\r\n      return new Date().getTime();\r\n    },\r\n    // 返回\r\n    back() {\r\n      this.parent.showFlag = true;\r\n      this.parent.addOrUpdateFlag = false;\r\n      this.parent.yuangongdanganCrossAddOrUpdateFlag = false;\r\n      this.parent.contentStyleChange();\r\n    },\r\n    yuangongdanganUploadChange(fileUrls) {\r\n\t    this.ruleForm.yuangongdangan = fileUrls;\r\n    },\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\t.amap-wrapper {\r\n\t\twidth: 100%;\r\n\t\theight: 500px;\r\n\t}\r\n\t\r\n\t.search-box {\r\n\t\tposition: absolute;\r\n\t}\r\n\t\r\n\t.el-date-editor.el-input {\r\n\t\twidth: auto;\r\n\t}\r\n\t\r\n\t.add-update-preview .el-form-item /deep/ .el-form-item__label {\r\n\t  \t  padding: 0 10px 0 0;\r\n\t  \t  color: #374254;\r\n\t  \t  font-weight: 600;\r\n\t  \t  width: 80px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  line-height: 40px;\r\n\t  \t  text-align: right;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .el-form-item /deep/ .el-form-item__content {\r\n\t  margin-left: 80px;\r\n\t}\r\n\t\r\n\t.add-update-preview .el-input /deep/ .el-input__inner {\r\n\t  \t  border: 1px solid rgba(167, 180, 201,.3);\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 12px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t.add-update-preview .el-input-number /deep/ .el-input__inner {\r\n\t\ttext-align: left;\r\n\t  \t  border: 1px solid rgba(167, 180, 201,.3);\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 12px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t.add-update-preview .el-input-number /deep/ .el-input-number__decrease {\r\n\t\tdisplay: none;\r\n\t}\r\n\t.add-update-preview .el-input-number /deep/ .el-input-number__increase {\r\n\t\tdisplay: none;\r\n\t}\r\n\t\r\n\t.add-update-preview .el-select /deep/ .el-input__inner {\r\n\t  \t  border:  1px solid rgba(167, 180, 201,.3) ;\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 10px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .el-date-editor /deep/ .el-input__inner {\r\n\t  \t  border:  1px solid rgba(167, 180, 201,.3) ;\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 10px 0 30px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview /deep/ .el-upload--picture-card {\r\n\t\tbackground: transparent;\r\n\t\tborder: 0;\r\n\t\tborder-radius: 0;\r\n\t\twidth: auto;\r\n\t\theight: auto;\r\n\t\tline-height: initial;\r\n\t\tvertical-align: middle;\r\n\t}\r\n\t\r\n\t.add-update-preview /deep/ .upload .upload-img {\r\n\t  \t  border: 2px dashed rgba(167, 180, 201,.3) ;\r\n\t  \t  cursor: pointer;\r\n\t  \t  border-radius: 6px;\r\n\t  \t  color: #a7b4c9  ;\r\n\t  \t  width: 200px;\r\n\t  \t  font-size: 32px;\r\n\t  \t  line-height: 200px;\r\n\t  \t  text-align: center;\r\n\t  \t  height: 200px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview /deep/ .el-upload-list .el-upload-list__item {\r\n\t  \t  border: 2px dashed rgba(167, 180, 201,.3) ;\r\n\t  \t  cursor: pointer;\r\n\t  \t  border-radius: 6px;\r\n\t  \t  color: #a7b4c9  ;\r\n\t  \t  width: 200px;\r\n\t  \t  font-size: 32px;\r\n\t  \t  line-height: 200px;\r\n\t  \t  text-align: center;\r\n\t  \t  height: 200px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview /deep/ .el-upload .el-icon-plus {\r\n\t  \t  border: 2px dashed rgba(167, 180, 201,.3) ;\r\n\t  \t  cursor: pointer;\r\n\t  \t  border-radius: 6px;\r\n\t  \t  color: #a7b4c9  ;\r\n\t  \t  width: 200px;\r\n\t  \t  font-size: 32px;\r\n\t  \t  line-height: 200px;\r\n\t  \t  text-align: center;\r\n\t  \t  height: 200px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .el-textarea /deep/ .el-textarea__inner {\r\n\t  \t  border: 1px solid rgba(167, 180, 201,.3)     ;\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 12px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9 ;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 120px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .btn .btn1 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #6574cd;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn1:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn2 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #6574cd;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn2:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn3 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #ff2b88;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn3:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn4 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #ff2b88;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn4:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn5 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #ff2b88;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn5:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n</style>\r\n"]}]}