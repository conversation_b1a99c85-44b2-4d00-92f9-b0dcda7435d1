{"remainingRequest": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\src\\components\\common\\ExcelFileUpload.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\src\\components\\common\\ExcelFileUpload.vue", "mtime": 1711079474000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ExcelFileUpload.vue"], "names": [], "mappings": ";AA2BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ExcelFileUpload.vue", "sourceRoot": "src/components/common", "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- 上传文件组件 -->\r\n    <el-upload\r\n      ref=\"upload\"\r\n      :action=\"getActionUrl\"\r\n      list-type=\"picture-card\"\r\n      accept=\".xls,.xlsx\"\r\n      :limit=\"limit\"\r\n      :headers=\"myHeaders\"\r\n      :on-exceed=\"handleExceed\"\r\n      :on-preview=\"handleUploadPreview\"\r\n      :on-remove=\"handleRemove\"\r\n      :on-success=\"handleUploadSuccess\"\r\n      :on-error=\"handleUploadErr\"\r\n      :before-upload=\"handleBeforeUpload\"\r\n      :show-file-list=\"false\"\r\n    >\r\n      <i class=\"el-icon-plus\"></i>\r\n      <div slot=\"tip\" class=\"el-upload__tip\" style=\"color:#838fa1;\">{{tip}}</div>\r\n    </el-upload>\r\n    <el-dialog :visible.sync=\"dialogVisible\" size=\"tiny\" append-to-body>\r\n      <img width=\"100%\" :src=\"dialogImageUrl\" alt>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n<script>\r\nimport storage from \"@/utils/storage\";\r\nimport base from \"@/utils/base\";\r\nexport default {\r\n  data() {\r\n    return {\r\n      // 查看大图\r\n      dialogVisible: false,\r\n      // 查看大图\r\n      dialogImageUrl: \"\",\r\n      // 组件渲染图片的数组字段，有特殊格式要求\r\n      fileList: [],\r\n      fileUrlList: [],\r\n      myHeaders:{}\r\n    };\r\n  },\r\n  props: [\"tip\", \"action\", \"limit\", \"multiple\", \"fileUrls\"],\r\n  mounted() {\r\n    this.init();\r\n    this.myHeaders= {\r\n      'Token':storage.get(\"Token\")\r\n    }\r\n  },\r\n  watch: {\r\n    fileUrls: function(val, oldVal) {\r\n      //   console.log(\"new: %s, old: %s\", val, oldVal);\r\n      this.init();\r\n    }\r\n  },\r\n  computed: {\r\n    // 计算属性的 getter\r\n    getActionUrl: function() {\r\n\t\tconsole.log(123)\r\n\t\tthis.fileList = []\r\n      // return base.url + this.action + \"?token=\" + storage.get(\"token\");\r\n      return `/${this.$base.name}/` + this.action;\r\n    }\r\n  },\r\n  methods: {\r\n    // 初始化\r\n    init() {\r\n      //   console.log(this.fileUrls);\r\n      if (this.fileUrls) {\r\n        this.fileUrlList = this.fileUrls.split(\",\");\r\n        let fileArray = [];\r\n        this.fileUrlList.forEach(function(item, index) {\r\n          var url = item;\r\n          var name = index;\r\n          var file = {\r\n            name: name,\r\n            url: url\r\n          };\r\n          fileArray.push(file);\r\n        });\r\n        this.setFileList(fileArray);\r\n      }\r\n    },\r\n    handleBeforeUpload(file) {\r\n    \r\n    },\r\n    // 上传文件成功后执行\r\n    handleUploadSuccess(res, file, fileList) {\r\n      if (res && res.code === 0) {\r\n        fileList[fileList.length - 1][\"url\"] = \"upload/\" + file.response.file;\r\n        this.setFileList(fileList);\r\n        this.$emit(\"change\", this.fileUrlList.join(\",\"));\r\n        this.$message.success(\"文件导入成功\");\r\n      } else {\r\n        this.$message.error(res.msg);\r\n      }\r\n    },\r\n    // 图片上传失败\r\n    handleUploadErr(err, file, fileList) {\r\n      this.$message.error(\"文件导入失败\");\r\n    },\r\n    // 移除图片\r\n    handleRemove(file, fileList) {\r\n      this.setFileList(fileList);\r\n      this.$emit(\"change\", this.fileUrlList.join(\",\"));\r\n    },\r\n    // 查看大图\r\n    handleUploadPreview(file) {\r\n      this.dialogImageUrl = file.url;\r\n      this.dialogVisible = true;\r\n    },\r\n    // 限制图片数量\r\n    handleExceed(files, fileList) {\r\n      this.$message.warning(`最多上传${this.limit}张图片`);\r\n    },\r\n    // 重新对fileList进行赋值\r\n    setFileList(fileList) {\r\n      var fileArray = [];\r\n      var fileUrlArray = [];\r\n      // 有些图片不是公开的，所以需要携带token信息做权限校验\r\n      var token = storage.get(\"token\");\r\n      let _this = this;\r\n      fileList.forEach(function(item, index) {\r\n        var url = item.url.split(\"?\")[0];\r\n    if(!url.startsWith(\"http\")) {\r\n      url = _this.$base.url+url\r\n    }\r\n        var name = item.name;\r\n        var file = {\r\n          name: name,\r\n          url: url + \"?token=\" + token\r\n        };\r\n        fileArray.push(file);\r\n        fileUrlArray.push(url);\r\n      });\r\n      this.fileList = fileArray;\r\n      this.fileUrlList = fileUrlArray;\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n</style>\r\n\r\n"]}]}