{"remainingRequest": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\src\\views\\update-password.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\src\\views\\update-password.vue", "mtime": 1711079474000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["update-password.vue"], "names": [], "mappings": ";AA6BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "update-password.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div :style='{\"padding\":\"30px\",\"margin\":\"0\"}'>\r\n    <el-form\r\n\t  :style='{\"border\":\"1px solid rgba(167, 180, 201,.3)  \",\"padding\":\"30px\",\"borderRadius\":\"6px\",\"background\":\"#fff\"}'\r\n      class=\"add-update-preview\"\r\n      ref=\"ruleForm\"\r\n      :rules=\"rules\"\r\n      :model=\"ruleForm\"\r\n      label-width=\"80px\"\r\n    >\r\n      <el-form-item :style='{\"margin\":\"0 0 20px 0\"}' label=\"原密码\" prop=\"password\">\r\n        <el-input v-model=\"ruleForm.password\" show-password></el-input>\r\n      </el-form-item>\r\n      <el-form-item :style='{\"margin\":\"0 0 20px 0\"}' label=\"新密码\" prop=\"newpassword\">\r\n        <el-input v-model=\"ruleForm.newpassword\" show-password></el-input>\r\n      </el-form-item>\r\n      <el-form-item :style='{\"margin\":\"0 0 20px 0\"}' label=\"确认密码\" prop=\"repassword\">\r\n        <el-input v-model=\"ruleForm.repassword\" show-password></el-input>\r\n      </el-form-item>\r\n      <el-form-item :style='{\"padding\":\"0\",\"margin\":\"0\"}'>\r\n\t\t<el-button class=\"btn3\" :style='{\"border\":\"0\",\"cursor\":\"pointer\",\"padding\":\"0 24px\",\"margin\":\"4px\",\"outline\":\"none\",\"color\":\"#fff\",\"borderRadius\":\"4px\",\"background\":\"#ff2b88\",\"width\":\"auto\",\"fontSize\":\"14px\",\"height\":\"40px\"}' type=\"primary\" @click=\"onUpdateHandler\">\r\n\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"margin\":\"0 2px\",\"fontSize\":\"14px\",\"color\":\"#fff\",\"height\":\"40px\"}'></span>\r\n\t\t\t提交\r\n\t\t</el-button>\r\n\t  </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tdialogVisible: false,\r\n\t\t\truleForm: {},\r\n\t\t\tuser: {},\r\n\t\t\trules: {\r\n\t\t\t\tpassword: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\tmessage: \"密码不能为空\",\r\n\t\t\t\t\t\ttrigger: \"blur\"\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\tnewpassword: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\tmessage: \"新密码不能为空\",\r\n\t\t\t\t\t\ttrigger: \"blur\"\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\trepassword: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\tmessage: \"确认密码不能为空\",\r\n\t\t\t\t\t\ttrigger: \"blur\"\r\n\t\t\t\t\t}\r\n\t\t\t\t]\r\n\t\t\t}\r\n\t\t};\r\n\t},\r\n\tmounted() {\r\n\t\tthis.$http({\r\n\t\t\turl: `${this.$storage.get(\"sessionTable\")}/session`,\r\n\t\t\tmethod: \"get\"\r\n\t\t}).then(({ data }) => {\r\n\t\t\tif (data && data.code === 0) {\r\n\t\t\t\tthis.user = data.data;\r\n\t\t\t} else {\r\n\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t}\r\n\t\t});\r\n\t},\r\n\tmethods: {\r\n\t\tonLogout() {\r\n\t\t\tthis.$storage.remove(\"Token\");\r\n\t\t\tthis.$router.replace({ name: \"login\" });\r\n\t\t},\r\n\t\t// 修改密码\r\n\t\tasync onUpdateHandler() {\r\n\t\t\tthis.$refs[\"ruleForm\"].validate(async valid => {\r\n\t\t\t\tif (valid) {\r\n\t\t\t\t\tvar password = \"\";\r\n\t\t\t\t\tif (this.user.mima) {\r\n\t\t\t\t\t\tpassword = this.user.mima;\r\n\t\t\t\t\t} else if (this.user.password) {\r\n\t\t\t\t\t\tpassword = this.user.password;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(this.$storage.get(\"sessionTable\")=='users'){\r\n\t\t\t\t\t\tif (this.ruleForm.password != password) {\r\n\t\t\t\t\t\t\tthis.$message.error(\"原密码错误\");\r\n\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (this.ruleForm.newpassword != this.ruleForm.repassword) {\r\n\t\t\t\t\t\t\tthis.$message.error(\"两次密码输入不一致\");\r\n\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.user.password = this.ruleForm.newpassword;\r\n\t\t\t\t\t\tthis.user.mima = this.ruleForm.newpassword;\r\n\t\t\t\t\t\tthis.$http({\r\n\t\t\t\t\t\t\turl: `${this.$storage.get(\"sessionTable\")}/update`,\r\n\t\t\t\t\t\t\tmethod: \"post\",\r\n\t\t\t\t\t\t\tdata: this.user\r\n\t\t\t\t\t\t}).then(({ data }) => {\r\n\t\t\t\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\t\t\tmessage: \"修改密码成功,下次登录系统生效\",\r\n\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\tduration: 1500,\r\n\t\t\t\t\t\t\t\t\tonClose: () => {\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\treturn false\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (this.ruleForm.password != password) {\r\n\t\t\t\t\t\tthis.$message.error(\"原密码错误\");\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (this.ruleForm.newpassword != this.ruleForm.repassword) {\r\n\t\t\t\t\t\tthis.$message.error(\"两次密码输入不一致\");\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (this.$storage.get(\"sessionTable\") == 'yuangong') {\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.user.password = this.ruleForm.newpassword;\r\n\t\t\t\t\tthis.user.mima = this.ruleForm.newpassword;\r\n\t\t\t\t\tthis.$http({\r\n\t\t\t\t\t\turl: `${this.$storage.get(\"sessionTable\")}/update`,\r\n\t\t\t\t\t\tmethod: \"post\",\r\n\t\t\t\t\t\tdata: this.user\r\n\t\t\t\t\t}).then(({ data }) => {\r\n\t\t\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\t\tmessage: \"修改密码成功,下次登录系统生效\",\r\n\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\tduration: 1500,\r\n\t\t\t\t\t\t\t\tonClose: () => {\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\t.el-date-editor.el-input {\r\n\t\twidth: auto;\r\n\t}\r\n\t\r\n\t.add-update-preview .el-form-item /deep/ .el-form-item__label {\r\n\t  \t  padding: 0 10px 0 0;\r\n\t  \t  color: #374254;\r\n\t  \t  font-weight: 600;\r\n\t  \t  width: 80px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  line-height: 40px;\r\n\t  \t  text-align: right;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .el-form-item /deep/ .el-form-item__content {\r\n\t  margin-left: 80px;\r\n\t}\r\n\t\r\n\t.add-update-preview .el-input /deep/ .el-input__inner {\r\n\t  \t  border: 1px solid rgba(167, 180, 201,.3);\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 12px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .el-select /deep/ .el-input__inner {\r\n\t  \t  border:  1px solid rgba(167, 180, 201,.3) ;\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 10px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .el-date-editor /deep/ .el-input__inner {\r\n\t  \t  border:  1px solid rgba(167, 180, 201,.3) ;\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 10px 0 30px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview /deep/ .el-upload--picture-card {\r\n\t\tbackground: transparent;\r\n\t\tborder: 0;\r\n\t\tborder-radius: 0;\r\n\t\twidth: auto;\r\n\t\theight: auto;\r\n\t\tline-height: initial;\r\n\t\tvertical-align: middle;\r\n\t}\r\n\t\r\n\t.add-update-preview /deep/ .el-upload-list .el-upload-list__item {\r\n\t  \t  border: 2px dashed rgba(167, 180, 201,.3) ;\r\n\t  \t  cursor: pointer;\r\n\t  \t  border-radius: 6px;\r\n\t  \t  color: #a7b4c9  ;\r\n\t  \t  width: 200px;\r\n\t  \t  font-size: 32px;\r\n\t  \t  line-height: 200px;\r\n\t  \t  text-align: center;\r\n\t  \t  height: 200px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview /deep/ .el-upload .el-icon-plus {\r\n\t  \t  border: 2px dashed rgba(167, 180, 201,.3) ;\r\n\t  \t  cursor: pointer;\r\n\t  \t  border-radius: 6px;\r\n\t  \t  color: #a7b4c9  ;\r\n\t  \t  width: 200px;\r\n\t  \t  font-size: 32px;\r\n\t  \t  line-height: 200px;\r\n\t  \t  text-align: center;\r\n\t  \t  height: 200px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .el-textarea /deep/ .el-textarea__inner {\r\n\t  \t  border: 1px solid rgba(167, 180, 201,.3)     ;\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 12px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9 ;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 120px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .btn3 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #ff2b88;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn3:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n</style>\r\n"]}]}