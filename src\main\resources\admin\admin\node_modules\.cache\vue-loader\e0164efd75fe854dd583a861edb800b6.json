{"remainingRequest": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\src\\views\\home.vue?vue&type=style&index=0&id=7eb2bc79&lang=scss&scoped=true", "dependencies": [{"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\src\\views\\home.vue", "mtime": 1755434670143}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["home.vue"], "names": [], "mappings": ";AAstBA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "home.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n<div class=\"content\" :style='{\"padding\":\"30px\"}'>\r\n\t<!-- notice -->\r\n\t<!-- title -->\r\n\t<div class=\"text\" :style='{\"margin\":\"20px auto\",\"fontSize\":\"24px\",\"color\":\" #374254\",\"textAlign\":\"center\",\"fontWeight\":\"bold\"}'>欢迎使用 {{this.$project.projectName}}</div>\r\n\t<!-- statis -->\r\n\t<div :style='{\"width\":\"100%\",\"margin\":\"0 0 20px 0\",\"alignItems\":\"center\",\"flexWrap\":\"wrap\",\"justifyContent\":\"center\",\"display\":\"flex\"}'>\r\n\t\t<div :style='{\"border\":\" 1px solid rgba(167, 180, 201,.3)    \",\"margin\":\"0 10px\",\"borderRadius\":\"4px\",\"background\":\"#fff\",\"display\":\"flex\"}' v-if=\"isAuth('caiwuxinxi','首页总数')\">\r\n\t\t\t<div :style='{\"alignItems\":\"center\",\"background\":\"#00e682\",\"display\":\"flex\",\"width\":\"80px\",\"justifyContent\":\"center\",\"height\":\"80px\",\"order\":\"2\"}'>\r\n\t\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"color\":\"#fff\",\"fontSize\":\"24px\"}'></span>\r\n\t\t\t</div>\r\n\t\t\t<div :style='{\"width\":\"120px\",\"alignItems\":\"center\",\"flexDirection\":\"column\",\"justifyContent\":\"center\",\"display\":\"flex\",\"order\":\"1\"}'>\r\n\t\t\t\t<div :style='{\"margin\":\"5px 0\",\"lineHeight\":\"24px\",\"fontSize\":\"20px\",\"color\":\"1\",\"fontWeight\":\"bold\",\"height\":\"24px\"}'>{{caiwuxinxiCount}}</div>\r\n\t\t\t\t<div :style='{\"margin\":\"5px 0\",\"lineHeight\":\"24px\",\"fontSize\":\"14px\",\"color\":\" #a3b1c9\",\"height\":\"24px\"}'>财物信息总数</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t</div>\r\n\t<!-- statis -->\r\n\t\r\n\r\n\t\r\n\t<!-- echarts -->\r\n\t<!-- 3 -->\r\n\t<div class=\"type3\" :style='{\"alignContent\":\"flex-start\",\"padding\":\"10px 20px\",\"flexWrap\":\"wrap\",\"background\":\"rebeccapurple\",\"display\":\"flex\",\"width\":\"100%\",\"position\":\"relative\",\"justifyContent\":\"space-between\",\"height\":\"auto\"}'>\r\n\t\t<div id=\"caiwuxinxiChart1\" class=\"echarts1\" v-if=\"isAuth('caiwuxinxi','首页统计')\"></div>\r\n\t\t<div id=\"caiwuxinxiChart2\" class=\"echarts2\" v-if=\"isAuth('caiwuxinxi','首页统计')\"></div>\r\n\t\t<div id=\"caiwuxinxiChart3\" class=\"echarts3\" v-if=\"isAuth('caiwuxinxi','首页统计')\"></div>\r\n\t</div>\r\n</div>\r\n</template>\r\n<script>\r\n//3\r\nimport router from '@/router/router-static'\r\nimport * as echarts from 'echarts'\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n            caiwuxinxiCount: 0,\r\n\t\t\tline: {\"backgroundColor\":\"transparent\",\"yAxis\":{\"axisLabel\":{\"borderType\":\"solid\",\"rotate\":0,\"padding\":0,\"shadowOffsetX\":0,\"margin\":15,\"backgroundColor\":\"transparent\",\"borderColor\":\"#000\",\"shadowOffsetY\":0,\"color\":\"#fff\",\"shadowBlur\":0,\"show\":true,\"inside\":false,\"ellipsis\":\"...\",\"overflow\":\"none\",\"borderRadius\":0,\"borderWidth\":0,\"width\":\"\",\"fontSize\":12,\"lineHeight\":24,\"shadowColor\":\"transparent\",\"fontWeight\":\"normal\",\"height\":\"\"},\"axisTick\":{\"show\":true,\"length\":5,\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"cap\":\"butt\",\"color\":\"#fff\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"rgba(0,0,0,.5)\"},\"inside\":false},\"splitLine\":{\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"cap\":\"butt\",\"color\":\"#ccc\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"rgba(0,0,0,.5)\"},\"show\":true},\"axisLine\":{\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"cap\":\"butt\",\"color\":\"#fff\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"rgba(0,0,0,.5)\"},\"show\":true},\"splitArea\":{\"show\":false,\"areaStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"color\":\"rgba(250,250,250,0.3)\",\"opacity\":1,\"shadowBlur\":10,\"shadowColor\":\"rgba(0,0,0,.5)\"}}},\"xAxis\":{\"axisLabel\":{\"borderType\":\"solid\",\"rotate\":0,\"padding\":0,\"shadowOffsetX\":0,\"margin\":4,\"backgroundColor\":\"transparent\",\"borderColor\":\"#000\",\"shadowOffsetY\":0,\"color\":\"#fff\",\"shadowBlur\":0,\"show\":true,\"inside\":false,\"ellipsis\":\"...\",\"overflow\":\"none\",\"borderRadius\":0,\"borderWidth\":0,\"width\":\"\",\"fontSize\":12,\"lineHeight\":24,\"shadowColor\":\"transparent\",\"fontWeight\":\"normal\",\"height\":\"\"},\"axisTick\":{\"show\":true,\"length\":5,\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"cap\":\"butt\",\"color\":\"#fff\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"rgba(0,0,0,.5)\"},\"inside\":false},\"splitLine\":{\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"cap\":\"butt\",\"color\":\"#fff\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"rgba(0,0,0,.5)\"},\"show\":false},\"axisLine\":{\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"cap\":\"butt\",\"color\":\"#fff\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"rgba(0,0,0,.5)\"},\"show\":true},\"splitArea\":{\"show\":false,\"areaStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"color\":\"rgba(255,255,255,.3)\",\"opacity\":1,\"shadowBlur\":10,\"shadowColor\":\"rgba(0,0,0,.5)\"}}},\"color\":[\"#5470c6\",\"#91cc75\",\"#fac858\",\"#ee6666\",\"#73c0de\",\"#3ba272\",\"#fc8452\",\"#9a60b4\",\"#ea7ccc\"],\"legend\":{\"padding\":5,\"itemGap\":10,\"shadowOffsetX\":0,\"backgroundColor\":\"transparent\",\"borderColor\":\"#ccc\",\"shadowOffsetY\":0,\"orient\":\"horizontal\",\"shadowBlur\":0,\"bottom\":\"auto\",\"itemHeight\":14,\"show\":true,\"icon\":\"roundRect\",\"itemStyle\":{\"borderType\":\"solid\",\"shadowOffsetX\":0,\"borderColor\":\"inherit\",\"shadowOffsetY\":0,\"color\":\"rgb(255,255,255)\",\"shadowBlur\":0,\"borderWidth\":0,\"opacity\":1,\"shadowColor\":\"transparent\"},\"right\":\"auto\",\"top\":\"auto\",\"borderRadius\":0,\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"color\":\"inherit\",\"shadowBlur\":0,\"width\":\"auto\",\"type\":\"inherit\",\"opacity\":1,\"shadowColor\":\"transparent\"},\"left\":\"right\",\"borderWidth\":0,\"width\":\"auto\",\"itemWidth\":25,\"textStyle\":{\"textBorderWidth\":0,\"color\":\"#fff\",\"textShadowColor\":\"transparent\",\"ellipsis\":\"...\",\"overflow\":\"none\",\"fontSize\":12,\"lineHeight\":24,\"textShadowOffsetX\":0,\"textShadowOffsetY\":0,\"textBorderType\":\"solid\",\"fontWeight\":500,\"textBorderColor\":\"transparent\",\"textShadowBlur\":0},\"shadowColor\":\"rgba(0,0,0,.3)\",\"height\":\"auto\"},\"series\":{\"symbol\":\"emptyCircle\",\"itemStyle\":{\"borderType\":\"solid\",\"shadowOffsetX\":0,\"borderColor\":\"#fff\",\"shadowOffsetY\":0,\"color\":\"\",\"shadowBlur\":0,\"borderWidth\":0,\"opacity\":1,\"shadowColor\":\"#000\"},\"showSymbol\":true,\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"color\":\"\",\"shadowBlur\":0,\"width\":2,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"#000\"},\"symbolSize\":4},\"tooltip\":{\"backgroundColor\":\"#123\",\"textStyle\":{\"color\":\"#fff\"}},\"title\":{\"borderType\":\"solid\",\"padding\":2,\"shadowOffsetX\":0,\"backgroundColor\":\"transparent\",\"borderColor\":\"#ccc\",\"shadowOffsetY\":0,\"shadowBlur\":0,\"bottom\":\"auto\",\"show\":true,\"right\":\"auto\",\"top\":\"auto\",\"borderRadius\":0,\"left\":\"left\",\"borderWidth\":0,\"textStyle\":{\"textBorderWidth\":0,\"color\":\"#fff\",\"textShadowColor\":\"transparent\",\"fontSize\":14,\"lineHeight\":24,\"textShadowOffsetX\":0,\"textShadowOffsetY\":0,\"textBorderType\":\"solid\",\"fontWeight\":500,\"textBorderColor\":\"#ccc\",\"textShadowBlur\":0},\"shadowColor\":\"transparent\"}},\r\n\t\t\tbar: {\"backgroundColor\":\"transparent\",\"yAxis\":{\"axisLabel\":{\"borderType\":\"solid\",\"rotate\":0,\"padding\":0,\"shadowOffsetX\":0,\"margin\":12,\"backgroundColor\":\"transparent\",\"borderColor\":\"#000\",\"shadowOffsetY\":0,\"color\":\"#fff\",\"shadowBlur\":0,\"show\":true,\"inside\":false,\"ellipsis\":\"...\",\"overflow\":\"none\",\"borderRadius\":0,\"borderWidth\":0,\"width\":\"\",\"fontSize\":12,\"lineHeight\":24,\"shadowColor\":\"transparent\",\"fontWeight\":\"normal\",\"height\":\"\"},\"axisTick\":{\"show\":true,\"length\":5,\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"cap\":\"butt\",\"color\":\"#fff\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"rgba(0,0,0,.5)\"},\"inside\":false},\"splitLine\":{\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"cap\":\"butt\",\"color\":\"#ccc\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"rgba(0,0,0,.5)\"},\"show\":true},\"axisLine\":{\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"cap\":\"butt\",\"color\":\"#fff\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"rgba(0,0,0,.5)\"},\"show\":true},\"splitArea\":{\"show\":false,\"areaStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"color\":\"rgba(250,250,250,0.3)\",\"opacity\":1,\"shadowBlur\":10,\"shadowColor\":\"rgba(0,0,0,.5)\"}}},\"xAxis\":{\"axisLabel\":{\"borderType\":\"solid\",\"rotate\":0,\"padding\":0,\"shadowOffsetX\":0,\"margin\":4,\"backgroundColor\":\"transparent\",\"borderColor\":\"#000\",\"shadowOffsetY\":0,\"color\":\"#fff\",\"shadowBlur\":0,\"show\":true,\"inside\":false,\"ellipsis\":\"...\",\"overflow\":\"none\",\"borderRadius\":0,\"borderWidth\":0,\"width\":\"\",\"fontSize\":12,\"lineHeight\":24,\"shadowColor\":\"transparent\",\"fontWeight\":\"normal\",\"height\":\"\"},\"axisTick\":{\"show\":true,\"length\":5,\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"cap\":\"butt\",\"color\":\"#fff\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"rgba(0,0,0,.5)\"},\"inside\":false},\"splitLine\":{\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"cap\":\"butt\",\"color\":\"#fff\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"rgba(0,0,0,.5)\"},\"show\":false},\"axisLine\":{\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"cap\":\"butt\",\"color\":\"#fff\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"rgba(0,0,0,.5)\"},\"show\":true},\"splitArea\":{\"show\":false,\"areaStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"color\":\"rgba(255,255,255,.3)\",\"opacity\":1,\"shadowBlur\":10,\"shadowColor\":\"rgba(0,0,0,.5)\"}}},\"color\":[\"#00ff00\",\"#91cc75\",\"#fac858\",\"#ee6666\",\"#73c0de\",\"#3ba272\",\"#fc8452\",\"#9a60b4\",\"#ea7ccc\"],\"legend\":{\"padding\":5,\"itemGap\":10,\"shadowOffsetX\":0,\"backgroundColor\":\"transparent\",\"borderColor\":\"#ccc\",\"shadowOffsetY\":0,\"orient\":\"horizontal\",\"shadowBlur\":0,\"bottom\":\"auto\",\"itemHeight\":14,\"show\":true,\"icon\":\"roundRect\",\"itemStyle\":{\"borderType\":\"solid\",\"shadowOffsetX\":0,\"borderColor\":\"inherit\",\"shadowOffsetY\":0,\"color\":\"rgb(255,255,255)\",\"shadowBlur\":0,\"borderWidth\":0,\"opacity\":1,\"shadowColor\":\"transparent\"},\"right\":\"auto\",\"top\":\"auto\",\"borderRadius\":0,\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"color\":\"inherit\",\"shadowBlur\":0,\"width\":\"auto\",\"type\":\"inherit\",\"opacity\":1,\"shadowColor\":\"transparent\"},\"left\":\"right\",\"borderWidth\":0,\"width\":\"auto\",\"itemWidth\":25,\"textStyle\":{\"textBorderWidth\":0,\"color\":\"#fff\",\"textShadowColor\":\"transparent\",\"ellipsis\":\"...\",\"overflow\":\"none\",\"fontSize\":12,\"lineHeight\":24,\"textShadowOffsetX\":0,\"textShadowOffsetY\":0,\"textBorderType\":\"solid\",\"fontWeight\":500,\"textBorderColor\":\"transparent\",\"textShadowBlur\":0},\"shadowColor\":\"rgba(0,0,0,.3)\",\"height\":\"auto\"},\"series\":{\"barWidth\":\"auto\",\"itemStyle\":{\"borderType\":\"solid\",\"shadowOffsetX\":0,\"borderColor\":\"#fff\",\"shadowOffsetY\":0,\"color\":\"\",\"shadowBlur\":0,\"borderWidth\":0,\"opacity\":1,\"shadowColor\":\"#000\"},\"colorBy\":\"data\",\"barCategoryGap\":\"20%\"},\"tooltip\":{\"backgroundColor\":\"#123\",\"textStyle\":{\"color\":\"#fff\"}},\"title\":{\"borderType\":\"solid\",\"padding\":2,\"shadowOffsetX\":0,\"backgroundColor\":\"transparent\",\"borderColor\":\"#ccc\",\"shadowOffsetY\":0,\"shadowBlur\":0,\"bottom\":\"auto\",\"show\":true,\"right\":\"auto\",\"top\":\"auto\",\"borderRadius\":0,\"left\":\"left\",\"borderWidth\":0,\"textStyle\":{\"textBorderWidth\":0,\"color\":\"#fff\",\"textShadowColor\":\"transparent\",\"fontSize\":14,\"lineHeight\":24,\"textShadowOffsetX\":0,\"textShadowOffsetY\":0,\"textBorderType\":\"solid\",\"fontWeight\":500,\"textBorderColor\":\"#ccc\",\"textShadowBlur\":0},\"shadowColor\":\"transparent\"},\"base\":{\"animate\":false,\"interval\":2000}},\r\n\t\t\tpie: {\"tooltip\":{\"backgroundColor\":\"#123\",\"textStyle\":{\"color\":\"#fff\"}},\"backgroundColor\":\"transparent\",\"color\":[\"#5470c6\",\"#91cc75\",\"#fac858\",\"#ee6666\",\"#73c0de\",\"#3ba272\",\"#fc8452\",\"#9a60b4\",\"#ea7ccc\"],\"title\":{\"borderType\":\"solid\",\"padding\":2,\"shadowOffsetX\":0,\"backgroundColor\":\"transparent\",\"borderColor\":\"#ccc\",\"shadowOffsetY\":0,\"shadowBlur\":0,\"bottom\":\"auto\",\"show\":true,\"right\":\"auto\",\"top\":\"auto\",\"borderRadius\":0,\"left\":\"left\",\"borderWidth\":0,\"textStyle\":{\"textBorderWidth\":0,\"color\":\"#fff\",\"textShadowColor\":\"transparent\",\"fontSize\":14,\"lineHeight\":24,\"textShadowOffsetX\":0,\"textShadowOffsetY\":0,\"textBorderType\":\"solid\",\"fontWeight\":500,\"textBorderColor\":\"#ccc\",\"textShadowBlur\":0},\"shadowColor\":\"transparent\"},\"legend\":{\"padding\":5,\"itemGap\":10,\"shadowOffsetX\":0,\"backgroundColor\":\"transparent\",\"borderColor\":\"#ccc\",\"shadowOffsetY\":0,\"orient\":\"horizontal\",\"shadowBlur\":0,\"bottom\":\"auto\",\"itemHeight\":14,\"show\":true,\"icon\":\"roundRect\",\"itemStyle\":{\"borderType\":\"solid\",\"shadowOffsetX\":0,\"borderColor\":\"inherit\",\"shadowOffsetY\":0,\"color\":\"inherit\",\"shadowBlur\":0,\"borderWidth\":0,\"opacity\":1,\"shadowColor\":\"transparent\"},\"right\":\"auto\",\"top\":\"auto\",\"borderRadius\":0,\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"color\":\"inherit\",\"shadowBlur\":0,\"width\":\"auto\",\"type\":\"inherit\",\"opacity\":1,\"shadowColor\":\"transparent\"},\"left\":\"right\",\"borderWidth\":0,\"width\":\"auto\",\"itemWidth\":25,\"textStyle\":{\"textBorderWidth\":0,\"color\":\"#fff\",\"textShadowColor\":\"transparent\",\"ellipsis\":\"...\",\"overflow\":\"none\",\"fontSize\":12,\"lineHeight\":24,\"textShadowOffsetX\":0,\"textShadowOffsetY\":0,\"textBorderType\":\"solid\",\"fontWeight\":500,\"textBorderColor\":\"transparent\",\"textShadowBlur\":0},\"shadowColor\":\"rgba(0,0,0,.3)\",\"height\":\"auto\"},\"series\":{\"itemStyle\":{\"borderType\":\"solid\",\"shadowOffsetX\":0,\"borderColor\":\"#fff\",\"shadowOffsetY\":0,\"color\":\"\",\"shadowBlur\":0,\"borderWidth\":0,\"opacity\":1,\"shadowColor\":\"#000\"},\"label\":{\"borderType\":\"solid\",\"rotate\":0,\"padding\":0,\"textBorderWidth\":0,\"backgroundColor\":\"transparent\",\"borderColor\":\"#fff\",\"color\":\"#fff\",\"show\":true,\"textShadowColor\":\"transparent\",\"distanceToLabelLine\":5,\"ellipsis\":\"...\",\"overflow\":\"none\",\"borderRadius\":0,\"borderWidth\":0,\"fontSize\":12,\"lineHeight\":18,\"textShadowOffsetX\":0,\"position\":\"outside\",\"textShadowOffsetY\":0,\"textBorderType\":\"solid\",\"textBorderColor\":\"#fff\",\"textShadowBlur\":0},\"labelLine\":{\"show\":true,\"length\":10,\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"color\":\"#fff\",\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"#000\"},\"length2\":14,\"smooth\":false}}},\r\n\t\t\tfunnel: {\"tooltip\":{\"backgroundColor\":\"#123\",\"textStyle\":{\"color\":\"#fff\"}},\"backgroundColor\":\"transparent\",\"color\":[\"#5470c6\",\"#91cc75\",\"#fac858\",\"#ee6666\",\"#73c0de\",\"#3ba272\",\"#fc8452\",\"#9a60b4\",\"#ea7ccc\"],\"title\":{\"borderType\":\"solid\",\"padding\":2,\"shadowOffsetX\":0,\"backgroundColor\":\"transparent\",\"borderColor\":\"#ccc\",\"shadowOffsetY\":0,\"shadowBlur\":0,\"bottom\":\"auto\",\"show\":true,\"right\":\"auto\",\"top\":\"auto\",\"borderRadius\":0,\"left\":\"center\",\"borderWidth\":1,\"textStyle\":{\"textBorderWidth\":0,\"color\":\"#fff\",\"textShadowColor\":\"transparent\",\"fontSize\":14,\"lineHeight\":24,\"textShadowOffsetX\":0,\"textShadowOffsetY\":0,\"textBorderType\":\"solid\",\"fontWeight\":500,\"textBorderColor\":\"#ccc\",\"textShadowBlur\":0},\"shadowColor\":\"transparent\"},\"legend\":{\"padding\":5,\"itemGap\":10,\"shadowOffsetX\":0,\"backgroundColor\":\"transparent\",\"borderColor\":\"#ccc\",\"shadowOffsetY\":0,\"orient\":\"vertical\",\"shadowBlur\":0,\"bottom\":\"auto\",\"itemHeight\":14,\"show\":true,\"icon\":\"roundRect\",\"itemStyle\":{\"borderType\":\"solid\",\"shadowOffsetX\":0,\"borderColor\":\"inherit\",\"shadowOffsetY\":0,\"color\":\"inherit\",\"shadowBlur\":0,\"borderWidth\":0,\"opacity\":1,\"shadowColor\":\"transparent\"},\"top\":\"auto\",\"borderRadius\":0,\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"color\":\"inherit\",\"shadowBlur\":0,\"width\":\"auto\",\"type\":\"inherit\",\"opacity\":1,\"shadowColor\":\"transparent\"},\"left\":\"left\",\"borderWidth\":0,\"width\":\"auto\",\"itemWidth\":25,\"textStyle\":{\"textBorderWidth\":0,\"color\":\"#fff\",\"textShadowColor\":\"transparent\",\"ellipsis\":\"...\",\"overflow\":\"none\",\"fontSize\":12,\"lineHeight\":24,\"textShadowOffsetX\":0,\"textShadowOffsetY\":0,\"textBorderType\":\"solid\",\"fontWeight\":500,\"textBorderColor\":\"transparent\",\"textShadowBlur\":0},\"shadowColor\":\"rgba(0,0,0,.3)\",\"height\":\"auto\"},\"series\":{\"itemStyle\":{\"borderType\":\"solid\",\"shadowOffsetX\":0,\"borderColor\":\"#000\",\"shadowOffsetY\":0,\"color\":\"\",\"shadowBlur\":0,\"borderWidth\":0,\"opacity\":1,\"shadowColor\":\"#000\"},\"label\":{\"borderType\":\"solid\",\"rotate\":0,\"padding\":0,\"textBorderWidth\":0,\"backgroundColor\":\"transparent\",\"borderColor\":\"#fff\",\"color\":\"\",\"show\":true,\"textShadowColor\":\"transparent\",\"distanceToLabelLine\":5,\"ellipsis\":\"...\",\"overflow\":\"none\",\"borderRadius\":0,\"borderWidth\":0,\"fontSize\":12,\"lineHeight\":18,\"textShadowOffsetX\":0,\"position\":\"outside\",\"textShadowOffsetY\":0,\"textBorderType\":\"solid\",\"textBorderColor\":\"#fff\",\"textShadowBlur\":0},\"labelLine\":{\"show\":true,\"length\":10,\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"shadowBlur\":0,\"width\":1,\"type\":\"solid\",\"opacity\":1,\"shadowColor\":\"#000\"},\"length2\":14,\"smooth\":false}}},\r\n\t\t\tboardBase: {\"funnelNum\":8,\"lineNum\":8,\"gaugeNum\":8,\"barNum\":8,\"pieNum\":8},\r\n\t\t\tgauge: {\"tooltip\":{\"backgroundColor\":\"#123\",\"textStyle\":{\"color\":\"#fff\"}},\"backgroundColor\":\"transparent\",\"color\":[\"#5470c6\",\"#91cc75\",\"#fac858\",\"#ee6666\",\"#73c0de\",\"#3ba272\",\"#fc8452\",\"#9a60b4\",\"#ea7ccc\"],\"title\":{\"top\":\"bottom\",\"left\":\"left\"},\"series\":{\"pointer\":{\"offsetCenter\":[0,\"10%\"],\"icon\":\"path://M2.9,0.7L2.9,0.7c1.4,0,2.6,1.2,2.6,2.6v115c0,1.4-1.2,2.6-2.6,2.6l0,0c-1.4,0-2.6-1.2-2.6-2.6V3.3C0.3,1.9,1.4,0.7,2.9,0.7z\",\"width\":8,\"length\":\"80%\"},\"axisLine\":{\"lineStyle\":{\"shadowOffsetX\":0,\"shadowOffsetY\":0,\"opacity\":0.5,\"shadowBlur\":1,\"shadowColor\":\"#000\"},\"roundCap\":true},\"anchor\":{\"show\":true,\"itemStyle\":{\"color\":\"inherit\"},\"size\":18,\"showAbove\":true},\"emphasis\":{\"disabled\":false},\"progress\":{\"show\":true,\"roundCap\":true,\"overlap\":true},\"splitNumber\":25,\"detail\":{\"formatter\":\"{value}\",\"backgroundColor\":\"inherit\",\"color\":\"#fff\",\"borderRadius\":3,\"width\":20,\"fontSize\":14,\"height\":16},\"title\":{\"fontSize\":14},\"animation\":true}},\r\n\t\t};\r\n\t},\r\n\tmounted(){\r\n\t\tthis.init();\r\n\t\tthis.getcaiwuxinxiCount();\r\n\t\tthis.caiwuxinxiChat1();\r\n\t\tthis.caiwuxinxiChat2();\r\n\t\tthis.caiwuxinxiChat3();\r\n\t},\r\n\tmethods:{\r\n\t\t// 词云\r\n\t\twordclouds(wordcloudData,echartsId) {\r\n\t\t\tlet wordcloud = $template2.back.board.wordcloud\r\n\t\t\twordcloud = JSON.parse(JSON.stringify(wordcloud), (k, v) => {\r\n\t\t\t  if(typeof v == 'string' && v.indexOf('function') > -1){\r\n\t\t\t\treturn eval(\"(function(){return \"+v+\" })()\")\r\n\t\t\t  }\r\n\t\t\t  return v;\r\n\t\t\t})\r\n\t\t\twordcloud.option.series[0].data=wordcloudData;\r\n\t\t\t\r\n\t\t\tthis.myChart0 = echarts.init(document.getElementById(echartsId));\r\n\t\t\tlet myChart = this.myChart0\r\n\t\t\tlet img = wordcloud.maskImage\r\n\t\t\r\n\t\t\tif (img) {\r\n\t\t\t\tvar maskImage = new Image();\r\n\t\t\t\tmaskImage.src = img\r\n\t\t\t\tmaskImage.onload = function() {\r\n\t\t\t\t\twordcloud.option.series[0].maskImage = maskImage\r\n\t\t\t\t\tmyChart.clear()\r\n\t\t\t\t\tmyChart.setOption(wordcloud.option)\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tdelete wordcloud.option.series[0].maskImage\r\n\t\t\t\tmyChart.clear()\r\n\t\t\t\tmyChart.setOption(wordcloud.option)\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 统计图动画\r\n\t\tmyChartInterval(type, xAxisData, seriesData, myChart) {\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tsetInterval(() => {\r\n\t\t\t\t\tlet xAxis = xAxisData.shift()\r\n\t\t\t\t\txAxisData.push(xAxis)\r\n\t\t\t\t\tlet series = seriesData.shift()\r\n\t\t\t\t\tseriesData.push(series)\r\n\t\t\t\t\r\n\t\t\t\t\tif (type == 1) {\r\n\t\t\t\t\t\tmyChart.setOption({\r\n\t\t\t\t\t\t\txAxis: [{\r\n\t\t\t\t\t\t\t\tdata: xAxisData\r\n\t\t\t\t\t\t\t}],\r\n\t\t\t\t\t\t\tseries: [{\r\n\t\t\t\t\t\t\t\tdata: seriesData\r\n\t\t\t\t\t\t\t}]\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (type == 2) {\r\n\t\t\t\t\t\tmyChart.setOption({\r\n\t\t\t\t\t\t\tyAxis: [{\r\n\t\t\t\t\t\t\t\tdata: xAxisData\r\n\t\t\t\t\t\t\t}],\r\n\t\t\t\t\t\t\tseries: [{\r\n\t\t\t\t\t\t\t\tdata: seriesData\r\n\t\t\t\t\t\t\t}]\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t}, $template2.back.board.bar.base.interval);\r\n\t\t\t})\r\n\t\t},\r\n\t\tinit(){\r\n\t\t\tif(this.$storage.get('Token')){\r\n\t\t\tthis.$http({\r\n\t\t\t\turl: `${this.$storage.get('sessionTable')}/session`,\r\n\t\t\t\tmethod: \"get\"\r\n\t\t\t}).then(({ data }) => {\r\n\t\t\t\tif (data && data.code != 0) {\r\n\t\t\t\trouter.push({ name: 'login' })\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t}else{\r\n\t\t\t\trouter.push({ name: 'login' })\r\n\t\t\t}\r\n\t\t},\r\n\t\tgetcaiwuxinxiCount() {\r\n\t\t\tthis.$http({\r\n\t\t\t\turl: `caiwuxinxi/count`,\r\n\t\t\t\tmethod: \"get\"\r\n\t\t\t}).then(({\r\n\t\t\t\tdata\r\n\t\t\t}) => {\r\n\t\t\t\tif (data && data.code == 0) {\r\n\t\t\t\t\tthis.caiwuxinxiCount = data.data\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tcaiwuxinxiChat1() {\r\n\t\t\tthis.$nextTick(()=>{\r\n\r\n        var caiwuxinxiChart1 = echarts.init(document.getElementById(\"caiwuxinxiChart1\"),'macarons');\r\n        this.$http({\r\n            url: `caiwuxinxi/value/dengjiriqi/shourujine/年`,\r\n            method: \"get\",\r\n        }).then(({ data }) => {\r\n            if (data && data.code === 0) {\r\n                let res = data.data;\r\n                let xAxis = [];\r\n                let yAxis = [];\r\n                let pArray = []\r\n                for(let i=0;i<res.length;i++){\r\n\t\t\t\t\tif(this.boardBase&&i==this.boardBase.funnelNum){\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n                    xAxis.push(res[i].dengjiriqi);\r\n                    yAxis.push(parseFloat((res[i].total)));\r\n                    pArray.push({\r\n                        value: parseFloat((res[i].total)),\r\n                        name: res[i].dengjiriqi\r\n                    })\r\n                }\r\n                var option = {};\r\n\t\t\t\tlet titleObj = this.funnel.title\r\n\t\t\t\ttitleObj.text = '收入统计'\r\n\t\t\t\t\r\n\t\t\t\tlet legendObj = {\r\n\t\t\t\t\tdata: xAxis,\r\n\t\t\t\t}\r\n\t\t\t\tlegendObj = Object.assign(legendObj , this.funnel.legend)\r\n\t\t\t\tlet tooltipObj = {trigger: 'item',formatter: '{b} : {c}'}\r\n\t\t\t\ttooltipObj = Object.assign(tooltipObj , this.funnel.tooltip?this.funnel.tooltip:{})\r\n\t\t\t\tlet seriesObj = {\r\n\t\t\t\t\tname: '收入统计',\r\n\t\t\t\t\tdata: pArray,\r\n\t\t\t\t\ttype: 'funnel',\r\n\t\t\t\t\tleft: '10%',\r\n\t\t\t\t\ttop: 60,\r\n\t\t\t\t\tbottom: 60,\r\n\t\t\t\t\twidth: '80%',\r\n\t\t\t\t\tminSize: '0%',\r\n\t\t\t\t\tmaxSize: '100%',\r\n\t\t\t\t}\r\n\t\t\t\tseriesObj = Object.assign(seriesObj , this.funnel.series)\r\n\t\t\t\toption = {\r\n\t\t\t\t\tbackgroundColor: this.funnel.backgroundColor,\r\n\t\t\t\t\tcolor: this.funnel.color,\r\n\t\t\t\t    title: titleObj,\r\n\t\t\t\t    legend: legendObj,\r\n\t\t\t\t    tooltip: tooltipObj,\r\n\t\t\t\t    series: seriesObj,\r\n\t\t\t\t}\r\n                // 使用刚指定的配置项和数据显示图表。\r\n                caiwuxinxiChart1.setOption(option);\r\n\t\t\t\t\r\n                  //根据窗口的大小变动图表\r\n                window.onresize = function() {\r\n                    caiwuxinxiChart1.resize();\r\n                };\r\n            }\r\n        });\r\n      })\r\n    },\r\n\r\n    caiwuxinxiChat2() {\r\n      this.$nextTick(()=>{\r\n\r\n        var caiwuxinxiChart2 = echarts.init(document.getElementById(\"caiwuxinxiChart2\"),'macarons');\r\n        this.$http({\r\n            url: `caiwuxinxi/value/dengjiriqi/zhichujine/年`,\r\n            method: \"get\",\r\n        }).then(({ data }) => {\r\n            if (data && data.code === 0) {\r\n                let res = data.data;\r\n                let xAxis = [];\r\n                let yAxis = [];\r\n                let pArray = []\r\n                for(let i=0;i<res.length;i++){\r\n\t\t\t\t\tif(this.boardBase&&i==this.boardBase.barNum){\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n                    xAxis.push(res[i].dengjiriqi);\r\n                    yAxis.push(parseFloat((res[i].total)));\r\n                    pArray.push({\r\n                        value: parseFloat((res[i].total)),\r\n                        name: res[i].dengjiriqi\r\n                    })\r\n                }\r\n                var option = {};\r\n                let titleObj = this.bar.title\r\n\t\t\t\ttitleObj.text = '支出统计'\r\n\t\t\t\t\r\n\t\t\t\tconst legendObj = this.bar.legend\r\n\t\t\t\tlet tooltipObj = {trigger: 'item',formatter: '{b} : {c}'}\r\n\t\t\t\ttooltipObj = Object.assign(tooltipObj , this.bar.tooltip?this.bar.tooltip:{})\r\n\t\t\t\t\r\n\t\t\t\tlet xAxisObj = this.bar.xAxis\r\n\t\t\t\txAxisObj.type = 'category'\r\n\t\t\t\txAxisObj.data = xAxis\r\n                xAxisObj.axisLabel.rotate=40\r\n\t\t\t\t\r\n\t\t\t\tlet yAxisObj = this.bar.yAxis\r\n\t\t\t\tyAxisObj.type = 'value'\r\n\t\t\t\tlet seriesObj = {\r\n\t\t\t\t\t\tdata: yAxis,\r\n\t\t\t\t\t\ttype: 'bar'\r\n\t\t\t\t\t}\r\n\t\t\t\tseriesObj = Object.assign(seriesObj , this.bar.series)\r\n\r\n\t\t\t\toption = {\r\n\t\t\t\t\tbackgroundColor: this.bar.backgroundColor,\r\n\t\t\t\t\tcolor: this.bar.color,\r\n\t\t\t\t\ttitle: titleObj,\r\n\t\t\t\t\tlegend: legendObj,\r\n\t\t\t\t\ttooltip: tooltipObj,\r\n\t\t\t\t\txAxis: xAxisObj,\r\n\t\t\t\t\tyAxis: yAxisObj,\r\n\t\t\t\t\tseries: [seriesObj]\r\n\t\t\t\t};\r\n                // 使用刚指定的配置项和数据显示图表。\r\n                caiwuxinxiChart2.setOption(option);\r\n\t\t\t\t\r\n                  //根据窗口的大小变动图表\r\n                window.onresize = function() {\r\n                    caiwuxinxiChart2.resize();\r\n                };\r\n            }\r\n        });\r\n      })\r\n    },\r\n\r\n    caiwuxinxiChat3() {\r\n      this.$nextTick(()=>{\r\n\r\n        var caiwuxinxiChart3 = echarts.init(document.getElementById(\"caiwuxinxiChart3\"),'macarons');\r\n        this.$http({\r\n            url: `caiwuxinxi/value/dengjiriqi/lirun/年`,\r\n            method: \"get\",\r\n        }).then(({ data }) => {\r\n            if (data && data.code === 0) {\r\n                let res = data.data;\r\n                let xAxis = [];\r\n                let yAxis = [];\r\n                let pArray = []\r\n                for(let i=0;i<res.length;i++){\r\n\t\t\t\t\tif(this.boardBase&&i==this.boardBase.lineNum){\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n                    xAxis.push(res[i].dengjiriqi);\r\n                    yAxis.push(parseFloat((res[i].total)));\r\n                    pArray.push({\r\n                        value: parseFloat((res[i].total)),\r\n                        name: res[i].dengjiriqi\r\n                    })\r\n                }\r\n                var option = {};\r\n                let titleObj = this.line.title\r\n\t\t\t\ttitleObj.text = '利润统计'\r\n\t\t\t\t\r\n\t\t\t\tconst legendObj = this.line.legend\r\n\t\t\t\tlet tooltipObj = { trigger: 'item',formatter: '{b} : {c}'}\r\n\t\t\t\ttooltipObj = Object.assign(tooltipObj , this.line.tooltip?this.line.tooltip:{})\r\n\t\t\t\t\r\n\t\t\t\tlet xAxisObj = this.line.xAxis\r\n\t\t\t\txAxisObj.type = 'category'\r\n\t\t\t\txAxisObj.boundaryGap = false\r\n\t\t\t\txAxisObj.data = xAxis\r\n                xAxisObj.axisLabel.rotate=70\r\n\t\t\t\t\r\n\t\t\t\tlet yAxisObj = this.line.yAxis\r\n\t\t\t\tyAxisObj.type = 'value'\r\n\t\t\t\t\r\n\t\t\t\tlet seriesObj = {\r\n\t\t\t\t\tdata: yAxis,\r\n\t\t\t\t\ttype: 'line',\r\n\t\t\t\t}\r\n\t\t\t\tseriesObj = Object.assign(seriesObj , this.line.series)\r\n\t\t\t\t\r\n\t\t\t\toption = {\r\n\t\t\t\t\tbackgroundColor: this.line.backgroundColor,\r\n\t\t\t\t\tcolor: this.line.color,\r\n\t\t\t\t\ttitle: titleObj,\r\n\t\t\t\t\tlegend: legendObj,\r\n\t\t\t\t\ttooltip: tooltipObj,\r\n\t\t\t\t\txAxis: xAxisObj,\r\n\t\t\t\t\tyAxis: yAxisObj,\r\n\t\t\t\t\tseries: [seriesObj]\r\n\t\t\t\t};\r\n                // 使用刚指定的配置项和数据显示图表。\r\n                caiwuxinxiChart3.setOption(option);\r\n                  //根据窗口的大小变动图表\r\n                window.onresize = function() {\r\n                    caiwuxinxiChart3.resize();\r\n                };\r\n            }\r\n        });\r\n      })\r\n    },\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n    .cardView {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        width: 100%;\r\n\r\n        .cards {\r\n            display: flex;\r\n            align-items: center;\r\n            width: 100%;\r\n            margin-bottom: 10px;\r\n            justify-content: center;\r\n            .card {\r\n                width: calc(25% - 20px);\r\n                margin: 0 10px;\r\n                /deep/.el-card__body{\r\n                    padding: 0;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\t\r\n\t// 日历\r\n\t.calendar td .text {\r\n\t\t\t\tborder-radius: 12px;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\tbackground: #fff;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\theight: 100%;\r\n\t\t\t}\r\n\t.calendar td .text:hover {\r\n\t\t\t\tbackground: rgba(78,110,242,.1);\r\n\t\t\t}\r\n\t.calendar td .text .new {\r\n\t\t\t\tcolor: #000;\r\n\t\t\t\tfont-size: 24px;\r\n\t\t\t\tline-height: 1.4;\r\n\t\t\t}\r\n\t.calendar td .text .old {\r\n\t\t\t\tcolor: #666;\r\n\t\t\t\tfont-size: 16px;\r\n\t\t\t\tline-height: 1.4;\r\n\t\t\t}\r\n\t.calendar td.festival .text {\r\n\t\t\t\tborder-radius: 12px;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\tbackground: rgba(235,51,51,.05);\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\theight: 100%;\r\n\t\t\t}\r\n\t.calendar td.festival .text:hover {\r\n\t\t\t\tbackground: rgba(78,110,242,.1);\r\n\t\t\t}\r\n\t.calendar td.festival .text .new {\r\n\t\t\t\tcolor: #000;\r\n\t\t\t\tfont-size: 24px;\r\n\t\t\t\tline-height: 1.4;\r\n\t\t\t}\r\n\t.calendar td.festival .text .old {\r\n\t\t\t\tcolor: #666;\r\n\t\t\t\tfont-size: 16px;\r\n\t\t\t\tline-height: 1.4;\r\n\t\t\t}\r\n\t.calendar td.other .text {\r\n\t\t\t\tborder-radius: 12px;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\tbackground: #fff;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\topacity: 0.3;\r\n\t\t\t\theight: 100%;\r\n\t\t\t}\r\n\t.calendar td.other .text:hover {\r\n\t\t\t\tbackground: rgba(78,110,242,.1);\r\n\t\t\t}\r\n\t.calendar td.other .text .new {\r\n\t\t\t\tcolor: #000;\r\n\t\t\t\tfont-size: 24px;\r\n\t\t\t\tline-height: 1.4;\r\n\t\t\t}\r\n\t.calendar td.other .text .old {\r\n\t\t\t\tcolor: #666;\r\n\t\t\t\tfont-size: 16px;\r\n\t\t\t\tline-height: 1.4;\r\n\t\t\t}\r\n\t.calendar td.today .text {\r\n\t\t\t\tborder-radius: 12px;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #2ddcd3;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\theight: 100%;\r\n\t\t\t}\r\n\t.calendar td.today .text:hover {\r\n\t\t\t\tbackground: rgba(64, 158, 255,.5);\r\n\t\t\t}\r\n\t.calendar td.today .text .new {\r\n\t\t\t\tcolor: inherit;\r\n\t\t\t\tfont-size: 24px;\r\n\t\t\t\tline-height: 1.4;\r\n\t\t\t}\r\n\t.calendar td.today .text .old {\r\n\t\t\t\tcolor: inherit;\r\n\t\t\t\tfont-size: 16px;\r\n\t\t\t\tline-height: 1.4;\r\n\t\t\t}\r\n\t\r\n\t// echarts1\r\n\t.type1 .echarts1 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: auto;\r\n\t\t\t}\r\n\t.type1 .echarts1:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t// echarts2\r\n\t.type2 .echarts1 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 49%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type2 .echarts1:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t.type2 .echarts2 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 49%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type2 .echarts2:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t// echarts3\r\n\t.type3 .echarts1 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type3 .echarts1:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t.type3 .echarts2 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 49%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type3 .echarts2:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t.type3 .echarts3 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 49%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type3 .echarts3:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t// echarts4\r\n\t.type4 .echarts1 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 49%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type4 .echarts1:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t.type4 .echarts2 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 49%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type4 .echarts2:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t.type4 .echarts3 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 49%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type4 .echarts3:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t.type4 .echarts4 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 49%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type4 .echarts4:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t// echarts5\r\n\t.type5 .echarts1 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type5 .echarts1:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t.type5 .echarts2 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 49%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type5 .echarts2:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t.type5 .echarts3 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 49%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type5 .echarts3:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t.type5 .echarts4 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 49%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type5 .echarts4:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t.type5 .echarts5 {\r\n\t\t\t\tborder-radius: 8px;\r\n\t\t\t\tpadding: 20px;\r\n\t\t\t\tmargin: 10px 0;\r\n\t\t\t\tbackground: rgba(255,255,255,.1);\r\n\t\t\t\twidth: 49%;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t\theight: 400px;\r\n\t\t\t}\r\n\t.type5 .echarts5:hover {\r\n\t\t\t\tbox-shadow: 1px 1px 20px rgba(255,255,255,.12);\r\n\t\t\t\ttransform: translate3d(0, -10px, 0);\r\n\t\t\t\tz-index: 1;\r\n\t\t\t\tbackground: rgba(255,255,255,.12);\r\n\t\t\t}\r\n\t\r\n\t.echarts-flag-2 {\r\n\t  display: flex;\r\n\t  flex-wrap: wrap;\r\n\t  justify-content: space-between;\r\n\t  padding: 10px 20px;\r\n\t  background: rebeccapurple;\r\n\t\r\n\t  &>div {\r\n\t    width: 32%;\r\n\t    height: 300px;\r\n\t    margin: 10px 0;\r\n\t    background: rgba(255,255,255,.1);\r\n\t    border-radius: 8px;\r\n\t    padding: 10px 20px;\r\n\t  }\r\n\t}\r\n</style>\r\n"]}]}