{"remainingRequest": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\src\\views\\login.vue?vue&type=style&index=0&id=7589b93f&lang=scss&scoped=true", "dependencies": [{"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\src\\views\\login.vue", "mtime": 1755434859228}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["login.vue"], "names": [], "mappings": ";AA2HA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "login.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"container\" :style='{\"minHeight\":\"100vh\",\"padding\":\"100px  180px  0 0\",\"alignItems\":\"center\",\"background\":\"url(http://codegen.caihongy.cn/20240127/c69581b3923448a1bec896fd2013b84c.png) no-repeat\",\"display\":\"flex\",\"width\":\"100%\",\"backgroundSize\":\"cover\",\"justifyContent\":\"flex-end\"}'>\r\n      <el-form :style='{\"padding\":\"40px 20px 20px\",\"boxShadow\":\"0 1px 20px rgba( 255,  255, 255, .8)\",\"margin\":\"0\",\"borderRadius\":\"4px\",\"background\":\"#fff\",\"width\":\"400px\",\"height\":\"auto\"}'>\r\n        <div v-if=\"true\" :style='{\"width\":\"100%\",\"margin\":\"0 0 10px 0\",\"lineHeight\":\"44px\",\"fontSize\":\"20px\",\"color\":\"#374254\",\"textAlign\":\"center\"}' class=\"title-container\">新彼岸财务管理系统登录</div>\r\n        <div v-if=\"loginType==1\" class=\"list-item\" :style='{\"width\":\"80%\",\"margin\":\"0 auto 10px\",\"alignItems\":\"center\",\"flexWrap\":\"wrap\",\"display\":\"flex\"}'>\r\n          <div v-if=\"true\" class=\"lable\" :style='{\"width\":\"64px\",\"lineHeight\":\"44px\",\"fontSize\":\"14px\",\"color\":\" #374254\",\"fontWeight\":\"600\"}'>用户名：</div>\r\n          <input :style='{\"border\":\"1px solid rgba(167, 180, 201,.3) \",\"padding\":\"0 10px\",\"color\":\" #a7b4c9 \",\"outlineOffset\":\"4px\",\"width\":\"100%\",\"fontSize\":\"14px\",\"height\":\"44px\"}' placeholder=\"请输入用户名\" name=\"username\" type=\"text\" v-model=\"rulesForm.username\">\r\n        </div>\r\n        <div v-if=\"loginType==1\" class=\"list-item\" :style='{\"width\":\"80%\",\"margin\":\"0 auto 10px\",\"alignItems\":\"center\",\"flexWrap\":\"wrap\",\"display\":\"flex\"}'>\r\n          <div v-if=\"true\" class=\"lable\" :style='{\"width\":\"64px\",\"lineHeight\":\"44px\",\"fontSize\":\"14px\",\"color\":\" #374254\",\"fontWeight\":\"600\"}'>密码：</div>\r\n          <input :style='{\"border\":\"1px solid rgba(167, 180, 201,.3) \",\"padding\":\"0 10px\",\"color\":\" #a7b4c9 \",\"outlineOffset\":\"4px\",\"width\":\"100%\",\"fontSize\":\"14px\",\"height\":\"44px\"}' placeholder=\"请输入密码\" name=\"password\" type=\"password\" v-model=\"rulesForm.password\">\r\n        </div>\r\n\r\n        <div :style='{\"width\":\"80%\",\"margin\":\"20px auto\"}' v-if=\"roles.length>1\" prop=\"loginInRole\" class=\"list-type\">\r\n          <el-radio v-for=\"item in roles\" v-bind:key=\"item.roleName\" v-model=\"rulesForm.role\" :label=\"item.roleName\">{{item.roleName}}</el-radio>\r\n        </div>\r\n\r\n\t\t\r\n        <div :style='{\"width\":\"80%\",\"margin\":\"20px auto\",\"alignItems\":\"center\",\"flexWrap\":\"wrap\",\"display\":\"flex\"}'>\r\n          <el-button v-if=\"loginType==1\" :style='{\"border\":\"0\",\"cursor\":\"pointer\",\"padding\":\"0 24px\",\"margin\":\"10px\",\"outline\":\"none\",\"color\":\"#fff\",\"borderRadius\":\"4px\",\"background\":\"rgb(45, 220, 211)\",\"width\":\"auto\",\"fontSize\":\"14px\",\"height\":\"44px\"}' type=\"primary\" @click=\"login()\" class=\"loginInBt\">登录</el-button>\r\n          <el-button :style='{\"cursor\":\"pointer\",\"border\":\"0\",\"padding\":\"0 24px\",\"margin\":\"10px\",\"outline\":\"none\",\"color\":\"#fff\",\"borderRadius\":\"4px\",\"background\":\"rgb(15, 116, 253)\",\"width\":\"auto\",\"fontSize\":\"14px\",\"height\":\"44px\"}' type=\"primary\" @click=\"register('yuangong')\" class=\"register\">注册人员</el-button>\r\n        </div>\r\n      </el-form>\r\n\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport menu from \"@/utils/menu\";\r\nexport default {\r\n  data() {\r\n    return {\r\n\t\tverifyCheck2: false,\r\n\t\tflag: false,\r\n      baseUrl:this.$base.url,\r\n      loginType: 1,\r\n      rulesForm: {\r\n        username: \"\",\r\n        password: \"\",\r\n        role: \"\",\r\n      },\r\n      menus: [],\r\n      roles: [],\r\n      tableName: \"\",\r\n    };\r\n  },\r\n  mounted() {\r\n    let menus = menu.list();\r\n    this.menus = menus;\r\n\r\n    for (let i = 0; i < this.menus.length; i++) {\r\n      if (this.menus[i].hasBackLogin=='是') {\r\n        this.roles.push(this.menus[i])\r\n      }\r\n    }\r\n\r\n  },\r\n  created() {\r\n\r\n  },\r\n  destroyed() {\r\n\t    },\r\n  components: {\r\n  },\r\n  methods: {\r\n\r\n    //注册\r\n    register(tableName){\r\n\t\tthis.$storage.set(\"loginTable\", tableName);\r\n\t\tthis.$router.push({path:'/register',query:{pageFlag:'register'}})\r\n    },\r\n    // 登陆\r\n    login() {\r\n\r\n\t\tif (!this.rulesForm.username) {\r\n\t\t\tthis.$message.error(\"请输入用户名\");\r\n\t\t\treturn;\r\n\t\t}\r\n\t\tif (!this.rulesForm.password) {\r\n\t\t\tthis.$message.error(\"请输入密码\");\r\n\t\t\treturn;\r\n\t\t}\r\n\t\tif(this.roles.length>1) {\r\n\t\t\tif (!this.rulesForm.role) {\r\n\t\t\t\tthis.$message.error(\"请选择角色\");\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tlet menus = this.menus;\r\n\t\t\tfor (let i = 0; i < menus.length; i++) {\r\n\t\t\t\tif (menus[i].roleName == this.rulesForm.role) {\r\n\t\t\t\t\tthis.tableName = menus[i].tableName;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tthis.tableName = this.roles[0].tableName;\r\n\t\t\tthis.rulesForm.role = this.roles[0].roleName;\r\n\t\t}\r\n\t\t\r\n\t\tthis.loginPost()\r\n    },\r\n\tloginPost() {\r\n\t\tthis.$http({\r\n\t\t\turl: `${this.tableName}/login?username=${this.rulesForm.username}&password=${this.rulesForm.password}`,\r\n\t\t\tmethod: \"post\"\r\n\t\t}).then(({ data }) => {\r\n\t\t\tif (data && data.code === 0) {\r\n\t\t\t\tthis.$storage.set(\"Token\", data.token);\r\n\t\t\t\tthis.$storage.set(\"role\", this.rulesForm.role);\r\n\t\t\t\tthis.$storage.set(\"sessionTable\", this.tableName);\r\n\t\t\t\tthis.$storage.set(\"adminName\", this.rulesForm.username);\r\n\t\t\t\tthis.$router.replace({ path: \"/\" });\r\n\t\t\t} else {\r\n\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t}\r\n\t\t});\r\n\t},\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.container {\r\n  min-height: 100vh;\r\n  position: relative;\r\n  background-repeat: no-repeat;\r\n  background-position: center center;\r\n  background-size: cover;\r\n      background: url(http://codegen.caihongy.cn/20240127/c69581b3923448a1bec896fd2013b84c.png) no-repeat;\r\n        \r\n  .list-item /deep/ .el-input .el-input__inner {\r\n\t\tborder: 1px solid rgba(167, 180, 201,.3) ;\r\n\t\tpadding: 0 10px;\r\n\t\tcolor:  #a7b4c9 ;\r\n\t\twidth: 100%;\r\n\t\tfont-size: 14px;\r\n\t\toutline-offset: 4px;\r\n\t\theight: 44px;\r\n\t  }\r\n  \r\n  .list-item.select /deep/ .el-select .el-input__inner {\r\n\t\tborder: 1px solid rgba(64, 158, 255, 1);\r\n\t\tpadding: 0 10px;\r\n\t\tbox-shadow: 0 0 6px rgba(64, 158, 255, .5);\r\n\t\toutline: 1px solid #efefef;\r\n\t\tcolor: rgba(64, 158, 255, 1);\r\n\t\twidth: 288px;\r\n\t\tfont-size: 14px;\r\n\t\toutline-offset: 4px;\r\n\t\theight: 44px;\r\n\t  }\r\n  \r\n  .list-code /deep/ .el-input .el-input__inner {\r\n  \t  \tborder: 1px solid rgba(167, 180, 201,.3) ;\r\n  \t  \tpadding: 0 10px;\r\n  \t  \toutline: none;\r\n  \t  \tcolor:  #a7b4c9 ;\r\n  \t  \twidth: calc(100% - 80px);\r\n  \t  \tfont-size: 14px;\r\n  \t  \theight: 44px;\r\n  \t  }\r\n\r\n  .list-type /deep/ .el-radio__input .el-radio__inner {\r\n\t\tbackground: rgba(53, 53, 53, 0);\r\n\t\tborder-color: #666666;\r\n\t  }\r\n  .list-type /deep/ .el-radio__input.is-checked .el-radio__inner {\r\n        background: #3b5998;\r\n        border-color: #3b5998;\r\n      }\r\n  .list-type /deep/ .el-radio__label {\r\n\t\tcolor: #666666;\r\n\t\tfont-size: 14px;\r\n\t  }\r\n  .list-type /deep/ .el-radio__input.is-checked+.el-radio__label {\r\n        color: #3b5998;\r\n        font-size: 14px;\r\n      }\r\n}\r\n\r\n</style>\r\n"]}]}