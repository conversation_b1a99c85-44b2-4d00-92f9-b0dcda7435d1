{"remainingRequest": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\src\\components\\common\\Editor.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\src\\components\\common\\Editor.vue", "mtime": 1711079474000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Editor.vue"], "names": [], "mappings": ";AA0BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Editor.vue", "sourceRoot": "src/components/common", "sourcesContent": ["<template>\n  <div>\n    <!-- 图片上传组件辅助-->\n    <el-upload\n      class=\"avatar-uploader\"\n      :action=\"getActionUrl\"\n      name=\"file\"\n      :headers=\"header\"\n      :show-file-list=\"false\"\n      :on-success=\"uploadSuccess\"\n      :on-error=\"uploadError\"\n      :before-upload=\"beforeUpload\"\n    ></el-upload>\n\n    <quill-editor\n      class=\"editor\"\n      v-model=\"value\"\n      ref=\"myQuillEditor\"\n      :options=\"editorOption\"\n      @blur=\"onEditorBlur($event)\"\n      @focus=\"onEditorFocus($event)\"\n      @change=\"onEditorChange($event)\"\n    ></quill-editor>\n  </div>\n</template>\n<script>\n// 工具栏配置\nconst toolbarOptions = [\n  [\"bold\", \"italic\", \"underline\", \"strike\"], // 加粗 斜体 下划线 删除线\n  [\"blockquote\", \"code-block\"], // 引用  代码块\n  [{ header: 1 }, { header: 2 }], // 1、2 级标题\n  [{ list: \"ordered\" }, { list: \"bullet\" }], // 有序、无序列表\n  [{ script: \"sub\" }, { script: \"super\" }], // 上标/下标\n  [{ indent: \"-1\" }, { indent: \"+1\" }], // 缩进\n  // [{'direction': 'rtl'}],                         // 文本方向\n  [{ size: [\"small\", false, \"large\", \"huge\"] }], // 字体大小\n  [{ header: [1, 2, 3, 4, 5, 6, false] }], // 标题\n  [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色\n  [{ font: [] }], // 字体种类\n  [{ align: [] }], // 对齐方式\n  [\"clean\"], // 清除文本格式\n  [\"link\", \"image\", \"video\"] // 链接、图片、视频\n];\n\nimport { quillEditor } from \"vue-quill-editor\";\nimport \"quill/dist/quill.core.css\";\nimport \"quill/dist/quill.snow.css\";\nimport \"quill/dist/quill.bubble.css\";\n\nexport default {\n  props: {\n    /*编辑器的内容*/\n    value: {\n      type: String\n    },\n    action: {\n      type: String\n    },\n    /*图片大小*/\n    maxSize: {\n      type: Number,\n      default: 4000 //kb\n    }\n  },\n\n  components: {\n    quillEditor\n  },\n\n  data() {\n    return {\n      content: this.value,\n      quillUpdateImg: false, // 根据图片上传状态来确定是否显示loading动画，刚开始是false,不显示\n      editorOption: {\n        placeholder: \"\",\n        theme: \"snow\", // or 'bubble'\n        modules: {\n          toolbar: {\n            container: toolbarOptions,\n            // container: \"#toolbar\",\n            handlers: {\n              image: function(value) {\n                if (value) {\n                  // 触发input框选择图片文件\n                  document.querySelector(\".avatar-uploader input\").click();\n                } else {\n                  this.quill.format(\"image\", false);\n                }\n              }\n              // link: function(value) {\n              //   if (value) {\n              //     var href = prompt('请输入url');\n              //     this.quill.format(\"link\", href);\n              //   } else {\n              //     this.quill.format(\"link\", false);\n              //   }\n              // },\n            }\n          }\n        }\n      },\n      // serverUrl: `${base.url}sys/storage/uploadSwiper?token=${storage.get('token')}`, // 这里写你要上传的图片服务器地址\n      header: {\n        // token: sessionStorage.token\n       'Token': this.$storage.get(\"Token\")\n      } // 有的图片服务器要求请求头需要有token\n    };\n  },\n  computed: {\n    // 计算属性的 getter\n    getActionUrl: function() {\n      // return this.$base.url + this.action + \"?token=\" + this.$storage.get(\"token\");\n      return `/${this.$base.name}/` + this.action;\n    }\n  },\n  methods: {\n    onEditorBlur() {\n      //失去焦点事件\n    },\n    onEditorFocus() {\n      //获得焦点事件\n    },\n    onEditorChange() {\n      console.log(this.value);\n      //内容改变事件\n      this.$emit(\"input\", this.value);\n    },\n    // 富文本图片上传前\n    beforeUpload() {\n      // 显示loading动画\n      this.quillUpdateImg = true;\n    },\n\n    uploadSuccess(res, file) {\n      // res为图片服务器返回的数据\n      // 获取富文本组件实例\n      let quill = this.$refs.myQuillEditor.quill;\n      // 如果上传成功\n      if (res.code === 0) {\n        // 获取光标所在位置\n        let length = quill.getSelection().index;\n        // 插入图片  res.url为服务器返回的图片地址\n        quill.insertEmbed(length, \"image\", this.$base.url+ \"upload/\" +res.file);\n        // 调整光标到最后\n        quill.setSelection(length + 1);\n      } else {\n        this.$message.error(\"图片插入失败\");\n      }\n      // loading动画消失\n      this.quillUpdateImg = false;\n    },\n    // 富文本图片上传失败\n    uploadError() {\n      // loading动画消失\n      this.quillUpdateImg = false;\n      this.$message.error(\"图片插入失败\");\n    }\n  }\n};\n</script> \n\n<style>\n.editor {\n  line-height: normal !important;\n}\n.ql-snow .ql-tooltip[data-mode=\"link\"]::before {\n  content: \"请输入链接地址:\";\n}\n.ql-snow .ql-tooltip.ql-editing a.ql-action::after {\n  border-right: 0px;\n  content: \"保存\";\n  padding-right: 0px;\n}\n\n.ql-snow .ql-tooltip[data-mode=\"video\"]::before {\n  content: \"请输入视频地址:\";\n}\n.ql-container {\n\theight: 400px;\n}\n\n.ql-snow .ql-picker.ql-size .ql-picker-label::before,\n.ql-snow .ql-picker.ql-size .ql-picker-item::before {\n  content: \"14px\";\n}\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"small\"]::before,\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"small\"]::before {\n  content: \"10px\";\n}\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"large\"]::before,\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"large\"]::before {\n  content: \"18px\";\n}\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=\"huge\"]::before,\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=\"huge\"]::before {\n  content: \"32px\";\n}\n\n.ql-snow .ql-picker.ql-header .ql-picker-label::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item::before {\n  content: \"文本\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"1\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"1\"]::before {\n  content: \"标题1\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"2\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"2\"]::before {\n  content: \"标题2\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"3\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"3\"]::before {\n  content: \"标题3\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"4\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"4\"]::before {\n  content: \"标题4\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"5\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"5\"]::before {\n  content: \"标题5\";\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"6\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"6\"]::before {\n  content: \"标题6\";\n}\n\n.ql-snow .ql-picker.ql-font .ql-picker-label::before,\n.ql-snow .ql-picker.ql-font .ql-picker-item::before {\n  content: \"标准字体\";\n}\n.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=\"serif\"]::before,\n.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=\"serif\"]::before {\n  content: \"衬线字体\";\n}\n.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=\"monospace\"]::before,\n.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=\"monospace\"]::before {\n  content: \"等宽字体\";\n}\n</style>"]}]}