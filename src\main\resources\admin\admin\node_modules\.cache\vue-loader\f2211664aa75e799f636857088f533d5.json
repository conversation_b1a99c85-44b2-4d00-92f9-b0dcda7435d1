{"remainingRequest": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\src\\views\\center.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\src\\views\\center.vue", "mtime": 1754737848836}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["center.vue"], "names": [], "mappings": ";AAmFA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;;;;;AAKA;AACA;AACA;;;;;AAKA;AACA;AACA;AACA;;;;;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "center.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div :style='{\"padding\":\"30px\",\"margin\":\"0\"}'>\r\n    <el-form\r\n\t  :style='{\"border\":\"1px solid rgba(167, 180, 201,.3)  \",\"padding\":\"30px\",\"borderRadius\":\"6px\",\"background\":\"#fff\"}'\r\n      class=\"add-update-preview\"\r\n      ref=\"ruleForm\"\r\n      :model=\"ruleForm\"\r\n      label-width=\"80px\"\r\n    >  \r\n     <el-row>\r\n        <el-form-item :style='{\"margin\":\"0 0 20px 0\"}'   v-if=\"flag=='yuangong'\"  label=\"工号\" prop=\"gonghao\">\r\n          <el-input v-model=\"ruleForm.gonghao\" readonly              placeholder=\"工号\" clearable></el-input>\r\n        </el-form-item>\r\n        <el-form-item :style='{\"margin\":\"0 0 20px 0\"}'   v-if=\"flag=='yuangong'\"  label=\"姓名\" prop=\"xingming\">\r\n          <el-input v-model=\"ruleForm.xingming\"               placeholder=\"姓名\" clearable></el-input>\r\n        </el-form-item>\r\n        <el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-if=\"flag=='yuangong'\" label=\"头像\" prop=\"touxiang\">\r\n          <file-upload\r\n          tip=\"点击上传头像\"\r\n          action=\"file/upload\"\r\n          :limit=\"3\"\r\n          :multiple=\"true\"\r\n          :fileUrls=\"ruleForm.touxiang?ruleForm.touxiang:''\"\r\n          @change=\"yuangongtouxiangUploadChange\"\r\n          ></file-upload>\r\n        </el-form-item>\r\n        <el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-if=\"flag=='yuangong'\"  label=\"性别\" prop=\"xingbie\">\r\n          <el-select v-model=\"ruleForm.xingbie\"  placeholder=\"请选择性别\">\r\n            <el-option\r\n                v-for=\"(item,index) in yuangongxingbieOptions\"\r\n                v-bind:key=\"index\"\r\n                :label=\"item\"\r\n                :value=\"item\">\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item :style='{\"margin\":\"0 0 20px 0\"}'   v-if=\"flag=='yuangong'\"  label=\"联系电话\" prop=\"lianxidianhua\">\r\n          <el-input v-model=\"ruleForm.lianxidianhua\"               placeholder=\"联系电话\" clearable></el-input>\r\n        </el-form-item>\r\n        <el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-if=\"flag=='yuangong'\"  label=\"部门\" prop=\"bumen\">\r\n          <el-select v-model=\"ruleForm.bumen\" :disabled=\"true\" placeholder=\"请选择部门\">\r\n            <el-option\r\n                v-for=\"(item,index) in yuangongbumenOptions\"\r\n                v-bind:key=\"index\"\r\n                :label=\"item\"\r\n                :value=\"item\">\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-if=\"flag=='yuangong'\"  label=\"职位\" prop=\"zhiwei\">\r\n          <el-select v-model=\"ruleForm.zhiwei\" :disabled=\"true\" placeholder=\"请选择职位\">\r\n            <el-option\r\n                v-for=\"(item,index) in yuangongzhiweiOptions\"\r\n                v-bind:key=\"index\"\r\n                :label=\"item\"\r\n                :value=\"item\">\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-if=\"flag=='users'\" label=\"用户名\" prop=\"username\">\r\n\t\t\t<el-input v-model=\"ruleForm.username\" placeholder=\"用户名\"></el-input>\r\n\t\t</el-form-item>\r\n\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-if=\"flag=='users'\" label=\"头像\" prop=\"image\">\r\n\t\t  <file-upload\r\n\t\t  tip=\"点击上传头像\"\r\n\t\t  action=\"file/upload\"\r\n\t\t  :limit=\"1\"\r\n\t\t  :multiple=\"false\"\r\n\t\t  :fileUrls=\"ruleForm.image?ruleForm.image:''\"\r\n\t\t  @change=\"usersimageUploadChange\"\r\n\t\t  ></file-upload>\r\n\t\t</el-form-item>\r\n\t\t<el-form-item :style='{\"padding\":\"0\",\"margin\":\"0\"}'>\r\n\t\t\t<el-button class=\"btn3\" :style='{\"border\":\"0\",\"cursor\":\"pointer\",\"padding\":\"0 24px\",\"margin\":\"4px\",\"outline\":\"none\",\"color\":\"#fff\",\"borderRadius\":\"4px\",\"background\":\"#ff2b88\",\"width\":\"auto\",\"fontSize\":\"14px\",\"height\":\"40px\"}' type=\"primary\" @click=\"onUpdateHandler\">\r\n\t\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"margin\":\"0 2px\",\"fontSize\":\"14px\",\"color\":\"#fff\",\"height\":\"40px\"}'></span>\r\n\t\t\t\t提交\r\n\t\t\t</el-button>\r\n\t\t</el-form-item>\r\n      </el-row>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\n// 数字，邮件，手机，url，身份证校验\r\nimport { isNumber,isIntNumer,isEmail,isMobile,isPhone,isURL,checkIdCard } from \"@/utils/validate\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      ruleForm: {},\r\n      flag: '',\r\n      usersFlag: false,\r\n      yuangongxingbieOptions: [],\r\n      yuangongbumenOptions: [],\r\n      yuangongzhiweiOptions: [],\r\n    };\r\n  },\r\n  mounted() {\r\n    var table = this.$storage.get(\"sessionTable\");\r\n    this.flag = table;\r\n    this.$http({\r\n      url: `${this.$storage.get(\"sessionTable\")}/session`,\r\n      method: \"get\"\r\n    }).then(({ data }) => {\r\n      if (data && data.code === 0) {\r\n        this.ruleForm = data.data;\r\n      } else {\r\n        this.$message.error(data.msg);\r\n      }\r\n    });\r\n    this.yuangongxingbieOptions = \"男,女\".split(',')\r\n    this.$http({\r\n      url: `option/bumen/bumen`,\r\n      method: \"get\"\r\n    }).then(({ data }) => {\r\n      if (data && data.code === 0) {\r\n        this.yuangongbumenOptions = data.data;\r\n      } else {\r\n        this.$message.error(data.msg);\r\n      }\r\n    });\r\n    this.$http({\r\n      url: `option/zhiwei/zhiwei`,\r\n      method: \"get\"\r\n    }).then(({ data }) => {\r\n      if (data && data.code === 0) {\r\n        this.yuangongzhiweiOptions = data.data;\r\n      } else {\r\n        this.$message.error(data.msg);\r\n      }\r\n    });\r\n  },\r\n  methods: {\r\n    yuangongtouxiangUploadChange(fileUrls) {\r\n        this.ruleForm.touxiang = fileUrls;\r\n    },\r\n\tusersimageUploadChange(fileUrls) {\r\n\t\tthis.ruleForm.image = fileUrls;\r\n\t},\r\n    onUpdateHandler() {\r\n      if((!this.ruleForm.gonghao)&& 'yuangong'==this.flag){\r\n        this.$message.error('工号不能为空');\r\n        return\r\n      }\r\n\r\n\r\n      if((!this.ruleForm.mima)&& 'yuangong'==this.flag){\r\n        this.$message.error('密码不能为空');\r\n        return\r\n      }\r\n\r\n\r\n      if((!this.ruleForm.xingming)&& 'yuangong'==this.flag){\r\n        this.$message.error('姓名不能为空');\r\n        return\r\n      }\r\n\r\n\r\n\r\n\r\n        if(this.ruleForm.touxiang!=null) {\r\n                this.ruleForm.touxiang = this.ruleForm.touxiang.replace(new RegExp(this.$base.url,\"g\"),\"\");\r\n        }\r\n\r\n\r\n\r\n\r\n      // if( 'yuangong' ==this.flag && this.ruleForm.lianxidianhua&&(!isMobile(this.ruleForm.lianxidianhua))){\r\n      //   this.$message.error(`联系电话应输入手机格式`);\r\n      //   return\r\n      // }\r\n\r\n\r\n\r\n\r\n      if('users'==this.flag && this.ruleForm.username.trim().length<1) {\r\n\tthis.$message.error(`用户名不能为空`);\r\n        return\t\r\n      }\r\n\t  if(this.flag=='users'){\r\n\t  \tthis.ruleForm.image = this.ruleForm.image.replace(new RegExp(this.$base.url,\"g\"),\"\")\r\n\t  }\r\n      this.$http({\r\n        url: `${this.$storage.get(\"sessionTable\")}/update`,\r\n        method: \"post\",\r\n        data: this.ruleForm\r\n      }).then(({ data }) => {\r\n        if (data && data.code === 0) {\r\n          this.$message({\r\n            message: \"修改信息成功\",\r\n            type: \"success\",\r\n            duration: 1500,\r\n            onClose: () => {\r\n\t\t\t\tif(this.flag=='users'){\r\n\t\t\t\t\tthis.$storage.set('headportrait',this.ruleForm.image)\r\n\t\t\t\t}\r\n            }\r\n          });\r\n        } else {\r\n          this.$message.error(data.msg);\r\n        }\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\t.el-date-editor.el-input {\r\n\t\twidth: auto;\r\n\t}\r\n\t\r\n\t.add-update-preview .el-form-item /deep/ .el-form-item__label {\r\n\t  \t  padding: 0 10px 0 0;\r\n\t  \t  color: #374254;\r\n\t  \t  font-weight: 600;\r\n\t  \t  width: 80px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  line-height: 40px;\r\n\t  \t  text-align: right;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .el-form-item /deep/ .el-form-item__content {\r\n\t  margin-left: 80px;\r\n\t}\r\n\t\r\n\t.add-update-preview .el-input /deep/ .el-input__inner {\r\n\t  \t  border: 1px solid rgba(167, 180, 201,.3);\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 12px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .el-select /deep/ .el-input__inner {\r\n\t  \t  border:  1px solid rgba(167, 180, 201,.3) ;\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 10px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .el-date-editor /deep/ .el-input__inner {\r\n\t  \t  border:  1px solid rgba(167, 180, 201,.3) ;\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 10px 0 30px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview /deep/ .el-upload--picture-card {\r\n\t\tbackground: transparent;\r\n\t\tborder: 0;\r\n\t\tborder-radius: 0;\r\n\t\twidth: auto;\r\n\t\theight: auto;\r\n\t\tline-height: initial;\r\n\t\tvertical-align: middle;\r\n\t}\r\n\t\r\n\t.add-update-preview /deep/ .el-upload-list .el-upload-list__item {\r\n\t  \t  border: 2px dashed rgba(167, 180, 201,.3) ;\r\n\t  \t  cursor: pointer;\r\n\t  \t  border-radius: 6px;\r\n\t  \t  color: #a7b4c9  ;\r\n\t  \t  width: 200px;\r\n\t  \t  font-size: 32px;\r\n\t  \t  line-height: 200px;\r\n\t  \t  text-align: center;\r\n\t  \t  height: 200px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview /deep/ .el-upload .el-icon-plus {\r\n\t  \t  border: 2px dashed rgba(167, 180, 201,.3) ;\r\n\t  \t  cursor: pointer;\r\n\t  \t  border-radius: 6px;\r\n\t  \t  color: #a7b4c9  ;\r\n\t  \t  width: 200px;\r\n\t  \t  font-size: 32px;\r\n\t  \t  line-height: 200px;\r\n\t  \t  text-align: center;\r\n\t  \t  height: 200px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .el-textarea /deep/ .el-textarea__inner {\r\n\t  \t  border: 1px solid rgba(167, 180, 201,.3)     ;\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 12px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9 ;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 120px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .btn3 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #ff2b88;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn3:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.editor>.avatar-uploader {\r\n\t\tline-height: 0;\r\n\t\theight: 0;\r\n\t}\r\n</style>\r\n"]}]}