{"remainingRequest": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\src\\views\\modules\\yuangongdangan\\add-or-update.vue?vue&type=style&index=0&id=a97fa832&lang=scss&scoped=true", "dependencies": [{"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\src\\views\\modules\\yuangongdangan\\add-or-update.vue", "mtime": 1755434650291}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\桌面\\springboot2g43t3k0\\src\\main\\resources\\admin\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["add-or-update.vue"], "names": [], "mappings": ";AA+vBA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "add-or-update.vue", "sourceRoot": "src/views/modules/yuangongdangan", "sourcesContent": ["<template>\r\n\t<div class=\"addEdit-block\" :style='{\"padding\":\"30px\",\"margin\":\"0\"}'>\r\n\t\t<el-form\r\n\t\t\t:style='{\"border\":\"1px solid rgba(167, 180, 201,.3)  \",\"padding\":\"30px\",\"borderRadius\":\"6px\",\"background\":\"#fff\"}'\r\n\t\t\tclass=\"add-update-preview\"\r\n\t\t\tref=\"ruleForm\"\r\n\t\t\t:model=\"ruleForm\"\r\n\t\t\t:rules=\"rules\"\r\n\t\t\tlabel-width=\"80px\"\r\n\t\t>\r\n\t\t\t<template >\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"select\" v-if=\"type!='info'\" label=\"工号\" prop=\"gonghao\">\r\n\t\t\t\t\t<el-select :disabled=\"ro.gonghao\" @change=\"gonghaoChange\" v-model=\"ruleForm.gonghao\" placeholder=\"请选择工号\">\r\n\t\t\t\t\t\t<el-option\r\n\t\t\t\t\t\t\tv-for=\"(item,index) in gonghaoOptions\"\r\n\t\t\t\t\t\t\tv-bind:key=\"index\"\r\n\t\t\t\t\t\t\t:label=\"item\"\r\n\t\t\t\t\t\t\t:value=\"item\">\r\n\t\t\t\t\t\t</el-option>\r\n\t\t\t\t\t</el-select>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-else-if=\"ruleForm.gonghao\" label=\"工号\" prop=\"gonghao\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.gonghao\" placeholder=\"工号\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"姓名\" prop=\"xingming\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.xingming\" placeholder=\"姓名\" clearable  :readonly=\"ro.xingming\"></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"姓名\" prop=\"xingming\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.xingming\" placeholder=\"姓名\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"性别\" prop=\"xingbie\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.xingbie\" placeholder=\"性别\" clearable  :readonly=\"ro.xingbie\"></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"性别\" prop=\"xingbie\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.xingbie\" placeholder=\"性别\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"联系电话\" prop=\"lianxidianhua\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.lianxidianhua\" placeholder=\"联系电话\" clearable  :readonly=\"ro.lianxidianhua\"></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"联系电话\" prop=\"lianxidianhua\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.lianxidianhua\" placeholder=\"联系电话\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"部门\" prop=\"bumen\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.bumen\" placeholder=\"部门\" clearable  :readonly=\"ro.bumen\"></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"部门\" prop=\"bumen\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.bumen\" placeholder=\"部门\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-if=\"type!='info'\"  label=\"职位\" prop=\"zhiwei\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.zhiwei\" placeholder=\"职位\" clearable  :readonly=\"ro.zhiwei\"></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"职位\" prop=\"zhiwei\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.zhiwei\" placeholder=\"职位\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"select\" v-if=\"type!='info'\"  label=\"人员状态\" prop=\"yuangongzhuangtai\">\r\n\t\t\t\t\t<el-select :disabled=\"ro.yuangongzhuangtai\" v-model=\"ruleForm.yuangongzhuangtai\" placeholder=\"请选择人员状态\" >\r\n\t\t\t\t\t\t<el-option\r\n\t\t\t\t\t\t\tv-for=\"(item,index) in yuangongzhuangtaiOptions\"\r\n\t\t\t\t\t\t\tv-bind:key=\"index\"\r\n\t\t\t\t\t\t\t:label=\"item\"\r\n\t\t\t\t\t\t\t:value=\"item\">\r\n\t\t\t\t\t\t</el-option>\r\n\t\t\t\t\t</el-select>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else class=\"input\" label=\"人员状态\" prop=\"yuangongzhuangtai\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.yuangongzhuangtai\"\r\n\t\t\t\t\t\tplaceholder=\"人员状态\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"upload\" v-if=\"type!='info'&& !ro.yuangongdangan\" label=\"人员档案\" prop=\"yuangongdangan\">\r\n\t\t\t\t\t<file-upload\r\n\t\t\t\t\t\ttip=\"点击上传人员档案\"\r\n\t\t\t\t\t\taction=\"file/upload\"\r\n\t\t\t\t\t\t:limit=\"1\"\r\n\t\t\t\t\t\t:multiple=\"true\"\r\n\t\t\t\t\t\t:fileUrls=\"ruleForm.yuangongdangan?ruleForm.yuangongdangan:''\"\r\n\t\t\t\t\t\t@change=\"yuangongdanganUploadChange\"\r\n\t\t\t\t\t></file-upload>\r\n\t\t\t\t</el-form-item>  \r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else-if=\"ruleForm.yuangongdangan\" label=\"人员档案\" prop=\"yuangongdangan\">\r\n\t\t\t\t\t<el-button :style='{\"border\":\"0\",\"cursor\":\"pointer\",\"padding\":\"0 15px\",\"margin\":\"0 20px 0 0\",\"outline\":\"none\",\"color\":\"rgba(255, 255, 255, 1)\",\"borderRadius\":\"4px\",\"background\":\"#18c1b9\",\"width\":\"auto\",\"lineHeight\":\"40px\",\"fontSize\":\"14px\",\"height\":\"40px\"}' type=\"text\" size=\"small\" @click=\"download($base.url+ruleForm.yuangongdangan)\">下载</el-button>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' v-else-if=\"!ruleForm.yuangongdangan\" label=\"人员档案\" prop=\"yuangongdangan\">\r\n\t\t\t\t\t<el-button :style='{\"border\":\"0\",\"cursor\":\"pointer\",\"padding\":\"0 15px\",\"margin\":\"0 20px 0 0\",\"outline\":\"none\",\"color\":\"rgba(255, 255, 255, 1)\",\"borderRadius\":\"4px\",\"background\":\"#18c1b9\",\"width\":\"auto\",\"lineHeight\":\"40px\",\"fontSize\":\"14px\",\"height\":\"40px\"}' type=\"text\" size=\"small\">无</el-button>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"date\" v-if=\"type!='info'\" label=\"入职日期\" prop=\"ruzhiriqi\">\r\n\t\t\t\t\t<el-date-picker\r\n\t\t\t\t\t\tformat=\"yyyy 年 MM 月 dd 日\"\r\n\t\t\t\t\t\tvalue-format=\"yyyy-MM-dd\"\r\n\t\t\t\t\t\tv-model=\"ruleForm.ruzhiriqi\" \r\n\t\t\t\t\t\ttype=\"date\"\r\n\t\t\t\t\t\t:readonly=\"ro.ruzhiriqi\"\r\n\t\t\t\t\t\tplaceholder=\"入职日期\"\r\n\t\t\t\t\t></el-date-picker> \r\n\t\t\t\t</el-form-item>\r\n\t\t\t\t<el-form-item :style='{\"margin\":\"0 0 20px 0\"}' class=\"input\" v-else-if=\"ruleForm.ruzhiriqi\" label=\"入职日期\" prop=\"ruzhiriqi\">\r\n\t\t\t\t\t<el-input v-model=\"ruleForm.ruzhiriqi\" placeholder=\"入职日期\" readonly></el-input>\r\n\t\t\t\t</el-form-item>\r\n\t\t\t</template>\r\n\t\t\t<el-form-item :style='{\"padding\":\"0\",\"margin\":\"0\"}' class=\"btn\">\r\n\t\t\t\t<el-button class=\"btn3\"  v-if=\"type!='info'\" type=\"success\" @click=\"onSubmit\">\r\n\t\t\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"margin\":\"0 2px\",\"fontSize\":\"14px\",\"color\":\"#fff\",\"height\":\"40px\"}'></span>\r\n\t\t\t\t\t提交\r\n\t\t\t\t</el-button>\r\n\t\t\t\t<el-button class=\"btn4\" v-if=\"type!='info'\" type=\"success\" @click=\"back()\">\r\n\t\t\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"margin\":\"0 2px\",\"fontSize\":\"14px\",\"color\":\"#fff\",\"height\":\"40px\"}'></span>\r\n\t\t\t\t\t取消\r\n\t\t\t\t</el-button>\r\n\t\t\t\t<el-button class=\"btn5\" v-if=\"type=='info'\" type=\"success\" @click=\"back()\">\r\n\t\t\t\t\t<span class=\"icon iconfont icon-xihuan\" :style='{\"margin\":\"0 2px\",\"fontSize\":\"14px\",\"color\":\"#fff\",\"height\":\"40px\"}'></span>\r\n\t\t\t\t\t返回\r\n\t\t\t\t</el-button>\r\n\t\t\t</el-form-item>\r\n\t\t</el-form>\r\n    \r\n\r\n  </div>\r\n</template>\r\n<script>\r\n// 数字，邮件，手机，url，身份证校验\r\nimport { isNumber,isIntNumer,isEmail,isPhone, isMobile,isURL,checkIdCard } from \"@/utils/validate\";\r\nexport default {\r\n\tdata() {\r\n\t\tlet self = this\r\n\t\tvar validateIdCard = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!checkIdCard(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的身份证号码\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateUrl = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isURL(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的URL地址\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateMobile = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isMobile(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的手机号码\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validatePhone = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isPhone(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的电话号码\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateEmail = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isEmail(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入正确的邮箱地址\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateNumber = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isNumber(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入数字\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\tvar validateIntNumber = (rule, value, callback) => {\r\n\t\t\tif(!value){\r\n\t\t\t\tcallback();\r\n\t\t\t} else if (!isIntNumer(value)) {\r\n\t\t\t\tcallback(new Error(\"请输入整数\"));\r\n\t\t\t} else {\r\n\t\t\t\tcallback();\r\n\t\t\t}\r\n\t\t};\r\n\t\treturn {\r\n\t\t\tid: '',\r\n\t\t\ttype: '',\r\n\t\t\t\r\n\t\t\t\r\n\t\t\tro:{\r\n\t\t\t\tgonghao : false,\r\n\t\t\t\txingming : false,\r\n\t\t\t\txingbie : false,\r\n\t\t\t\tlianxidianhua : false,\r\n\t\t\t\tbumen : false,\r\n\t\t\t\tzhiwei : false,\r\n\t\t\t\tyuangongzhuangtai : false,\r\n\t\t\t\tyuangongdangan : false,\r\n\t\t\t\truzhiriqi : false,\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t\r\n\t\t\truleForm: {\r\n\t\t\t\tgonghao: '',\r\n\t\t\t\txingming: '',\r\n\t\t\t\txingbie: '',\r\n\t\t\t\tlianxidianhua: '',\r\n\t\t\t\tbumen: '',\r\n\t\t\t\tzhiwei: '',\r\n\t\t\t\tyuangongzhuangtai: '',\r\n\t\t\t\tyuangongdangan: '',\r\n\t\t\t\truzhiriqi: '',\r\n\t\t\t},\r\n\t\t\r\n\t\t\tgonghaoOptions: [],\r\n\t\t\tyuangongzhuangtaiOptions: [],\r\n\r\n\t\t\t\r\n\t\t\trules: {\r\n\t\t\t\tgonghao: [\r\n\t\t\t\t],\r\n\t\t\t\txingming: [\r\n\t\t\t\t],\r\n\t\t\t\txingbie: [\r\n\t\t\t\t],\r\n\t\t\t\tlianxidianhua: [\r\n\t\t\t\t],\r\n\t\t\t\tbumen: [\r\n\t\t\t\t],\r\n\t\t\t\tzhiwei: [\r\n\t\t\t\t],\r\n\t\t\t\tyuangongzhuangtai: [\r\n\t\t\t\t\t{ required: true, message: '人员状态不能为空', trigger: 'blur' },\r\n\t\t\t\t],\r\n\t\t\t\tyuangongdangan: [\r\n\t\t\t\t],\r\n\t\t\t\truzhiriqi: [\r\n\t\t\t\t],\r\n\t\t\t}\r\n\t\t};\r\n\t},\r\n\tprops: [\"parent\"],\r\n\tcomputed: {\r\n\r\n\r\n\r\n\t},\r\n    components: {\r\n    },\r\n\tcreated() {\r\n\t},\r\n\tmethods: {\r\n\t\t\r\n\t\t// 下载\r\n\t\tdownload(file){\r\n\t\t\twindow.open(`${file}`)\r\n\t\t},\r\n\t\t// 初始化\r\n\t\tinit(id,type) {\r\n\t\t\tif (id) {\r\n\t\t\t\tthis.id = id;\r\n\t\t\t\tthis.type = type;\r\n\t\t\t}\r\n\t\t\tif(this.type=='info'||this.type=='else'){\r\n\t\t\t\tthis.info(id);\r\n\t\t\t}else if(this.type=='logistics'){\r\n\t\t\t\tthis.logistics=false;\r\n\t\t\t\tthis.info(id);\r\n\t\t\t}else if(this.type=='cross'){\r\n\t\t\t\tvar obj = this.$storage.getObj('crossObj');\r\n\t\t\t\tfor (var o in obj){\r\n\t\t\t\t\t\tif(o=='gonghao'){\r\n\t\t\t\t\t\t\tthis.ruleForm.gonghao = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.gonghao = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='xingming'){\r\n\t\t\t\t\t\t\tthis.ruleForm.xingming = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.xingming = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='xingbie'){\r\n\t\t\t\t\t\t\tthis.ruleForm.xingbie = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.xingbie = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='lianxidianhua'){\r\n\t\t\t\t\t\t\tthis.ruleForm.lianxidianhua = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.lianxidianhua = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='bumen'){\r\n\t\t\t\t\t\t\tthis.ruleForm.bumen = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.bumen = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='zhiwei'){\r\n\t\t\t\t\t\t\tthis.ruleForm.zhiwei = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.zhiwei = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='yuangongzhuangtai'){\r\n\t\t\t\t\t\t\tthis.ruleForm.yuangongzhuangtai = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.yuangongzhuangtai = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='yuangongdangan'){\r\n\t\t\t\t\t\t\tthis.ruleForm.yuangongdangan = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.yuangongdangan = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(o=='ruzhiriqi'){\r\n\t\t\t\t\t\t\tthis.ruleForm.ruzhiriqi = obj[o];\r\n\t\t\t\t\t\t\tthis.ro.ruzhiriqi = true;\r\n\t\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 获取用户信息\r\n\t\t\tthis.$http({\r\n\t\t\t\turl: `${this.$storage.get('sessionTable')}/session`,\r\n\t\t\t\tmethod: \"get\"\r\n\t\t\t}).then(({ data }) => {\r\n\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\t\r\n\t\t\t\t\tvar json = data.data;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t\r\n            this.$http({\r\n\t\t\t\turl: `option/yuangong/gonghao`,\r\n\t\t\t\tmethod: \"get\"\r\n            }).then(({ data }) => {\r\n\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\tthis.gonghaoOptions = data.data;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t\t}\r\n            });\r\n            this.yuangongzhuangtaiOptions = \"在职,离职\".split(',')\r\n\t\t\t\r\n\t\t},\r\n\t\t\t// 下二随\r\n\t\t\tgonghaoChange () {\r\n\t\t\t\tthis.$http({\r\n\t\t\t\t\turl: `follow/yuangong/gonghao?columnValue=`+ this.ruleForm.gonghao,\r\n\t\t\t\t\tmethod: \"get\"\r\n\t\t\t\t}).then(({ data }) => {\r\n\t\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\t\tif(data.data.xingming){\r\n\t\t\t\t\t\t\tthis.ruleForm.xingming = data.data.xingming\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(data.data.xingbie){\r\n\t\t\t\t\t\t\tthis.ruleForm.xingbie = data.data.xingbie\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(data.data.lianxidianhua){\r\n\t\t\t\t\t\t\tthis.ruleForm.lianxidianhua = data.data.lianxidianhua\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(data.data.bumen){\r\n\t\t\t\t\t\t\tthis.ruleForm.bumen = data.data.bumen\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif(data.data.zhiwei){\r\n\t\t\t\t\t\t\tthis.ruleForm.zhiwei = data.data.zhiwei\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n    // 多级联动参数\r\n\r\n    info(id) {\r\n      this.$http({\r\n        url: `yuangongdangan/info/${id}`,\r\n        method: \"get\"\r\n      }).then(({ data }) => {\r\n        if (data && data.code === 0) {\r\n        this.ruleForm = data.data;\r\n        //解决前台上传图片后台不显示的问题\r\n        let reg=new RegExp('../../../upload','g')//g代表全部\r\n        } else {\r\n          this.$message.error(data.msg);\r\n        }\r\n      });\r\n    },\r\n\r\n\r\n    // 提交\r\n    onSubmit() {\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\tif(this.ruleForm.yuangongdangan!=null) {\r\n\t\tthis.ruleForm.yuangongdangan = this.ruleForm.yuangongdangan.replace(new RegExp(this.$base.url,\"g\"),\"\");\r\n\t}\r\n\r\n\r\nvar objcross = this.$storage.getObj('crossObj');\r\n      //更新跨表属性\r\n       var crossuserid;\r\n       var crossrefid;\r\n       var crossoptnum;\r\n       if(this.type=='cross'){\r\n                var statusColumnName = this.$storage.get('statusColumnName');\r\n                var statusColumnValue = this.$storage.get('statusColumnValue');\r\n                if(statusColumnName!='') {\r\n                        var obj = this.$storage.getObj('crossObj');\r\n                       if(statusColumnName && !statusColumnName.startsWith(\"[\")) {\r\n                               for (var o in obj){\r\n                                 if(o==statusColumnName){\r\n                                   obj[o] = statusColumnValue;\r\n                                 }\r\n                               }\r\n                               var table = this.$storage.get('crossTable');\r\n                             this.$http({\r\n                                 url: `${table}/update`,\r\n                                 method: \"post\",\r\n                                 data: obj\r\n                               }).then(({ data }) => {});\r\n                       } else {\r\n                               crossuserid=this.$storage.get('userid');\r\n                               crossrefid=obj['id'];\r\n                               crossoptnum=this.$storage.get('statusColumnName');\r\n                               crossoptnum=crossoptnum.replace(/\\[/,\"\").replace(/\\]/,\"\");\r\n                        }\r\n                }\r\n        }\r\n\t\tthis.$refs[\"ruleForm\"].validate(valid => {\r\n\t\t\tif (valid) {\r\n\t\t\t\tif(crossrefid && crossuserid) {\r\n\t\t\t\t\tthis.ruleForm.crossuserid = crossuserid;\r\n\t\t\t\t\tthis.ruleForm.crossrefid = crossrefid;\r\n\t\t\t\t\tlet params = { \r\n\t\t\t\t\t\tpage: 1, \r\n\t\t\t\t\t\tlimit: 10, \r\n\t\t\t\t\t\tcrossuserid:this.ruleForm.crossuserid,\r\n\t\t\t\t\t\tcrossrefid:this.ruleForm.crossrefid,\r\n\t\t\t\t\t} \r\n\t\t\t\tthis.$http({ \r\n\t\t\t\t\turl: \"yuangongdangan/page\", \r\n\t\t\t\t\tmethod: \"get\", \r\n\t\t\t\t\tparams: params \r\n\t\t\t\t}).then(({ \r\n\t\t\t\t\tdata \r\n\t\t\t\t}) => { \r\n\t\t\t\t\tif (data && data.code === 0) { \r\n\t\t\t\t\t\tif(data.data.total>=crossoptnum) {\r\n\t\t\t\t\t\t\tthis.$message.error(this.$storage.get('tips'));\r\n\t\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.$http({\r\n\t\t\t\t\t\t\t\turl: `yuangongdangan/${!this.ruleForm.id ? \"save\" : \"update\"}`,\r\n\t\t\t\t\t\t\t\tmethod: \"post\",\r\n\t\t\t\t\t\t\t\tdata: this.ruleForm\r\n\t\t\t\t\t\t\t}).then(({ data }) => {\r\n\t\t\t\t\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\t\t\t\t\tmessage: \"操作成功\",\r\n\t\t\t\t\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\t\t\t\t\tduration: 1500,\r\n\t\t\t\t\t\t\t\t\t\tonClose: () => {\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.showFlag = true;\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.addOrUpdateFlag = false;\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.yuangongdanganCrossAddOrUpdateFlag = false;\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.search();\r\n\t\t\t\t\t\t\t\t\t\t\tthis.parent.contentStyleChange();\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else { \r\n\t\t\t\t} \r\n\t\t\t});\r\n\t\t} else {\r\n\t\t\tthis.$http({\r\n\t\t\t\turl: `yuangongdangan/${!this.ruleForm.id ? \"save\" : \"update\"}`,\r\n\t\t\t\tmethod: \"post\",\r\n\t\t\t   data: this.ruleForm\r\n\t\t\t}).then(({ data }) => {\r\n\t\t\t\tif (data && data.code === 0) {\r\n\t\t\t\t\tthis.$message({\r\n\t\t\t\t\t\tmessage: \"操作成功\",\r\n\t\t\t\t\t\ttype: \"success\",\r\n\t\t\t\t\t\tduration: 1500,\r\n\t\t\t\t\t\tonClose: () => {\r\n\t\t\t\t\t\t\tthis.parent.showFlag = true;\r\n\t\t\t\t\t\t\tthis.parent.addOrUpdateFlag = false;\r\n\t\t\t\t\t\t\tthis.parent.yuangongdanganCrossAddOrUpdateFlag = false;\r\n\t\t\t\t\t\t\tthis.parent.search();\r\n\t\t\t\t\t\t\tthis.parent.contentStyleChange();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$message.error(data.msg);\r\n\t\t\t   }\r\n\t\t\t});\r\n\t\t }\r\n         }\r\n       });\r\n    },\r\n    // 获取uuid\r\n    getUUID () {\r\n      return new Date().getTime();\r\n    },\r\n    // 返回\r\n    back() {\r\n      this.parent.showFlag = true;\r\n      this.parent.addOrUpdateFlag = false;\r\n      this.parent.yuangongdanganCrossAddOrUpdateFlag = false;\r\n      this.parent.contentStyleChange();\r\n    },\r\n    yuangongdanganUploadChange(fileUrls) {\r\n\t    this.ruleForm.yuangongdangan = fileUrls;\r\n    },\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\t.amap-wrapper {\r\n\t\twidth: 100%;\r\n\t\theight: 500px;\r\n\t}\r\n\t\r\n\t.search-box {\r\n\t\tposition: absolute;\r\n\t}\r\n\t\r\n\t.el-date-editor.el-input {\r\n\t\twidth: auto;\r\n\t}\r\n\t\r\n\t.add-update-preview .el-form-item /deep/ .el-form-item__label {\r\n\t  \t  padding: 0 10px 0 0;\r\n\t  \t  color: #374254;\r\n\t  \t  font-weight: 600;\r\n\t  \t  width: 80px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  line-height: 40px;\r\n\t  \t  text-align: right;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .el-form-item /deep/ .el-form-item__content {\r\n\t  margin-left: 80px;\r\n\t}\r\n\t\r\n\t.add-update-preview .el-input /deep/ .el-input__inner {\r\n\t  \t  border: 1px solid rgba(167, 180, 201,.3);\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 12px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t.add-update-preview .el-input-number /deep/ .el-input__inner {\r\n\t\ttext-align: left;\r\n\t  \t  border: 1px solid rgba(167, 180, 201,.3);\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 12px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t.add-update-preview .el-input-number /deep/ .el-input-number__decrease {\r\n\t\tdisplay: none;\r\n\t}\r\n\t.add-update-preview .el-input-number /deep/ .el-input-number__increase {\r\n\t\tdisplay: none;\r\n\t}\r\n\t\r\n\t.add-update-preview .el-select /deep/ .el-input__inner {\r\n\t  \t  border:  1px solid rgba(167, 180, 201,.3) ;\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 10px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .el-date-editor /deep/ .el-input__inner {\r\n\t  \t  border:  1px solid rgba(167, 180, 201,.3) ;\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 0 10px 0 30px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 40px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview /deep/ .el-upload--picture-card {\r\n\t\tbackground: transparent;\r\n\t\tborder: 0;\r\n\t\tborder-radius: 0;\r\n\t\twidth: auto;\r\n\t\theight: auto;\r\n\t\tline-height: initial;\r\n\t\tvertical-align: middle;\r\n\t}\r\n\t\r\n\t.add-update-preview /deep/ .upload .upload-img {\r\n\t  \t  border: 2px dashed rgba(167, 180, 201,.3) ;\r\n\t  \t  cursor: pointer;\r\n\t  \t  border-radius: 6px;\r\n\t  \t  color: #a7b4c9  ;\r\n\t  \t  width: 200px;\r\n\t  \t  font-size: 32px;\r\n\t  \t  line-height: 200px;\r\n\t  \t  text-align: center;\r\n\t  \t  height: 200px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview /deep/ .el-upload-list .el-upload-list__item {\r\n\t  \t  border: 2px dashed rgba(167, 180, 201,.3) ;\r\n\t  \t  cursor: pointer;\r\n\t  \t  border-radius: 6px;\r\n\t  \t  color: #a7b4c9  ;\r\n\t  \t  width: 200px;\r\n\t  \t  font-size: 32px;\r\n\t  \t  line-height: 200px;\r\n\t  \t  text-align: center;\r\n\t  \t  height: 200px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview /deep/ .el-upload .el-icon-plus {\r\n\t  \t  border: 2px dashed rgba(167, 180, 201,.3) ;\r\n\t  \t  cursor: pointer;\r\n\t  \t  border-radius: 6px;\r\n\t  \t  color: #a7b4c9  ;\r\n\t  \t  width: 200px;\r\n\t  \t  font-size: 32px;\r\n\t  \t  line-height: 200px;\r\n\t  \t  text-align: center;\r\n\t  \t  height: 200px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .el-textarea /deep/ .el-textarea__inner {\r\n\t  \t  border: 1px solid rgba(167, 180, 201,.3)     ;\r\n\t  \t  border-radius: 4px;\r\n\t  \t  padding: 12px;\r\n\t  \t  outline: none;\r\n\t  \t  color: #a7b4c9 ;\r\n\t  \t  width: 400px;\r\n\t  \t  font-size: 14px;\r\n\t  \t  height: 120px;\r\n\t  \t}\r\n\t\r\n\t.add-update-preview .btn .btn1 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #6574cd;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn1:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn2 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #6574cd;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn2:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn3 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #ff2b88;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn3:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn4 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #ff2b88;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn4:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn5 {\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\tborder-radius: 4px;\r\n\t\t\t\tpadding: 0 24px;\r\n\t\t\t\tmargin: 4px;\r\n\t\t\t\toutline: none;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground: #ff2b88;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\theight: 40px;\r\n\t\t\t}\r\n\t\r\n\t.add-update-preview .btn .btn5:hover {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n</style>\r\n"]}]}