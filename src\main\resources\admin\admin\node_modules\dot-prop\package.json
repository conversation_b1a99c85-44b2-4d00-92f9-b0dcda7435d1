{"name": "dot-prop", "version": "4.2.0", "description": "Get, set, or delete a property from a nested object using a dot path", "license": "MIT", "repository": "sindresorhus/dot-prop", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava", "bench": "matcha bench.js"}, "files": ["index.js"], "keywords": ["obj", "object", "prop", "property", "dot", "path", "get", "set", "delete", "del", "access", "notation", "dotty"], "dependencies": {"is-obj": "^1.0.0"}, "devDependencies": {"ava": "*", "matcha": "^0.7.0", "xo": "*"}, "xo": {"esnext": true}, "_resolved": "https://registry.npm.taobao.org/dot-prop/download/dot-prop-4.2.0.tgz", "_integrity": "sha1-HxngwuGqDjJ5fEl5nyg3rGr2nFc=", "_from": "dot-prop@4.2.0"}