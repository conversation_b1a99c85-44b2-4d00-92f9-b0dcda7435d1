{"name": "is-accessor-descriptor", "description": "Returns true if a value has the characteristics of a valid JavaScript accessor descriptor.", "version": "1.0.0", "homepage": "https://github.com/jonschlinkert/is-accessor-descriptor", "author": "<PERSON> (https://github.com/jonschlinkert)", "contributors": ["<PERSON> (http://twitter.com/jonschlink<PERSON>)", "<PERSON><PERSON><PERSON> (www.rouvenwessling.de)"], "repository": "jonschlinkert/is-accessor-descriptor", "bugs": {"url": "https://github.com/jonschlinkert/is-accessor-descriptor/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"kind-of": "^6.0.0"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.5.3"}, "keywords": ["accessor", "check", "data", "descriptor", "get", "getter", "is", "keys", "object", "properties", "property", "set", "setter", "type", "valid", "value"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["is-accessor-descriptor", "is-data-descriptor", "is-descriptor", "is-plain-object", "isobject"]}, "lint": {"reflinks": true}}, "_resolved": "https://registry.npm.taobao.org/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz", "_integrity": "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=", "_from": "is-accessor-descriptor@1.0.0"}