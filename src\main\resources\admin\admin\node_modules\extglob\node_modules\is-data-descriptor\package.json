{"name": "is-data-descriptor", "description": "Returns true if a value has the characteristics of a valid JavaScript data descriptor.", "version": "1.0.0", "homepage": "https://github.com/jonschlinkert/is-data-descriptor", "author": "<PERSON> (https://github.com/jonschlinkert)", "contributors": ["<PERSON> (http://twitter.com/jonschlink<PERSON>)", "<PERSON><PERSON><PERSON> (www.rouvenwessling.de)"], "repository": "jonschlinkert/is-data-descriptor", "bugs": {"url": "https://github.com/jonschlinkert/is-data-descriptor/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"kind-of": "^6.0.0"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.5.3"}, "keywords": ["accessor", "check", "data", "descriptor", "get", "getter", "is", "keys", "object", "properties", "property", "set", "setter", "type", "valid", "value"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["is-accessor-descriptor", "is-data-descriptor", "is-descriptor", "isobject"]}, "lint": {"reflinks": true}}, "_resolved": "https://registry.npm.taobao.org/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz", "_integrity": "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=", "_from": "is-data-descriptor@1.0.0"}