{"name": "is-descriptor", "description": "Returns true if a value has the characteristics of a valid JavaScript descriptor. Works for data descriptors and accessor descriptors.", "version": "1.0.2", "homepage": "https://github.com/jonschlinkert/is-descriptor", "author": "<PERSON> (https://github.com/jonschlinkert)", "contributors": ["<PERSON> (https://twitter.com/doowb)", "<PERSON> (http://twitter.com/jonschlink<PERSON>)", "(https://github.com/wtgtybhertgeghgtwtg)"], "repository": "jonschlinkert/is-descriptor", "bugs": {"url": "https://github.com/jonschlinkert/is-descriptor/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.5.3"}, "keywords": ["accessor", "check", "data", "descriptor", "get", "getter", "is", "keys", "object", "properties", "property", "set", "setter", "type", "valid", "value"], "verb": {"related": {"list": ["is-accessor-descriptor", "is-data-descriptor", "is-descriptor", "isobject"]}, "plugins": ["gulp-format-md"], "toc": false, "layout": "default", "tasks": ["readme"], "lint": {"reflinks": true}}, "_resolved": "https://registry.npm.taobao.org/is-descriptor/download/is-descriptor-1.0.2.tgz", "_integrity": "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=", "_from": "is-descriptor@1.0.2"}