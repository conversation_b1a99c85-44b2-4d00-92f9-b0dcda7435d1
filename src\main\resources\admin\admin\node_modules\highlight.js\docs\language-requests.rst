On requesting new languages
===========================

This is a general answer to requests for adding new languages that appear from
time to time in the highlight.js issue tracker and discussion group.

    Highlight.js doesn't have a fundamental plan for implementing languages,
    instead the project works by accepting language definitions from
    interested contributors. There are also no rules at the moment forbidding
    any languages from being added to the library, no matter how obscure or
    weird.

    This means that there's no point in requesting a new language without
    providing an implementation for it. If you want to see a particular language
    included in highlight.js but cannot implement it, the best way to make it
    happen is to get another developer interested in doing so. Here's our
    :doc:`language-guide`.
