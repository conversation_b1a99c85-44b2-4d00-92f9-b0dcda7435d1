{"name": "html-entities", "version": "1.2.1", "description": "Faster HTML entities encode/decode library.", "keywords": ["html", "html entities", "html entities encode", "html entities decode", "entities", "entities encode", "entities decode"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"chai": "^1.9.1", "mocha": "^1.21.4", "unit-coverage": "^3.0.1", "node-html-encoder": "*", "entities": "*", "coveralls": "^2.11.2"}, "repository": {"type": "git", "url": "https://github.com/mdevils/node-html-entities.git"}, "main": "index", "engines": ["node >= 0.4.0"], "unit-coverage": {"common": ["-s", "lib/**/*.js", "-t", "test/**/*.js"]}, "scripts": {"test": "mocha --recursive -R spec test", "benchmark": "node benchmark/benchmark", "coverage": "unit-coverage run -p common", "coverage-html": "unit-coverage run -p common -r html -o coverage.html", "travis": "npm test && unit-coverage run -p common -r lcov -o coverage.lcov && cat coverage.lcov | coveralls"}, "files": ["index.js", "lib", "LICENSE"], "license": "MIT", "_resolved": "https://registry.npm.taobao.org/html-entities/download/html-entities-1.2.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhtml-entities%2Fdownload%2Fhtml-entities-1.2.1.tgz", "_integrity": "sha1-DfKTUfByEWNRXfueVUPl9u7VFi8=", "_from": "html-entities@1.2.1"}