{"name": "big.js", "description": "A small, fast, easy-to-use library for arbitrary-precision decimal arithmetic", "version": "3.2.0", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "repository": {"type": "git", "url": "https://github.com/MikeMcl/big.js.git"}, "main": "big.js", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/MikeMcl/big.js/issues"}, "engines": {"node": "*"}, "license": "MIT", "scripts": {"test": "node ./test/every-test.js", "build": "uglifyjs big.js --source-map doc/big.js.map -c -m -o big.min.js --preamble \"/* big.js v3.2.0 https://github.com/MikeMcl/big.js/LICENCE */\""}, "files": ["big.js", "big.min.js"], "_resolved": "https://registry.npm.taobao.org/big.js/download/big.js-3.2.0.tgz", "_integrity": "sha1-pfwpi4G54Nyi5FiCR4S2XFK6WI4=", "_from": "big.js@3.2.0"}