{"name": "iferr", "version": "0.1.5", "description": "Higher-order functions for easier error handling", "main": "index.js", "scripts": {"test": "mocha", "prepublish": "coffee -c index.coffee"}, "repository": {"type": "git", "url": "https://github.com/shesek/iferr"}, "keywords": ["error", "errors"], "author": "Nadav I<PERSON>gi", "license": "MIT", "bugs": {"url": "https://github.com/shesek/iferr/issues"}, "homepage": "https://github.com/shesek/iferr", "devDependencies": {"coffee-script": "^1.7.1", "mocha": "^1.18.2"}, "_resolved": "https://registry.npm.taobao.org/iferr/download/iferr-0.1.5.tgz", "_integrity": "sha1-xg7taebY/bazEEofy8ocGS3FtQE=", "_from": "iferr@0.1.5"}