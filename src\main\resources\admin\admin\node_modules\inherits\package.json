{"name": "inherits", "description": "Browser-friendly inheritance fully compatible with standard node.js inherits()", "version": "2.0.4", "keywords": ["inheritance", "class", "klass", "oop", "object-oriented", "inherits", "browser", "browserify"], "main": "./inherits.js", "browser": "./inherits_browser.js", "repository": "git://github.com/isaacs/inherits", "license": "ISC", "scripts": {"test": "tap"}, "devDependencies": {"tap": "^14.2.4"}, "files": ["inherits.js", "inherits_browser.js"], "_resolved": "https://registry.npm.taobao.org/inherits/download/inherits-2.0.4.tgz?cache=0&sync_timestamp=1560975547815&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Finherits%2Fdownload%2Finherits-2.0.4.tgz", "_integrity": "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=", "_from": "inherits@2.0.4"}