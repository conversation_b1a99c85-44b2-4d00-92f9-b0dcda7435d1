{"name": "invert-kv", "version": "2.0.0", "description": "Invert the key/value of an object. Example: `{foo: 'bar'}` → `{bar: 'foo'}`", "license": "MIT", "repository": "sindresorhus/invert-kv", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["object", "key", "value", "kv", "invert"], "devDependencies": {"ava": "*", "xo": "*"}, "_resolved": "https://registry.npm.taobao.org/invert-kv/download/invert-kv-2.0.0.tgz", "_integrity": "sha1-c5P1r6Weyf9fZ6J2INEcIm4+7AI=", "_from": "invert-kv@2.0.0"}