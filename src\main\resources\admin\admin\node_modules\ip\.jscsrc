{"disallowKeywordsOnNewLine": ["else"], "disallowMixedSpacesAndTabs": true, "disallowMultipleLineStrings": true, "disallowMultipleVarDecl": true, "disallowNewlineBeforeBlockStatements": true, "disallowQuotedKeysInObjects": true, "disallowSpaceAfterObjectKeys": true, "disallowSpaceAfterPrefixUnaryOperators": true, "disallowSpaceBeforePostfixUnaryOperators": true, "disallowSpacesInCallExpression": true, "disallowTrailingComma": true, "disallowTrailingWhitespace": true, "disallowYodaConditions": true, "requireCommaBeforeLineBreak": true, "requireOperatorBeforeLineBreak": true, "requireSpaceAfterBinaryOperators": true, "requireSpaceAfterKeywords": ["if", "for", "while", "else", "try", "catch"], "requireSpaceAfterLineComment": true, "requireSpaceBeforeBinaryOperators": true, "requireSpaceBeforeBlockStatements": true, "requireSpaceBeforeKeywords": ["else", "catch"], "requireSpaceBeforeObjectValues": true, "requireSpaceBetweenArguments": true, "requireSpacesInAnonymousFunctionExpression": {"beforeOpeningCurlyBrace": true}, "requireSpacesInFunctionDeclaration": {"beforeOpeningCurlyBrace": true}, "requireSpacesInFunctionExpression": {"beforeOpeningCurlyBrace": true}, "requireSpacesInConditionalExpression": true, "requireSpacesInForStatement": true, "requireSpacesInsideArrayBrackets": "all", "requireSpacesInsideObjectBrackets": "all", "requireDotNotation": true, "maximumLineLength": 80, "validateIndentation": 2, "validateLineBreaks": "LF", "validateParameterSeparator": ", ", "validateQuoteMarks": "'"}