{"name": "ipaddr.js", "description": "A library for manipulating IPv4 and IPv6 addresses in JavaScript.", "version": "1.9.0", "author": "whitequark <<EMAIL>>", "directories": {"lib": "./lib"}, "dependencies": {}, "devDependencies": {"coffee-script": "~1.12.6", "uglify-js": "~3.0.19", "nodeunit": ">=0.8.2 <0.8.7"}, "scripts": {"test": "cake build test"}, "files": ["lib/", "ipaddr.min.js"], "keywords": ["ip", "ipv4", "ipv6"], "repository": "git://github.com/whitequark/ipaddr.js", "main": "./lib/ipaddr.js", "engines": {"node": ">= 0.10"}, "license": "MIT", "types": "./lib/ipaddr.js.d.ts", "_resolved": "https://registry.npm.taobao.org/ipaddr.js/download/ipaddr.js-1.9.0.tgz", "_integrity": "sha1-N9905DCg5HVQ/lSi3v4w2KzZX2U=", "_from": "ipaddr.js@1.9.0"}