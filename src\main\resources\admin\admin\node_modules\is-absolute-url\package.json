{"name": "is-absolute-url", "version": "2.1.0", "description": "Check if an URL is absolute", "license": "MIT", "repository": "sindresorhus/is-absolute-url", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": ["url", "absolute", "relative", "uri", "is", "check"], "devDependencies": {"mocha": "*"}, "_resolved": "https://registry.npm.taobao.org/is-absolute-url/download/is-absolute-url-2.1.0.tgz", "_integrity": "sha1-UFMN+4T8yap9vnhS6Do3uTufKqY=", "_from": "is-absolute-url@2.1.0"}