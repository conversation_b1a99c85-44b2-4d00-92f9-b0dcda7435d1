{"name": "is-arguments", "version": "1.0.4", "description": "Is this an arguments object? It's a harder question than you think.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "license": "MIT", "main": "index.js", "scripts": {"pretest": "npm run --silent lint", "test": "npm run --silent tests-only", "tests-only": "node test.js", "posttest": "npm run --silent security", "security": "nsp check", "coverage": "covert test.js", "lint": "npm run --silent jscs && npm run --silent eslint", "jscs": "jscs *.js", "eslint": "eslint *.js"}, "repository": {"type": "git", "url": "git://github.com/ljharb/is-arguments.git"}, "bugs": {"url": "https://github.com/ljharb/is-arguments/issues"}, "homepage": "https://github.com/ljharb/is-arguments", "keywords": ["arguments", "js", "javascript", "is-arguments", "is", "object"], "dependencies": {}, "devDependencies": {"@ljharb/eslint-config": "^13.0.0", "covert": "^1.1.0", "eslint": "^5.8.0", "jscs": "^3.0.7", "nsp": "^3.2.1", "tape": "^4.9.1"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "_resolved": "https://registry.npm.taobao.org/is-arguments/download/is-arguments-1.0.4.tgz", "_integrity": "sha1-P6+WbHy6D/Q3+zH2JQCC/PBEjPM=", "_from": "is-arguments@1.0.4"}