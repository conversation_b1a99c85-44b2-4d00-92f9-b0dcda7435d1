{"name": "is-binary-path", "version": "1.0.1", "description": "Check if a filepath is a binary file", "license": "MIT", "repository": "sindresorhus/is-binary-path", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js"}, "files": ["index.js"], "keywords": ["bin", "binary", "ext", "extensions", "extension", "file", "path", "check", "detect", "is"], "dependencies": {"binary-extensions": "^1.0.0"}, "devDependencies": {"ava": "0.0.4"}, "_resolved": "https://registry.npm.taobao.org/is-binary-path/download/is-binary-path-1.0.1.tgz", "_integrity": "sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg=", "_from": "is-binary-path@1.0.1"}