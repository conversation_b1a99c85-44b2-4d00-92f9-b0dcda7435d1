{"name": "is-callable", "version": "1.1.5", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "description": "Is this JS value callable? Works with Functions and GeneratorFunctions, despite ES6 @@toStringTag.", "license": "MIT", "main": "index.js", "scripts": {"pretest": "npm run --silent lint", "test": "npm run --silent tests-only", "posttest": "npx aud", "tests-only": "npm run --silent test:stock && npm run --silent test:staging", "test:stock": "node test", "test:staging": "node --es-staging test", "coverage": "npm run --silent istanbul", "covert": "covert test", "covert:quiet": "covert test --quiet", "istanbul": "npm run --silent istanbul:clean && npm run --silent istanbul:std && npm run --silent istanbul:harmony && npm run --silent istanbul:merge && istanbul check", "istanbul:clean": "rimraf coverage coverage-std coverage-harmony", "istanbul:merge": "istanbul-merge --out coverage/coverage.raw.json coverage-harmony/coverage.raw.json coverage-std/coverage.raw.json && istanbul report html", "istanbul:harmony": "node --harmony ./node_modules/istanbul/lib/cli.js cover test --dir coverage-harmony", "istanbul:std": "istanbul cover test --report html --dir coverage-std", "prelint": "eclint check *", "lint": "eslint ."}, "repository": {"type": "git", "url": "git://github.com/ljharb/is-callable.git"}, "keywords": ["Function", "function", "callable", "generator", "generator function", "arrow", "arrow function", "ES6", "toStringTag", "@@toStringTag"], "dependencies": {}, "devDependencies": {"@ljharb/eslint-config": "^15.0.2", "covert": "^1.1.1", "eclint": "^2.8.1", "eslint": "^6.7.2", "foreach": "^2.0.5", "istanbul": "1.1.0-alpha.1", "istanbul-merge": "^1.1.1", "make-arrow-function": "^1.1.0", "make-generator-function": "^1.1.0", "rimraf": "^2.7.1", "tape": "^4.12.0"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "_resolved": "https://registry.npm.taobao.org/is-callable/download/is-callable-1.1.5.tgz", "_integrity": "sha1-9+RrWWiQRW23Tn9ul2yzJz0G+qs=", "_from": "is-callable@1.1.5"}