{"name": "is-ci", "version": "1.2.1", "description": "Detect if the current environment is a CI server", "bin": "bin.js", "main": "index.js", "dependencies": {"ci-info": "^1.5.0"}, "devDependencies": {"clear-require": "^1.0.1", "standard": "^11.0.1"}, "scripts": {"test": "standard && node test.js"}, "repository": {"type": "git", "url": "https://github.com/watson/is-ci.git"}, "keywords": ["ci", "continuous", "integration", "test", "detect"], "author": "<PERSON> <<EMAIL>> (https://twitter.com/wa7son)", "license": "MIT", "bugs": {"url": "https://github.com/watson/is-ci/issues"}, "homepage": "https://github.com/watson/is-ci", "coordinates": [55.778255, 12.593033], "_resolved": "https://registry.npm.taobao.org/is-ci/download/is-ci-1.2.1.tgz", "_integrity": "sha1-43ecjuF/zPQoSI9uKBGH8uYyhBw=", "_from": "is-ci@1.2.1"}