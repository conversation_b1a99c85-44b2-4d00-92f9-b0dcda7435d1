{"name": "is-data-descriptor", "description": "Returns true if a value has the characteristics of a valid JavaScript data descriptor.", "version": "0.1.4", "homepage": "https://github.com/jonschlinkert/is-data-descriptor", "author": "<PERSON> (https://github.com/jonschlinkert)", "repository": "jonschlinkert/is-data-descriptor", "bugs": {"url": "https://github.com/jonschlinkert/is-data-descriptor/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"kind-of": "^3.0.2"}, "devDependencies": {"mocha": "*", "should": "*"}, "keywords": ["accessor", "check", "data", "descriptor", "get", "getter", "is", "keys", "object", "properties", "property", "set", "setter", "type", "valid", "value"], "verb": {"related": {"list": ["is-accessor-descriptor", "is-data-descriptor", "is-descriptor", "isobject"]}, "plugins": ["gulp-format-md"]}, "_resolved": "https://registry.npm.taobao.org/is-data-descriptor/download/is-data-descriptor-0.1.4.tgz", "_integrity": "sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=", "_from": "is-data-descriptor@0.1.4"}