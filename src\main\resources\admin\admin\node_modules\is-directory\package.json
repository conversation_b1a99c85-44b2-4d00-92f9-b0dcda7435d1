{"name": "is-directory", "description": "Returns true if a filepath exists on the file system and it's directory.", "version": "0.3.1", "homepage": "https://github.com/jonschlinkert/is-directory", "author": "<PERSON> (https://github.com/jonschlinkert)", "repository": "jonschlinkert/is-directory", "bugs": {"url": "https://github.com/jonschlinkert/is-directory/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"gulp-format-md": "^0.1.9", "mocha": "^2.4.5"}, "keywords": ["dir", "directories", "directory", "dirs", "file", "filepath", "files", "fp", "fs", "node", "node.js", "path", "paths", "system"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["is-glob", "is-relative", "is-absolute"]}, "lint": {"reflinks": true}, "reflinks": ["verb"]}, "_resolved": "https://registry.npm.taobao.org/is-directory/download/is-directory-0.3.1.tgz", "_integrity": "sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE=", "_from": "is-directory@0.3.1"}