{"name": "is-path-cwd", "version": "2.2.0", "description": "Check if a path is the current working directory", "license": "MIT", "repository": "sindresorhus/is-path-cwd", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["path", "cwd", "pwd", "check", "filepath", "file", "folder"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "_resolved": "https://registry.npm.taobao.org/is-path-cwd/download/is-path-cwd-2.2.0.tgz?cache=0&sync_timestamp=1562347283002&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-path-cwd%2Fdownload%2Fis-path-cwd-2.2.0.tgz", "_integrity": "sha1-Z9Q7gmZKe1GR/ZEZEn6zAASKn9s=", "_from": "is-path-cwd@2.2.0"}