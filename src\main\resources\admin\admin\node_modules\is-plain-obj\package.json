{"name": "is-plain-obj", "version": "1.1.0", "description": "Check if a value is a plain object", "license": "MIT", "repository": "sindresorhus/is-plain-obj", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js"}, "files": ["index.js"], "keywords": ["obj", "object", "is", "check", "test", "type", "plain", "vanilla", "pure", "simple"], "devDependencies": {"ava": "0.0.4"}, "_resolved": "https://registry.npm.taobao.org/is-plain-obj/download/is-plain-obj-1.1.0.tgz", "_integrity": "sha1-caUMhCnfync8kqOQpKA7OfzVHT4=", "_from": "is-plain-obj@1.1.0"}