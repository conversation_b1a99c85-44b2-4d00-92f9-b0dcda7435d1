{"name": "is-promise", "version": "2.1.0", "description": "Test whether an object looks like a promises-a+ promise", "main": "index.js", "scripts": {"test": "mocha -R spec"}, "repository": {"type": "git", "url": "https://github.com/then/is-promise.git"}, "author": "ForbesLindesay", "license": "MIT", "devDependencies": {"better-assert": "~0.1.0", "mocha": "~1.7.4"}, "_resolved": "https://registry.npm.taobao.org/is-promise/download/is-promise-2.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-promise%2Fdownload%2Fis-promise-2.1.0.tgz", "_integrity": "sha1-eaKp7OfwlugPNtKy87wWwf9L8/o=", "_from": "is-promise@2.1.0"}