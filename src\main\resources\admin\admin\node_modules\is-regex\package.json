{"name": "is-regex", "version": "1.0.5", "description": "Is this value a JS regex? Works cross-realm/iframe, and despite ES6 @@toStringTag", "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "main": "index.js", "scripts": {"pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "node --harmony --es-staging test", "posttest": "npx aud", "coverage": "covert test/index.js", "lint": "eslint .", "eccheck": "eclint check *.js **/*.js > /dev/null", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git://github.com/ljharb/is-regex.git"}, "bugs": {"url": "https://github.com/ljharb/is-regex/issues"}, "homepage": "https://github.com/ljharb/is-regex", "keywords": ["regex", "regexp", "is", "regular expression", "regular", "expression"], "dependencies": {"has": "^1.0.3"}, "devDependencies": {"@ljharb/eslint-config": "^15.0.2", "auto-changelog": "^1.16.2", "covert": "^1.1.1", "eclint": "^2.8.1", "eslint": "^6.7.2", "tape": "^4.11.0"}, "testling": {"files": "test.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..12.0", "opera/15.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false}, "_resolved": "https://registry.npm.taobao.org/is-regex/download/is-regex-1.0.5.tgz", "_integrity": "sha1-OdWJo1i/GJZ/cmlnEguPwa7XTq4=", "_from": "is-regex@1.0.5"}