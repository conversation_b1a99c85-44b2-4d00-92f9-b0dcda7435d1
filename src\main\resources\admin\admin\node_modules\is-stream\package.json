{"name": "is-stream", "version": "1.1.0", "description": "Check if something is a Node.js stream", "license": "MIT", "repository": "sindresorhus/is-stream", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["stream", "type", "streams", "writable", "readable", "duplex", "transform", "check", "detect", "is"], "devDependencies": {"ava": "*", "tempfile": "^1.1.0", "xo": "*"}, "_resolved": "https://registry.npm.taobao.org/is-stream/download/is-stream-1.1.0.tgz", "_integrity": "sha1-EtSj3U5o4Lec6428hBc66A2RykQ=", "_from": "is-stream@1.1.0"}