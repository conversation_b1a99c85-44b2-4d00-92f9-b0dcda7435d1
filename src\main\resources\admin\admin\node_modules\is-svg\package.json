{"name": "is-svg", "version": "3.0.0", "description": "Check if a string or buffer is SVG", "license": "MIT", "repository": "sindresorhus/is-svg", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["svg", "vector", "graphics", "image", "img", "pic", "picture", "type", "detect", "check", "is", "string", "str", "buffer"], "dependencies": {"html-comment-regex": "^1.1.0"}, "devDependencies": {"ava": "*", "xo": "*"}, "_resolved": "https://registry.npm.taobao.org/is-svg/download/is-svg-3.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-svg%2Fdownload%2Fis-svg-3.0.0.tgz", "_integrity": "sha1-kyHb0pwhLlypnE+peUxxS8r6L3U=", "_from": "is-svg@3.0.0"}