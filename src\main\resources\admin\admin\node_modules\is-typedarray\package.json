{"name": "is-typedarray", "version": "1.0.0", "description": "Detect whether or not an object is a Typed Array", "main": "index.js", "scripts": {"test": "node test"}, "author": "<PERSON> <<EMAIL>> (http://hughsk.io/)", "license": "MIT", "dependencies": {}, "devDependencies": {"tape": "^2.13.1"}, "repository": {"type": "git", "url": "git://github.com/hughsk/is-typedarray.git"}, "keywords": ["typed", "array", "detect", "is", "util"], "bugs": {"url": "https://github.com/hughsk/is-typedarray/issues"}, "homepage": "https://github.com/hughsk/is-typedarray", "_resolved": "https://registry.npm.taobao.org/is-typedarray/download/is-typedarray-1.0.0.tgz", "_integrity": "sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=", "_from": "is-typedarray@1.0.0"}