{"name": "isstream", "version": "0.1.2", "description": "Determine if an object is a Stream", "main": "isstream.js", "scripts": {"test": "tar --xform 's/^package/readable-stream-1.0/' -zxf readable-stream-1.0.*.tgz && tar --xform 's/^package/readable-stream-1.1/' -zxf readable-stream-1.1.*.tgz && node test.js; rm -rf readable-stream-1.?/"}, "repository": {"type": "git", "url": "https://github.com/rvagg/isstream.git"}, "keywords": ["stream", "type", "streams", "readable-stream", "hippo"], "devDependencies": {"tape": "~2.12.3", "core-util-is": "~1.0.0", "isarray": "0.0.1", "string_decoder": "~0.10.x", "inherits": "~2.0.1"}, "author": "<PERSON> Vagg <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/rvagg/isstream/issues"}, "homepage": "https://github.com/rvagg/isstream", "_resolved": "https://registry.npm.taobao.org/isstream/download/isstream-0.1.2.tgz", "_integrity": "sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=", "_from": "isstream@0.1.2"}