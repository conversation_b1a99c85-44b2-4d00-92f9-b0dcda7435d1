{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;AAAA,2CAAuC;AACvC,mCAAwC;AAUxC;;GAEG;AACH,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;AAErC;;GAEG;AACH,SAAgB,SAAS,CACvB,KAAU,EACV,QAA0B,EAC1B,MAA+B,EAC/B,UAAmB,EAAE;IAErB,MAAM,KAAK,GAAG,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC;IAC5E,MAAM,IAAI,GAAkB,EAAE,CAAC;IAC/B,MAAM,KAAK,GAAG,IAAI,GAAG,EAAE,CAAC;IACxB,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAsB,CAAC;IAC/C,MAAM,MAAM,GAAG,IAAI,GAAG,EAAgC,CAAC;IACvD,IAAI,UAAU,GAAG,CAAC,CAAC;IAEnB,MAAM,EACJ,QAAQ,GAAG,GAAG,EACd,UAAU,GAAG,KAAK,EAClB,uBAAuB,GAAG,KAAK,EAC/B,SAAS,GAAG,MAAM,EACnB,GAAG,OAAO,CAAC;IAEZ,yEAAyE;IACzE,MAAM,aAAa,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAEjD,2DAA2D;IAC3D,MAAM,MAAM,GAAS,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;QAClC,IAAI,EAAE,UAAU,GAAG,SAAS;YAAE,OAAO;QACrC,IAAI,uBAAuB,IAAI,KAAK,KAAK,SAAS;YAAE,OAAO;QAC3D,IAAI,IAAI,CAAC,MAAM,GAAG,QAAQ;YAAE,OAAO;QAEnC,yDAAyD;QACzD,IAAI,GAAG,KAAK,SAAS;YAAE,OAAO,aAAa,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;QAEvE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACf,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,EAAE,GAAG,KAAK,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACvE,IAAI,CAAC,GAAG,EAAE,CAAC;QACX,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC;IAEF,MAAM,OAAO,GAAS,UAAU;QAC9B,CAAC,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;YACb,IACE,KAAK,KAAK,IAAI;gBACd,CAAC,OAAO,KAAK,KAAK,QAAQ;oBACxB,OAAO,KAAK,KAAK,UAAU;oBAC3B,OAAO,KAAK,KAAK,QAAQ,CAAC,EAC5B;gBACA,gCAAgC;gBAChC,IAAI,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;oBACvB,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAE,CAAC,CAAC;oBAChD,OAAO,CAAC,uDAAuD;iBAChE;gBAED,2BAA2B;gBAC3B,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;aACpC;YAED,OAAO,aAAa,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;QAClD,CAAC;QACH,CAAC,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;YACb,qBAAqB;YACrB,IAAI,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC;gBAAE,OAAO;YAE7B,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YACjB,MAAM,MAAM,GAAG,aAAa,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;YACxD,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACpB,OAAO,MAAM,CAAC;QAChB,CAAC,CAAC;IAEN,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;IAE5C,0CAA0C;IAC1C,IAAI,MAAM,CAAC,IAAI,EAAE;QACf,MAAM,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;QAC5B,MAAM,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;QAC9B,IAAI,OAAO,GAAG,QAAQ,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,GAAG,EAAE,CAAC;QAEjD,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,EAAE,EAAE;YAC3C,MAAM,OAAO,GAAG,qBAAa,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YAC3C,MAAM,SAAS,GAAG,qBAAa,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YAE/C,OAAO,IAAI,IAAI,OAAO,GAAG,EAAE,IAAI,EAAE,IAAI,SAAS,IAAI,GAAG,EAAE,CAAC;SACzD;QAED,OAAO,YAAY,EAAE,KAAK,EAAE,IAAI,GAAG,GAAG,OAAO,YAAY,GAAG,MAAM,CAAC;KACpE;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAvFD,8BAuFC;AAED;;GAEG;AACH,SAAS,gBAAgB,CAAC,QAA0B;IAClD,IAAI,CAAC,QAAQ;QAAE,OAAO,oBAAQ,CAAC;IAE/B,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE;QACjC,OAAO,QAAQ,CACb,KAAK,EACL,KAAK,EACL,CAAC,KAAU,EAAE,EAAE,CAAC,oBAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,CAAC,EACjD,GAAG,CACJ,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["import { toString } from \"./stringify\";\nimport { stringifyPath } from \"./quote\";\nimport { Next, ToString } from \"./types\";\n\nexport interface Options {\n  maxDepth?: number;\n  maxValues?: number;\n  references?: boolean;\n  skipUndefinedProperties?: boolean;\n}\n\n/**\n * Root path node.\n */\nconst ROOT_SENTINEL = Symbol(\"root\");\n\n/**\n * Stringify any JavaScript value.\n */\nexport function stringify(\n  value: any,\n  replacer?: ToString | null,\n  indent?: string | number | null,\n  options: Options = {}\n) {\n  const space = typeof indent === \"string\" ? indent : \" \".repeat(indent || 0);\n  const path: PropertyKey[] = [];\n  const stack = new Set();\n  const tracking = new Map<any, PropertyKey[]>();\n  const unpack = new Map<PropertyKey[], PropertyKey[]>();\n  let valueCount = 0;\n\n  const {\n    maxDepth = 100,\n    references = false,\n    skipUndefinedProperties = false,\n    maxValues = 100000\n  } = options;\n\n  // Wrap replacer function to support falling back on supported stringify.\n  const valueToString = replacerToString(replacer);\n\n  // Every time you call `next(value)` execute this function.\n  const onNext: Next = (value, key) => {\n    if (++valueCount > maxValues) return;\n    if (skipUndefinedProperties && value === undefined) return;\n    if (path.length > maxDepth) return;\n\n    // An undefined key is treated as an out-of-band \"value\".\n    if (key === undefined) return valueToString(value, space, onNext, key);\n\n    path.push(key);\n    const result = builder(value, key === ROOT_SENTINEL ? undefined : key);\n    path.pop();\n    return result;\n  };\n\n  const builder: Next = references\n    ? (value, key) => {\n        if (\n          value !== null &&\n          (typeof value === \"object\" ||\n            typeof value === \"function\" ||\n            typeof value === \"symbol\")\n        ) {\n          // Track nodes to restore later.\n          if (tracking.has(value)) {\n            unpack.set(path.slice(1), tracking.get(value)!);\n            return; // Avoid serializing referenced nodes on an expression.\n          }\n\n          // Track encountered nodes.\n          tracking.set(value, path.slice(1));\n        }\n\n        return valueToString(value, space, onNext, key);\n      }\n    : (value, key) => {\n        // Stop on recursion.\n        if (stack.has(value)) return;\n\n        stack.add(value);\n        const result = valueToString(value, space, onNext, key);\n        stack.delete(value);\n        return result;\n      };\n\n  const result = onNext(value, ROOT_SENTINEL);\n\n  // Attempt to restore circular references.\n  if (unpack.size) {\n    const sp = space ? \" \" : \"\";\n    const eol = space ? \"\\n\" : \"\";\n    let wrapper = `var x${sp}=${sp}${result};${eol}`;\n\n    for (const [key, value] of unpack.entries()) {\n      const keyPath = stringifyPath(key, onNext);\n      const valuePath = stringifyPath(value, onNext);\n\n      wrapper += `x${keyPath}${sp}=${sp}x${valuePath};${eol}`;\n    }\n\n    return `(function${sp}()${sp}{${eol}${wrapper}return x;${eol}}())`;\n  }\n\n  return result;\n}\n\n/**\n * Create `toString()` function from replacer.\n */\nfunction replacerToString(replacer?: ToString | null): ToString {\n  if (!replacer) return toString;\n\n  return (value, space, next, key) => {\n    return replacer(\n      value,\n      space,\n      (value: any) => toString(value, space, next, key),\n      key\n    );\n  };\n}\n"]}