{"version": 3, "file": "index.spec.js", "sourceRoot": "", "sources": ["../src/index.spec.ts"], "names": [], "mappings": ";;AAAA,iCAAiC;AACjC,mCAAmC;AACnC,mCAA6C;AAK7C,oCAAoC;AACpC,MAAM,SAAS,GAAG,CAAC,GAAuB,EAAE,EAAE;IAC5C,mCAAmC;IACnC,OAAO,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC;AAC1B,CAAC,CAAC;AAEF;;GAEG;AACH,SAAS,IAAI,CACX,KAAU,EACV,MAAc,EACd,MAA+B,EAC/B,OAAiB;IAEjB,OAAO,GAAG,EAAE;QACV,MAAM,CAAC,iBAAS,CAAC,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAClE,CAAC,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,aAAa,CACpB,UAAkB,EAClB,MAAwB,EACxB,OAAiB;IAEjB,OAAO,GAAG,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,CAAC;AAC1E,CAAC;AAED;;GAEG;AACH,SAAS,WAAW,CAAC,IAAY;IAC/B,IAAI;QACF,mCAAmC;QACnC,IAAI,CAAC,IAAI,CAAC,CAAC;QACX,OAAO,IAAI,CAAC;KACb;IAAC,OAAO,GAAG,EAAE;QACZ,IAAI,GAAG,CAAC,IAAI,KAAK,aAAa;YAAE,OAAO,KAAK,CAAC;QAC7C,MAAM,GAAG,CAAC;KACX;AACH,CAAC;AAED;;GAEG;AACH,SAAS,KAAK,CAAC,KAA6B;IAC1C,OAAO,GAAG,EAAE;QACV,KAAK,MAAM,KAAK,IAAI,KAAK,EAAE;YACzB,IAAI,KAAK;gBAAE,EAAE,CAAC,KAAK,EAAE,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;SAC5C;IACH,CAAC,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,UAAU,CACjB,WAAmB,EACnB,SAAkB,EAClB,EAAsB;IAEtB,OAAO,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;AAChF,CAAC;AAED,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;IACpC,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE;QACrB,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE;YACxB,EAAE,CAAC,uBAAuB,EAAE,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;YACvB,EAAE,CAAC,8BAA8B,EAAE,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAC;YAE/D,EAAE,CAAC,gCAAgC,EAAE,IAAI,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC;YAErE,EAAE,CACA,kCAAkC,EAClC,IAAI,CAAC,aAAa,EAAE,gBAAgB,CAAC,CACtC,CAAC;YAEF,EAAE,CAAC,4BAA4B,EAAE,IAAI,CAAC,aAAa,EAAE,iBAAiB,CAAC,CAAC,CAAC;YAEzE,EAAE,CACA,yCAAyC,EACzC,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,CAC5B,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;YACvB,EAAE,CAAC,2BAA2B,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;YAEhD,EAAE,CAAC,yBAAyB,EAAE,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;YAElD,EAAE,CAAC,wBAAwB,EAAE,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;YAEjD,EAAE,CAAC,6BAA6B,EAAE,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAC;YAE9D,EAAE,CAAC,8BAA8B,EAAE,IAAI,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAC;YAEjE,EAAE,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;YACtB,EAAE,CAAC,qCAAqC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC;YAEtE,EAAE,CACA,wBAAwB,EACxB,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,2BAA2B,EAAE,IAAI,CAAC,CACrD,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;YACvB,EAAE,CACA,sCAAsC,EACtC,IAAI,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,sBAAsB,CAAC,CACxD,CAAC;YAEF,EAAE,CACA,iCAAiC,EACjC,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,sBAAsB,CAAC,CACxD,CAAC;YAEF,EAAE,CACA,sCAAsC,EACtC,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;gBAChD,uBAAuB,EAAE,IAAI;aAC9B,CAAC,CACH,CAAC;YAEF,EAAE,CACA,iCAAiC,EACjC,IAAI,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,0BAA0B,CAAC,CAC5D,CAAC;YAEF,EAAE,CACA,wCAAwC,EACxC,IAAI,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,EAAE,4BAA4B,CAAC,CACpE,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,WAAW,EAAE,GAAG,EAAE;YACzB,EAAE,CACA,iCAAiC,EACjC,IAAI,CACF,SAAS,CACP;;;;cAIE,CACH,EACD,2DAA2D,EAC3D,CAAC,CACF,CACF,CAAC;YAEF,EAAE,CACA,4CAA4C,EAC5C,IAAI,CACF,SAAS,CAAC;;;;;;;;WAQT,CAAC,EACF,+EAA+E,EAC/E,CAAC,CACF,CACF,CAAC;YAEF,EAAE,CACA,2CAA2C,EAC3C,IAAI,CACF,SAAS,CAAC;;;;;;YAMR,CAAC,EACH,2EAA2E,EAC3E,CAAC,CACF,CACF,CAAC;YAEF,EAAE,CACA,wCAAwC,EACxC,aAAa,CAAC,qCAAqC,EAAE,CAAC,CAAC,CACxD,CAAC;YAEF,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;gBACvE,MAAM,YAAY,GAAG,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC;gBAEjD,QAAQ,CAAC,SAAS,CAAC,QAAQ,GAAG,GAAG,EAAE,CAAC,QAAQ,CAAC;gBAE7C,IAAI;oBACF,MAAM,CACJ,iBAAS,CAAC;wBACR,WAAW;oBACb,CAAC,CAAC,CACH,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;iBAC5B;wBAAS;oBACR,QAAQ,CAAC,SAAS,CAAC,QAAQ,GAAG,YAAY,CAAC;iBAC5C;YACH,CAAC,CAAC,CAAC;YAEH,QAAQ,CACN,8BAA8B,EAC9B,KAAK,CAAC,CAAC,uBAAuB,EAAE,gCAAgC,CAAC,CAAC,CACnE,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;YAChC,QAAQ,CAAC,MAAM,EAAE,GAAG,EAAE;gBACpB,MAAM,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;gBAExB,EAAE,CAAC,kBAAkB,EAAE,IAAI,CAAC,IAAI,EAAE,WAAW,GAAG,IAAI,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;YACzE,CAAC,CAAC,CAAC;YAEH,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;gBACtB,EAAE,CAAC,+BAA+B,EAAE,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC;YACpE,CAAC,CAAC,CAAC;YAEH,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;gBACtB,EAAE,CAAC,kBAAkB,EAAE,IAAI,CAAC,IAAI,MAAM,CAAC,EAAE,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC;YACjE,CAAC,CAAC,CAAC;YAEH,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;gBACtB,EAAE,CAAC,kBAAkB,EAAE,IAAI,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE,mBAAmB,CAAC,CAAC,CAAC;YACvE,CAAC,CAAC,CAAC;YAEH,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;gBACvB,EAAE,CAAC,kBAAkB,EAAE,IAAI,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE,mBAAmB,CAAC,CAAC,CAAC;YACvE,CAAC,CAAC,CAAC;YAEH,UAAU,CAAC,QAAQ,EAAE,OAAQ,MAAc,KAAK,UAAU,EAAE,GAAG,EAAE;gBAC/D,EAAE,CAAC,kBAAkB,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,oBAAoB,CAAC,CAAC,CAAC;YAC1E,CAAC,CAAC,CAAC;YAEH,UAAU,CAAC,QAAQ,EAAE,OAAQ,MAAc,KAAK,UAAU,EAAE,GAAG,EAAE;gBAC/D,EAAE,CAAC,kBAAkB,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC;YAC7D,CAAC,CAAC,CAAC;YAEH,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE;gBACrB,EAAE,CAAC,kBAAkB,EAAE,IAAI,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE,mBAAmB,CAAC,CAAC,CAAC;YACvE,CAAC,CAAC,CAAC;YAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;gBACnC,EAAE,CACA,mBAAmB,EACnB,IAAI,CACF;oBACE,CAAC,EACC,OAAQ,OAAe,KAAK,WAAW;wBACrC,CAAC,CAAC,MAAM,CAAC,SAAS;wBAClB,CAAC,CAAC,OAAO;iBACd,EACD,IAAI,CACL,CACF,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,UAAU,CAAC,KAAK,EAAE,OAAQ,KAAa,CAAC,IAAI,KAAK,UAAU,EAAE,GAAG,EAAE;YAChE,UAAU,CAAC,KAAK,EAAE,OAAQ,GAAW,KAAK,UAAU,EAAE,GAAG,EAAE;gBACzD,EAAE,CACA,kBAAkB,EAClB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,4BAA4B,CAAC,CAChE,CAAC;YACJ,CAAC,CAAC,CAAC;YAEH,UAAU,CAAC,KAAK,EAAE,OAAQ,GAAW,KAAK,UAAU,EAAE,GAAG,EAAE;gBACzD,EAAE,CACA,kBAAkB,EAClB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,EAAE,0BAA0B,CAAC,CAC5D,CAAC;YACJ,CAAC,CAAC,CAAC;YAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;gBAC/B,QAAQ,CACN,kBAAkB,EAClB,KAAK,CAAC;oBACJ,iBAAiB;oBACjB,4BAA4B;oBAC5B,oCAAoC;oBACpC,wBAAwB;oBACxB,uBAAuB;iBACxB,CAAC,CACH,CAAC;gBAEF,EAAE,CACA,iCAAiC,EACjC,IAAI,CACF,SAAS,CACP,0BAA0B;oBACxB,gCAAgC;oBAChC,sCAAsC;oBACtC,sBAAsB;oBACtB,kBAAkB,CACrB,EACD,qDAAqD,EACrD,CAAC,CACF,CACF,CAAC;gBAEF,UAAU,CAAC,sBAAsB,EAAE,WAAW,CAAC,YAAY,CAAC,EAAE,GAAG,EAAE;oBACjE,QAAQ,CACN,kBAAkB,EAClB,KAAK,CAAC;wBACJ,qBAAqB;wBACrB,gDAAgD;wBAChD,uDAAuD;qBACxD,CAAC,CACH,CAAC;gBACJ,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;gBAC1B,EAAE,CAAC,kBAAkB,EAAE,aAAa,CAAC,4BAA4B,CAAC,CAAC,CAAC;YACtE,CAAC,CAAC,CAAC;YAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;gBAC9B,EAAE,CAAC,0BAA0B,EAAE,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC;gBAC1D,EAAE,CACA,mCAAmC,EACnC,aAAa,CAAC,uBAAuB,CAAC,CACvC,CAAC;gBACF,EAAE,CACA,+BAA+B,EAC/B,aAAa,CAAC,wBAAwB,CAAC,CACxC,CAAC;gBACF,EAAE,CACA,+BAA+B,EAC/B,aAAa,CAAC,gCAAgC,CAAC,CAChD,CAAC;YACJ,CAAC,CAAC,CAAC;YAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;gBAC/B,EAAE,CAAC,kBAAkB,EAAE,aAAa,CAAC,6BAA6B,CAAC,CAAC,CAAC;gBAErE,EAAE,CACA,oCAAoC,EACpC,aAAa,CAAC,sBAAsB,CAAC,CACtC,CAAC;gBAEF,QAAQ,CACN,sCAAsC,EACtC,KAAK,CAAC;oBACJ,wCAAwC;oBACxC,iCAAiC;oBACjC,0BAA0B;oBAC1B,4CAA4C;oBAC5C,8CAA8C;iBAC/C,CAAC,CACH,CAAC;gBAEF,EAAE,CACA,gDAAgD,EAChD,aAAa,CAAC,yCAAyC,CAAC,CACzD,CAAC;gBAEF,EAAE,CACA,qCAAqC,EACrC,aAAa,CAAC,8BAA8B,CAAC,CAC9C,CAAC;gBAEF,EAAE,CAAC,uDAAuD,EAAE,GAAG,EAAE;oBAC/D,MAAM,EAAE,GAAG,SAAS,CAAC,8BAA8B,CAAC,CAAC,QAAQ,CAAC,CAAC;oBAC/D,MAAM,CAAC,iBAAS,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;gBAClD,CAAC,CAAC,CAAC;gBAEH,QAAQ,CACN,yCAAyC,EACzC,KAAK,CAAC;oBACJ,qBAAqB;oBACrB,gBAAgB;oBAChB,2BAA2B;oBAC3B,8BAA8B;oBAC9B,+BAA+B;oBAC/B,iCAAiC;oBACjC,gCAAgC;oBAChC,kBAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,aAAa,CAAC;wBAC7C,CAAC,CAAC,uDAAuD;wBACzD,CAAC,CAAC,SAAS;iBACd,CAAC,CACH,CAAC;gBAEF,QAAQ,CACN,yCAAyC,EACzC,KAAK,CAAC;oBACJ,kCAAkC;oBAClC,qCAAqC;oBACrC,kCAAkC;oBAClC,kCAAkC;oBAClC,oCAAoC;oBACpC,0CAA0C;oBAC1C,yCAAyC;oBACzC,iCAAiC;oBACjC,yCAAyC;oBACzC,kCAAkC;oBAClC,6BAA6B;oBAC7B,mCAAmC;oBACnC,0BAA0B;oBAC1B,+BAA+B;oBAC/B,+BAA+B;oBAC/B,mCAAmC;oBACnC,kCAAkC;oBAClC,kCAAkC;oBAClC,6BAA6B;oBAC7B,+BAA+B;oBAC/B,iCAAiC;oBACjC,iCAAiC;oBACjC,kCAAkC;oBAClC,kCAAkC;oBAClC,kCAAkC;oBAClC,kCAAkC;oBAClC,kCAAkC;oBAClC,kCAAkC;oBAClC,mCAAmC;oBACnC,mCAAmC;oBACnC,wCAAwC;iBACzC,CAAC,CACH,CAAC;gBAEF,QAAQ,CAAC,wCAAwC,EAAE,GAAG,EAAE;oBACtD,EAAE,CACA,GAAG,EACH,IAAI,CACF,SAAS,CAAC,8CAA8C,CAAC,EACzD,4BAA4B,CAC7B,CACF,CAAC;oBAEF,EAAE,CACA,GAAG,EACH,IAAI,CACF,SAAS,CACP,qHAAqH,CACtH,EACD,kIAAkI,CACnI,CACF,CAAC;oBAEF,EAAE,CACA,GAAG,EACH,IAAI,CACF,SAAS,CACP,+EAA+E,CAChF,EACD,uCAAuC,CACxC,CACF,CAAC;oBAEF,EAAE,CACA,GAAG,EACH,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC,EAAE,oBAAoB,CAAC,CAC9D,CAAC;oBAEF,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC,EAAE,kBAAkB,CAAC,CAAC,CAAC;oBAEnE,EAAE,CACA,GAAG,EACH,IAAI,CACF,SAAS,CAAC,kCAAkC,CAAC,EAC7C,gCAAgC,CACjC,CACF,CAAC;gBACJ,CAAC,CAAC,CAAC;gBAEH,gDAAgD;gBAChD,+DAA+D;gBAC/D,kEAAkE;gBAClE,oEAAoE;gBACpE,6DAA6D;gBAC7D,6DAA6D;gBAC7D,iEAAiE;gBACjE,iCAAiC;gBACjC,QAAQ,CAAC,oDAAoD,EAAE,GAAG,EAAE;oBAClE,EAAE,CACA,GAAG,EACH,IAAI,CACF,SAAS,CACP,iKAAiK,CAClK,EACD,0LAA0L,CAC3L,CACF,CAAC;oBAEF,EAAE,CACA,GAAG,EACH,IAAI,CACF,SAAS,CACP,yKAAyK,CAC1K,EACD,yKAAyK,CAC1K,CACF,CAAC;gBACJ,CAAC,CAAC,CAAC;gBAEH,EAAE,CACA,kCAAkC,EAClC,IAAI,CACF,SAAS,CACP,0DAA0D,CAC3D,EACD,sCAAsC,CACvC,CACF,CAAC;gBAEF,EAAE,CAAC,oCAAoC,EAAE,GAAG,EAAE;oBAC5C,MAAM,EAAE,GAAG,SAAS,CAAC,8BAA8B,CAAC,CAAC,GAAG,CAAC;oBACzD,MAAM,CAAC,iBAAS,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,mCAAmC,CAAC,CAAC;gBACrE,CAAC,CAAC,CAAC;gBAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;oBAC/C,MAAM,EAAE,GAAG,SAAS,CAAC,0BAA0B,CAAC,CAAC,GAAG,CAAC;oBACrD,MAAM,CAAC,iBAAS,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,+BAA+B,CAAC,CAAC;gBACjE,CAAC,CAAC,CAAC;gBAEH,EAAE,CAAC,sDAAsD,EAAE,GAAG,EAAE;oBAC9D,MAAM,EAAE,GAAG,SAAS,CAAC,gCAAgC,CAAC,CAAC,KAAK,CAAC,CAAC;oBAC9D,MAAM,CAAC,iBAAS,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;gBAClE,CAAC,CAAC,CAAC;gBAEH,EAAE,CAAC,iEAAiE,EAAE,GAAG,EAAE;oBACzE,MAAM,EAAE,GAAG,SAAS,CAAC,4CAA4C,CAAC,CAChE,iBAAiB,CAClB,CAAC;oBACF,MAAM,CAAC,iBAAS,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;gBAClE,CAAC,CAAC,CAAC;gBAEH,EAAE,CAAC,qDAAqD,EAAE,GAAG,EAAE;oBAC7D,MAAM,EAAE,GAAG,SAAS,CAAC,6BAA6B,CAAC,CAAC,EAAE,CAAC,CAAC;oBACxD,MAAM,CAAC,iBAAS,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;gBAClE,CAAC,CAAC,CAAC;gBAEH,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;oBAC1C,MAAM,EAAE,GAAG,SAAS,CAAC,8BAA8B,CAAC,CAAC,GAAG,CAAC;oBAEzD,MAAM,CAAC,iBAAS,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CACpC,yCAAyC,CAC1C,CAAC;gBACJ,CAAC,CAAC,CAAC;gBAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;oBAC1D,MAAM,EAAE,GAAG,SAAS,CAAC,0BAA0B,CAAC,CAAC,GAAG,CAAC;oBAErD,MAAM,CAAC,iBAAS,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CACpC,qCAAqC,CACtC,CAAC;gBACJ,CAAC,CAAC,CAAC;gBAEH,EAAE,CACA,yBAAyB,EACzB,IAAI,CACF,SAAS,CACP,oBAAoB;oBAClB,2BAA2B;oBAC3B,kCAAkC;oBAClC,wCAAwC;oBACxC,wBAAwB;oBACxB,sBAAsB;oBACtB,kBAAkB,CACrB,EACD,oEAAoE,EACpE,CAAC,CACF,CACF,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;YACtB,UAAU,CACR,iBAAiB,EACjB,WAAW,CAAC,wBAAwB,CAAC,EACrC,GAAG,EAAE;gBACH,EAAE,CACA,kBAAkB,EAClB,aAAa,CAAC,iCAAiC,CAAC,CACjD,CAAC;gBAEF,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;oBACvE,MAAM,YAAY,GAAG,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC;oBAEjD,QAAQ,CAAC,SAAS,CAAC,QAAQ,GAAG,GAAG,EAAE,CAAC,QAAQ,CAAC;oBAE7C,IAAI;wBACF,MAAM,CAAC,iBAAS,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,OAAO,CAC1D,eAAe,CAChB,CAAC;qBACH;4BAAS;wBACR,QAAQ,CAAC,SAAS,CAAC,QAAQ,GAAG,YAAY,CAAC;qBAC5C;gBACH,CAAC,CAAC,CAAC;YACL,CAAC,CACF,CAAC;YAEF,UAAU,CAAC,cAAc,EAAE,WAAW,CAAC,gBAAgB,CAAC,EAAE,GAAG,EAAE;gBAC7D,QAAQ,CACN,kBAAkB,EAClB,KAAK,CAAC;oBACJ,oBAAoB;oBACpB,kBAAkB;oBAClB,0CAA0C;iBAC3C,CAAC,CACH,CAAC;gBAEF,QAAQ,CACN,uCAAuC,EACvC,KAAK,CAAC;oBACJ,sBAAsB;oBACtB,kBAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,aAAa,CAAC;wBAC7C,CAAC,CAAC,mEAAmE;wBACrE,CAAC,CAAC,SAAS;iBACd,CAAC,CACH,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;YACtB,UAAU,CACR,kBAAkB,EAClB,WAAW,CAAC,yBAAyB,CAAC,EACtC,GAAG,EAAE;gBACH,EAAE,CACA,kBAAkB,EAClB,aAAa,CAAC,kCAAkC,CAAC,CAClD,CAAC;gBAEF,EAAE,CAAC,+DAA+D,EAAE,GAAG,EAAE;oBACvE,MAAM,YAAY,GAAG,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC;oBAEjD,QAAQ,CAAC,SAAS,CAAC,QAAQ,GAAG,GAAG,EAAE,CAAC,QAAQ,CAAC;oBAE7C,IAAI;wBACF,MAAM,CAAC,iBAAS,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,OAAO,CAC3D,eAAe,CAChB,CAAC;qBACH;4BAAS;wBACR,QAAQ,CAAC,SAAS,CAAC,QAAQ,GAAG,YAAY,CAAC;qBAC5C;gBACH,CAAC,CAAC,CAAC;YACL,CAAC,CACF,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;YACtB,EAAE,CACA,qDAAqD,EACrD,aAAa,CAAC,2BAA2B,CAAC,CAC3C,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,GAAG,GAAQ,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC;YAClC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC;YAEd,MAAM,MAAM,GAAG,iBAAS,CAAC,GAAG,CAAC,CAAC;YAE9B,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sBAAsB,EAAE,GAAG,EAAE;YAC9B,MAAM,GAAG,GAAQ,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC;YAClC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC;YAEd,MAAM,MAAM,GAAG,iBAAS,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;YAEhE,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CACpB,uDAAuD,CACxD,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,GAAG,GAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC3B,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAEd,MAAM,MAAM,GAAG,iBAAS,CAAC,GAAG,CAAC,CAAC;YAE9B,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,GAAG,GAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC3B,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAEd,MAAM,MAAM,GAAG,iBAAS,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;YAEhE,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CACpB,0DAA0D,CAC3D,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yDAAyD,EAAE,GAAG,EAAE;YACjE,MAAM,GAAG,GAAQ,EAAE,CAAC;YACpB,MAAM,KAAK,GAAG,EAAE,CAAC;YAEjB,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;YACd,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;YAEd,MAAM,MAAM,GAAG,iBAAS,CAAC,GAAG,CAAC,CAAC;YAE9B,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,GAAG,EAAE;YACxC,MAAM,GAAG,GAAQ,EAAE,CAAC;YACpB,MAAM,KAAK,GAAG,EAAE,CAAC;YAEjB,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;YACd,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;YAEd,MAAM,MAAM,GAAG,iBAAS,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;YAEhE,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,gDAAgD,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE;YACpD,MAAM,GAAG,GAAQ,EAAE,CAAC;YACpB,MAAM,KAAK,GAAG,EAAE,CAAC;YAEjB,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;YACd,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;YAEd,MAAM,MAAM,GAAG,iBAAS,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;YAE7D,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CACpB,qEAAqE,CACtE,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;YAChB,MAAM,MAAM,GAAG,iBAAS,CACtB;gBACE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBACf,MAAM,EAAE;oBACN,GAAG,EAAE,OAAO;iBACb;aACF,EACD,IAAI,EACJ,IAAI,CACL,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CACpB,KAAK;gBACH,0CAA0C;gBAC1C,sCAAsC;gBACtC,GAAG,CACN,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE;YACjB,MAAM,MAAM,GAAG,iBAAS,CACtB;gBACE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBACf,MAAM,EAAE;oBACN,GAAG,EAAE,OAAO;iBACb;aACF,EACD,IAAI,EACJ,CAAC,CACF,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CACpB,KAAK;gBACH,0CAA0C;gBAC1C,sCAAsC;gBACtC,GAAG,CACN,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;YACf,MAAM,MAAM,GAAG,iBAAS,CACtB;gBACE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBACf,MAAM,EAAE;oBACN,GAAG,EAAE,OAAO;iBACb;aACF,EACD,IAAI,EACJ,GAAG,CACJ,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CACpB,KAAK;gBACH,0CAA0C;gBAC1C,sCAAsC;gBACtC,GAAG,CACN,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,IAAI,SAAS,GAAG,CAAC,CAAC;YAElB,MAAM,MAAM,GAAG,iBAAS,CACtB;gBACE,IAAI,EAAE,OAAO;aACd,EACD,UAAS,KAAK,EAAE,MAAM,EAAE,IAAI;gBAC1B,SAAS,EAAE,CAAC;gBAEZ,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;oBAC7B,OAAO,SAAS,CAAC;iBAClB;gBAED,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC;YACrB,CAAC,CACF,CAAC;YAEF,MAAM,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,MAAM,GAAG,iBAAS,CACtB;gBACE,IAAI,EAAE,EAAE;aACT,EACD,UAAS,KAAK,EAAE,MAAM,EAAE,IAAI;gBAC1B,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;oBAC7B,OAAO,IAAI,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC,CAAC;iBAC/B;gBAED,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC;YACrB,CAAC,CACF,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpC,MAAM,MAAM,GAAG,iBAAS,CACtB;gBACE,IAAI,EAAE,EAAE;aACT,EACD,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAC/C,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,SAAS,OAAO,CAAC,GAAW;gBAC1B,MAAM,EAAE,GAAG,GAAG,EAAE;oBACd,WAAW;gBACb,CAAC,CAAC;gBACF,EAAE,CAAC,YAAY,GAAG,GAAG,CAAC;gBACtB,OAAO,EAAE,CAAC;YACZ,CAAC;YAED,MAAM,MAAM,GAAG,iBAAS,CACtB;gBACE,YAAY,EAAE,OAAO,CACnB,yDAAyD,CAC1D;gBACD,aAAa,EAAE,OAAO,CACpB,yDAAyD,CAC1D;aACF,EACD,CAAC,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE;gBACzB,IAAI,GAAG,IAAI,GAAG,CAAC,YAAY,EAAE;oBAC3B,OAAO,GAAG,CAAC,YAAY,CAAC;iBACzB;gBACD,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC;YACxB,CAAC,EACD,CAAC,CACF,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;;;EAG3B,CAAC,CAAC;QACA,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,WAAW,EAAE,GAAG,EAAE;QACzB,MAAM,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;QAEnC,EAAE,CAAC,uBAAuB,EAAE,IAAI,CAAC,GAAG,EAAE,eAAe,CAAC,CAAC,CAAC;QAExD,EAAE,CACA,+BAA+B,EAC/B,IAAI,CAAC,GAAG,EAAE,YAAY,EAAE,IAAI,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,CAC/C,CAAC;QAEF,EAAE,CACA,wDAAwD,EACxD,IAAI,CAAC,GAAG,EAAE,YAAY,EAAE,IAAI,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CACjE,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,EAAE,CAAC,MAAM,CACP,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC,EAAE;gBACjC,MAAM,CAAC,SAAS,CAAC,iBAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YACrD,CAAC,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["import * as fc from \"fast-check\";\nimport { satisfies } from \"semver\";\nimport { stringify, Options } from \"./index\";\n\n// Declare global window type.\ndeclare const window: any;\n\n// Evaluate a string into JavaScript\nconst evalValue = (str: string | undefined) => {\n  // tslint:disable-next-line no-eval\n  return eval(`(${str})`);\n};\n\n/**\n * Create a quick test function wrapper.\n */\nfunction test(\n  value: any,\n  result: string,\n  indent?: string | number | null,\n  options?: Options\n) {\n  return () => {\n    expect(stringify(value, null, indent, options)).toEqual(result);\n  };\n}\n\n/**\n * Create a wrapper for round-trip eval tests.\n */\nfunction testRoundTrip(\n  expression: string,\n  indent?: string | number,\n  options?: Options\n) {\n  return () => test(evalValue(expression), expression, indent, options)();\n}\n\n/**\n * Check if syntax is supported.\n */\nfunction isSupported(expr: string) {\n  try {\n    // tslint:disable-next-line no-eval\n    eval(expr);\n    return true;\n  } catch (err) {\n    if (err.name === \"SyntaxError\") return false;\n    throw err;\n  }\n}\n\n/**\n * Generate a list of test cases to run.\n */\nfunction cases(cases: (string | undefined)[]) {\n  return () => {\n    for (const value of cases) {\n      if (value) it(value, testRoundTrip(value));\n    }\n  };\n}\n\n/**\n * Conditionally execute test cases.\n */\nfunction describeIf(\n  description: string,\n  condition: boolean,\n  fn: jest.EmptyFunction\n) {\n  return condition ? describe(description, fn) : describe.skip(description, fn);\n}\n\ndescribe(\"javascript-stringify\", () => {\n  describe(\"types\", () => {\n    describe(\"booleans\", () => {\n      it(\"should be stringified\", test(true, \"true\"));\n    });\n\n    describe(\"strings\", () => {\n      it(\"should wrap in single quotes\", test(\"string\", \"'string'\"));\n\n      it(\"should escape quote characters\", test(\"'test'\", \"'\\\\'test\\\\''\"));\n\n      it(\n        \"should escape control characters\",\n        test(\"multi\\nline\", \"'multi\\\\nline'\")\n      );\n\n      it(\"should escape back slashes\", test(\"back\\\\slash\", \"'back\\\\\\\\slash'\"));\n\n      it(\n        \"should escape certain unicode sequences\",\n        test(\"\\u0602\", \"'\\\\u0602'\")\n      );\n    });\n\n    describe(\"numbers\", () => {\n      it(\"should stringify integers\", test(10, \"10\"));\n\n      it(\"should stringify floats\", test(10.5, \"10.5\"));\n\n      it('should stringify \"NaN\"', test(10.5, \"10.5\"));\n\n      it('should stringify \"Infinity\"', test(Infinity, \"Infinity\"));\n\n      it('should stringify \"-Infinity\"', test(-Infinity, \"-Infinity\"));\n\n      it('should stringify \"-0\"', test(-0, \"-0\"));\n    });\n\n    describe(\"arrays\", () => {\n      it(\"should stringify as array shorthand\", test([1, 2, 3], \"[1,2,3]\"));\n\n      it(\n        \"should indent elements\",\n        test([{ x: 10 }], \"[\\n\\t{\\n\\t\\tx: 10\\n\\t}\\n]\", \"\\t\")\n      );\n    });\n\n    describe(\"objects\", () => {\n      it(\n        \"should stringify as object shorthand\",\n        test({ key: \"value\", \"-\": 10 }, \"{key:'value','-':10}\")\n      );\n\n      it(\n        \"should stringify undefined keys\",\n        test({ a: true, b: undefined }, \"{a:true,b:undefined}\")\n      );\n\n      it(\n        \"should stringify omit undefined keys\",\n        test({ a: true, b: undefined }, \"{a:true}\", null, {\n          skipUndefinedProperties: true\n        })\n      );\n\n      it(\n        \"should quote reserved word keys\",\n        test({ if: true, else: false }, \"{'if':true,'else':false}\")\n      );\n\n      it(\n        \"should not quote Object.prototype keys\",\n        test({ constructor: 1, toString: 2 }, \"{constructor:1,toString:2}\")\n      );\n    });\n\n    describe(\"functions\", () => {\n      it(\n        \"should reindent function bodies\",\n        test(\n          evalValue(\n            `function() {\n              if (true) {\n                return \"hello\";\n              }\n            }`\n          ),\n          'function () {\\n  if (true) {\\n    return \"hello\";\\n  }\\n}',\n          2\n        )\n      );\n\n      it(\n        \"should reindent function bodies in objects\",\n        test(\n          evalValue(`\n          {\n            fn: function() {\n              if (true) {\n                return \"hello\";\n              }\n            }\n          }\n          `),\n          '{\\n  fn: function () {\\n    if (true) {\\n      return \"hello\";\\n    }\\n  }\\n}',\n          2\n        )\n      );\n\n      it(\n        \"should reindent function bodies in arrays\",\n        test(\n          evalValue(`[\n            function() {\n              if (true) {\n                return \"hello\";\n              }\n            }\n          ]`),\n          '[\\n  function () {\\n    if (true) {\\n      return \"hello\";\\n    }\\n  }\\n]',\n          2\n        )\n      );\n\n      it(\n        \"should not need to reindent one-liners\",\n        testRoundTrip(\"{\\n  fn: function () { return; }\\n}\", 2)\n      );\n\n      it(\"should gracefully handle unexpected Function.toString formats\", () => {\n        const origToString = Function.prototype.toString;\n\n        Function.prototype.toString = () => \"{nope}\";\n\n        try {\n          expect(\n            stringify(function() {\n              /* Empty */\n            })\n          ).toEqual(\"void '{nope}'\");\n        } finally {\n          Function.prototype.toString = origToString;\n        }\n      });\n\n      describe(\n        \"omit the names of their keys\",\n        cases([\"{name:function () {}}\", \"{'tricky name':function () {}}\"])\n      );\n    });\n\n    describe(\"native instances\", () => {\n      describe(\"Date\", () => {\n        const date = new Date();\n\n        it(\"should stringify\", test(date, \"new Date(\" + date.getTime() + \")\"));\n      });\n\n      describe(\"RegExp\", () => {\n        it(\"should stringify as shorthand\", test(/[abc]/gi, \"/[abc]/gi\"));\n      });\n\n      describe(\"Number\", () => {\n        it(\"should stringify\", test(new Number(10), \"new Number(10)\"));\n      });\n\n      describe(\"String\", () => {\n        it(\"should stringify\", test(new String(\"abc\"), \"new String('abc')\"));\n      });\n\n      describe(\"Boolean\", () => {\n        it(\"should stringify\", test(new Boolean(true), \"new Boolean(true)\"));\n      });\n\n      describeIf(\"Buffer\", typeof (Buffer as any) === \"function\", () => {\n        it(\"should stringify\", test(Buffer.from(\"test\"), \"new Buffer('test')\"));\n      });\n\n      describeIf(\"BigInt\", typeof (BigInt as any) === \"function\", () => {\n        it(\"should stringify\", test(BigInt(\"10\"), \"BigInt('10')\"));\n      });\n\n      describe(\"Error\", () => {\n        it(\"should stringify\", test(new Error(\"test\"), \"new Error('test')\"));\n      });\n\n      describe(\"unknown native type\", () => {\n        it(\n          \"should be omitted\",\n          test(\n            {\n              k:\n                typeof (process as any) === \"undefined\"\n                  ? window.navigator\n                  : process\n            },\n            \"{}\"\n          )\n        );\n      });\n    });\n\n    describeIf(\"ES6\", typeof (Array as any).from === \"function\", () => {\n      describeIf(\"Map\", typeof (Map as any) === \"function\", () => {\n        it(\n          \"should stringify\",\n          test(new Map([[\"key\", \"value\"]]), \"new Map([['key','value']])\")\n        );\n      });\n\n      describeIf(\"Set\", typeof (Set as any) === \"function\", () => {\n        it(\n          \"should stringify\",\n          test(new Set([\"key\", \"value\"]), \"new Set(['key','value'])\")\n        );\n      });\n\n      describe(\"arrow functions\", () => {\n        describe(\n          \"should stringify\",\n          cases([\n            \"(a, b) => a + b\",\n            \"o => { return o.a + o.b; }\",\n            \"(a, b) => { if (a) { return b; } }\",\n            \"(a, b) => ({ [a]: b })\",\n            \"a => b => () => a + b\"\n          ])\n        );\n\n        it(\n          \"should reindent function bodies\",\n          test(\n            evalValue(\n              \"               () => {\\n\" +\n                \"                 if (true) {\\n\" +\n                '                   return \"hello\";\\n' +\n                \"                 }\\n\" +\n                \"               }\"\n            ),\n            '() => {\\n  if (true) {\\n    return \"hello\";\\n  }\\n}',\n            2\n          )\n        );\n\n        describeIf(\"arrows with patterns\", isSupported(\"({x}) => x\"), () => {\n          describe(\n            \"should stringify\",\n            cases([\n              \"({ x, y }) => x + y\",\n              \"({ x, y }) => { if (x === '}') { return y; } }\",\n              \"({ x, y = /[/})]/.test(x) }) => { return y ? x : 0; }\"\n            ])\n          );\n        });\n      });\n\n      describe(\"generators\", () => {\n        it(\"should stringify\", testRoundTrip(\"function* (x) { yield x; }\"));\n      });\n\n      describe(\"class notation\", () => {\n        it(\"should stringify classes\", testRoundTrip(\"class {}\"));\n        it(\n          \"should stringify class and method\",\n          testRoundTrip(\"class { method() {} }\")\n        );\n        it(\n          \"should stringify with newline\",\n          testRoundTrip(\"class\\n{ method() {} }\")\n        );\n        it(\n          \"should stringify with comment\",\n          testRoundTrip(\"class/*test*/\\n{ method() {} }\")\n        );\n      });\n\n      describe(\"method notation\", () => {\n        it(\"should stringify\", testRoundTrip(\"{a(b, c) { return b + c; }}\"));\n\n        it(\n          \"should stringify generator methods\",\n          testRoundTrip(\"{*a(b) { yield b; }}\")\n        );\n\n        describe(\n          \"should not be fooled by tricky names\",\n          cases([\n            \"{'function a'(b, c) { return b + c; }}\",\n            \"{'a(a'(b, c) { return b + c; }}\",\n            \"{'() => function '() {}}\",\n            \"{'['() { return x[y]()\\n{ return true; }}}\",\n            \"{'() { return false;//'() { return true;\\n}}\"\n          ])\n        );\n\n        it(\n          \"should not be fooled by tricky generator names\",\n          testRoundTrip(\"{*'function a'(b, c) { return b + c; }}\")\n        );\n\n        it(\n          \"should not be fooled by empty names\",\n          testRoundTrip(\"{''(b, c) { return b + c; }}\")\n        );\n\n        it(\"should not be fooled by keys that look like functions\", () => {\n          const fn = evalValue('{ \"() => \": () => () => 42 }')[\"() => \"];\n          expect(stringify(fn)).toEqual(\"() => () => 42\");\n        });\n\n        describe(\n          \"should not be fooled by arrow functions\",\n          cases([\n            \"{a:(b, c) => b + c}\",\n            \"{a:a => a + 1}\",\n            \"{'() => ':() => () => 42}\",\n            '{\\'() => \"\\':() => \"() {//\"}',\n            '{\\'() => \"\\':() => \"() {`//\"}',\n            '{\\'() => \"\\':() => \"() {`${//\"}',\n            '{\\'() => \"\\':() => \"() {/*//\"}',\n            satisfies(process.versions.node, \"<=4 || >=10\")\n              ? \"{'a => function ':a => function () { return a + 1; }}\"\n              : undefined\n          ])\n        );\n\n        describe(\n          \"should not be fooled by regexp literals\",\n          cases([\n            \"{' '(s) { return /}/.test(s); }}\",\n            \"{' '(s) { return /abc/ .test(s); }}\",\n            \"{' '() { return x / y; // /}\\n}}\",\n            \"{' '() { return / y; }//* } */}}\",\n            \"{' '() { return delete / y; }/.x}}\",\n            \"{' '() { switch (x) { case / y; }}/: }}}\",\n            \"{' '() { if (x) return; else / y;}/; }}\",\n            \"{' '() { return x in / y;}/; }}\",\n            \"{' '() { return x instanceof / y;}/; }}\",\n            \"{' '() { return new / y;}/.x; }}\",\n            \"{' '() { throw / y;}/.x; }}\",\n            \"{' '() { return typeof / y;}/; }}\",\n            \"{' '() { void / y;}/; }}\",\n            \"{' '() { return x, / y;}/; }}\",\n            \"{' '() { return x; / y;}/; }}\",\n            \"{' '() { return { x: / y;}/ }; }}\",\n            \"{' '() { return x + / y;}/.x; }}\",\n            \"{' '() { return x - / y;}/.x; }}\",\n            \"{' '() { return !/ y;}/; }}\",\n            \"{' '() { return ~/ y;}/.x; }}\",\n            \"{' '() { return x && / y;}/; }}\",\n            \"{' '() { return x || / y;}/; }}\",\n            \"{' '() { return x ^ / y;}/.x; }}\",\n            \"{' '() { return x * / y;}/.x; }}\",\n            \"{' '() { return x / / y;}/.x; }}\",\n            \"{' '() { return x % / y;}/.x; }}\",\n            \"{' '() { return x < / y;}/.x; }}\",\n            \"{' '() { return x > / y;}/.x; }}\",\n            \"{' '() { return x <= / y;}/.x; }}\",\n            \"{' '() { return x /= / y;}/.x; }}\",\n            \"{' '() { return x ? / y;}/ : false; }}\"\n          ])\n        );\n\n        describe(\"should not be fooled by computed names\", () => {\n          it(\n            \"1\",\n            test(\n              evalValue('{ [\"foobar\".slice(3)](x) { return x + 1; } }'),\n              \"{bar(x) { return x + 1; }}\"\n            )\n          );\n\n          it(\n            \"2\",\n            test(\n              evalValue(\n                '{[((s,a,b)=>a+s(a)+\",\"+s(b)+b)(JSON.stringify,\"[((s,a,b)=>a+s(a)+\\\\\",\\\\\"+s(b)+b)(JSON.stringify,\",\")]() {}\")]() {}}'\n              ),\n              '{\\'[((s,a,b)=>a+s(a)+\",\"+s(b)+b)(JSON.stringify,\"[((s,a,b)=>a+s(a)+\\\\\\\\\",\\\\\\\\\"+s(b)+b)(JSON.stringify,\",\")]() {}\")]() {}\\'() {}}'\n            )\n          );\n\n          it(\n            \"3\",\n            test(\n              evalValue(\n                '{[`over${`6${\"0\".repeat(3)}`.replace(\"6\", \"9\")}`]() { this.activateHair(); }}'\n              ),\n              \"{over9000() { this.activateHair(); }}\"\n            )\n          );\n\n          it(\n            \"4\",\n            test(evalValue(\"{[\\\"() {'\\\"]() {''}}\"), \"{'() {\\\\''() {''}}\")\n          );\n\n          it(\"5\", test(evalValue('{[\"() {`\"]() {``}}'), \"{'() {`'() {``}}\"));\n\n          it(\n            \"6\",\n            test(\n              evalValue('{[\"() {/*\"]() {/*`${()=>{/*}*/}}'),\n              \"{'() {/*'() {/*`${()=>{/*}*/}}\"\n            )\n          );\n        });\n\n        // These two cases demonstrate that branching on\n        // METHOD_NAMES_ARE_QUOTED is unavoidable--you can't write code\n        // without it that will pass both of these cases on both node.js 4\n        // and node.js 10. (If you think you can, consider that the name and\n        // toString of the first case when executed on node.js 10 are\n        // identical to the name and toString of the second case when\n        // executed on node.js 4, so good luck telling them apart without\n        // knowing which node you're on.)\n        describe(\"should handle different versions of node correctly\", () => {\n          it(\n            \"1\",\n            test(\n              evalValue(\n                '{[((s,a,b)=>a+s(a)+\",\"+s(b)+b)(JSON.stringify,\"[((s,a,b)=>a+s(a)+\\\\\",\\\\\"+s(b)+b)(JSON.stringify,\",\")]() { return 0; /*\")]() { return 0; /*() {/* */ return 1;}}'\n              ),\n              '{\\'[((s,a,b)=>a+s(a)+\",\"+s(b)+b)(JSON.stringify,\"[((s,a,b)=>a+s(a)+\\\\\\\\\",\\\\\\\\\"+s(b)+b)(JSON.stringify,\",\")]() { return 0; /*\")]() { return 0; /*\\'() { return 0; /*() {/* */ return 1;}}'\n            )\n          );\n\n          it(\n            \"2\",\n            test(\n              evalValue(\n                '{\\'[((s,a,b)=>a+s(a)+\",\"+s(b)+b)(JSON.stringify,\"[((s,a,b)=>a+s(a)+\\\\\\\\\",\\\\\\\\\"+s(b)+b)(JSON.stringify,\",\")]() { return 0; /*\")]() { return 0; /*\\'() {/* */ return 1;}}'\n              ),\n              '{\\'[((s,a,b)=>a+s(a)+\",\"+s(b)+b)(JSON.stringify,\"[((s,a,b)=>a+s(a)+\\\\\\\\\",\\\\\\\\\"+s(b)+b)(JSON.stringify,\",\")]() { return 0; /*\")]() { return 0; /*\\'() {/* */ return 1;}}'\n            )\n          );\n        });\n\n        it(\n          \"should not be fooled by comments\",\n          test(\n            evalValue(\n              \"{'method' /* a comment! */ () /* another comment! */ {}}\"\n            ),\n            \"{method() /* another comment! */ {}}\"\n          )\n        );\n\n        it(\"should stringify extracted methods\", () => {\n          const fn = evalValue(\"{ foo(x) { return x + 1; } }\").foo;\n          expect(stringify(fn)).toEqual(\"function foo(x) { return x + 1; }\");\n        });\n\n        it(\"should stringify extracted generators\", () => {\n          const fn = evalValue(\"{ *foo(x) { yield x; } }\").foo;\n          expect(stringify(fn)).toEqual(\"function* foo(x) { yield x; }\");\n        });\n\n        it(\"should stringify extracted methods with tricky names\", () => {\n          const fn = evalValue('{ \"a(a\"(x) { return x + 1; } }')[\"a(a\"];\n          expect(stringify(fn)).toEqual(\"function (x) { return x + 1; }\");\n        });\n\n        it(\"should stringify extracted methods with arrow-like tricky names\", () => {\n          const fn = evalValue('{ \"() => function \"(x) { return x + 1; } }')[\n            \"() => function \"\n          ];\n          expect(stringify(fn)).toEqual(\"function (x) { return x + 1; }\");\n        });\n\n        it(\"should stringify extracted methods with empty names\", () => {\n          const fn = evalValue('{ \"\"(x) { return x + 1; } }')[\"\"];\n          expect(stringify(fn)).toEqual(\"function (x) { return x + 1; }\");\n        });\n\n        it(\"should handle transplanted names\", () => {\n          const fn = evalValue(\"{ foo(x) { return x + 1; } }\").foo;\n\n          expect(stringify({ bar: fn })).toEqual(\n            \"{bar:function foo(x) { return x + 1; }}\"\n          );\n        });\n\n        it(\"should handle transplanted names with generators\", () => {\n          const fn = evalValue(\"{ *foo(x) { yield x; } }\").foo;\n\n          expect(stringify({ bar: fn })).toEqual(\n            \"{bar:function* foo(x) { yield x; }}\"\n          );\n        });\n\n        it(\n          \"should reindent methods\",\n          test(\n            evalValue(\n              \"               {\\n\" +\n                \"                 fn() {\\n\" +\n                \"                   if (true) {\\n\" +\n                '                     return \"hello\";\\n' +\n                \"                   }\\n\" +\n                \"                 }\\n\" +\n                \"               }\"\n            ),\n            '{\\n  fn() {\\n    if (true) {\\n      return \"hello\";\\n    }\\n  }\\n}',\n            2\n          )\n        );\n      });\n    });\n\n    describe(\"ES2017\", () => {\n      describeIf(\n        \"async functions\",\n        isSupported(\"(async function () {})\"),\n        () => {\n          it(\n            \"should stringify\",\n            testRoundTrip(\"async function (x) { await x; }\")\n          );\n\n          it(\"should gracefully handle unexpected Function.toString formats\", () => {\n            const origToString = Function.prototype.toString;\n\n            Function.prototype.toString = () => \"{nope}\";\n\n            try {\n              expect(stringify(evalValue(\"async function () {}\"))).toEqual(\n                \"void '{nope}'\"\n              );\n            } finally {\n              Function.prototype.toString = origToString;\n            }\n          });\n        }\n      );\n\n      describeIf(\"async arrows\", isSupported(\"async () => {}\"), () => {\n        describe(\n          \"should stringify\",\n          cases([\n            \"async (x) => x + 1\",\n            \"async x => x + 1\",\n            \"async x => { await x.then(y => y + 1); }\"\n          ])\n        );\n\n        describe(\n          \"should stringify as object properties\",\n          cases([\n            \"{f:async a => a + 1}\",\n            satisfies(process.versions.node, \"<=4 || >=10\")\n              ? \"{'async a => function ':async a => function () { return a + 1; }}\"\n              : undefined\n          ])\n        );\n      });\n    });\n\n    describe(\"ES2018\", () => {\n      describeIf(\n        \"async generators\",\n        isSupported(\"(async function* () {})\"),\n        () => {\n          it(\n            \"should stringify\",\n            testRoundTrip(\"async function* (x) { yield x; }\")\n          );\n\n          it(\"should gracefully handle unexpected Function.toString formats\", () => {\n            const origToString = Function.prototype.toString;\n\n            Function.prototype.toString = () => \"{nope}\";\n\n            try {\n              expect(stringify(evalValue(\"async function* () {}\"))).toEqual(\n                \"void '{nope}'\"\n              );\n            } finally {\n              Function.prototype.toString = origToString;\n            }\n          });\n        }\n      );\n    });\n\n    describe(\"global\", () => {\n      it(\n        \"should access the global in the current environment\",\n        testRoundTrip(\"Function('return this')()\")\n      );\n    });\n  });\n\n  describe(\"circular references\", () => {\n    it(\"should omit circular references\", () => {\n      const obj: any = { key: \"value\" };\n      obj.obj = obj;\n\n      const result = stringify(obj);\n\n      expect(result).toEqual(\"{key:'value'}\");\n    });\n\n    it(\"should restore value\", () => {\n      const obj: any = { key: \"value\" };\n      obj.obj = obj;\n\n      const result = stringify(obj, null, null, { references: true });\n\n      expect(result).toEqual(\n        \"(function(){var x={key:'value'};x.obj=x;return x;}())\"\n      );\n    });\n\n    it(\"should omit recursive array value\", () => {\n      const obj: any = [1, 2, 3];\n      obj.push(obj);\n\n      const result = stringify(obj);\n\n      expect(result).toEqual(\"[1,2,3,undefined]\");\n    });\n\n    it(\"should restore array value\", () => {\n      const obj: any = [1, 2, 3];\n      obj.push(obj);\n\n      const result = stringify(obj, null, null, { references: true });\n\n      expect(result).toEqual(\n        \"(function(){var x=[1,2,3,undefined];x[3]=x;return x;}())\"\n      );\n    });\n\n    it(\"should print repeated values when no references enabled\", () => {\n      const obj: any = {};\n      const child = {};\n\n      obj.a = child;\n      obj.b = child;\n\n      const result = stringify(obj);\n\n      expect(result).toEqual(\"{a:{},b:{}}\");\n    });\n\n    it(\"should restore repeated values\", () => {\n      const obj: any = {};\n      const child = {};\n\n      obj.a = child;\n      obj.b = child;\n\n      const result = stringify(obj, null, null, { references: true });\n\n      expect(result).toEqual(\"(function(){var x={a:{}};x.b=x.a;return x;}())\");\n    });\n\n    it(\"should restore repeated values with indentation\", function() {\n      const obj: any = {};\n      const child = {};\n\n      obj.a = child;\n      obj.b = child;\n\n      const result = stringify(obj, null, 2, { references: true });\n\n      expect(result).toEqual(\n        \"(function () {\\nvar x = {\\n  a: {}\\n};\\nx.b = x.a;\\nreturn x;\\n}())\"\n      );\n    });\n  });\n\n  describe(\"custom indent\", () => {\n    it(\"string\", () => {\n      const result = stringify(\n        {\n          test: [1, 2, 3],\n          nested: {\n            key: \"value\"\n          }\n        },\n        null,\n        \"\\t\"\n      );\n\n      expect(result).toEqual(\n        \"{\\n\" +\n          \"\\ttest: [\\n\\t\\t1,\\n\\t\\t2,\\n\\t\\t3\\n\\t],\\n\" +\n          \"\\tnested: {\\n\\t\\tkey: 'value'\\n\\t}\\n\" +\n          \"}\"\n      );\n    });\n\n    it(\"integer\", () => {\n      const result = stringify(\n        {\n          test: [1, 2, 3],\n          nested: {\n            key: \"value\"\n          }\n        },\n        null,\n        2\n      );\n\n      expect(result).toEqual(\n        \"{\\n\" +\n          \"  test: [\\n    1,\\n    2,\\n    3\\n  ],\\n\" +\n          \"  nested: {\\n    key: 'value'\\n  }\\n\" +\n          \"}\"\n      );\n    });\n\n    it(\"float\", () => {\n      const result = stringify(\n        {\n          test: [1, 2, 3],\n          nested: {\n            key: \"value\"\n          }\n        },\n        null,\n        2.6\n      );\n\n      expect(result).toEqual(\n        \"{\\n\" +\n          \"  test: [\\n    1,\\n    2,\\n    3\\n  ],\\n\" +\n          \"  nested: {\\n    key: 'value'\\n  }\\n\" +\n          \"}\"\n      );\n    });\n  });\n\n  describe(\"replacer function\", () => {\n    it(\"should allow custom replacements\", () => {\n      let callCount = 0;\n\n      const result = stringify(\n        {\n          test: \"value\"\n        },\n        function(value, indent, next) {\n          callCount++;\n\n          if (typeof value === \"string\") {\n            return '\"hello\"';\n          }\n\n          return next(value);\n        }\n      );\n\n      expect(callCount).toEqual(2);\n      expect(result).toEqual('{test:\"hello\"}');\n    });\n\n    it(\"change primitive to object\", () => {\n      const result = stringify(\n        {\n          test: 10\n        },\n        function(value, indent, next) {\n          if (typeof value === \"number\") {\n            return next({ obj: \"value\" });\n          }\n\n          return next(value);\n        }\n      );\n\n      expect(result).toEqual(\"{test:{obj:'value'}}\");\n    });\n\n    it(\"change object to primitive\", () => {\n      const result = stringify(\n        {\n          test: 10\n        },\n        value => Object.prototype.toString.call(value)\n      );\n\n      expect(result).toEqual(\"[object Object]\");\n    });\n\n    it(\"should support object functions\", () => {\n      function makeRaw(str: string) {\n        const fn = () => {\n          /* Noop. */\n        };\n        fn.__expression = str;\n        return fn;\n      }\n\n      const result = stringify(\n        {\n          \"no-console\": makeRaw(\n            `process.env.NODE_ENV === 'production' ? 'error' : 'off'`\n          ),\n          \"no-debugger\": makeRaw(\n            `process.env.NODE_ENV === 'production' ? 'error' : 'off'`\n          )\n        },\n        (val, indent, stringify) => {\n          if (val && val.__expression) {\n            return val.__expression;\n          }\n          return stringify(val);\n        },\n        2\n      );\n\n      expect(result).toEqual(`{\n  'no-console': process.env.NODE_ENV === 'production' ? 'error' : 'off',\n  'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'off'\n}`);\n    });\n  });\n\n  describe(\"max depth\", () => {\n    const obj = { a: { b: { c: 1 } } };\n\n    it(\"should get all object\", test(obj, \"{a:{b:{c:1}}}\"));\n\n    it(\n      \"should get part of the object\",\n      test(obj, \"{a:{b:{}}}\", null, { maxDepth: 2 })\n    );\n\n    it(\n      \"should get part of the object when tracking references\",\n      test(obj, \"{a:{b:{}}}\", null, { maxDepth: 2, references: true })\n    );\n  });\n\n  describe(\"property based\", () => {\n    it(\"should produce string evaluating to the original value\", () => {\n      fc.assert(\n        fc.property(fc.anything(), value => {\n          expect(evalValue(stringify(value))).toEqual(value);\n        })\n      );\n    });\n  });\n});\n"]}