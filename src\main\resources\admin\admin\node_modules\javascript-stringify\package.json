{"name": "javascript-stringify", "version": "2.0.1", "description": "Stringify is to `eval` as `JSON.stringify` is to `JSON.parse`", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist/"], "scripts": {"prettier": "prettier --write", "format": "npm run prettier -- \"{.,src/**}/*.{js,ts,json,md,yml,css}\"", "lint": "tslint \"src/**/*.ts\" --project tsconfig.json", "build": "rimraf dist/ && tsc", "specs": "jest --coverage", "test": "npm run lint && npm run build && npm run specs", "prepare": "npm run build"}, "repository": "https://github.com/blakeembrey/javascript-stringify.git", "keywords": ["stringify", "javascript", "object", "eval", "string", "code"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blakeembrey.me"}, "license": "MIT", "bugs": {"url": "https://github.com/blakeembrey/javascript-stringify/issues"}, "homepage": "https://github.com/blakeembrey/javascript-stringify", "jest": {"roots": ["<rootDir>/src/"], "transform": {"\\.tsx?$": "ts-jest"}}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,ts,json,md,yml,css}": ["npm run prettier", "git add"]}, "publishConfig": {"access": "public"}, "devDependencies": {"@types/jest": "^24.0.9", "@types/node": "^11.10.4", "@types/semver": "^5.5.0", "fast-check": "^1.12.0", "husky": "^1.3.1", "jest": "^24.0.0", "lint-staged": "^8.1.1", "prettier": "^1.16.1", "rimraf": "^2.5.4", "semver": "^5.6.0", "ts-jest": "^24.0.0", "tslint": "^5.0.0", "tslint-config-prettier": "^1.17.0", "tslint-config-standard": "^8.0.0", "typescript": "^3.3.1"}, "_resolved": "https://registry.npm.taobao.org/javascript-stringify/download/javascript-stringify-2.0.1.tgz?cache=0&sync_timestamp=1572948916758&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjavascript-stringify%2Fdownload%2Fjavascript-stringify-2.0.1.tgz", "_integrity": "sha1-bvNYA1MQ411mfGde1j0+t8GqGeU=", "_from": "javascript-stringify@2.0.1"}