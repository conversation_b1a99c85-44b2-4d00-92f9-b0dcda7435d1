{"name": "json-stringify-safe", "version": "5.0.1", "description": "Like JSON.stringify, but doesn't blow up on circular refs.", "keywords": ["json", "stringify", "circular", "safe"], "homepage": "https://github.com/isaacs/json-stringify-safe", "bugs": "https://github.com/isaacs/json-stringify-safe/issues", "author": "<PERSON> <<EMAIL>> (http://blog.izs.me)", "contributors": ["<PERSON><PERSON> <<EMAIL>> (http://themoll.com)"], "license": "ISC", "repository": {"type": "git", "url": "git://github.com/isaacs/json-stringify-safe"}, "main": "stringify.js", "scripts": {"test": "node test.js"}, "devDependencies": {"mocha": ">= 2.1.0 < 3", "must": ">= 0.12 < 0.13", "sinon": ">= 1.12.2 < 2"}, "_resolved": "https://registry.npm.taobao.org/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz?cache=0&sync_timestamp=1560524653371&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjson-stringify-safe%2Fdownload%2Fjson-stringify-safe-5.0.1.tgz", "_integrity": "sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=", "_from": "json-stringify-safe@5.0.1"}