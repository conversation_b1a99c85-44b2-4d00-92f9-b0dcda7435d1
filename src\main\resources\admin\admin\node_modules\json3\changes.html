<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <title>Releases | JSON 3</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" href="page/style.css" media="screen">
  </head>
  <body>
    <ul id="navigation">
      
    </ul>
    <div id="content">
      <h1 id="json-3-releases">JSON 3 Releases</h1>
<h2 id="3-3-2">3.3.2</h2>
<h3 id="2014-06-22">2014-06-22</h3>
<ul>
<li>Test the minified version on Travis [<a href="https://github.com/bestiejs/json3/issues/35">#35</a>].</li>
<li>Add a change log and contribution guidelines [<a href="https://github.com/bestiejs/json3/issues/55">#55</a>].</li>
<li>Include the minified version in the npm package [<a href="https://github.com/bestiejs/json3/issues/59">#59</a>].</li>
<li>Simplify <code>bower.json</code>.</li>
</ul>
<h2 id="3-3-1">3.3.1</h2>
<h3 id="2014-04-08">2014-04-08</h3>
<ul>
<li>Reduce the Bower package size by lazily downloading the Closure Compiler [<a href="https://github.com/bestiejs/json3/issues/54">#54</a>].</li>
<li>Make <code>JSON3.noConflict()</code> idempotent [<a href="https://github.com/bestiejs/json3/issues/56">#56</a>].</li>
<li>Improve AMD <code>define</code> pragma detection before minifying.</li>
<li>Support <a href="https://github.com/rogerwang/node-webkit"><code>node-webkit</code></a> and web workers.</li>
</ul>
<h2 id="3-3-0">3.3.0</h2>
<h3 id="2014-01-20">2014-01-20</h3>
<ul>
<li>Export a <code>JSON3</code> global in browsers and JavaScript engines.</li>
<li>Add <code>JSON3.noConflict()</code> and <code>JSON3.runInContext()</code> [<a href="https://github.com/bestiejs/json3/issues/50">#50</a>].</li>
<li>Add a post-minification step to remove multiple IIFE wrappers.</li>
<li>Optimize <code>quote</code>.</li>
</ul>
<h2 id="3-2-6">3.2.6</h2>
<h3 id="2013-10-25">2013-10-25</h3>
<ul>
<li>Add Travis CI integration.</li>
<li>Support <a href="http://bower.io/">Bower</a>, <a href="https://component.github.io/">Component</a>, <a href="http://jamjs.org/">Jam</a>, and <a href="http://volojs.org/">Volo</a>.</li>
<li>Test with Node, PhantomJS, RingoJS, Rhino, and Narwhal on Travis.</li>
<li>Simplify exports.</li>
<li><code>stringify()</code> optimizations.</li>
<li>Add a <code>?minified</code> query parameter to the browser harness for testing the minified version [<a href="https://github.com/bestiejs/json3/issues/35">#35</a>].</li>
<li>Detect trailing comma and trailing decimal extensions in Rhino 1.7R3-R4 [<a href="https://github.com/bestiejs/json3/issues/46">#46</a>].</li>
</ul>
<h2 id="3-2-5">3.2.5</h2>
<h3 id="2013-06-14">2013-06-14</h3>
<ul>
<li>Use <code>object.hasOwnProperty(prop)</code> instead of <code>Object#hasOwnProperty.call(object, prop)</code> when iterating over host objects [<a href="https://github.com/bestiejs/json3/issues/18">#18</a>].</li>
<li>Minification improvements; avoid munging the AMD <code>define</code> pragma [<a href="https://github.com/bestiejs/json3/issues/22">#22</a>; <a href="https://github.com/bestiejs/json3/pull/25">#25</a>].</li>
<li>Use character codes instead of strings in <code>lex()</code>. Optimize for valid source strings [<a href="https://github.com/bestiejs/json3/issues/23">#23</a>; <a href="https://github.com/bestiejs/json3/pull/27">#27</a>].</li>
<li>Support Adobe ExtendScript [<a href="https://github.com/bestiejs/json3/issues/29">#29</a>].</li>
<li>Handle serializing ExtendScript host objects that throw exceptions [<a href="https://github.com/bestiejs/json3/issues/30">#30</a>; <a href="https://github.com/bestiejs/json3/pull/31">#31</a>].</li>
<li>Support Browserify and RequireJS by exporting for CommonJS and AMD [<a href="https://github.com/bestiejs/json3/pull/33">#33</a>].</li>
<li>Use square bracket character access in <code>parse</code>. Add a <code>charIndexBuggy</code> flag.</li>
<li>Add a benchmark suite.</li>
</ul>
<h2 id="3-2-4">3.2.4</h2>
<h3 id="2012-10-11">2012-10-11</h3>
<ul>
<li>Change the export order to prefer <code>module.exports</code>, <code>exports</code>, and then <code>define</code> [<a href="https://github.com/bestiejs/json3/pull/14">#14</a>].</li>
<li>Avoid conflating duplicate properties and circular references [<a href="https://github.com/bestiejs/json3/issues/15">#15</a>].</li>
<li>Export <code>parse</code> and <code>stringify</code> globally even if an AMD loader is present [<a href="https://github.com/bestiejs/json3/pull/17">#17</a>].</li>
<li>Isolate the feature tests into a <code>has()</code> function for <a href="https://github.com/phiggins42/has.js"><code>has.js</code></a> compatibility [<a href="https://github.com/bestiejs/json3/issues/19">#19</a>].</li>
</ul>
<h2 id="3-2-3">3.2.3</h2>
<h3 id="2012-07-13">2012-07-13</h3>
<ul>
<li>Prototype &lt;= 1.6.1 compatibility [<a href="https://github.com/bestiejs/json3/issues/8">#8</a>].</li>
<li><code>stringify()</code>: Iterate over whitelisted properties in order [<a href="https://github.com/bestiejs/json3/issues/12">#12</a>].</li>
<li>Correctly detect trailing commas in array literals.</li>
</ul>
<h2 id="3-2-2">3.2.2</h2>
<h3 id="2012-05-05">2012-05-05</h3>
<ul>
<li>Correctly detect native <code>parse()</code> implementations in AMD loaders and CommonJS environments [<a href="https://github.com/bestiejs/json3/issues/9">#9</a>].</li>
<li><code>parse()</code>: Use <code>delete</code> instead of <code>Array#splice()</code> when removing elements from traversed arrays [<a href="https://github.com/bestiejs/json3/issues/10">#10</a>].</li>
<li>Detect <code>parse()</code> number grammar extensions in IE 9 [<a href="https://github.com/bestiejs/json3/issues/11">#11</a>].</li>
</ul>
<h2 id="3-2-1">3.2.1</h2>
<h3 id="2012-04-26">2012-04-26</h3>
<ul>
<li>Reduce the file size by removing parse error strings [<a href="https://github.com/bestiejs/json3/issues/5">#5</a>].</li>
<li>Fall back to the native <code>stringify()</code> and <code>parse()</code> implementations in AMD loaders and CommonJS environments [<a href="https://github.com/bestiejs/json3/issues/6">#6</a>].</li>
<li>Use the correct global object when exporting for browsers and JavaScript engines.</li>
<li>Support building on Windows by using <code>zlib</code> instead of shelling out to <code>gzip</code>.</li>
<li>Switch to the Closure Compiler for generating the minified version.</li>
<li><a href="http://requirejs.org/docs/optimization.html"><code>r.js</code></a> compatibility.</li>
<li>Safari &lt; 2.0.2 and Opera &gt;= 10.53 support.</li>
</ul>
<h2 id="3-2-0">3.2.0</h2>
<h3 id="2012-04-15">2012-04-15</h3>
<ul>
<li>Override native <code>stringify()</code> implementations to work around date serialization bugs.</li>
<li>Ensure the date serialization tests pass in all time zones [<a href="https://github.com/bestiejs/json3/issues/3">#3</a>].</li>
<li>Add a workaround for buggy <code>Date#getUTC{FullYear, Month, Date}</code> implementations in Opera &gt; 9.64 [<a href="https://github.com/bestiejs/json3/issues/4">#4</a>].</li>
<li>Ensure Firefox &lt;= 11.0 serializes negative years as six-digit extended years.</li>
<li>Ensure Safari &lt;= 5.1.5 serializes milliseconds correctly.</li>
<li>Add a Node-based build script.</li>
<li>Vendor all dependencies.</li>
<li>Opera 7.54u2 support.</li>
</ul>
<h2 id="3-1-0">3.1.0</h2>
<h3 id="2012-03-22">2012-03-22</h3>
<ul>
<li>Switched to <code>bestiejs</code> organisation</li>
<li>Added support for a list of properties as the <code>filter</code> argument for <code>JSON.stringify</code></li>
<li>Fixed Firefox 4 and 4.0.1 allowing non-standard extensions to <code>JSON.parse</code></li>
</ul>
<h2 id="3-0-0">3.0.0</h2>
<h3 id="2012-03-20">2012-03-20</h3>
<ul>
<li>Renamed <code>JSON3</code> to <code>JSON</code></li>
<li>Removed <code>JSON3.Version</code></li>
<li>Added minified version of library</li>
<li>Created a <a href="http://bestiejs.github.io/json3">GitHub Project Page</a></li>
<li>Preserved alphanumeric order when iterating over shadowed properties on objects</li>
</ul>
<h2 id="0-8-5">0.8.5</h2>
<h3 id="2012-03-16">2012-03-16</h3>
<ul>
<li>Avoided relying on native functions <code>Math.abs</code>, and <code>isFinite</code>, and native constructors <code>String</code>, <code>Number</code>, <code>Object</code>, and <code>Array</code></li>
<li>Fixed AMD export logic</li>
</ul>
<h2 id="0-8-0">0.8.0</h2>
<h3 id="2012-03-15">2012-03-15</h3>
<ul>
<li>Renamed <code>Prim</code> to <code>JSON3</code></li>
<li>Added <code>JSON3.Version</code></li>
<li>Added support for AMD lodaers as the <code>&quot;json&quot;</code> module</li>
<li>Added feature tests for native <code>JSON</code> implementations</li>
<li>Added string coercion for the <code>source</code> argument in <code>JSON3.parse</code></li>
<li>Fixed the date serialization routine in <code>JSON3.stringify</code></li>
</ul>
<h2 id="0-5-0">0.5.0</h2>
<h3 id="2012-02-18">2012-02-18</h3>
<ul>
<li>Fixed <code>Prim.stringify</code>’s handling of the <code>width</code> argument</li>
<li>Added Microsoft’s <a href="https://es5conform.codeplex.com/">ES5 Conformance Tests</a> to the test suite</li>
</ul>
<h2 id="0-2-0">0.2.0</h2>
<h3 id="2012-02-17">2012-02-17</h3>
<ul>
<li>Added <code>Prim.stringify</code> for serializing values</li>
<li>Renamed <code>Prim.Escapes</code> to <code>Prim.Unescapes</code></li>
<li>Disallowed unescaped tab characters in strings passed to <code>Prim.parse</code></li>
</ul>
<h2 id="0-1-0">0.1.0</h2>
<h3 id="2012-02-16">2012-02-16</h3>
<ul>
<li>Initial release of Prim</li>
</ul>

    </div>
    <div id="footer">
      <p>&copy; 2012-2015 <a href="http://kitcambridge.be/">Kit Cambridge</a>, <a href="https://d10.github.io/">Benjamin Tan</a>.</p>
    </div>
  </body>
</html>
