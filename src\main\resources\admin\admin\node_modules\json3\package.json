{"name": "json3", "version": "3.3.3", "description": "A JSON polyfill for older JavaScript platforms.", "homepage": "https://bestiejs.github.io/json3", "main": "./lib/json3", "keywords": ["json", "spec", "ecma", "es5", "lexer", "parser", "stringify"], "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "web": "http://kitcambridge.be/"}, "maintainers": [{"name": "<PERSON>", "email": "<EMAIL>", "web": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "web": "https://d10.github.io/"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "web": "http://tech.roxee.tv/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "web": "http://fb.me/ok"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "web": "http://oxy.fi/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "web": "https://github.com/rma4ok"}], "bugs": {"url": "https://github.com/bestiejs/json3/issues"}, "scripts": {"test": "node test/test_*.js"}, "repository": {"type": "git", "url": "git://github.com/bestiejs/json3.git"}, "files": ["README.md", "LICENSE", "lib/json3.js", "lib/json3.min.js"], "jam": {"main": "./lib/json3.js", "includes": ["README.md", "LICENSE", "lib/json3.js", "lib/json3.min.js"]}, "volo": {"type": "directory", "ignore": [".*", "build.js", "index.html", "component.json", "bower.json", "benchmark", "page", "test", "vendor"]}, "devDependencies": {"curl-amd": "~0.8.12", "highlight.js": "~8.3.0", "marked": "~0.3.2", "requirejs": "~2.1.15", "spec": "~1.0.1", "tar": "~1.0.2"}, "_resolved": "https://registry.npm.taobao.org/json3/download/json3-3.3.3.tgz", "_integrity": "sha1-f8EON1/FrkLEcFpcwKpvYr4wW4E=", "_from": "json3@3.3.3"}