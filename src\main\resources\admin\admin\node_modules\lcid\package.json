{"name": "lcid", "version": "2.0.0", "description": "Mapping between standard locale identifiers and Windows locale identifiers (LCID)", "license": "MIT", "repository": "sindresorhus/lcid", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava"}, "files": ["index.js", "lcid.json"], "keywords": ["lcid", "locale", "string", "str", "id", "identifier", "windows", "language", "lang", "map", "mapping", "convert", "json", "bcp47", "ietf", "tag"], "dependencies": {"invert-kv": "^2.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}, "_resolved": "https://registry.npm.taobao.org/lcid/download/lcid-2.0.0.tgz", "_integrity": "sha1-bvXS32DlL4LrIopMNz6NHzlyU88=", "_from": "lcid@2.0.0"}