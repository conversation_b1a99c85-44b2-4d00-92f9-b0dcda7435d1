{"name": "levenary", "version": "1.1.1", "main": "index.js", "module": "index.mjs", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "repository": "tanhauhau/levenary", "scripts": {"test": "jest", "build": "babel index.mjs --out-file index.js", "bench": "matcha bench.js"}, "dependencies": {"leven": "^3.1.0"}, "files": ["index.mjs", "index.js", "index.d.ts", "index.flow.js"], "engines": {"node": ">= 6"}, "devDependencies": {"@babel/cli": "^7.7.5", "@babel/core": "^7.7.5", "@babel/plugin-transform-for-of": "^7.7.4", "@babel/preset-env": "^7.7.6", "babel-jest": "^24.9.0", "bench": "^0.3.6", "didyoumean": "^1.2.1", "didyoumean2": "^3.1.2", "jest": "^24.9.0", "matcha": "^0.7.0"}, "browserslist": "> 0.25%, not dead", "keywords": ["leven", "<PERSON><PERSON><PERSON><PERSON>", "distance", "array", "string", "algorithm", "algo", "string", "difference", "diff", "fast", "fuzzy", "similar", "similarity", "compare", "comparison", "edit", "text", "match", "matching"], "_resolved": "https://registry.npm.taobao.org/levenary/download/levenary-1.1.1.tgz", "_integrity": "sha1-hCqe6Y0gdap/ru2+MmeekgX0b3c=", "_from": "levenary@1.1.1"}