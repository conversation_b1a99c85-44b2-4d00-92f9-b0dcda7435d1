{"version": 3, "sources": ["node_modules/browser-pack/_prelude.js", "lib/entry.js", "lib/event/close.js", "lib/event/emitter.js", "lib/event/event.js", "lib/event/eventtarget.js", "lib/event/trans-message.js", "lib/facade.js", "lib/iframe-bootstrap.js", "lib/info-ajax.js", "lib/info-iframe-receiver.js", "lib/info-iframe.js", "lib/info-receiver.js", "lib/location.js", "lib/main.js", "lib/shims.js", "lib/transport-list.js", "lib/transport/browser/abstract-xhr.js", "lib/transport/browser/eventsource.js", "lib/transport/browser/websocket.js", "lib/transport/eventsource.js", "lib/transport/htmlfile.js", "lib/transport/iframe.js", "lib/transport/jsonp-polling.js", "lib/transport/lib/ajax-based.js", "lib/transport/lib/buffered-sender.js", "lib/transport/lib/iframe-wrap.js", "lib/transport/lib/polling.js", "lib/transport/lib/sender-receiver.js", "lib/transport/receiver/eventsource.js", "lib/transport/receiver/htmlfile.js", "lib/transport/receiver/jsonp.js", "lib/transport/receiver/xhr.js", "lib/transport/sender/jsonp.js", "lib/transport/sender/xdr.js", "lib/transport/sender/xhr-cors.js", "lib/transport/sender/xhr-fake.js", "lib/transport/sender/xhr-local.js", "lib/transport/websocket.js", "lib/transport/xdr-polling.js", "lib/transport/xdr-streaming.js", "lib/transport/xhr-polling.js", "lib/transport/xhr-streaming.js", "lib/utils/browser-crypto.js", "lib/utils/browser.js", "lib/utils/escape.js", "lib/utils/event.js", "lib/utils/iframe.js", "lib/utils/log.js", "lib/utils/object.js", "lib/utils/random.js", "lib/utils/transport.js", "lib/utils/url.js", "lib/version.js", "node_modules/inherits/inherits_browser.js", "node_modules/querystringify/index.js", "node_modules/requires-port/index.js", "node_modules/url-parse/index.js"], "names": ["f", "exports", "module", "define", "amd", "window", "global", "self", "this", "SockJS", "r", "e", "n", "t", "o", "i", "c", "require", "u", "a", "Error", "code", "p", "call", "length", "1", "transportList", "setTimeout", "_sockjs_onload", "inherits", "Event", "CloseEvent", "initEvent", "<PERSON><PERSON><PERSON>", "reason", "EventTarget", "EventEmitter", "prototype", "removeAllListeners", "type", "_listeners", "once", "listener", "fired", "on", "g", "removeListener", "apply", "arguments", "emit", "listeners", "l", "args", "Array", "ai", "addListener", "addEventListener", "removeEventListener", "eventType", "canBubble", "cancelable", "bubbles", "timeStamp", "Date", "stopPropagation", "preventDefault", "CAPTURING_PHASE", "AT_TARGET", "BUBBLING_PHASE", "arr", "indexOf", "concat", "idx", "slice", "dispatchEvent", "event", "TransportMessageEvent", "data", "iframe<PERSON><PERSON>s", "FacadeJS", "transport", "_transport", "_transportMessage", "bind", "_transportClose", "postMessage", "JSON", "stringify", "frame", "_send", "send", "_close", "close", "urlUtils", "eventUtils", "InfoIframeReceiver", "loc", "debug", "availableTransports", "parent<PERSON><PERSON>in", "transportMap", "for<PERSON>ach", "at", "facadeTransport", "transportName", "bootstrap_iframe", "facade", "currentWindowId", "hash", "attachEvent", "source", "parent", "origin", "iframeMessage", "parse", "ignored", "windowId", "version", "transUrl", "baseUrl", "isOriginEqual", "href", "objectUtils", "InfoAjax", "url", "AjaxObject", "t0", "xo", "status", "text", "info", "rtt", "isObject", "XHRLocalObject", "InfoReceiverIframe", "ir", "utils", "IframeTransport", "InfoIframe", "go", "ifr", "msg", "d", "document", "body", "enabled", "XDR", "XHRCors", "XHRLocal", "XHRFake", "InfoReceiver", "urlInfo", "doXhr", "_getReceiver", "<PERSON><PERSON><PERSON><PERSON>", "sameScheme", "addPath", "timeoutRef", "_cleanup", "timeout", "clearTimeout", "location", "protocol", "host", "port", "transports", "URL", "random", "escape", "browser", "log", "protocols", "options", "TypeError", "readyState", "CONNECTING", "extensions", "protocols_whitelist", "warn", "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "_transportOptions", "transportOptions", "_timeout", "sessionId", "_generateSessionId", "string", "_server", "server", "numberString", "parsedUrl", "SyntaxError", "secure", "isLoopbackAddr", "hostname", "isArray", "sortedProtocols", "sort", "proto", "<PERSON><PERSON><PERSON><PERSON>", "_origin", "toLowerCase", "set", "pathname", "replace", "_urlInfo", "<PERSON><PERSON><PERSON><PERSON>", "hasDomain", "isSchemeEqual", "_ir", "_receiveInfo", "userSetCode", "CLOSING", "CLOSED", "OPEN", "quote", "_rto", "countRTO", "_transUrl", "base_url", "extend", "enabledTransports", "filterToEnabled", "_transports", "main", "_connect", "Transport", "shift", "needBody", "unshift", "timeoutMs", "Math", "max", "roundTrips", "_transportTimeoutId", "_transportTimeout", "transportUrl", "transportObj", "payload", "content", "_open", "forceFail", "onmessage", "onclose", "onerror", "isFunction", "val", "ObjectPrototype", "toString", "isString", "obj", "_toString", "defineProperty", "ArrayPrototype", "Object", "FunctionPrototype", "Function", "StringPrototype", "String", "array_slice", "supportsDescriptors", "object", "name", "method", "forceAssign", "configurable", "enumerable", "writable", "value", "defineProperties", "map", "hasOwnProperty", "toObject", "Empty", "that", "target", "<PERSON><PERSON><PERSON><PERSON>", "boundArgs", "push", "bound", "join", "result", "properlyBoxesNonStrict", "properlyBoxesStrict", "boxedString", "splitString", "fun", "split", "thisp", "_", "__", "context", "hasFirefox2IndexOfBug", "sought", "num", "floor", "abs", "toInteger", "compliantExecNpcg", "string_split", "exec", "separator", "limit", "separator2", "match", "lastIndex", "last<PERSON><PERSON><PERSON>", "output", "flags", "ignoreCase", "multiline", "extended", "sticky", "lastLastIndex", "RegExp", "x", "ToUint32", "index", "test", "string_substr", "substr", "hasNegativeSubstrBug", "start", "XHR", "XMLHttpRequest", "AbstractXHRObject", "opts", "_start", "xhr", "<PERSON><PERSON><PERSON><PERSON>", "unloadRef", "unloadAdd", "open", "ontimeout", "noCredentials", "supportsCORS", "withCredentials", "headers", "key", "setRequestHeader", "onreadystatechange", "responseText", "abort", "unloadDel", "axo", "cors", "EventSource", "Driver", "WebSocket", "MozWebSocket", "undefined", "AjaxBasedTransport", "EventSourceReceiver", "XHRCorsObject", "EventSourceDriver", "EventSourceTransport", "HtmlfileReceiver", "HtmlFileTransport", "iframeUrl", "iframeObj", "createIframe", "onmessageCallback", "_message", "detachEvent", "cleanup", "loaded", "cdata", "post", "message", "iframeEnabled", "SenderReceiver", "JsonpReceiver", "jsonpSender", "JsonPTransport", "urlSuffix", "Receiver", "callback", "opt", "Content-type", "ajaxUrl", "err", "createAjaxSender", "BufferedSender", "sender", "send<PERSON><PERSON><PERSON>", "sendStop", "sendSchedule", "sendScheduleWait", "tref", "IframeWrapTransport", "iframeInfo", "Polling", "receiveUrl", "_scheduleReceiver", "poll", "pollIsClosing", "senderFunc", "pollUrl", "es", "decodeURI", "polluteGlobalNamespace", "id", "decodeURIComponent", "WPrefix", "htmlfileEnabled", "constructFunc", "createHtmlfile", "stop", "urlWithId", "encodeURIComponent", "_callback", "_createScript", "timeoutId", "_abort", "scriptErrorTimeout", "aborting", "script2", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "script", "onload", "onclick", "_scriptError", "errorTimer", "loaded<PERSON>kay", "createElement", "src", "charset", "htmlFor", "async", "isOpera", "head", "getElementsByTagName", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "XhrReceiver", "bufferPosition", "_<PERSON><PERSON><PERSON><PERSON>", "buf", "form", "area", "style", "display", "position", "enctype", "acceptCharset", "append<PERSON><PERSON><PERSON>", "action", "iframe", "submit", "completed", "XDRObject", "xdr", "XDomainRequest", "_error", "onprogress", "XhrDriver", "to", "WebsocketDriver", "WebSocketTransport", "ignore", "ws", "XdrStreamingTransport", "XdrPollingTransport", "cookie_needed", "XhrPollingTransport", "XhrStreamingTransport", "crypto", "getRandomValues", "randomBytes", "bytes", "Uint8Array", "navigator", "userAgent", "isKonqueror", "domain", "extraLookup", "extraEscapable", "quoted", "escapable", "unrolled", "fromCharCode", "charCodeAt", "unrollLookup", "onUnload", "afterUnload", "isChromePackagedApp", "chrome", "app", "runtime", "ref", "triggerUnloadCallbacks", "<PERSON><PERSON><PERSON><PERSON>", "unattach", "contentWindow", "doc", "CollectGarbage", "write", "parentWindow", "logObject", "level", "levelExists", "console", "prop", "_randomStringChars", "ret", "number", "transports<PERSON><PERSON><PERSON><PERSON>", "trans", "websocket", "b", "res", "path", "qs", "q", "addr", "create", "ctor", "superCtor", "super_", "constructor", "TempCtor", "has", "decode", "input", "prefix", "pairs", "isNaN", "query", "part", "parser", "required", "controlOrWhitespace", "CRHTLF", "slashes", "protocolre", "windowsDriveLetter", "trimLeft", "str", "rules", "address", "isSpecial", "NaN", "lolcation", "finaldestination", "Url", "unescape", "scheme", "extractProtocol", "rest", "forwardSlashes", "otherSlashes", "slashesCount", "relative", "extracted", "instruction", "instructions", "lastIndexOf", "char<PERSON>t", "base", "last", "up", "splice", "resolve", "username", "password", "auth", "fn", "pop", "char", "ins"], "mappings": ";CAAA,SAAAA,GAAA,GAAA,iBAAAC,SAAA,oBAAAC,OAAAA,OAAAD,QAAAD,SAAA,GAAA,mBAAAG,QAAAA,OAAAC,IAAAD,OAAA,GAAAH,OAAA,EAAA,oBAAAK,OAAAA,OAAA,oBAAAC,OAAAA,OAAA,oBAAAC,KAAAA,KAAAC,MAAAC,OAAAT,KAAA,CAAA,WAAA,OAAA,SAAAU,EAAAC,EAAAC,EAAAC,GAAA,SAAAC,EAAAC,EAAAf,GAAA,IAAAY,EAAAG,GAAA,CAAA,IAAAJ,EAAAI,GAAA,CAAA,IAAAC,EAAA,mBAAAC,SAAAA,QAAA,IAAAjB,GAAAgB,EAAA,OAAAA,EAAAD,GAAA,GAAA,GAAAG,EAAA,OAAAA,EAAAH,GAAA,GAAA,IAAAI,EAAA,IAAAC,MAAA,uBAAAL,EAAA,KAAA,MAAAI,EAAAE,KAAA,mBAAAF,EAAA,IAAAG,EAAAV,EAAAG,GAAA,CAAAd,QAAA,IAAAU,EAAAI,GAAA,GAAAQ,KAAAD,EAAArB,QAAA,SAAAS,GAAA,OAAAI,EAAAH,EAAAI,GAAA,GAAAL,IAAAA,IAAAY,EAAAA,EAAArB,QAAAS,EAAAC,EAAAC,EAAAC,GAAA,OAAAD,EAAAG,GAAAd,QAAA,IAAA,IAAAiB,EAAA,mBAAAD,SAAAA,QAAAF,EAAA,EAAAA,EAAAF,EAAAW,OAAAT,IAAAD,EAAAD,EAAAE,IAAA,OAAAD,EAAA,CAAA,CAAAW,EAAA,CAAA,SAAAR,EAAAf,EAAAD,4BCAA,aAEA,IAAAyB,EAAAT,EAAA,oBAEAf,EAAAD,QAAAgB,EAAA,SAAAA,CAAAS,GAGA,mBAAApB,GACAqB,WAAArB,EAAAsB,eAAA,2LCRA,aAEA,IAAAC,EAAAZ,EAAA,YACAa,EAAAb,EAAA,WAGA,SAAAc,IACAD,EAAAP,KAAAf,MACAA,KAAAwB,UAAA,SAAA,GAAA,GACAxB,KAAAyB,UAAA,EACAzB,KAAAa,KAAA,EACAb,KAAA0B,OAAA,GAGAL,EAAAE,EAAAD,GAEA5B,EAAAD,QAAA8B,mDChBA,aAEA,IAAAF,EAAAZ,EAAA,YACAkB,EAAAlB,EAAA,iBAGA,SAAAmB,IACAD,EAAAZ,KAAAf,MAGAqB,EAAAO,EAAAD,GAEAC,EAAAC,UAAAC,mBAAA,SAAAC,GACAA,SACA/B,KAAAgC,WAAAD,GAEA/B,KAAAgC,WAAA,IAIAJ,EAAAC,UAAAI,KAAA,SAAAF,EAAAG,GACA,IAAAnC,EAAAC,KACAmC,GAAA,EAWAnC,KAAAoC,GAAAL,EATA,SAAAM,IACAtC,EAAAuC,eAAAP,EAAAM,GAEAF,IACAA,GAAA,EACAD,EAAAK,MAAAvC,KAAAwC,eAOAZ,EAAAC,UAAAY,KAAA,WACA,IAAAV,EAAAS,UAAA,GACAE,EAAA1C,KAAAgC,WAAAD,GACA,GAAAW,EAAA,CAMA,IAFA,IAAAC,EAAAH,UAAAxB,OACA4B,EAAA,IAAAC,MAAAF,EAAA,GACAG,EAAA,EAAAA,EAAAH,EAAAG,IACAF,EAAAE,EAAA,GAAAN,UAAAM,GAEA,IAAA,IAAAvC,EAAA,EAAAA,EAAAmC,EAAA1B,OAAAT,IACAmC,EAAAnC,GAAAgC,MAAAvC,KAAA4C,KAIAhB,EAAAC,UAAAO,GAAAR,EAAAC,UAAAkB,YAAApB,EAAAE,UAAAmB,iBACApB,EAAAC,UAAAS,eAAAX,EAAAE,UAAAoB,oBAEAvD,EAAAD,QAAAmC,aAAAA,yDCxDA,aAEA,SAAAN,EAAA4B,GACAlD,KAAA+B,KAAAmB,EAGA5B,EAAAO,UAAAL,UAAA,SAAA0B,EAAAC,EAAAC,GAKA,OAJApD,KAAA+B,KAAAmB,EACAlD,KAAAqD,QAAAF,EACAnD,KAAAoD,WAAAA,EACApD,KAAAsD,WAAA,IAAAC,KACAvD,MAGAsB,EAAAO,UAAA2B,gBAAA,aACAlC,EAAAO,UAAA4B,eAAA,aAEAnC,EAAAoC,gBAAA,EACApC,EAAAqC,UAAA,EACArC,EAAAsC,eAAA,EAEAlE,EAAAD,QAAA6B,0BCrBA,aAMA,SAAAK,IACA3B,KAAAgC,WAAA,GAGAL,EAAAE,UAAAmB,iBAAA,SAAAE,EAAAhB,GACAgB,KAAAlD,KAAAgC,aACAhC,KAAAgC,WAAAkB,GAAA,IAEA,IAAAW,EAAA7D,KAAAgC,WAAAkB,IAEA,IAAAW,EAAAC,QAAA5B,KAEA2B,EAAAA,EAAAE,OAAA,CAAA7B,KAEAlC,KAAAgC,WAAAkB,GAAAW,GAGAlC,EAAAE,UAAAoB,oBAAA,SAAAC,EAAAhB,GACA,IAAA2B,EAAA7D,KAAAgC,WAAAkB,GACA,GAAAW,EAAA,CAGA,IAAAG,EAAAH,EAAAC,QAAA5B,IACA,IAAA8B,IACA,EAAAH,EAAA7C,OAEAhB,KAAAgC,WAAAkB,GAAAW,EAAAI,MAAA,EAAAD,GAAAD,OAAAF,EAAAI,MAAAD,EAAA,WAEAhE,KAAAgC,WAAAkB,MAMAvB,EAAAE,UAAAqC,cAAA,WACA,IAAAC,EAAA3B,UAAA,GACAnC,EAAA8D,EAAApC,KAEAa,EAAA,IAAAJ,UAAAxB,OAAA,CAAAmD,GAAAtB,MAAAN,MAAA,KAAAC,WAQA,GAHAxC,KAAA,KAAAK,IACAL,KAAA,KAAAK,GAAAkC,MAAAvC,KAAA4C,GAEAvC,KAAAL,KAAAgC,WAGA,IADA,IAAAU,EAAA1C,KAAAgC,WAAA3B,GACAE,EAAA,EAAAA,EAAAmC,EAAA1B,OAAAT,IACAmC,EAAAnC,GAAAgC,MAAAvC,KAAA4C,IAKAlD,EAAAD,QAAAkC,0BC7DA,aAEA,IAAAN,EAAAZ,EAAA,YACAa,EAAAb,EAAA,WAGA,SAAA2D,EAAAC,GACA/C,EAAAP,KAAAf,MACAA,KAAAwB,UAAA,WAAA,GAAA,GACAxB,KAAAqE,KAAAA,EAGAhD,EAAA+C,EAAA9C,GAEA5B,EAAAD,QAAA2E,mDCdA,aAEA,IAAAE,EAAA7D,EAAA,kBAGA,SAAA8D,EAAAC,IACAxE,KAAAyE,WAAAD,GACApC,GAAA,UAAApC,KAAA0E,kBAAAC,KAAA3E,OACAwE,EAAApC,GAAA,QAAApC,KAAA4E,gBAAAD,KAAA3E,OAGAuE,EAAA1C,UAAA+C,gBAAA,SAAA/D,EAAAa,GACA4C,EAAAO,YAAA,IAAAC,KAAAC,UAAA,CAAAlE,EAAAa,MAEA6C,EAAA1C,UAAA6C,kBAAA,SAAAM,GACAV,EAAAO,YAAA,IAAAG,IAEAT,EAAA1C,UAAAoD,MAAA,SAAAZ,GACArE,KAAAyE,WAAAS,KAAAb,IAEAE,EAAA1C,UAAAsD,OAAA,WACAnF,KAAAyE,WAAAW,QACApF,KAAAyE,WAAA3C,sBAGApC,EAAAD,QAAA8E,6CCzBA,aAEA,IAAAc,EAAA5E,EAAA,eACA6E,EAAA7E,EAAA,iBACA8D,EAAA9D,EAAA,YACA8E,EAAA9E,EAAA,0BACA6D,EAAA7D,EAAA,kBACA+E,EAAA/E,EAAA,cAGAgF,EAAA,aAKA/F,EAAAD,QAAA,SAAAQ,EAAAyF,GACA,IAUAC,EAVAC,EAAA,GACAF,EAAAG,QAAA,SAAAC,GACAA,EAAAC,kBACAH,EAAAE,EAAAC,gBAAAC,eAAAF,EAAAC,mBAMAH,EAAAL,EAAAS,eAAAT,EAIAtF,EAAAgG,iBAAA,WAEA,IAAAC,EACA5B,EAAA6B,gBAAAX,EAAAY,KAAAnC,MAAA,GA+DAqB,EAAAe,YAAA,UA9DA,SAAAlG,GACA,GAAAA,EAAAmG,SAAAC,cAGA,IAAAZ,IACAA,EAAAxF,EAAAqG,QAEArG,EAAAqG,SAAAb,GAAA,CAIA,IAAAc,EACA,IACAA,EAAA3B,KAAA4B,MAAAvG,EAAAkE,MACA,MAAAsC,GAEA,YADAlB,EAAA,WAAAtF,EAAAkE,MAIA,GAAAoC,EAAAG,WAAAtC,EAAA6B,gBAGA,OAAAM,EAAA1E,MACA,IAAA,IACA,IAAAjB,EACA,IACAA,EAAAgE,KAAA4B,MAAAD,EAAApC,MACA,MAAAsC,GACAlB,EAAA,WAAAgB,EAAApC,MACA,MAEA,IAAAwC,EAAA/F,EAAA,GACA0D,EAAA1D,EAAA,GACAgG,EAAAhG,EAAA,GACAiG,EAAAjG,EAAA,GAGA,GAFA2E,EAAAoB,EAAArC,EAAAsC,EAAAC,GAEAF,IAAA5G,EAAA4G,QACA,MAAA,IAAAjG,MAAA,yCACAiG,EAAA,mBACA5G,EAAA4G,QAAA,MAGA,IAAAxB,EAAA2B,cAAAF,EAAAtB,EAAAyB,QACA5B,EAAA2B,cAAAD,EAAAvB,EAAAyB,MACA,MAAA,IAAArG,MAAA,6DACA4E,EAAAyB,KAAA,KAAAH,EAAA,KAAAC,EAAA,KAEAb,EAAA,IAAA3B,EAAA,IAAAqB,EAAApB,GAAAsC,EAAAC,IACA,MACA,IAAA,IACAb,EAAAjB,MAAAwB,EAAApC,MACA,MACA,IAAA,IACA6B,GACAA,EAAAf,SAEAe,EAAA,SAQA5B,EAAAO,YAAA,8JClGA,aAEA,IAAAjD,EAAAnB,EAAA,UAAAmB,aACAP,EAAAZ,EAAA,YACAyG,EAAAzG,EAAA,kBAGAgF,EAAA,aAKA,SAAA0B,EAAAC,EAAAC,GACAzF,EAAAb,KAAAf,MAEA,IAAAD,EAAAC,KACAsH,GAAA,IAAA/D,KACAvD,KAAAuH,GAAA,IAAAF,EAAA,MAAAD,GAEApH,KAAAuH,GAAAtF,KAAA,SAAA,SAAAuF,EAAAC,GACA,IAAAC,EAAAC,EACA,GAAA,MAAAH,EAAA,CAEA,GADAG,GAAA,IAAApE,KAAA+D,EACAG,EACA,IACAC,EAAA5C,KAAA4B,MAAAe,GACA,MAAAtH,GACAsF,EAAA,WAAAgC,GAIAP,EAAAU,SAAAF,KACAA,EAAA,IAGA3H,EAAA0C,KAAA,SAAAiF,EAAAC,GACA5H,EAAA+B,uBAIAT,EAAA8F,EAAAvF,GAEAuF,EAAAtF,UAAAuD,MAAA,WACApF,KAAA8B,qBACA9B,KAAAuH,GAAAnC,SAGA1F,EAAAD,QAAA0H,sFC/CA,aAEA,IAAA9F,EAAAZ,EAAA,YACAmB,EAAAnB,EAAA,UAAAmB,aACAiG,EAAApH,EAAA,gCACA0G,EAAA1G,EAAA,eAGA,SAAAqH,EAAAhB,GACA,IAAA/G,EAAAC,KACA4B,EAAAb,KAAAf,MAEAA,KAAA+H,GAAA,IAAAZ,EAAAL,EAAAe,GACA7H,KAAA+H,GAAA9F,KAAA,SAAA,SAAAyF,EAAAC,GACA5H,EAAAgI,GAAA,KACAhI,EAAA0C,KAAA,UAAAqC,KAAAC,UAAA,CAAA2C,EAAAC,OAIAtG,EAAAyG,EAAAlG,GAEAkG,EAAA9B,cAAA,uBAEA8B,EAAAjG,UAAAuD,MAAA,WACApF,KAAA+H,KACA/H,KAAA+H,GAAA3C,QACApF,KAAA+H,GAAA,MAEA/H,KAAA8B,sBAGApC,EAAAD,QAAAqI,8HC/BA,aAEA,IAAAlG,EAAAnB,EAAA,UAAAmB,aACAP,EAAAZ,EAAA,YACAuH,EAAAvH,EAAA,iBACAwH,EAAAxH,EAAA,sBACAqH,EAAArH,EAAA,0BAGAgF,EAAA,aAKA,SAAAyC,EAAAnB,EAAAK,GACA,IAAArH,EAAAC,KACA4B,EAAAb,KAAAf,MAEA,SAAAmI,IACA,IAAAC,EAAArI,EAAAqI,IAAA,IAAAH,EAAAH,EAAA9B,cAAAoB,EAAAL,GAEAqB,EAAAnG,KAAA,UAAA,SAAAoG,GACA,GAAAA,EAAA,CACA,IAAAC,EACA,IACAA,EAAAxD,KAAA4B,MAAA2B,GACA,MAAAlI,GAIA,OAHAsF,EAAA,WAAA4C,GACAtI,EAAA0C,KAAA,eACA1C,EAAAqF,QAIA,IAAAsC,EAAAY,EAAA,GAAAX,EAAAW,EAAA,GACAvI,EAAA0C,KAAA,SAAAiF,EAAAC,GAEA5H,EAAAqF,UAGAgD,EAAAnG,KAAA,QAAA,WACAlC,EAAA0C,KAAA,UACA1C,EAAAqF,UAKAtF,EAAAyI,SAAAC,KAGAL,IAFAH,EAAA3B,YAAA,OAAA8B,GAMA9G,EAAA6G,EAAAtG,GAEAsG,EAAAO,QAAA,WACA,OAAAR,EAAAQ,WAGAP,EAAArG,UAAAuD,MAAA,WACApF,KAAAoI,KACApI,KAAAoI,IAAAhD,QAEApF,KAAA8B,qBACA9B,KAAAoI,IAAA,MAGA1I,EAAAD,QAAAyI,wQCnEA,aAEA,IAAAtG,EAAAnB,EAAA,UAAAmB,aACAP,EAAAZ,EAAA,YACA4E,EAAA5E,EAAA,eACAiI,EAAAjI,EAAA,0BACAkI,EAAAlI,EAAA,+BACAmI,EAAAnI,EAAA,gCACAoI,EAAApI,EAAA,+BACAyH,EAAAzH,EAAA,iBACA0G,EAAA1G,EAAA,eAGAgF,EAAA,aAKA,SAAAqD,EAAA/B,EAAAgC,GACAtD,EAAAsB,GACA,IAAAhH,EAAAC,KACA4B,EAAAb,KAAAf,MAEAmB,WAAA,WACApB,EAAAiJ,MAAAjC,EAAAgC,IACA,GAGA1H,EAAAyH,EAAAlH,GAIAkH,EAAAG,aAAA,SAAAlC,EAAAK,EAAA2B,GAEA,OAAAA,EAAAG,WACA,IAAA/B,EAAAC,EAAAwB,GAEAD,EAAAF,QACA,IAAAtB,EAAAC,EAAAuB,GAEAD,EAAAD,SAAAM,EAAAI,WACA,IAAAhC,EAAAC,EAAAsB,GAEAR,EAAAO,UACA,IAAAP,EAAAnB,EAAAK,GAEA,IAAAD,EAAAC,EAAAyB,IAGAC,EAAAjH,UAAAmH,MAAA,SAAAjC,EAAAgC,GACA,IAAAhJ,EAAAC,KACAoH,EAAA/B,EAAA+D,QAAArC,EAAA,SAEAtB,EAAA,QAAA2B,GAEApH,KAAAuH,GAAAuB,EAAAG,aAAAlC,EAAAK,EAAA2B,GAEA/I,KAAAqJ,WAAAlI,WAAA,WACAsE,EAAA,WACA1F,EAAAuJ,UAAA,GACAvJ,EAAA0C,KAAA,WACAqG,EAAAS,SAEAvJ,KAAAuH,GAAAtF,KAAA,SAAA,SAAAyF,EAAAC,GACAlC,EAAA,SAAAiC,EAAAC,GACA5H,EAAAuJ,UAAA,GACAvJ,EAAA0C,KAAA,SAAAiF,EAAAC,MAIAmB,EAAAjH,UAAAyH,SAAA,SAAA7H,GACAgE,EAAA,YACA+D,aAAAxJ,KAAAqJ,YACArJ,KAAAqJ,WAAA,MACA5H,GAAAzB,KAAAuH,IACAvH,KAAAuH,GAAAnC,QAEApF,KAAAuH,GAAA,MAGAuB,EAAAjH,UAAAuD,MAAA,WACAK,EAAA,SACAzF,KAAA8B,qBACA9B,KAAAsJ,UAAA,IAGAR,EAAAS,QAAA,IAEA7J,EAAAD,QAAAqJ,+QCxFA,aAEApJ,EAAAD,QAAAK,EAAA2J,UAAA,CACAjD,OAAA,sBACAkD,SAAA,QACAC,KAAA,YACAC,KAAA,GACA3C,KAAA,oBACAb,KAAA,qLCRA,aAEA3F,EAAA,WAEA,IAuBAoJ,EAvBAC,EAAArJ,EAAA,aACAY,EAAAZ,EAAA,YACAsJ,EAAAtJ,EAAA,kBACAuJ,EAAAvJ,EAAA,kBACA4E,EAAA5E,EAAA,eACA6E,EAAA7E,EAAA,iBACA+D,EAAA/D,EAAA,qBACAyG,EAAAzG,EAAA,kBACAwJ,EAAAxJ,EAAA,mBACAyJ,EAAAzJ,EAAA,eACAa,EAAAb,EAAA,iBACAkB,EAAAlB,EAAA,uBACA+E,EAAA/E,EAAA,cACAc,EAAAd,EAAA,iBACA2D,EAAA3D,EAAA,yBACAqI,EAAArI,EAAA,mBAGAgF,EAAA,aAQA,SAAAxF,EAAAmH,EAAA+C,EAAAC,GACA,KAAApK,gBAAAC,GACA,OAAA,IAAAA,EAAAmH,EAAA+C,EAAAC,GAEA,GAAA5H,UAAAxB,OAAA,EACA,MAAA,IAAAqJ,UAAA,wEAEA1I,EAAAZ,KAAAf,MAEAA,KAAAsK,WAAArK,EAAAsK,WACAvK,KAAAwK,WAAA,GACAxK,KAAA0J,SAAA,IAGAU,EAAAA,GAAA,IACAK,qBACAP,EAAAQ,KAAA,kEAEA1K,KAAA2K,qBAAAP,EAAAP,WACA7J,KAAA4K,kBAAAR,EAAAS,kBAAA,GACA7K,KAAA8K,SAAAV,EAAAb,SAAA,EAEA,IAAAwB,EAAAX,EAAAW,WAAA,EACA,GAAA,mBAAAA,EACA/K,KAAAgL,mBAAAD,MACA,CAAA,GAAA,iBAAAA,EAKA,MAAA,IAAAV,UAAA,+EAJArK,KAAAgL,mBAAA,WACA,OAAAjB,EAAAkB,OAAAF,IAMA/K,KAAAkL,QAAAd,EAAAe,QAAApB,EAAAqB,aAAA,KAGA,IAAAC,EAAA,IAAAvB,EAAA1C,GACA,IAAAiE,EAAA1B,OAAA0B,EAAA3B,SACA,MAAA,IAAA4B,YAAA,YAAAlE,EAAA,gBACA,GAAAiE,EAAAjF,KACA,MAAA,IAAAkF,YAAA,uCACA,GAAA,UAAAD,EAAA3B,UAAA,WAAA2B,EAAA3B,SACA,MAAA,IAAA4B,YAAA,yDAAAD,EAAA3B,SAAA,qBAGA,IAAA6B,EAAA,WAAAF,EAAA3B,SAEA,GAAA,WAAAlE,EAAAkE,WAAA6B,IAEAlG,EAAAmG,eAAAH,EAAAI,UACA,MAAA,IAAA7K,MAAA,mGAMAuJ,EAEAtH,MAAA6I,QAAAvB,KACAA,EAAA,CAAAA,IAFAA,EAAA,GAMA,IAAAwB,EAAAxB,EAAAyB,OACAD,EAAA9F,QAAA,SAAAgG,EAAAtL,GACA,IAAAsL,EACA,MAAA,IAAAP,YAAA,wBAAAO,EAAA,iBAEA,GAAAtL,EAAAoL,EAAA3K,OAAA,GAAA6K,IAAAF,EAAApL,EAAA,GACA,MAAA,IAAA+K,YAAA,wBAAAO,EAAA,sBAKA,IAAAvL,EAAA+E,EAAAyG,UAAAtG,EAAAyB,MACAjH,KAAA+L,QAAAzL,EAAAA,EAAA0L,cAAA,KAGAX,EAAAY,IAAA,WAAAZ,EAAAa,SAAAC,QAAA,OAAA,KAGAnM,KAAAoH,IAAAiE,EAAApE,KACAxB,EAAA,YAAAzF,KAAAoH,KAKApH,KAAAoM,SAAA,CACAC,YAAApC,EAAAqC,YACApD,WAAA7D,EAAA2B,cAAAhH,KAAAoH,IAAA5B,EAAAyB,MACAkC,WAAA9D,EAAAkH,cAAAvM,KAAAoH,IAAA5B,EAAAyB,OAGAjH,KAAAwM,IAAA,IAAA1D,EAAA9I,KAAAoH,IAAApH,KAAAoM,UACApM,KAAAwM,IAAAvK,KAAA,SAAAjC,KAAAyM,aAAA9H,KAAA3E,OAKA,SAAA0M,EAAA7L,GACA,OAAA,MAAAA,GAAA,KAAAA,GAAAA,GAAA,KAHAQ,EAAApB,EAAA0B,GAMA1B,EAAA4B,UAAAuD,MAAA,SAAAvE,EAAAa,GAEA,GAAAb,IAAA6L,EAAA7L,GACA,MAAA,IAAAD,MAAA,oCAGA,GAAAc,GAAA,IAAAA,EAAAV,OACA,MAAA,IAAAsK,YAAA,yCAIA,GAAAtL,KAAAsK,aAAArK,EAAA0M,SAAA3M,KAAAsK,aAAArK,EAAA2M,OAAA,CAMA5M,KAAAmF,OAAAtE,GAAA,IAAAa,GAAA,kBADA,KAIAzB,EAAA4B,UAAAqD,KAAA,SAAAb,GAMA,GAHA,iBAAAA,IACAA,EAAA,GAAAA,GAEArE,KAAAsK,aAAArK,EAAAsK,WACA,MAAA,IAAA3J,MAAA,kEAEAZ,KAAAsK,aAAArK,EAAA4M,MAGA7M,KAAAyE,WAAAS,KAAA8E,EAAA8C,MAAAzI,KAGApE,EAAA4G,QAAApG,EAAA,aAEAR,EAAAsK,WAAA,EACAtK,EAAA4M,KAAA,EACA5M,EAAA0M,QAAA,EACA1M,EAAA2M,OAAA,EAEA3M,EAAA4B,UAAA4K,aAAA,SAAA/E,EAAAC,GAGA,GAFAlC,EAAA,eAAAkC,GACA3H,KAAAwM,IAAA,KACA9E,EAAA,CAOA1H,KAAA+M,KAAA/M,KAAAgN,SAAArF,GAEA3H,KAAAiN,UAAAvF,EAAAwF,SAAAxF,EAAAwF,SAAAlN,KAAAoH,IACAM,EAAAR,EAAAiG,OAAAzF,EAAA1H,KAAAoM,UACA3G,EAAA,OAAAiC,GAEA,IAAA0F,EAAAvD,EAAAwD,gBAAArN,KAAA2K,qBAAAjD,GACA1H,KAAAsN,YAAAF,EAAAG,KACA9H,EAAAzF,KAAAsN,YAAAtM,OAAA,uBAEAhB,KAAAwN,gBAhBAxN,KAAAmF,OAAA,KAAA,6BAmBAlF,EAAA4B,UAAA2L,SAAA,WACA,IAAA,IAAAC,EAAAzN,KAAAsN,YAAAI,QAAAD,EAAAA,EAAAzN,KAAAsN,YAAAI,QAAA,CAEA,GADAjI,EAAA,UAAAgI,EAAAzH,eACAyH,EAAAE,YACA7N,EAAAyI,SAAAC,WACA,IAAA1I,EAAAyI,SAAA+B,YACA,aAAAxK,EAAAyI,SAAA+B,YACA,gBAAAxK,EAAAyI,SAAA+B,YAIA,OAHA7E,EAAA,oBACAzF,KAAAsN,YAAAM,QAAAH,QACAnI,EAAAe,YAAA,OAAArG,KAAAwN,SAAA7I,KAAA3E,OAMA,IAAA6N,EAAAC,KAAAC,IAAA/N,KAAA8K,SAAA9K,KAAA+M,KAAAU,EAAAO,YAAA,KACAhO,KAAAiO,oBAAA9M,WAAAnB,KAAAkO,kBAAAvJ,KAAA3E,MAAA6N,GACApI,EAAA,gBAAAoI,GAEA,IAAAM,EAAA9I,EAAA+D,QAAApJ,KAAAiN,UAAA,IAAAjN,KAAAkL,QAAA,IAAAlL,KAAAgL,sBACAZ,EAAApK,KAAA4K,kBAAA6C,EAAAzH,eACAP,EAAA,gBAAA0I,GACA,IAAAC,EAAA,IAAAX,EAAAU,EAAAnO,KAAAiN,UAAA7C,GAMA,OALAgE,EAAAhM,GAAA,UAAApC,KAAA0E,kBAAAC,KAAA3E,OACAoO,EAAAnM,KAAA,QAAAjC,KAAA4E,gBAAAD,KAAA3E,OACAoO,EAAApI,cAAAyH,EAAAzH,mBACAhG,KAAAyE,WAAA2J,GAIApO,KAAAmF,OAAA,IAAA,yBAAA,IAGAlF,EAAA4B,UAAAqM,kBAAA,WACAzI,EAAA,qBACAzF,KAAAsK,aAAArK,EAAAsK,aACAvK,KAAAyE,YACAzE,KAAAyE,WAAAW,QAGApF,KAAA4E,gBAAA,KAAA,yBAIA3E,EAAA4B,UAAA6C,kBAAA,SAAA2D,GACA5C,EAAA,oBAAA4C,GACA,IAGAgG,EAHAtO,EAAAC,KACA+B,EAAAsG,EAAApE,MAAA,EAAA,GACAqK,EAAAjG,EAAApE,MAAA,GAKA,OAAAlC,GACA,IAAA,IAEA,YADA/B,KAAAuO,QAEA,IAAA,IAGA,OAFAvO,KAAAkE,cAAA,IAAA5C,EAAA,mBACAmE,EAAA,YAAAzF,KAAAwE,WAIA,GAAA8J,EACA,IACAD,EAAAvJ,KAAA4B,MAAA4H,GACA,MAAAnO,GACAsF,EAAA,WAAA6I,GAIA,QAAA,IAAAD,EAKA,OAAAtM,GACA,IAAA,IACAc,MAAA6I,QAAA2C,IACAA,EAAAxI,QAAA,SAAA/E,GACA2E,EAAA,UAAA1F,EAAAyE,UAAA1D,GACAf,EAAAmE,cAAA,IAAAE,EAAAtD,MAGA,MACA,IAAA,IACA2E,EAAA,UAAAzF,KAAAwE,UAAA6J,GACArO,KAAAkE,cAAA,IAAAE,EAAAiK,IACA,MACA,IAAA,IACAxL,MAAA6I,QAAA2C,IAAA,IAAAA,EAAArN,QACAhB,KAAAmF,OAAAkJ,EAAA,GAAAA,EAAA,IAAA,QAnBA5I,EAAA,gBAAA6I,IAyBArO,EAAA4B,UAAA+C,gBAAA,SAAA/D,EAAAa,GACA+D,EAAA,kBAAAzF,KAAAwE,UAAA3D,EAAAa,GACA1B,KAAAyE,aACAzE,KAAAyE,WAAA3C,qBACA9B,KAAAyE,WAAA,KACAzE,KAAAwE,UAAA,MAGAkI,EAAA7L,IAAA,MAAAA,GAAAb,KAAAsK,aAAArK,EAAAsK,WAKAvK,KAAAmF,OAAAtE,EAAAa,GAJA1B,KAAAwN,YAOAvN,EAAA4B,UAAA0M,MAAA,WACA9I,EAAA,QAAAzF,KAAAyE,YAAAzE,KAAAyE,WAAAuB,cAAAhG,KAAAsK,YACAtK,KAAAsK,aAAArK,EAAAsK,YACAvK,KAAAiO,sBACAzE,aAAAxJ,KAAAiO,qBACAjO,KAAAiO,oBAAA,MAEAjO,KAAAsK,WAAArK,EAAA4M,KACA7M,KAAAwE,UAAAxE,KAAAyE,WAAAuB,cACAhG,KAAAkE,cAAA,IAAA5C,EAAA,SACAmE,EAAA,YAAAzF,KAAAwE,YAIAxE,KAAAmF,OAAA,KAAA,wBAIAlF,EAAA4B,UAAAsD,OAAA,SAAAtE,EAAAa,EAAAD,GACAgE,EAAA,SAAAzF,KAAAwE,UAAA3D,EAAAa,EAAAD,EAAAzB,KAAAsK,YACA,IAAAkE,GAAA,EAaA,GAXAxO,KAAAwM,MACAgC,GAAA,EACAxO,KAAAwM,IAAApH,QACApF,KAAAwM,IAAA,MAEAxM,KAAAyE,aACAzE,KAAAyE,WAAAW,QACApF,KAAAyE,WAAA,KACAzE,KAAAwE,UAAA,MAGAxE,KAAAsK,aAAArK,EAAA2M,OACA,MAAA,IAAAhM,MAAA,qDAGAZ,KAAAsK,WAAArK,EAAA0M,QACAxL,WAAA,WACAnB,KAAAsK,WAAArK,EAAA2M,OAEA4B,GACAxO,KAAAkE,cAAA,IAAA5C,EAAA,UAGA,IAAAnB,EAAA,IAAAoB,EAAA,SACApB,EAAAsB,SAAAA,IAAA,EACAtB,EAAAU,KAAAA,GAAA,IACAV,EAAAuB,OAAAA,EAEA1B,KAAAkE,cAAA/D,GACAH,KAAAyO,UAAAzO,KAAA0O,QAAA1O,KAAA2O,QAAA,KACAlJ,EAAA,iBACAd,KAAA3E,MAAA,IAKAC,EAAA4B,UAAAmL,SAAA,SAAArF,GAOA,OAAA,IAAAA,EACA,EAAAA,EAEA,IAAAA,GAGAjI,EAAAD,QAAA,SAAAiG,GAGA,OAFAmE,EAAArF,EAAAkB,GACAjF,EAAA,qBAAAA,CAAAR,EAAAyF,GACAzF,ihBChYA,aAWA,SAAA2O,EAAAC,GACA,MAAA,sBAAAC,EAAAC,SAAAhO,KAAA8N,GAKA,SAAAG,EAAAC,GACA,MAAA,oBAAAC,EAAAnO,KAAAkO,GAdA,IA4BAE,EA5BAC,EAAAvM,MAAAhB,UACAiN,EAAAO,OAAAxN,UACAyN,EAAAC,SAAA1N,UACA2N,EAAAC,OAAA5N,UACA6N,EAAAN,EAAAnL,MAEAiL,EAAAJ,EAAAC,SAWAY,EAAAN,OAAAF,gBAAA,WACA,IAEA,OADAE,OAAAF,eAAA,GAAA,IAAA,KACA,EACA,MAAAhP,GACA,OAAA,GALA,GAaAgP,EADAQ,EACA,SAAAC,EAAAC,EAAAC,EAAAC,IACAA,GAAAF,KAAAD,GACAP,OAAAF,eAAAS,EAAAC,EAAA,CACAG,cAAA,EACAC,YAAA,EACAC,UAAA,EACAC,MAAAL,KAIA,SAAAF,EAAAC,EAAAC,EAAAC,IACAA,GAAAF,KAAAD,IACAA,EAAAC,GAAAC,IAGA,SAAAM,EAAAR,EAAAS,EAAAN,GACA,IAAA,IAAAF,KAAAQ,EACAvB,EAAAwB,eAAAvP,KAAAsP,EAAAR,IACAV,EAAAS,EAAAC,EAAAQ,EAAAR,GAAAE,GAKA,SAAAQ,EAAAjQ,GACA,GAAA,MAAAA,EACA,MAAA,IAAA+J,UAAA,iBAAA/J,EAAA,cAEA,OAAA+O,OAAA/O,GAkCA,SAAAkQ,KAEAJ,EAAAd,EAAA,CACA3K,KAAA,SAAA8L,GAEA,IAAAC,EAAA1Q,KAEA,IAAA4O,EAAA8B,GACA,MAAA,IAAArG,UAAA,kDAAAqG,GAmFA,IA9EA,IAAA9N,EAAA8M,EAAA3O,KAAAyB,UAAA,GAyEAmO,EAAA7C,KAAAC,IAAA,EAAA2C,EAAA1P,OAAA4B,EAAA5B,QAIA4P,EAAA,GACArQ,EAAA,EAAAA,EAAAoQ,EAAApQ,IACAqQ,EAAAC,KAAA,IAAAtQ,GASA,IAAAuQ,EAAAvB,SAAA,SAAA,oBAAAqB,EAAAG,KAAA,KAAA,6CAAAxB,CA9EA,WAEA,GAAAvP,gBAAA8Q,EAAA,CAiBA,IAAAE,EAAAN,EAAAnO,MACAvC,KACA4C,EAAAmB,OAAA2L,EAAA3O,KAAAyB,aAEA,OAAA6M,OAAA2B,KAAAA,EACAA,EAEAhR,KAsBA,OAAA0Q,EAAAnO,MACAkO,EACA7N,EAAAmB,OAAA2L,EAAA3O,KAAAyB,eA0DA,OA5BAkO,EAAA7O,YACA2O,EAAA3O,UAAA6O,EAAA7O,UACAiP,EAAAjP,UAAA,IAAA2O,EAEAA,EAAA3O,UAAA,MAwBAiP,KAYAV,EAAAvN,MAAA,CAAA6I,QAhOA,SAAAuD,GACA,MAAA,mBAAAC,EAAAnO,KAAAkO,MAkOA,IAGAa,EAEAmB,EACAC,EANAC,EAAA9B,OAAA,KACA+B,EAAA,MAAAD,EAAA,MAAA,KAAAA,GAmBAf,EAAAhB,EAAA,CACAvJ,QAAA,SAAAwL,EAAA,GACA,IAAAzB,EAAAW,EAAAvQ,MACAD,EAAAqR,GAAApC,EAAAhP,MAAAA,KAAAsR,MAAA,IAAA1B,EACA2B,EAHA,EAIAhR,GAAA,EACAS,EAAAjB,EAAAiB,SAAA,EAGA,IAAA4N,EAAAyC,GACA,MAAA,IAAAhH,UAGA,OAAA9J,EAAAS,GACAT,KAAAR,GAIAsR,EAAAtQ,KAAAwQ,EAAAxR,EAAAQ,GAAAA,EAAAqP,MAnCAE,EAuCAV,EAAAvJ,QApCAqL,EADAD,GAAA,EAEAnB,IACAA,EAAA/O,KAAA,MAAA,SAAAyQ,EAAAC,EAAAC,GACA,iBAAAA,IAAAT,GAAA,KAGAnB,EAAA/O,KAAA,CAAA,GAAA,WAEAmQ,EAAA,iBAAAlR,MACA,QAEA8P,GAAAmB,GAAAC,KA8BA,IAAAS,EAAA9O,MAAAhB,UAAAiC,UAAA,IAAA,CAAA,EAAA,GAAAA,QAAA,EAAA,GACAsM,EAAAhB,EAAA,CACAtL,QAAA,SAAA8N,EAAA,GACA,IAAA7R,EAAAqR,GAAApC,EAAAhP,MAAAA,KAAAsR,MAAA,IAAAf,EAAAvQ,MACAgB,EAAAjB,EAAAiB,SAAA,EAEA,IAAAA,EACA,OAAA,EAGA,IAAAT,EAAA,EAOA,IANA,EAAAiC,UAAAxB,SACAT,EAnOA,SAAAsR,GACA,IAAAzR,GAAAyR,EAMA,OALAzR,GAAAA,EACAA,EAAA,EACA,IAAAA,GAAAA,IAAA,EAAA,GAAAA,KAAA,EAAA,IACAA,GAAA,EAAAA,IAAA,GAAA0N,KAAAgE,MAAAhE,KAAAiE,IAAA3R,KAEAA,EA4NA4R,CAVA,IAcAzR,EAAA,GAAAA,EAAAA,EAAAuN,KAAAC,IAAA,EAAA/M,EAAAT,GACAA,EAAAS,EAAAT,IACA,GAAAA,KAAAR,GAAAA,EAAAQ,KAAAqR,EACA,OAAArR,EAGA,OAAA,IAEAoR,GAsBA,IAUAM,EAVAC,EAAA1C,EAAA8B,MAEA,IAAA,KAAAA,MAAA,WAAAtQ,QACA,IAAA,IAAAsQ,MAAA,YAAAtQ,QACA,MAAA,QAAAsQ,MAAA,QAAA,IACA,IAAA,OAAAA,MAAA,QAAA,GAAAtQ,QACA,GAAAsQ,MAAA,MAAAtQ,QACA,EAAA,IAAAsQ,MAAA,QAAAtQ,QAGAiR,OAAA,IAAA,OAAAE,KAAA,IAAA,GAEA3C,EAAA8B,MAAA,SAAAc,EAAAC,GACA,IAAApH,EAAAjL,KACA,QAAA,IAAAoS,GAAA,IAAAC,EACA,MAAA,GAIA,GAAA,oBAAAnD,EAAAnO,KAAAqR,GACA,OAAAF,EAAAnR,KAAAf,KAAAoS,EAAAC,GAGA,IAOAC,EAAAC,EAAAC,EAAAC,EAPAC,EAAA,GACAC,GAAAP,EAAAQ,WAAA,IAAA,KACAR,EAAAS,UAAA,IAAA,KACAT,EAAAU,SAAA,IAAA,KACAV,EAAAW,OAAA,IAAA,IACAC,EAAA,EAmBA,IAhBAZ,EAAA,IAAAa,OAAAb,EAAA9L,OAAAqM,EAAA,KACA1H,GAAA,GACAgH,IAEAK,EAAA,IAAAW,OAAA,IAAAb,EAAA9L,OAAA,WAAAqM,IASAN,OAAA,IAAAA,GACA,IAAA,EAxSA,SAAAa,GACA,OAAAA,IAAA,EAwSAC,CAAAd,IACAE,EAAAH,EAAAD,KAAAlH,OAGA+H,GADAR,EAAAD,EAAAa,MAAAb,EAAA,GAAAvR,UAEA0R,EAAA7B,KAAA5F,EAAAhH,MAAA+O,EAAAT,EAAAa,SAGAnB,GAAA,EAAAM,EAAAvR,QACAuR,EAAA,GAAApG,QAAAmG,EAAA,WACA,IAAA,IAAA/R,EAAA,EAAAA,EAAAiC,UAAAxB,OAAA,EAAAT,SACA,IAAAiC,UAAAjC,KACAgS,EAAAhS,QAAA,KAKA,EAAAgS,EAAAvR,QAAAuR,EAAAa,MAAAnI,EAAAjK,QACAoO,EAAAyB,KAAAtO,MAAAmQ,EAAAH,EAAAtO,MAAA,IAEAwO,EAAAF,EAAA,GAAAvR,OACAgS,EAAAR,EACAE,EAAA1R,QAAAqR,KAIAD,EAAAI,YAAAD,EAAAa,OACAhB,EAAAI,YAUA,OAPAQ,IAAA/H,EAAAjK,QACAyR,GAAAL,EAAAiB,KAAA,KACAX,EAAA7B,KAAA,IAGA6B,EAAA7B,KAAA5F,EAAAhH,MAAA+O,IAEAN,EAAA1R,OAAAqR,EAAAK,EAAAzO,MAAA,EAAAoO,GAAAK,IAUA,IAAApB,WAAA,EAAA,GAAAtQ,SACAwO,EAAA8B,MAAA,SAAAc,EAAAC,GACA,YAAA,IAAAD,GAAA,IAAAC,EAAA,GACAH,EAAAnR,KAAAf,KAAAoS,EAAAC,KASA,IAAAiB,EAAA9D,EAAA+D,OACAC,EAAA,GAAAD,QAAA,MAAA,KAAAA,QAAA,GACAnD,EAAAZ,EAAA,CACA+D,OAAA,SAAAE,EAAAzS,GACA,OAAAsS,EAAAvS,KACAf,KACAyT,EAAA,IAAAA,EAAAzT,KAAAgB,OAAAyS,GAAA,EAAA,EAAAA,EACAzS,KAGAwS,4BCncA,aAEA9T,EAAAD,QAAA,CAEAgB,EAAA,yBACAA,EAAA,6BACAA,EAAA,6BACAA,EAAA,2BACAA,EAAA,8BAAAA,CAAAA,EAAA,4BAGAA,EAAA,wBACAA,EAAA,8BAAAA,CAAAA,EAAA,yBACAA,EAAA,2BACAA,EAAA,2BACAA,EAAA,8BAAAA,CAAAA,EAAA,4BACAA,EAAA,yVChBA,aAEA,IAAAmB,EAAAnB,EAAA,UAAAmB,aACAP,EAAAZ,EAAA,YACAuH,EAAAvH,EAAA,qBACA4E,EAAA5E,EAAA,mBACAiT,EAAA5T,EAAA6T,eAGAlO,EAAA,aAKA,SAAAmO,EAAA9D,EAAA1I,EAAAiH,EAAAwF,GACApO,EAAAqK,EAAA1I,GACA,IAAArH,EAAAC,KACA4B,EAAAb,KAAAf,MAEAmB,WAAA,WACApB,EAAA+T,OAAAhE,EAAA1I,EAAAiH,EAAAwF,IACA,GAGAxS,EAAAuS,EAAAhS,GAEAgS,EAAA/R,UAAAiS,OAAA,SAAAhE,EAAA1I,EAAAiH,EAAAwF,GACA,IAAA9T,EAAAC,KAEA,IACAA,KAAA+T,IAAA,IAAAL,EACA,MAAAR,IAIA,IAAAlT,KAAA+T,IAIA,OAHAtO,EAAA,UACAzF,KAAAyC,KAAA,SAAA,EAAA,uBACAzC,KAAAsJ,WAKAlC,EAAA/B,EAAA2O,SAAA5M,EAAA,OAAA,IAAA7D,MAIAvD,KAAAiU,UAAAjM,EAAAkM,UAAA,WACAzO,EAAA,kBACA1F,EAAAuJ,UAAA,KAEA,IACAtJ,KAAA+T,IAAAI,KAAArE,EAAA1I,GAAA,GACApH,KAAAuJ,SAAA,YAAAvJ,KAAA+T,MACA/T,KAAA+T,IAAAxK,QAAAvJ,KAAAuJ,QACAvJ,KAAA+T,IAAAK,UAAA,WACA3O,EAAA,eACA1F,EAAA0C,KAAA,SAAA,EAAA,IACA1C,EAAAuJ,UAAA,KAGA,MAAAnJ,GAKA,OAJAsF,EAAA,YAAAtF,GAEAH,KAAAyC,KAAA,SAAA,EAAA,SACAzC,KAAAsJ,UAAA,GAWA,GAPAuK,GAAAA,EAAAQ,gBAAAT,EAAAU,eACA7O,EAAA,mBAIAzF,KAAA+T,IAAAQ,iBAAA,GAEAV,GAAAA,EAAAW,QACA,IAAA,IAAAC,KAAAZ,EAAAW,QACAxU,KAAA+T,IAAAW,iBAAAD,EAAAZ,EAAAW,QAAAC,IAIAzU,KAAA+T,IAAAY,mBAAA,WACA,GAAA5U,EAAAgU,IAAA,CACA,IACAtM,EAAAD,EADA0L,EAAAnT,EAAAgU,IAGA,OADAtO,EAAA,aAAAyN,EAAA5I,YACA4I,EAAA5I,YACA,KAAA,EAGA,IACA9C,EAAA0L,EAAA1L,OACAC,EAAAyL,EAAA0B,aACA,MAAAzU,IAGAsF,EAAA,SAAA+B,GAEA,OAAAA,IACAA,EAAA,KAIA,MAAAA,GAAAC,GAAA,EAAAA,EAAAzG,SACAyE,EAAA,SACA1F,EAAA0C,KAAA,QAAA+E,EAAAC,IAEA,MACA,KAAA,EACAD,EAAA0L,EAAA1L,OACA/B,EAAA,SAAA+B,GAEA,OAAAA,IACAA,EAAA,KAIA,QAAAA,GAAA,QAAAA,IACAA,EAAA,GAGA/B,EAAA,SAAA+B,EAAA0L,EAAA0B,cACA7U,EAAA0C,KAAA,SAAA+E,EAAA0L,EAAA0B,cACA7U,EAAAuJ,UAAA,MAMA,IACAvJ,EAAAgU,IAAA7O,KAAAmJ,GACA,MAAAlO,GACAJ,EAAA0C,KAAA,SAAA,EAAA,IACA1C,EAAAuJ,UAAA,KAIAsK,EAAA/R,UAAAyH,SAAA,SAAAuL,GAEA,GADApP,EAAA,WACAzF,KAAA+T,IAAA,CAYA,GATA/T,KAAA8B,qBACAkG,EAAA8M,UAAA9U,KAAAiU,WAGAjU,KAAA+T,IAAAY,mBAAA,aACA3U,KAAA+T,IAAAK,YACApU,KAAA+T,IAAAK,UAAA,MAGAS,EACA,IACA7U,KAAA+T,IAAAc,QACA,MAAA3B,IAIAlT,KAAAiU,UAAAjU,KAAA+T,IAAA,OAGAH,EAAA/R,UAAAuD,MAAA,WACAK,EAAA,SACAzF,KAAAsJ,UAAA,IAGAsK,EAAAnL,UAAAiL,EAGA,IAAAqB,EAAA,CAAA,UAAAhR,OAAA,UAAAgN,KAAA,MACA6C,EAAAnL,SAAAsM,KAAAjV,IACA2F,EAAA,6BAQAmO,EAAAnL,UAAA,IAPAiL,EAAA,WACA,IACA,OAAA,IAAA5T,EAAAiV,GAAA,qBACA,MAAA5U,GACA,OAAA,SAMA,IAAA6U,GAAA,EACA,IACAA,EAAA,oBAAA,IAAAtB,EACA,MAAA/M,IAIAiN,EAAAU,aAAAU,EAEAtV,EAAAD,QAAAmU,sQChMAlU,EAAAD,QAAAK,EAAAmV,6LCAA,aAEA,IAAAC,EAAApV,EAAAqV,WAAArV,EAAAsV,aAEA1V,EAAAD,QADAyV,EACA,SAAA9N,GACA,OAAA,IAAA8N,EAAA9N,SAGAiO,0JCRA,aAEA,IAAAhU,EAAAZ,EAAA,YACA6U,EAAA7U,EAAA,oBACA8U,EAAA9U,EAAA,0BACA+U,EAAA/U,EAAA,qBACAgV,EAAAhV,EAAA,eAGA,SAAAiV,EAAA5O,GACA,IAAA4O,EAAAjN,UACA,MAAA,IAAA7H,MAAA,mCAGA0U,EAAAvU,KAAAf,KAAA8G,EAAA,eAAAyO,EAAAC,GAGAnU,EAAAqU,EAAAJ,GAEAI,EAAAjN,QAAA,WACA,QAAAgN,GAGAC,EAAA1P,cAAA,cACA0P,EAAA1H,WAAA,EAEAtO,EAAAD,QAAAiW,kIC1BA,aAEA,IAAArU,EAAAZ,EAAA,YACAkV,EAAAlV,EAAA,uBACAoH,EAAApH,EAAA,sBACA6U,EAAA7U,EAAA,oBAGA,SAAAmV,EAAA9O,GACA,IAAA6O,EAAAlN,QACA,MAAA,IAAA7H,MAAA,mCAEA0U,EAAAvU,KAAAf,KAAA8G,EAAA,YAAA6O,EAAA9N,GAGAxG,EAAAuU,EAAAN,GAEAM,EAAAnN,QAAA,SAAAf,GACA,OAAAiO,EAAAlN,SAAAf,EAAAwB,YAGA0M,EAAA5P,cAAA,WACA4P,EAAA5H,WAAA,EAEAtO,EAAAD,QAAAmW,+GCxBA,aAUA,IAAAvU,EAAAZ,EAAA,YACAmB,EAAAnB,EAAA,UAAAmB,aACAiF,EAAApG,EAAA,cACA4E,EAAA5E,EAAA,gBACA6D,EAAA7D,EAAA,mBACA6E,EAAA7E,EAAA,kBACAsJ,EAAAtJ,EAAA,mBAGAgF,EAAA,aAKA,SAAAwC,EAAAzD,EAAAsC,EAAAC,GACA,IAAAkB,EAAAQ,UACA,MAAA,IAAA7H,MAAA,mCAEAgB,EAAAb,KAAAf,MAEA,IAAAD,EAAAC,KACAA,KAAAwG,OAAAnB,EAAAyG,UAAA/E,GACA/G,KAAA+G,QAAAA,EACA/G,KAAA8G,SAAAA,EACA9G,KAAAwE,UAAAA,EACAxE,KAAA4G,SAAAmD,EAAAkB,OAAA,GAEA,IAAA4K,EAAAxQ,EAAA+D,QAAArC,EAAA,gBAAA,IAAA/G,KAAA4G,SACAnB,EAAAjB,EAAAsC,EAAA+O,GAEA7V,KAAA8V,UAAAxR,EAAAyR,aAAAF,EAAA,SAAA3V,GACAuF,EAAA,gBACA1F,EAAA0C,KAAA,QAAA,KAAA,6BAAAvC,EAAA,KACAH,EAAAqF,UAGApF,KAAAgW,kBAAAhW,KAAAiW,SAAAtR,KAAA3E,MACAsF,EAAAe,YAAA,UAAArG,KAAAgW,mBAGA3U,EAAA4G,EAAArG,GAEAqG,EAAApG,UAAAuD,MAAA,WAGA,GAFAK,EAAA,SACAzF,KAAA8B,qBACA9B,KAAA8V,UAAA,CACAxQ,EAAA4Q,YAAA,UAAAlW,KAAAgW,mBACA,IAGAhW,KAAA6E,YAAA,KACA,MAAAqO,IAGAlT,KAAA8V,UAAAK,UACAnW,KAAA8V,UAAA,KACA9V,KAAAgW,kBAAAhW,KAAA8V,UAAA,OAIA7N,EAAApG,UAAAoU,SAAA,SAAA9V,GAEA,GADAsF,EAAA,UAAAtF,EAAAkE,MACAgB,EAAA2B,cAAA7G,EAAAqG,OAAAxG,KAAAwG,QAAA,CAKA,IAAAC,EACA,IACAA,EAAA3B,KAAA4B,MAAAvG,EAAAkE,MACA,MAAAsC,GAEA,YADAlB,EAAA,WAAAtF,EAAAkE,MAIA,GAAAoC,EAAAG,WAAA5G,KAAA4G,SAKA,OAAAH,EAAA1E,MACA,IAAA,IACA/B,KAAA8V,UAAAM,SAEApW,KAAA6E,YAAA,IAAAC,KAAAC,UAAA,CACA8B,EACA7G,KAAAwE,UACAxE,KAAA8G,SACA9G,KAAA+G,WAEA,MACA,IAAA,IACA/G,KAAAyC,KAAA,UAAAgE,EAAApC,MACA,MACA,IAAA,IACA,IAAAgS,EACA,IACAA,EAAAvR,KAAA4B,MAAAD,EAAApC,MACA,MAAAsC,GAEA,YADAlB,EAAA,WAAAgB,EAAApC,MAGArE,KAAAyC,KAAA,QAAA4T,EAAA,GAAAA,EAAA,IACArW,KAAAoF,aA3BAK,EAAA,uBAAAgB,EAAAG,SAAA5G,KAAA4G,eAbAnB,EAAA,kBAAAtF,EAAAqG,OAAAxG,KAAAwG,SA6CAyB,EAAApG,UAAAgD,YAAA,SAAA9C,EAAAsC,GACAoB,EAAA,cAAA1D,EAAAsC,GACArE,KAAA8V,UAAAQ,KAAAxR,KAAAC,UAAA,CACA6B,SAAA5G,KAAA4G,SACA7E,KAAAA,EACAsC,KAAAA,GAAA,KACArE,KAAAwG,SAGAyB,EAAApG,UAAAqD,KAAA,SAAAqR,GACA9Q,EAAA,OAAA8Q,GACAvW,KAAA6E,YAAA,IAAA0R,IAGAtO,EAAAQ,QAAA,WACA,OAAAnE,EAAAkS,eAGAvO,EAAAjC,cAAA,SACAiC,EAAA+F,WAAA,EAEAtO,EAAAD,QAAAwI,2LC3IA,aAUA,IAAA5G,EAAAZ,EAAA,YACAgW,EAAAhW,EAAA,yBACAiW,EAAAjW,EAAA,oBACAkW,EAAAlW,EAAA,kBAGA,SAAAmW,EAAA9P,GACA,IAAA8P,EAAAnO,UACA,MAAA,IAAA7H,MAAA,mCAEA6V,EAAA1V,KAAAf,KAAA8G,EAAA,SAAA6P,EAAAD,GAGArV,EAAAuV,EAAAH,GAEAG,EAAAnO,QAAA,WACA,QAAA3I,EAAAyI,UAGAqO,EAAA5Q,cAAA,gBACA4Q,EAAA5I,WAAA,EACA4I,EAAAjJ,UAAA,EAEAjO,EAAAD,QAAAmX,4OCjCA,aAEA,IAAAvV,EAAAZ,EAAA,YACA4E,EAAA5E,EAAA,mBACAgW,EAAAhW,EAAA,qBAGAgF,EAAA,aAmCA,SAAA6P,EAAAxO,EAAA+P,EAAAC,EAAAzP,GACAoP,EAAA1V,KAAAf,KAAA8G,EAAA+P,EA/BA,SAAAxP,GACA,OAAA,SAAAD,EAAAiH,EAAA0I,GACAtR,EAAA,qBAAA2B,EAAAiH,GACA,IAAA2I,EAAA,GACA,iBAAA3I,IACA2I,EAAAxC,QAAA,CAAAyC,eAAA,eAEA,IAAAC,EAAA7R,EAAA+D,QAAAhC,EAAA,aACAG,EAAA,IAAAF,EAAA,OAAA6P,EAAA7I,EAAA2I,GAUA,OATAzP,EAAAtF,KAAA,SAAA,SAAAuF,GAIA,GAHA/B,EAAA,SAAA+B,GACAD,EAAA,KAEA,MAAAC,GAAA,MAAAA,EACA,OAAAuP,EAAA,IAAAnW,MAAA,eAAA4G,IAEAuP,MAEA,WACAtR,EAAA,SACA8B,EAAAnC,QACAmC,EAAA,KAEA,IAAA4P,EAAA,IAAAvW,MAAA,WACAuW,EAAAtW,KAAA,IACAkW,EAAAI,KAMAC,CAAA/P,GAAAyP,EAAAzP,GAGAhG,EAAAiU,EAAAmB,GAEA/W,EAAAD,QAAA6V,mGChDA,aAEA,IAAAjU,EAAAZ,EAAA,YACAmB,EAAAnB,EAAA,UAAAmB,aAGA6D,EAAA,aAKA,SAAA4R,EAAAjQ,EAAAkQ,GACA7R,EAAA2B,GACAxF,EAAAb,KAAAf,MACAA,KAAAuX,WAAA,GACAvX,KAAAsX,OAAAA,EACAtX,KAAAoH,IAAAA,EAGA/F,EAAAgW,EAAAzV,GAEAyV,EAAAxV,UAAAqD,KAAA,SAAAqR,GACA9Q,EAAA,OAAA8Q,GACAvW,KAAAuX,WAAA1G,KAAA0F,GACAvW,KAAAwX,UACAxX,KAAAyX,gBAYAJ,EAAAxV,UAAA6V,iBAAA,WACAjS,EAAA,oBACA,IACAkS,EADA5X,EAAAC,KAEAA,KAAAwX,SAAA,WACA/R,EAAA,YACA1F,EAAAyX,SAAA,KACAhO,aAAAmO,IAEAA,EAAAxW,WAAA,WACAsE,EAAA,WACA1F,EAAAyX,SAAA,KACAzX,EAAA0X,gBACA,KAGAJ,EAAAxV,UAAA4V,aAAA,WACAhS,EAAA,eAAAzF,KAAAuX,WAAAvW,QACA,IAAAjB,EAAAC,KACA,GAAA,EAAAA,KAAAuX,WAAAvW,OAAA,CACA,IAAAqN,EAAA,IAAArO,KAAAuX,WAAAxG,KAAA,KAAA,IACA/Q,KAAAwX,SAAAxX,KAAAsX,OAAAtX,KAAAoH,IAAAiH,EAAA,SAAA8I,GACApX,EAAAyX,SAAA,KACAL,GACA1R,EAAA,QAAA0R,GACApX,EAAA0C,KAAA,QAAA0U,EAAAtW,MAAA,KAAA,kBAAAsW,GACApX,EAAAqF,SAEArF,EAAA2X,qBAGA1X,KAAAuX,WAAA,KAIAF,EAAAxV,UAAAyH,SAAA,WACA7D,EAAA,YACAzF,KAAA8B,sBAGAuV,EAAAxV,UAAAuD,MAAA,WACAK,EAAA,SACAzF,KAAAsJ,WACAtJ,KAAAwX,WACAxX,KAAAwX,WACAxX,KAAAwX,SAAA,OAIA9X,EAAAD,QAAA4X,2FCtFA,aAEA,IAAAhW,EAAAZ,EAAA,YACAwH,EAAAxH,EAAA,aACAyG,EAAAzG,EAAA,sBAGAf,EAAAD,QAAA,SAAA+E,GAEA,SAAAoT,EAAA9Q,EAAAC,GACAkB,EAAAlH,KAAAf,KAAAwE,EAAAwB,cAAAc,EAAAC,GAqBA,OAlBA1F,EAAAuW,EAAA3P,GAEA2P,EAAAnP,QAAA,SAAArB,EAAAM,GACA,IAAA5H,EAAAyI,SACA,OAAA,EAGA,IAAAsP,EAAA3Q,EAAAiG,OAAA,GAAAzF,GAEA,OADAmQ,EAAA3O,YAAA,EACA1E,EAAAiE,QAAAoP,IAAA5P,EAAAQ,WAGAmP,EAAA5R,cAAA,UAAAxB,EAAAwB,cACA4R,EAAAjK,UAAA,EACAiK,EAAA5J,WAAA/F,EAAA+F,WAAAxJ,EAAAwJ,WAAA,EAEA4J,EAAA7R,gBAAAvB,EAEAoT,+MC/BA,aAEA,IAAAvW,EAAAZ,EAAA,YACAmB,EAAAnB,EAAA,UAAAmB,aAGA6D,EAAA,aAKA,SAAAqS,EAAAhB,EAAAiB,EAAA1Q,GACA5B,EAAAsS,GACAnW,EAAAb,KAAAf,MACAA,KAAA8W,SAAAA,EACA9W,KAAA+X,WAAAA,EACA/X,KAAAqH,WAAAA,EACArH,KAAAgY,oBAGA3W,EAAAyW,EAAAlW,GAEAkW,EAAAjW,UAAAmW,kBAAA,WACAvS,EAAA,qBACA,IAAA1F,EAAAC,KACAiY,EAAAjY,KAAAiY,KAAA,IAAAjY,KAAA8W,SAAA9W,KAAA+X,WAAA/X,KAAAqH,YAEA4Q,EAAA7V,GAAA,UAAA,SAAAiG,GACA5C,EAAA,UAAA4C,GACAtI,EAAA0C,KAAA,UAAA4F,KAGA4P,EAAAhW,KAAA,QAAA,SAAApB,EAAAa,GACA+D,EAAA,QAAA5E,EAAAa,EAAA3B,EAAAmY,eACAnY,EAAAkY,KAAAA,EAAA,KAEAlY,EAAAmY,gBACA,YAAAxW,EACA3B,EAAAiY,qBAEAjY,EAAA0C,KAAA,QAAA5B,GAAA,KAAAa,GACA3B,EAAA+B,0BAMAgW,EAAAjW,UAAAgT,MAAA,WACApP,EAAA,SACAzF,KAAA8B,qBACA9B,KAAAkY,eAAA,EACAlY,KAAAiY,MACAjY,KAAAiY,KAAApD,SAIAnV,EAAAD,QAAAqY,kECxDA,aAEA,IAAAzW,EAAAZ,EAAA,YACA4E,EAAA5E,EAAA,mBACA4W,EAAA5W,EAAA,qBACAqX,EAAArX,EAAA,aAGAgF,EAAA,aAKA,SAAAgR,EAAA3P,EAAA+P,EAAAsB,EAAArB,EAAAzP,GACA,IAAA+Q,EAAA/S,EAAA+D,QAAAtC,EAAA+P,GACApR,EAAA2S,GACA,IAAArY,EAAAC,KACAqX,EAAAtW,KAAAf,KAAA8G,EAAAqR,GAEAnY,KAAAiY,KAAA,IAAAH,EAAAhB,EAAAsB,EAAA/Q,GACArH,KAAAiY,KAAA7V,GAAA,UAAA,SAAAiG,GACA5C,EAAA,eAAA4C,GACAtI,EAAA0C,KAAA,UAAA4F,KAEArI,KAAAiY,KAAAhW,KAAA,QAAA,SAAApB,EAAAa,GACA+D,EAAA,aAAA5E,EAAAa,GACA3B,EAAAkY,KAAA,KACAlY,EAAA0C,KAAA,QAAA5B,EAAAa,GACA3B,EAAAqF,UAIA/D,EAAAoV,EAAAY,GAEAZ,EAAA5U,UAAAuD,MAAA,WACAiS,EAAAxV,UAAAuD,MAAArE,KAAAf,MACAyF,EAAA,SACAzF,KAAA8B,qBACA9B,KAAAiY,OACAjY,KAAAiY,KAAApD,QACA7U,KAAAiY,KAAA,OAIAvY,EAAAD,QAAAgX,kHC5CA,aAEA,IAAApV,EAAAZ,EAAA,YACAmB,EAAAnB,EAAA,UAAAmB,aACA6T,EAAAhV,EAAA,eAGAgF,EAAA,aAKA,SAAA8P,EAAAnO,GACA3B,EAAA2B,GACAxF,EAAAb,KAAAf,MAEA,IAAAD,EAAAC,KACAqY,EAAArY,KAAAqY,GAAA,IAAA5C,EAAArO,GACAiR,EAAA5J,UAAA,SAAAtO,GACAsF,EAAA,UAAAtF,EAAAkE,MACAtE,EAAA0C,KAAA,UAAA6V,UAAAnY,EAAAkE,QAEAgU,EAAA1J,QAAA,SAAAxO,GACAsF,EAAA,QAAA4S,EAAA/N,WAAAnK,GAGA,IAAAuB,EAAA,IAAA2W,EAAA/N,WAAA,UAAA,YACAvK,EAAAuJ,WACAvJ,EAAAoF,OAAAzD,IAIAL,EAAAkU,EAAA3T,GAEA2T,EAAA1T,UAAAgT,MAAA,WACApP,EAAA,SACAzF,KAAAsJ,WACAtJ,KAAAmF,OAAA,SAGAoQ,EAAA1T,UAAAyH,SAAA,WACA7D,EAAA,WACA,IAAA4S,EAAArY,KAAAqY,GACAA,IACAA,EAAA5J,UAAA4J,EAAA1J,QAAA,KACA0J,EAAAjT,QACApF,KAAAqY,GAAA,OAIA9C,EAAA1T,UAAAsD,OAAA,SAAAzD,GACA+D,EAAA,QAAA/D,GACA,IAAA3B,EAAAC,KAIAmB,WAAA,WACApB,EAAA0C,KAAA,QAAA,KAAAf,GACA3B,EAAA+B,sBACA,MAGApC,EAAAD,QAAA8V,4GC9DA,aAEA,IAAAlU,EAAAZ,EAAA,YACA6D,EAAA7D,EAAA,sBACA4E,EAAA5E,EAAA,mBACAmB,EAAAnB,EAAA,UAAAmB,aACAmI,EAAAtJ,EAAA,sBAGAgF,EAAA,aAKA,SAAAkQ,EAAAvO,GACA3B,EAAA2B,GACAxF,EAAAb,KAAAf,MACA,IAAAD,EAAAC,KACAsE,EAAAiU,yBAEAvY,KAAAwY,GAAA,IAAAzO,EAAAkB,OAAA,GACA7D,EAAA/B,EAAA2O,SAAA5M,EAAA,KAAAqR,mBAAAnU,EAAAoU,QAAA,IAAA1Y,KAAAwY,KAEA/S,EAAA,iBAAAkQ,EAAAgD,iBACA,IAAAC,EAAAjD,EAAAgD,gBACArU,EAAAuU,eAAAvU,EAAAyR,aAEAjW,EAAAwE,EAAAoU,SAAA1Y,KAAAwY,IAAA,CACA/E,MAAA,WACAhO,EAAA,SACA1F,EAAA+V,UAAAM,UAEAG,QAAA,SAAAlS,GACAoB,EAAA,UAAApB,GACAtE,EAAA0C,KAAA,UAAA4B,IAEAyU,KAAA,WACArT,EAAA,QACA1F,EAAAuJ,WACAvJ,EAAAoF,OAAA,aAGAnF,KAAA8V,UAAA8C,EAAAxR,EAAA,WACA3B,EAAA,YACA1F,EAAAuJ,WACAvJ,EAAAoF,OAAA,eAIA9D,EAAAsU,EAAA/T,GAEA+T,EAAA9T,UAAAgT,MAAA,WACApP,EAAA,SACAzF,KAAAsJ,WACAtJ,KAAAmF,OAAA,SAGAwQ,EAAA9T,UAAAyH,SAAA,WACA7D,EAAA,YACAzF,KAAA8V,YACA9V,KAAA8V,UAAAK,UACAnW,KAAA8V,UAAA,aAEAhW,EAAAwE,EAAAoU,SAAA1Y,KAAAwY,KAGA7C,EAAA9T,UAAAsD,OAAA,SAAAzD,GACA+D,EAAA,SAAA/D,GACA1B,KAAAyC,KAAA,QAAA,KAAAf,GACA1B,KAAA8B,sBAGA6T,EAAAgD,iBAAA,EAGA,IAAA5D,EAAA,CAAA,UAAAhR,OAAA,UAAAgN,KAAA,KACA,GAAAgE,KAAAjV,EACA,IACA6V,EAAAgD,kBAAA,IAAA7Y,EAAAiV,GAAA,YACA,MAAA7B,IAKAyC,EAAAlN,QAAAkN,EAAAgD,iBAAArU,EAAAkS,cAEA9W,EAAAD,QAAAkW,+RCtFA,aAEA,IAAA3N,EAAAvH,EAAA,sBACAsJ,EAAAtJ,EAAA,sBACAwJ,EAAAxJ,EAAA,uBACA4E,EAAA5E,EAAA,mBACAY,EAAAZ,EAAA,YACAmB,EAAAnB,EAAA,UAAAmB,aAGA6D,EAAA,aAKA,SAAAiR,EAAAtP,GACA3B,EAAA2B,GACA,IAAArH,EAAAC,KACA4B,EAAAb,KAAAf,MAEAgI,EAAAuQ,yBAEAvY,KAAAwY,GAAA,IAAAzO,EAAAkB,OAAA,GACA,IAAA8N,EAAA1T,EAAA2O,SAAA5M,EAAA,KAAA4R,mBAAAhR,EAAA0Q,QAAA,IAAA1Y,KAAAwY,KAEA1Y,EAAAkI,EAAA0Q,SAAA1Y,KAAAwY,IAAAxY,KAAAiZ,UAAAtU,KAAA3E,MACAA,KAAAkZ,cAAAH,GAGA/Y,KAAAmZ,UAAAhY,WAAA,WACAsE,EAAA,WACA1F,EAAAqZ,OAAA,IAAAxY,MAAA,8CACA8V,EAAAnN,SAGAlI,EAAAqV,EAAA9U,GAEA8U,EAAA7U,UAAAgT,MAAA,WAEA,GADApP,EAAA,SACA3F,EAAAkI,EAAA0Q,SAAA1Y,KAAAwY,IAAA,CACA,IAAArB,EAAA,IAAAvW,MAAA,2BACAuW,EAAAtW,KAAA,IACAb,KAAAoZ,OAAAjC,KAIAT,EAAAnN,QAAA,KACAmN,EAAA2C,mBAAA,IAEA3C,EAAA7U,UAAAoX,UAAA,SAAA5U,GACAoB,EAAA,YAAApB,GACArE,KAAAsJ,WAEAtJ,KAAAsZ,WAIAjV,IACAoB,EAAA,UAAApB,GACArE,KAAAyC,KAAA,UAAA4B,IAEArE,KAAAyC,KAAA,QAAA,KAAA,WACAzC,KAAA8B,uBAGA4U,EAAA7U,UAAAuX,OAAA,SAAAjC,GACA1R,EAAA,SAAA0R,GACAnX,KAAAsJ,WACAtJ,KAAAsZ,UAAA,EACAtZ,KAAAyC,KAAA,QAAA0U,EAAAtW,KAAAsW,EAAAZ,SACAvW,KAAA8B,sBAGA4U,EAAA7U,UAAAyH,SAAA,WAOA,GANA7D,EAAA,YACA+D,aAAAxJ,KAAAmZ,WACAnZ,KAAAuZ,UACAvZ,KAAAuZ,QAAAC,WAAAC,YAAAzZ,KAAAuZ,SACAvZ,KAAAuZ,QAAA,MAEAvZ,KAAA0Z,OAAA,CACA,IAAAA,EAAA1Z,KAAA0Z,OAGAA,EAAAF,WAAAC,YAAAC,GACAA,EAAA/E,mBAAA+E,EAAA/K,QACA+K,EAAAC,OAAAD,EAAAE,QAAA,KACA5Z,KAAA0Z,OAAA,YAEA5Z,EAAAkI,EAAA0Q,SAAA1Y,KAAAwY,KAGA9B,EAAA7U,UAAAgY,aAAA,WACApU,EAAA,gBACA,IAAA1F,EAAAC,KACAA,KAAA8Z,aAIA9Z,KAAA8Z,WAAA3Y,WAAA,WACApB,EAAAga,YACAha,EAAAqZ,OAAA,IAAAxY,MAAA,8CAEA8V,EAAA2C,sBAGA3C,EAAA7U,UAAAqX,cAAA,SAAA9R,GACA3B,EAAA,gBAAA2B,GACA,IAEAmS,EAFAxZ,EAAAC,KACA0Z,EAAA1Z,KAAA0Z,OAAA5Z,EAAAyI,SAAAyR,cAAA,UA0CA,GAvCAN,EAAAlB,GAAA,IAAAzO,EAAAkB,OAAA,GACAyO,EAAAO,IAAA7S,EACAsS,EAAA3X,KAAA,kBACA2X,EAAAQ,QAAA,QACAR,EAAA/K,QAAA3O,KAAA6Z,aAAAlV,KAAA3E,MACA0Z,EAAAC,OAAA,WACAlU,EAAA,UACA1F,EAAAqZ,OAAA,IAAAxY,MAAA,6CAKA8Y,EAAA/E,mBAAA,WAEA,GADAlP,EAAA,qBAAAiU,EAAApP,YACA,gBAAA+I,KAAAqG,EAAApP,YAAA,CACA,GAAAoP,GAAAA,EAAAS,SAAAT,EAAAE,QAAA,CACA7Z,EAAAga,YAAA,EACA,IAEAL,EAAAE,UACA,MAAA1G,KAIAwG,GACA3Z,EAAAqZ,OAAA,IAAAxY,MAAA,+DAcA,IAAA8Y,EAAAU,OAAAta,EAAAyI,SAAAlC,YAIA,GAAA4D,EAAAoQ,WAWAd,EAAAvZ,KAAAuZ,QAAAzZ,EAAAyI,SAAAyR,cAAA,WACAvS,KAAA,wCAAAiS,EAAAlB,GAAA,oCACAkB,EAAAU,MAAAb,EAAAa,OAAA,MAbA,CAEA,IACAV,EAAAS,QAAAT,EAAAlB,GACAkB,EAAAvV,MAAA,UACA,MAAA+O,IAGAwG,EAAAU,OAAA,OAQA,IAAAV,EAAAU,QACAV,EAAAU,OAAA,GAGA,IAAAE,EAAAxa,EAAAyI,SAAAgS,qBAAA,QAAA,GACAD,EAAAE,aAAAd,EAAAY,EAAAG,YACAlB,GACAe,EAAAE,aAAAjB,EAAAe,EAAAG,aAIA/a,EAAAD,QAAAiX,+RCtLA,aAEA,IAAArV,EAAAZ,EAAA,YACAmB,EAAAnB,EAAA,UAAAmB,aAGA6D,EAAA,aAKA,SAAAiV,EAAAtT,EAAAC,GACA5B,EAAA2B,GACAxF,EAAAb,KAAAf,MACA,IAAAD,EAAAC,KAEAA,KAAA2a,eAAA,EAEA3a,KAAAuH,GAAA,IAAAF,EAAA,OAAAD,EAAA,MACApH,KAAAuH,GAAAnF,GAAA,QAAApC,KAAA4a,cAAAjW,KAAA3E,OACAA,KAAAuH,GAAAtF,KAAA,SAAA,SAAAuF,EAAAC,GACAhC,EAAA,SAAA+B,EAAAC,GACA1H,EAAA6a,cAAApT,EAAAC,GACA1H,EAAAwH,GAAA,KACA,IAAA7F,EAAA,MAAA8F,EAAA,UAAA,YACA/B,EAAA,QAAA/D,GACA3B,EAAA0C,KAAA,QAAA,KAAAf,GACA3B,EAAAuJ,aAIAjI,EAAAqZ,EAAA9Y,GAEA8Y,EAAA7Y,UAAA+Y,cAAA,SAAApT,EAAAC,GAEA,GADAhC,EAAA,gBAAA+B,GACA,MAAAA,GAAAC,EAIA,IAAA,IAAAzD,GAAA,GAAAhE,KAAA2a,gBAAA3W,EAAA,EAAA,CACA,IAAA6W,EAAApT,EAAAxD,MAAAjE,KAAA2a,gBAEA,IAAA,KADA3W,EAAA6W,EAAA/W,QAAA,OAEA,MAEA,IAAAuE,EAAAwS,EAAA5W,MAAA,EAAAD,GACAqE,IACA5C,EAAA,UAAA4C,GACArI,KAAAyC,KAAA,UAAA4F,MAKAqS,EAAA7Y,UAAAyH,SAAA,WACA7D,EAAA,YACAzF,KAAA8B,sBAGA4Y,EAAA7Y,UAAAgT,MAAA,WACApP,EAAA,SACAzF,KAAAuH,KACAvH,KAAAuH,GAAAnC,QACAK,EAAA,SACAzF,KAAAyC,KAAA,QAAA,KAAA,QACAzC,KAAAuH,GAAA,MAEAvH,KAAAsJ,YAGA5J,EAAAD,QAAAib,2FCrEA,aAEA,IASAI,EAAAC,EATAhR,EAAAtJ,EAAA,sBACA4E,EAAA5E,EAAA,mBAGAgF,EAAA,aAmCA/F,EAAAD,QAAA,SAAA2H,EAAAiH,EAAA0I,GACAtR,EAAA2B,EAAAiH,GACAyM,IAjBArV,EAAA,eACAqV,EAAAhb,EAAAyI,SAAAyR,cAAA,SACAgB,MAAAC,QAAA,OACAH,EAAAE,MAAAE,SAAA,WACAJ,EAAAhL,OAAA,OACAgL,EAAAK,QAAA,oCACAL,EAAAM,cAAA,SAEAL,EAAAjb,EAAAyI,SAAAyR,cAAA,aACAnK,KAAA,IACAiL,EAAAO,YAAAN,GAEAjb,EAAAyI,SAAAC,KAAA6S,YAAAP,IAQA,IAAAtC,EAAA,IAAAzO,EAAAkB,OAAA,GACA6P,EAAApK,OAAA8H,EACAsC,EAAAQ,OAAAjW,EAAA2O,SAAA3O,EAAA+D,QAAAhC,EAAA,eAAA,KAAAoR,GAEA,IAAA+C,EArCA,SAAA/C,GACA/S,EAAA,eAAA+S,GACA,IAEA,OAAA1Y,EAAAyI,SAAAyR,cAAA,iBAAAxB,EAAA,MACA,MAAAtF,GACA,IAAAqI,EAAAzb,EAAAyI,SAAAyR,cAAA,UAEA,OADAuB,EAAA1L,KAAA2I,EACA+C,GA6BAxF,CAAAyC,GACA+C,EAAA/C,GAAAA,EACA+C,EAAAP,MAAAC,QAAA,OACAH,EAAAO,YAAAE,GAEA,IACAR,EAAA5K,MAAA9B,EACA,MAAAlO,IAGA2a,EAAAU,SAEA,SAAAC,EAAAtE,GACA1R,EAAA,YAAA+S,EAAArB,GACAoE,EAAA5M,UAGA4M,EAAA5G,mBAAA4G,EAAA5M,QAAA4M,EAAA5B,OAAA,KAGAxY,WAAA,WACAsE,EAAA,cAAA+S,GACA+C,EAAA/B,WAAAC,YAAA8B,GACAA,EAAA,MACA,KACAR,EAAA5K,MAAA,GAGA4G,EAAAI,IAgBA,OAdAoE,EAAA5M,QAAA,WACAlJ,EAAA,UAAA+S,GACAiD,KAEAF,EAAA5B,OAAA,WACAlU,EAAA,SAAA+S,GACAiD,KAEAF,EAAA5G,mBAAA,SAAAxU,GACAsF,EAAA,qBAAA+S,EAAA+C,EAAAjR,WAAAnK,GACA,aAAAob,EAAAjR,YACAmR,KAGA,WACAhW,EAAA,UAAA+S,GACAiD,EAAA,IAAA7a,MAAA,0PChGA,aAEA,IAAAgB,EAAAnB,EAAA,UAAAmB,aACAP,EAAAZ,EAAA,YACA6E,EAAA7E,EAAA,qBACAwJ,EAAAxJ,EAAA,uBACA4E,EAAA5E,EAAA,mBAGAgF,EAAA,aASA,SAAAiW,EAAA5L,EAAA1I,EAAAiH,GACA5I,EAAAqK,EAAA1I,GACA,IAAArH,EAAAC,KACA4B,EAAAb,KAAAf,MAEAmB,WAAA,WACApB,EAAA+T,OAAAhE,EAAA1I,EAAAiH,IACA,GAGAhN,EAAAqa,EAAA9Z,GAEA8Z,EAAA7Z,UAAAiS,OAAA,SAAAhE,EAAA1I,EAAAiH,GACA5I,EAAA,UACA,IAAA1F,EAAAC,KACA2b,EAAA,IAAA7b,EAAA8b,eAEAxU,EAAA/B,EAAA2O,SAAA5M,EAAA,OAAA,IAAA7D,MAEAoY,EAAAhN,QAAA,WACAlJ,EAAA,WACA1F,EAAA8b,UAEAF,EAAAvH,UAAA,WACA3O,EAAA,aACA1F,EAAA8b,UAEAF,EAAAG,WAAA,WACArW,EAAA,WAAAkW,EAAA/G,cACA7U,EAAA0C,KAAA,QAAA,IAAAkZ,EAAA/G,eAEA+G,EAAAhC,OAAA,WACAlU,EAAA,QACA1F,EAAA0C,KAAA,SAAA,IAAAkZ,EAAA/G,cACA7U,EAAAuJ,UAAA,IAEAtJ,KAAA2b,IAAAA,EACA3b,KAAAiU,UAAA3O,EAAA4O,UAAA,WACAnU,EAAAuJ,UAAA,KAEA,IAEAtJ,KAAA2b,IAAAxH,KAAArE,EAAA1I,GACApH,KAAAuJ,UACAvJ,KAAA2b,IAAApS,QAAAvJ,KAAAuJ,SAEAvJ,KAAA2b,IAAAzW,KAAAmJ,GACA,MAAA6E,GACAlT,KAAA6b,WAIAH,EAAA7Z,UAAAga,OAAA,WACA7b,KAAAyC,KAAA,SAAA,EAAA,IACAzC,KAAAsJ,UAAA,IAGAoS,EAAA7Z,UAAAyH,SAAA,SAAAuL,GAEA,GADApP,EAAA,UAAAoP,GACA7U,KAAA2b,IAAA,CAOA,GAJA3b,KAAA8B,qBACAwD,EAAAwP,UAAA9U,KAAAiU,WAEAjU,KAAA2b,IAAAvH,UAAApU,KAAA2b,IAAAhN,QAAA3O,KAAA2b,IAAAG,WAAA9b,KAAA2b,IAAAhC,OAAA,KACA9E,EACA,IACA7U,KAAA2b,IAAA9G,QACA,MAAA3B,IAIAlT,KAAAiU,UAAAjU,KAAA2b,IAAA,OAGAD,EAAA7Z,UAAAuD,MAAA,WACAK,EAAA,SACAzF,KAAAsJ,UAAA,IAIAoS,EAAAjT,WAAA3I,EAAA8b,iBAAA3R,EAAAqC,aAEA5M,EAAAD,QAAAic,sQCtGA,aAEA,IAAAra,EAAAZ,EAAA,YACAsb,EAAAtb,EAAA,iBAGA,SAAA+U,EAAA1F,EAAA1I,EAAAiH,EAAAwF,GACAkI,EAAAhb,KAAAf,KAAA8P,EAAA1I,EAAAiH,EAAAwF,GAGAxS,EAAAmU,EAAAuG,GAEAvG,EAAA/M,QAAAsT,EAAAtT,SAAAsT,EAAAzH,aAEA5U,EAAAD,QAAA+V,2DCdA,aAEA,IAAA5T,EAAAnB,EAAA,UAAAmB,aAIA,SAAAiH,IACA,IAAA9I,EAAAC,KACA4B,EAAAb,KAAAf,MAEAA,KAAAgc,GAAA7a,WAAA,WACApB,EAAA0C,KAAA,SAAA,IAAA,OACAoG,EAAAU,SATA9I,EAAA,WAYAY,CAAAwH,EAAAjH,GAEAiH,EAAAhH,UAAAuD,MAAA,WACAoE,aAAAxJ,KAAAgc,KAGAnT,EAAAU,QAAA,IAEA7J,EAAAD,QAAAoJ,mDCvBA,aAEA,IAAAxH,EAAAZ,EAAA,YACAsb,EAAAtb,EAAA,iBAGA,SAAAoH,EAAAiI,EAAA1I,EAAAiH,GACA0N,EAAAhb,KAAAf,KAAA8P,EAAA1I,EAAAiH,EAAA,CACAgG,eAAA,IAIAhT,EAAAwG,EAAAkU,GAEAlU,EAAAY,QAAAsT,EAAAtT,QAEA/I,EAAAD,QAAAoI,2DChBA,aAEA,IAAAG,EAAAvH,EAAA,kBACA4E,EAAA5E,EAAA,gBACAY,EAAAZ,EAAA,YACAmB,EAAAnB,EAAA,UAAAmB,aACAqa,EAAAxb,EAAA,sBAGAgF,EAAA,aAKA,SAAAyW,EAAApV,EAAAqV,EAAA/R,GACA,IAAA8R,EAAAzT,UACA,MAAA,IAAA7H,MAAA,mCAGAgB,EAAAb,KAAAf,MACAyF,EAAA,cAAAqB,GAEA,IAAA/G,EAAAC,KACAoH,EAAA/B,EAAA+D,QAAAtC,EAAA,cAEAM,EADA,UAAAA,EAAAnD,MAAA,EAAA,GACA,MAAAmD,EAAAnD,MAAA,GAEA,KAAAmD,EAAAnD,MAAA,GAEAjE,KAAAoH,IAAAA,EAEApH,KAAAoc,GAAA,IAAAH,EAAAjc,KAAAoH,IAAA,GAAAgD,GACApK,KAAAoc,GAAA3N,UAAA,SAAAtO,GACAsF,EAAA,gBAAAtF,EAAAkE,MACAtE,EAAA0C,KAAA,UAAAtC,EAAAkE,OAQArE,KAAAiU,UAAAjM,EAAAkM,UAAA,WACAzO,EAAA,UACA1F,EAAAqc,GAAAhX,UAEApF,KAAAoc,GAAA1N,QAAA,SAAAvO,GACAsF,EAAA,cAAAtF,EAAAU,KAAAV,EAAAuB,QACA3B,EAAA0C,KAAA,QAAAtC,EAAAU,KAAAV,EAAAuB,QACA3B,EAAAuJ,YAEAtJ,KAAAoc,GAAAzN,QAAA,SAAAxO,GACAsF,EAAA,cAAAtF,GACAJ,EAAA0C,KAAA,QAAA,KAAA,+BACA1C,EAAAuJ,YAIAjI,EAAA6a,EAAAta,GAEAsa,EAAAra,UAAAqD,KAAA,SAAAb,GACA,IAAAgE,EAAA,IAAAhE,EAAA,IACAoB,EAAA,OAAA4C,GACArI,KAAAoc,GAAAlX,KAAAmD,IAGA6T,EAAAra,UAAAuD,MAAA,WACAK,EAAA,SACA,IAAA2W,EAAApc,KAAAoc,GACApc,KAAAsJ,WACA8S,GACAA,EAAAhX,SAIA8W,EAAAra,UAAAyH,SAAA,WACA7D,EAAA,YACA,IAAA2W,EAAApc,KAAAoc,GACAA,IACAA,EAAA3N,UAAA2N,EAAA1N,QAAA0N,EAAAzN,QAAA,MAEA3G,EAAA8M,UAAA9U,KAAAiU,WACAjU,KAAAiU,UAAAjU,KAAAoc,GAAA,KACApc,KAAA8B,sBAGAoa,EAAAzT,QAAA,WAEA,OADAhD,EAAA,aACAwW,GAEAC,EAAAlW,cAAA,YAMAkW,EAAAlO,WAAA,EAEAtO,EAAAD,QAAAyc,gIClGA,aAEA,IAAA7a,EAAAZ,EAAA,YACA6U,EAAA7U,EAAA,oBACA4b,EAAA5b,EAAA,mBACAia,EAAAja,EAAA,kBACAib,EAAAjb,EAAA,gBAGA,SAAA6b,EAAAxV,GACA,IAAA4U,EAAAjT,QACA,MAAA,IAAA7H,MAAA,mCAEA0U,EAAAvU,KAAAf,KAAA8G,EAAA,OAAA4T,EAAAgB,GAGAra,EAAAib,EAAAhH,GAEAgH,EAAA7T,QAAA4T,EAAA5T,QACA6T,EAAAtW,cAAA,cACAsW,EAAAtO,WAAA,EAEAtO,EAAAD,QAAA6c,yHCtBA,aAEA,IAAAjb,EAAAZ,EAAA,YACA6U,EAAA7U,EAAA,oBACAia,EAAAja,EAAA,kBACAib,EAAAjb,EAAA,gBAOA,SAAA4b,EAAAvV,GACA,IAAA4U,EAAAjT,QACA,MAAA,IAAA7H,MAAA,mCAEA0U,EAAAvU,KAAAf,KAAA8G,EAAA,iBAAA4T,EAAAgB,GAGAra,EAAAgb,EAAA/G,GAEA+G,EAAA5T,QAAA,SAAAf,GACA,OAAAA,EAAA6U,gBAAA7U,EAAA2E,aAGAqP,EAAAjT,SAAAf,EAAAyB,aAGAkT,EAAArW,cAAA,gBACAqW,EAAArO,WAAA,EAEAtO,EAAAD,QAAA4c,oGC/BA,aAEA,IAAAhb,EAAAZ,EAAA,YACA6U,EAAA7U,EAAA,oBACAia,EAAAja,EAAA,kBACA+U,EAAA/U,EAAA,qBACAoH,EAAApH,EAAA,sBAGA,SAAA+b,EAAA1V,GACA,IAAAe,EAAAY,UAAA+M,EAAA/M,QACA,MAAA,IAAA7H,MAAA,mCAEA0U,EAAAvU,KAAAf,KAAA8G,EAAA,OAAA4T,EAAAlF,GAGAnU,EAAAmb,EAAAlH,GAEAkH,EAAA/T,QAAA,SAAAf,GACA,OAAAA,EAAA2E,gBAIAxE,EAAAY,UAAAf,EAAAwB,aAGAsM,EAAA/M,UAGA+T,EAAAxW,cAAA,cACAwW,EAAAxO,WAAA,EAEAtO,EAAAD,QAAA+c,0JChCA,aAEA,IAAAnb,EAAAZ,EAAA,YACA6U,EAAA7U,EAAA,oBACAia,EAAAja,EAAA,kBACA+U,EAAA/U,EAAA,qBACAoH,EAAApH,EAAA,sBACAwJ,EAAAxJ,EAAA,oBAGA,SAAAgc,EAAA3V,GACA,IAAAe,EAAAY,UAAA+M,EAAA/M,QACA,MAAA,IAAA7H,MAAA,mCAEA0U,EAAAvU,KAAAf,KAAA8G,EAAA,iBAAA4T,EAAAlF,GAGAnU,EAAAob,EAAAnH,GAEAmH,EAAAhU,QAAA,SAAAf,GACA,OAAAA,EAAA2E,cAKApC,EAAAoQ,WAIA7E,EAAA/M,UAGAgU,EAAAzW,cAAA,gBACAyW,EAAAzO,WAAA,EAKAyO,EAAA9O,WAAA7N,EAAAyI,SAEA7I,EAAAD,QAAAgd,+SCxCA,aAEA3c,EAAA4c,QAAA5c,EAAA4c,OAAAC,gBACAjd,EAAAD,QAAAmd,YAAA,SAAA5b,GACA,IAAA6b,EAAA,IAAAC,WAAA9b,GAEA,OADAlB,EAAA4c,OAAAC,gBAAAE,GACAA,GAGAnd,EAAAD,QAAAmd,YAAA,SAAA5b,GAEA,IADA,IAAA6b,EAAA,IAAAha,MAAA7B,GACAT,EAAA,EAAAA,EAAAS,EAAAT,IACAsc,EAAAtc,GAAAuN,KAAAgE,MAAA,IAAAhE,KAAA/D,UAEA,OAAA8S,oLCdA,aAEAnd,EAAAD,QAAA,CACA4a,QAAA,WACA,OAAAva,EAAAid,WACA,SAAA1J,KAAAvT,EAAAid,UAAAC,YAGAC,YAAA,WACA,OAAAnd,EAAAid,WACA,aAAA1J,KAAAvT,EAAAid,UAAAC,YAIA1Q,UAAA,WAEA,IAAAxM,EAAAyI,SACA,OAAA,EAGA,IACA,QAAAzI,EAAAyI,SAAA2U,OACA,MAAA/c,GACA,OAAA,6JCvBA,aAKA,IACAgd,EADAC,EAAA,0/BAwBA1d,EAAAD,QAAA,CACAqN,MAAA,SAAA7B,GACA,IAAAoS,EAAAvY,KAAAC,UAAAkG,GAIA,OADAmS,EAAA5K,UAAA,EACA4K,EAAA/J,KAAAgK,IAKAF,EADAA,GA7BA,SAAAG,GACA,IAAA/c,EACAgd,EAAA,GACA/c,EAAA,GACA,IAAAD,EAAA,EAAAA,EAAA,MAAAA,IACAC,EAAAqQ,KAAApB,OAAA+N,aAAAjd,IAQA,OANA+c,EAAA9K,UAAA,EACAhS,EAAAuQ,KAAA,IAAA5E,QAAAmR,EAAA,SAAA3c,GAEA,OADA4c,EAAA5c,GAAA,OAAA,OAAAA,EAAA8c,WAAA,GAAA1O,SAAA,KAAA9K,OAAA,GACA,KAEAqZ,EAAA9K,UAAA,EACA+K,EAiBAG,CAAAN,GAGAC,EAAAlR,QAAAiR,EAAA,SAAAzc,GACA,OAAAwc,EAAAxc,MARA0c,sDCpCA,aAEA,IAAAtT,EAAAtJ,EAAA,YAEAkd,EAAA,GACAC,GAAA,EAEAC,EAAA/d,EAAAge,QAAAhe,EAAAge,OAAAC,KAAAje,EAAAge,OAAAC,IAAAC,QAGAte,EAAAD,QAAA,CACA4G,YAAA,SAAAlC,EAAAjC,QACA,IAAApC,EAAAkD,iBACAlD,EAAAkD,iBAAAmB,EAAAjC,GAAA,GACApC,EAAAyI,UAAAzI,EAAAuG,cAIAvG,EAAAyI,SAAAlC,YAAA,KAAAlC,EAAAjC,GAEApC,EAAAuG,YAAA,KAAAlC,EAAAjC,KAIAgU,YAAA,SAAA/R,EAAAjC,QACA,IAAApC,EAAAkD,iBACAlD,EAAAmD,oBAAAkB,EAAAjC,GAAA,GACApC,EAAAyI,UAAAzI,EAAAoW,cACApW,EAAAyI,SAAA2N,YAAA,KAAA/R,EAAAjC,GACApC,EAAAoW,YAAA,KAAA/R,EAAAjC,KAIAgS,UAAA,SAAAhS,GACA,GAAA2b,EACA,OAAA,KAGA,IAAAI,EAAAlU,EAAAkB,OAAA,GAKA,OAJA0S,EAAAM,GAAA/b,EACA0b,GACAzc,WAAAnB,KAAAke,uBAAA,GAEAD,GAGAnJ,UAAA,SAAAmJ,GACAA,KAAAN,UACAA,EAAAM,IAIAC,uBAAA,WACA,IAAA,IAAAD,KAAAN,EACAA,EAAAM,YACAN,EAAAM,KAeAJ,GACAne,EAAAD,QAAA4G,YAAA,SAXA,WACAuX,IAGAA,GAAA,EACAle,EAAAD,QAAAye,0NCjEA,aAEA,IAAA5Y,EAAA7E,EAAA,WACAwJ,EAAAxJ,EAAA,aAGAgF,EAAA,aAKA/F,EAAAD,QAAA,CACAiZ,QAAA,MACAvS,gBAAA,KAEAoS,uBAAA,WACA7Y,EAAAD,QAAAiZ,WAAA5Y,IACAA,EAAAJ,EAAAD,QAAAiZ,SAAA,KAIA7T,YAAA,SAAA9C,EAAAsC,GACAvE,EAAAyG,SAAAzG,EACAA,EAAAyG,OAAA1B,YAAAC,KAAAC,UAAA,CACA6B,SAAAlH,EAAAD,QAAA0G,gBACApE,KAAAA,EACAsC,KAAAA,GAAA,KACA,KAEAoB,EAAA,wCAAA1D,EAAAsC,IAIA0R,aAAA,SAAAF,EAAAsI,GAGA,SAAAC,IACA3Y,EAAA,YACA+D,aAAAmO,GAEA,IACA4D,EAAA5B,OAAA,KACA,MAAAzG,IAGAqI,EAAA5M,QAAA,KAEA,SAAAwH,IACA1Q,EAAA,WACA8V,IACA6C,IAIAjd,WAAA,WACAoa,GACAA,EAAA/B,WAAAC,YAAA8B,GAEAA,EAAA,MACA,GACAjW,EAAAwP,UAAAb,IAGA,SAAAtF,EAAAwI,GACA1R,EAAA,UAAA0R,GACAoE,IACApF,IACAgI,EAAAhH,IAjCA,IACAQ,EAAA1D,EADAsH,EAAAzb,EAAAyI,SAAAyR,cAAA,UAuEA,OApBAuB,EAAAtB,IAAApE,EACA0F,EAAAP,MAAAC,QAAA,OACAM,EAAAP,MAAAE,SAAA,WACAK,EAAA5M,QAAA,WACAA,EAAA,YAEA4M,EAAA5B,OAAA,WACAlU,EAAA,UAGA+D,aAAAmO,GACAA,EAAAxW,WAAA,WACAwN,EAAA,mBACA,MAEA7O,EAAAyI,SAAAC,KAAA6S,YAAAE,GACA5D,EAAAxW,WAAA,WACAwN,EAAA,YACA,MACAsF,EAAA3O,EAAA4O,UAAAiC,GACA,CACAG,KApCA,SAAAjO,EAAA7B,GACAf,EAAA,OAAA4C,EAAA7B,GACArF,WAAA,WACA,IAGAoa,GAAAA,EAAA8C,eACA9C,EAAA8C,cAAAxZ,YAAAwD,EAAA7B,GAEA,MAAA0M,MAGA,IAyBAiD,QAAAA,EACAC,OAAAgI,IAKAvF,eAAA,SAAAhD,EAAAsI,GAKA,SAAAC,IACA5U,aAAAmO,GACA4D,EAAA5M,QAAA,KAEA,SAAAwH,IACAmI,IACAF,IACA9Y,EAAAwP,UAAAb,GACAsH,EAAA/B,WAAAC,YAAA8B,GACAA,EAAA+C,EAAA,KACAC,kBAGA,SAAA5P,EAAAzO,GACAuF,EAAA,UAAAvF,GACAoe,IACAnI,IACAgI,EAAAje,IArBA,IAEAyX,EAAA1D,EACAsH,EAHAxG,EAAA,CAAA,UAAAhR,OAAA,UAAAgN,KAAA,KACAuN,EAAA,IAAAxe,EAAAiV,GAAA,YAqCAuJ,EAAAnK,OACAmK,EAAAE,MAAA,kCACA1e,EAAAyI,SAAA2U,OAAA,uBAEAoB,EAAAlZ,QACAkZ,EAAAG,aAAA/e,EAAAD,QAAAiZ,SAAA5Y,EAAAJ,EAAAD,QAAAiZ,SACA,IAAAlY,EAAA8d,EAAAtE,cAAA,OAYA,OAXAsE,EAAA9V,KAAA6S,YAAA7a,GACA+a,EAAA+C,EAAAtE,cAAA,UACAxZ,EAAA6a,YAAAE,GACAA,EAAAtB,IAAApE,EACA0F,EAAA5M,QAAA,WACAA,EAAA,YAEAgJ,EAAAxW,WAAA,WACAwN,EAAA,YACA,MACAsF,EAAA3O,EAAA4O,UAAAiC,GACA,CACAG,KAjCA,SAAAjO,EAAA7B,GACA,IAGArF,WAAA,WACAoa,GAAAA,EAAA8C,eACA9C,EAAA8C,cAAAxZ,YAAAwD,EAAA7B,IAEA,GACA,MAAA0M,MAyBAiD,QAAAA,EACAC,OAAAgI,KAKA1e,EAAAD,QAAA+W,eAAA,EACA1W,EAAAyI,WAGA7I,EAAAD,QAAA+W,eAAA,mBAAA1W,EAAA+E,aACA,iBAAA/E,EAAA+E,eAAAoF,EAAAgT,0OCvLA,aAEA,IAAAyB,EAAA,GACA,CAAA,MAAA,QAAA,QAAA7Y,QAAA,SAAA8Y,GACA,IAAAC,EAEA,IACAA,EAAA9e,EAAA+e,SAAA/e,EAAA+e,QAAAF,IAAA7e,EAAA+e,QAAAF,GAAApc,MACA,MAAApC,IAIAue,EAAAC,GAAAC,EAAA,WACA,OAAA9e,EAAA+e,QAAAF,GAAApc,MAAAzC,EAAA+e,QAAArc,YACA,QAAAmc,EAAA,aAAAD,EAAAxU,MAGAxK,EAAAD,QAAAif,0JCjBA,aAEAhf,EAAAD,QAAA,CACAmI,SAAA,SAAAqH,GACA,IAAAlN,SAAAkN,EACA,MAAA,YAAAlN,GAAA,UAAAA,KAAAkN,GAGA9B,OAAA,SAAA8B,GACA,IAAAjP,KAAA4H,SAAAqH,GACA,OAAAA,EAGA,IADA,IAAA3I,EAAAwY,EACAve,EAAA,EAAAS,EAAAwB,UAAAxB,OAAAT,EAAAS,EAAAT,IAEA,IAAAue,KADAxY,EAAA9D,UAAAjC,GAEA8O,OAAAxN,UAAAyO,eAAAvP,KAAAuF,EAAAwY,KACA7P,EAAA6P,GAAAxY,EAAAwY,IAIA,OAAA7P,6BCrBA,aAEA,IAAAyN,EAAAjc,EAAA,UAIAse,EAAA,mCACArf,EAAAD,QAAA,CACAwL,OAAA,SAAAjK,GAIA,IAHA,IAAA+M,EAAAgR,EAAA/d,OACA6b,EAAAH,EAAAE,YAAA5b,GACAge,EAAA,GACAze,EAAA,EAAAA,EAAAS,EAAAT,IACAye,EAAAnO,KAAAkO,EAAAxL,OAAAsJ,EAAAtc,GAAAwN,EAAA,IAEA,OAAAiR,EAAAjO,KAAA,KAGAkO,OAAA,SAAAlR,GACA,OAAAD,KAAAgE,MAAAhE,KAAA/D,SAAAgE,IAGA3C,aAAA,SAAA2C,GACA,IAAA1N,GAAA,IAAA0N,EAAA,IAAA/M,OAEA,OADA,IAAA6B,MAAAxC,EAAA,GAAA0Q,KAAA,KACA/Q,KAAAif,OAAAlR,IAAA9J,OAAA5D,yCCzBA,aAEA,IAAAoF,EAAA,aAKA/F,EAAAD,QAAA,SAAAiG,GACA,MAAA,CACA2H,gBAAA,SAAA6R,EAAAxX,GACA,IAAAmC,EAAA,CACA0D,KAAA,GACArH,OAAA,IAkCA,OAhCAgZ,EAEA,iBAAAA,IACAA,EAAA,CAAAA,IAFAA,EAAA,GAKAxZ,EAAAG,QAAA,SAAAsZ,GACAA,IAIA,cAAAA,EAAAnZ,gBAAA,IAAA0B,EAAA0X,UAKAF,EAAAle,SACA,IAAAke,EAAApb,QAAAqb,EAAAnZ,eACAP,EAAA,mBAAA0Z,EAAAnZ,eAIAmZ,EAAA1W,QAAAf,IACAjC,EAAA,UAAA0Z,EAAAnZ,eACA6D,EAAA0D,KAAAsD,KAAAsO,GACAA,EAAApZ,iBACA8D,EAAA3D,OAAA2K,KAAAsO,EAAApZ,kBAGAN,EAAA,WAAA0Z,EAAAnZ,eAjBAP,EAAA,uBAAA,gBAoBAoE,4CC9CA,aAEA,IAAAC,EAAArJ,EAAA,aAEAgF,EAAA,aAKA/F,EAAAD,QAAA,CACAqM,UAAA,SAAA1E,GACA,IAAAA,EACA,OAAA,KAGA,IAAAtG,EAAA,IAAAgJ,EAAA1C,GACA,GAAA,UAAAtG,EAAA4I,SACA,OAAA,KAGA,IAAAE,EAAA9I,EAAA8I,KAKA,OAHAA,EADAA,IACA,WAAA9I,EAAA4I,SAAA,MAAA,MAGA5I,EAAA4I,SAAA,KAAA5I,EAAA2K,SAAA,IAAA7B,GAGA5C,cAAA,SAAArG,EAAA0e,GACA,IAAAC,EAAAtf,KAAA8L,UAAAnL,KAAAX,KAAA8L,UAAAuT,GAEA,OADA5Z,EAAA,OAAA9E,EAAA0e,EAAAC,GACAA,GAGA/S,cAAA,SAAA5L,EAAA0e,GACA,OAAA1e,EAAA2Q,MAAA,KAAA,KAAA+N,EAAA/N,MAAA,KAAA,IAGAlI,QAAA,SAAAhC,EAAAmY,GACA,IAAAC,EAAApY,EAAAkK,MAAA,KACA,OAAAkO,EAAA,GAAAD,GAAAC,EAAA,GAAA,IAAAA,EAAA,GAAA,KAGAxL,SAAA,SAAA5M,EAAAqY,GACA,OAAArY,IAAA,IAAAA,EAAAtD,QAAA,KAAA,IAAA2b,EAAA,IAAAA,IAGAjU,eAAA,SAAAkU,GACA,MAAA,mDAAArM,KAAAqM,IAAA,YAAArM,KAAAqM,2DChDAhgB,EAAAD,QAAA,iCCAA,mBAAA4P,OAAAsQ,OAEAjgB,EAAAD,QAAA,SAAAmgB,EAAAC,GACAA,IACAD,EAAAE,OAAAD,EACAD,EAAA/d,UAAAwN,OAAAsQ,OAAAE,EAAAhe,UAAA,CACAke,YAAA,CACA5P,MAAAyP,EACA3P,YAAA,EACAC,UAAA,EACAF,cAAA,OAOAtQ,EAAAD,QAAA,SAAAmgB,EAAAC,GACA,GAAAA,EAAA,CACAD,EAAAE,OAAAD,EACA,SAAAG,KACAA,EAAAne,UAAAge,EAAAhe,UACA+d,EAAA/d,UAAA,IAAAme,EACAJ,EAAA/d,UAAAke,YAAAH,6BCvBA,aAEA,IAAAK,EAAA5Q,OAAAxN,UAAAyO,eAUA,SAAA4P,EAAAC,GACA,IACA,OAAA1H,mBAAA0H,EAAAhU,QAAA,MAAA,MACA,MAAAhM,GACA,OAAA,MAoGAV,EAAAsF,UA1CA,SAAAkK,EAAAmR,GACAA,EAAAA,GAAA,GAEA,IACAjQ,EACAsE,EAFA4L,EAAA,GASA,IAAA5L,IAFA,iBAAA2L,IAAAA,EAAA,KAEAnR,EACA,GAAAgR,EAAAlf,KAAAkO,EAAAwF,GAAA,CAkBA,IAjBAtE,EAAAlB,EAAAwF,KAMAtE,MAAAA,IAAAmQ,MAAAnQ,KACAA,EAAA,IAGAsE,EAAAuE,mBAAAvE,GACAtE,EAAA6I,mBAAA7I,GAMA,OAAAsE,GAAA,OAAAtE,EAAA,SACAkQ,EAAAxP,KAAA4D,EAAA,IAAAtE,GAIA,OAAAkQ,EAAArf,OAAAof,EAAAC,EAAAtP,KAAA,KAAA,IAOAtR,EAAAiH,MA3EA,SAAA6Z,GAKA,IAJA,IAEAC,EAFAC,EAAA,sBACAzP,EAAA,GAGAwP,EAAAC,EAAAtO,KAAAoO,IAAA,CACA,IAAA9L,EAAAyL,EAAAM,EAAA,IACArQ,EAAA+P,EAAAM,EAAA,IAUA,OAAA/L,GAAA,OAAAtE,GAAAsE,KAAAzD,IACAA,EAAAyD,GAAAtE,GAGA,OAAAa,4BC/DA,aAWAtR,EAAAD,QAAA,SAAAmK,EAAAF,GAIA,GAHAA,EAAAA,EAAA4H,MAAA,KAAA,KACA1H,GAAAA,GAEA,OAAA,EAEA,OAAAF,GACA,IAAA,OACA,IAAA,KACA,OAAA,KAAAE,EAEA,IAAA,QACA,IAAA,MACA,OAAA,MAAAA,EAEA,IAAA,MACA,OAAA,KAAAA,EAEA,IAAA,SACA,OAAA,KAAAA,EAEA,IAAA,OACA,OAAA,EAGA,OAAA,IAAAA,qDCpCA,aAEA,IAAA8W,EAAAjgB,EAAA,iBACA+e,EAAA/e,EAAA,kBACAkgB,EAAA,6EACAC,EAAA,YACAC,EAAA,gCACAjX,EAAA,QACAkX,EAAA,mDACAC,EAAA,aAUA,SAAAC,EAAAC,GACA,OAAAA,GAAA,IAAAlS,WAAA5C,QAAAwU,EAAA,IAeA,IAAAO,EAAA,CACA,CAAA,IAAA,QACA,CAAA,IAAA,SACA,SAAAC,EAAA/Z,GACA,OAAAga,EAAAha,EAAAsC,UAAAyX,EAAAhV,QAAA,MAAA,KAAAgV,GAEA,CAAA,IAAA,YACA,CAAA,IAAA,OAAA,GACA,CAAAE,IAAA,YAAAhM,EAAA,EAAA,GACA,CAAA,UAAA,YAAAA,EAAA,GACA,CAAAgM,IAAA,gBAAAhM,EAAA,EAAA,IAWA8G,EAAA,CAAA/V,KAAA,EAAAma,MAAA,GAcA,SAAAe,EAAA9b,GACA,IAYAiP,EALAhL,GALA,oBAAA5J,OAAAA,YACA,IAAAC,EAAAA,EACA,oBAAAC,KAAAA,KACA,IAEA0J,UAAA,GAGA8X,EAAA,GACAxf,SAHAyD,EAAAA,GAAAiE,GAMA,GAAA,UAAAjE,EAAAkE,SACA6X,EAAA,IAAAC,EAAAC,SAAAjc,EAAA0G,UAAA,SACA,GAAA,UAAAnK,EAEA,IAAA0S,KADA8M,EAAA,IAAAC,EAAAhc,EAAA,IACA2W,SAAAoF,EAAA9M,QACA,GAAA,UAAA1S,EAAA,CACA,IAAA0S,KAAAjP,EACAiP,KAAA0H,IACAoF,EAAA9M,GAAAjP,EAAAiP,SAGAY,IAAAkM,EAAAV,UACAU,EAAAV,QAAAA,EAAAxN,KAAA7N,EAAAyB,OAIA,OAAAsa,EAUA,SAAAH,EAAAM,GACA,MACA,UAAAA,GACA,SAAAA,GACA,UAAAA,GACA,WAAAA,GACA,QAAAA,GACA,SAAAA,EAoBA,SAAAC,EAAAR,EAAA1X,GAEA0X,GADAA,EAAAH,EAAAG,IACAhV,QAAAyU,EAAA,IACAnX,EAAAA,GAAA,GAEA,IAKAmY,EALArP,EAAAuO,EAAA3O,KAAAgP,GACAzX,EAAA6I,EAAA,GAAAA,EAAA,GAAAvG,cAAA,GACA6V,IAAAtP,EAAA,GACAuP,IAAAvP,EAAA,GACAwP,EAAA,EAkCA,OA/BAF,EAGAE,EAFAD,GACAF,EAAArP,EAAA,GAAAA,EAAA,GAAAA,EAAA,GACAA,EAAA,GAAAvR,OAAAuR,EAAA,GAAAvR,SAEA4gB,EAAArP,EAAA,GAAAA,EAAA,GACAA,EAAA,GAAAvR,QAGA8gB,GACAF,EAAArP,EAAA,GAAAA,EAAA,GACAwP,EAAAxP,EAAA,GAAAvR,QAEA4gB,EAAArP,EAAA,GAIA,UAAA7I,EACA,GAAAqY,IACAH,EAAAA,EAAA3d,MAAA,IAEAmd,EAAA1X,GACAkY,EAAArP,EAAA,GACA7I,EACAmY,IACAD,EAAAA,EAAA3d,MAAA,IAEA,GAAA8d,GAAAX,EAAA3X,EAAAC,YACAkY,EAAArP,EAAA,IAGA,CACA7I,SAAAA,EACAmX,QAAAgB,GAAAT,EAAA1X,GACAqY,aAAAA,EACAH,KAAAA,GAsDA,SAAAJ,EAAAL,EAAA1X,EAAAgX,GAIA,GAFAU,GADAA,EAAAH,EAAAG,IACAhV,QAAAyU,EAAA,MAEA5gB,gBAAAwhB,GACA,OAAA,IAAAA,EAAAL,EAAA1X,EAAAgX,GAGA,IAAAuB,EAAAC,EAAAvb,EAAAwb,EAAA9O,EAAAqB,EACA0N,EAAAjB,EAAAjd,QACAlC,SAAA0H,EACArC,EAAApH,KACAO,EAAA,EA8CA,IAjCA,UAAAwB,GAAA,UAAAA,IACA0e,EAAAhX,EACAA,EAAA,MAGAgX,GAAA,mBAAAA,IAAAA,EAAAjB,EAAA9Y,OAQAsb,IADAC,EAAAN,EAAAR,GAAA,GALA1X,EAAA6X,EAAA7X,KAMAC,WAAAuY,EAAApB,QACAzZ,EAAAyZ,QAAAoB,EAAApB,SAAAmB,GAAAvY,EAAAoX,QACAzZ,EAAAsC,SAAAuY,EAAAvY,UAAAD,EAAAC,UAAA,GACAyX,EAAAc,EAAAL,MAOA,UAAAK,EAAAvY,WACA,IAAAuY,EAAAF,cAAAhB,EAAA1N,KAAA8N,MACAc,EAAApB,UACAoB,EAAAvY,UACAuY,EAAAF,aAAA,IACAX,EAAAha,EAAAsC,cAEAyY,EAAA,GAAA,CAAA,OAAA,aAGA5hB,EAAA4hB,EAAAnhB,OAAAT,IAGA,mBAFA2hB,EAAAC,EAAA5hB,KAOAmG,EAAAwb,EAAA,GACAzN,EAAAyN,EAAA,GAEAxb,GAAAA,EACAU,EAAAqN,GAAA0M,EACA,iBAAAza,IACA0M,EAAA,MAAA1M,EACAya,EAAAiB,YAAA1b,GACAya,EAAArd,QAAA4C,MAKAya,EAFA,iBAAAe,EAAA,IACA9a,EAAAqN,GAAA0M,EAAAld,MAAA,EAAAmP,GACA+N,EAAAld,MAAAmP,EAAA8O,EAAA,MAEA9a,EAAAqN,GAAA0M,EAAAld,MAAAmP,GACA+N,EAAAld,MAAA,EAAAmP,MAGAA,EAAA1M,EAAAyL,KAAAgP,MACA/Z,EAAAqN,GAAArB,EAAA,GACA+N,EAAAA,EAAAld,MAAA,EAAAmP,EAAAA,QAGAhM,EAAAqN,GAAArN,EAAAqN,IACAuN,GAAAE,EAAA,IAAAzY,EAAAgL,IAAA,GAOAyN,EAAA,KAAA9a,EAAAqN,GAAArN,EAAAqN,GAAAzI,gBApCAmV,EAAAe,EAAAf,EAAA/Z,GA4CAqZ,IAAArZ,EAAAmZ,MAAAE,EAAArZ,EAAAmZ,QAMAyB,GACAvY,EAAAoX,SACA,MAAAzZ,EAAA8E,SAAAmW,OAAA,KACA,KAAAjb,EAAA8E,UAAA,KAAAzC,EAAAyC,YAEA9E,EAAA8E,SA/JA,SAAA8V,EAAAM,GACA,GAAA,KAAAN,EAAA,OAAAM,EAQA,IANA,IAAA/C,GAAA+C,GAAA,KAAAhR,MAAA,KAAArN,MAAA,GAAA,GAAAF,OAAAie,EAAA1Q,MAAA,MACA/Q,EAAAgf,EAAAve,OACAuhB,EAAAhD,EAAAhf,EAAA,GACAqN,GAAA,EACA4U,EAAA,EAEAjiB,KACA,MAAAgf,EAAAhf,GACAgf,EAAAkD,OAAAliB,EAAA,GACA,OAAAgf,EAAAhf,IACAgf,EAAAkD,OAAAliB,EAAA,GACAiiB,KACAA,IACA,IAAAjiB,IAAAqN,GAAA,GACA2R,EAAAkD,OAAAliB,EAAA,GACAiiB,KAOA,OAHA5U,GAAA2R,EAAA3R,QAAA,IACA,MAAA2U,GAAA,OAAAA,GAAAhD,EAAA1O,KAAA,IAEA0O,EAAAxO,KAAA,KAsIA2R,CAAAtb,EAAA8E,SAAAzC,EAAAyC,WAOA,MAAA9E,EAAA8E,SAAAmW,OAAA,IAAAjB,EAAAha,EAAAsC,YACAtC,EAAA8E,SAAA,IAAA9E,EAAA8E,UAQAwU,EAAAtZ,EAAAwC,KAAAxC,EAAAsC,YACAtC,EAAAuC,KAAAvC,EAAAqE,SACArE,EAAAwC,KAAA,IAMAxC,EAAAub,SAAAvb,EAAAwb,SAAA,GAEAxb,EAAAyb,SACAzP,EAAAhM,EAAAyb,KAAA/e,QAAA,OAGAsD,EAAAub,SAAAvb,EAAAyb,KAAA5e,MAAA,EAAAmP,GACAhM,EAAAub,SAAA3J,mBAAAP,mBAAArR,EAAAub,WAEAvb,EAAAwb,SAAAxb,EAAAyb,KAAA5e,MAAAmP,EAAA,GACAhM,EAAAwb,SAAA5J,mBAAAP,mBAAArR,EAAAwb,YAEAxb,EAAAub,SAAA3J,mBAAAP,mBAAArR,EAAAyb,OAGAzb,EAAAyb,KAAAzb,EAAAwb,SAAAxb,EAAAub,SAAA,IAAAvb,EAAAwb,SAAAxb,EAAAub,UAGAvb,EAAAZ,OAAA,UAAAY,EAAAsC,UAAA0X,EAAAha,EAAAsC,WAAAtC,EAAAuC,KACAvC,EAAAsC,SAAA,KAAAtC,EAAAuC,KACA,OAKAvC,EAAAH,KAAAG,EAAA2H,WA4KAyS,EAAA3f,UAAA,CAAAoK,IA5JA,SAAAuU,EAAArQ,EAAA2S,GACA,IAAA1b,EAAApH,KAEA,OAAAwgB,GACA,IAAA,QACA,iBAAArQ,GAAAA,EAAAnP,SACAmP,GAAA2S,GAAAtD,EAAA9Y,OAAAyJ,IAGA/I,EAAAoZ,GAAArQ,EACA,MAEA,IAAA,OACA/I,EAAAoZ,GAAArQ,EAEAuQ,EAAAvQ,EAAA/I,EAAAsC,UAGAyG,IACA/I,EAAAuC,KAAAvC,EAAAqE,SAAA,IAAA0E,IAHA/I,EAAAuC,KAAAvC,EAAAqE,SACArE,EAAAoZ,GAAA,IAKA,MAEA,IAAA,WACApZ,EAAAoZ,GAAArQ,EAEA/I,EAAAwC,OAAAuG,GAAA,IAAA/I,EAAAwC,MACAxC,EAAAuC,KAAAwG,EACA,MAEA,IAAA,OACA/I,EAAAoZ,GAAArQ,EAEAvG,EAAAyJ,KAAAlD,IACAA,EAAAA,EAAAmB,MAAA,KACAlK,EAAAwC,KAAAuG,EAAA4S,MACA3b,EAAAqE,SAAA0E,EAAAY,KAAA,OAEA3J,EAAAqE,SAAA0E,EACA/I,EAAAwC,KAAA,IAGA,MAEA,IAAA,WACAxC,EAAAsC,SAAAyG,EAAAnE,cACA5E,EAAAyZ,SAAAiC,EACA,MAEA,IAAA,WACA,IAAA,OACA,GAAA3S,EAAA,CACA,IAAA6S,EAAA,aAAAxC,EAAA,IAAA,IACApZ,EAAAoZ,GAAArQ,EAAAkS,OAAA,KAAAW,EAAAA,EAAA7S,EAAAA,OAEA/I,EAAAoZ,GAAArQ,EAEA,MAEA,IAAA,WACA,IAAA,WACA/I,EAAAoZ,GAAAxH,mBAAA7I,GACA,MAEA,IAAA,OACA,IAAAiD,EAAAjD,EAAArM,QAAA,MAEAsP,GACAhM,EAAAub,SAAAxS,EAAAlM,MAAA,EAAAmP,GACAhM,EAAAub,SAAA3J,mBAAAP,mBAAArR,EAAAub,WAEAvb,EAAAwb,SAAAzS,EAAAlM,MAAAmP,EAAA,GACAhM,EAAAwb,SAAA5J,mBAAAP,mBAAArR,EAAAwb,YAEAxb,EAAAub,SAAA3J,mBAAAP,mBAAAtI,IAIA,IAAA,IAAA5P,EAAA,EAAAA,EAAA2gB,EAAAlgB,OAAAT,IAAA,CACA,IAAA0iB,EAAA/B,EAAA3gB,GAEA0iB,EAAA,KAAA7b,EAAA6b,EAAA,IAAA7b,EAAA6b,EAAA,IAAAjX,eAWA,OARA5E,EAAAyb,KAAAzb,EAAAwb,SAAAxb,EAAAub,SAAA,IAAAvb,EAAAwb,SAAAxb,EAAAub,SAEAvb,EAAAZ,OAAA,UAAAY,EAAAsC,UAAA0X,EAAAha,EAAAsC,WAAAtC,EAAAuC,KACAvC,EAAAsC,SAAA,KAAAtC,EAAAuC,KACA,OAEAvC,EAAAH,KAAAG,EAAA2H,WAEA3H,GA+DA2H,SArDA,SAAAhK,GACAA,GAAA,mBAAAA,IAAAA,EAAAya,EAAAza,WAEA,IAAAwb,EACAnZ,EAAApH,KACA2J,EAAAvC,EAAAuC,KACAD,EAAAtC,EAAAsC,SAEAA,GAAA,MAAAA,EAAA2Y,OAAA3Y,EAAA1I,OAAA,KAAA0I,GAAA,KAEA,IAAAsH,EACAtH,GACAtC,EAAAsC,UAAAtC,EAAAyZ,SAAAO,EAAAha,EAAAsC,UAAA,KAAA,IAsCA,OApCAtC,EAAAub,UACA3R,GAAA5J,EAAAub,SACAvb,EAAAwb,WAAA5R,GAAA,IAAA5J,EAAAwb,UACA5R,GAAA,KACA5J,EAAAwb,UACA5R,GAAA,IAAA5J,EAAAwb,SACA5R,GAAA,KAEA,UAAA5J,EAAAsC,UACA0X,EAAAha,EAAAsC,YACAC,GACA,MAAAvC,EAAA8E,WAMA8E,GAAA,MAQA,MAAArH,EAAAA,EAAA3I,OAAA,IAAA4I,EAAAyJ,KAAAjM,EAAAqE,YAAArE,EAAAwC,QACAD,GAAA,KAGAqH,GAAArH,EAAAvC,EAAA8E,UAEAqU,EAAA,iBAAAnZ,EAAAmZ,MAAAxb,EAAAqC,EAAAmZ,OAAAnZ,EAAAmZ,SACAvP,GAAA,MAAAuP,EAAA8B,OAAA,GAAA,IAAA9B,EAAAA,GAEAnZ,EAAAhB,OAAA4K,GAAA5J,EAAAhB,MAEA4K,IASAwQ,EAAAG,gBAAAA,EACAH,EAAA/X,SAAA6X,EACAE,EAAAR,SAAAA,EACAQ,EAAAhC,GAAAA,EAEA9f,EAAAD,QAAA+hB,oLzD5kBA", "file": "sockjs.min.js", "sourcesContent": ["(function(){function r(e,n,t){function o(i,f){if(!n[i]){if(!e[i]){var c=\"function\"==typeof require&&require;if(!f&&c)return c(i,!0);if(u)return u(i,!0);var a=new Error(\"Cannot find module '\"+i+\"'\");throw a.code=\"MODULE_NOT_FOUND\",a}var p=n[i]={exports:{}};e[i][0].call(p.exports,function(r){var n=e[i][1][r];return o(n||r)},p,p.exports,r,e,n,t)}return n[i].exports}for(var u=\"function\"==typeof require&&require,i=0;i<t.length;i++)o(t[i]);return o}return r})()", "'use strict';\n\nvar transportList = require('./transport-list');\n\nmodule.exports = require('./main')(transportList);\n\n// TODO can't get rid of this until all servers do\nif ('_sockjs_onload' in global) {\n  setTimeout(global._sockjs_onload, 1);\n}\n", "'use strict';\n\nvar inherits = require('inherits')\n  , Event = require('./event')\n  ;\n\nfunction CloseEvent() {\n  Event.call(this);\n  this.initEvent('close', false, false);\n  this.wasClean = false;\n  this.code = 0;\n  this.reason = '';\n}\n\ninherits(CloseEvent, Event);\n\nmodule.exports = CloseEvent;\n", "'use strict';\n\nvar inherits = require('inherits')\n  , EventTarget = require('./eventtarget')\n  ;\n\nfunction EventEmitter() {\n  EventTarget.call(this);\n}\n\ninherits(EventEmitter, EventTarget);\n\nEventEmitter.prototype.removeAllListeners = function(type) {\n  if (type) {\n    delete this._listeners[type];\n  } else {\n    this._listeners = {};\n  }\n};\n\nEventEmitter.prototype.once = function(type, listener) {\n  var self = this\n    , fired = false;\n\n  function g() {\n    self.removeListener(type, g);\n\n    if (!fired) {\n      fired = true;\n      listener.apply(this, arguments);\n    }\n  }\n\n  this.on(type, g);\n};\n\nEventEmitter.prototype.emit = function() {\n  var type = arguments[0];\n  var listeners = this._listeners[type];\n  if (!listeners) {\n    return;\n  }\n  // equivalent of Array.prototype.slice.call(arguments, 1);\n  var l = arguments.length;\n  var args = new Array(l - 1);\n  for (var ai = 1; ai < l; ai++) {\n    args[ai - 1] = arguments[ai];\n  }\n  for (var i = 0; i < listeners.length; i++) {\n    listeners[i].apply(this, args);\n  }\n};\n\nEventEmitter.prototype.on = EventEmitter.prototype.addListener = EventTarget.prototype.addEventListener;\nEventEmitter.prototype.removeListener = EventTarget.prototype.removeEventListener;\n\nmodule.exports.EventEmitter = EventEmitter;\n", "'use strict';\n\nfunction Event(eventType) {\n  this.type = eventType;\n}\n\nEvent.prototype.initEvent = function(eventType, canBubble, cancelable) {\n  this.type = eventType;\n  this.bubbles = canBubble;\n  this.cancelable = cancelable;\n  this.timeStamp = +new Date();\n  return this;\n};\n\nEvent.prototype.stopPropagation = function() {};\nEvent.prototype.preventDefault = function() {};\n\nEvent.CAPTURING_PHASE = 1;\nEvent.AT_TARGET = 2;\nEvent.BUBBLING_PHASE = 3;\n\nmodule.exports = Event;\n", "'use strict';\n\n/* Simplified implementation of DOM2 EventTarget.\n *   http://www.w3.org/TR/DOM-Level-2-Events/events.html#Events-EventTarget\n */\n\nfunction EventTarget() {\n  this._listeners = {};\n}\n\nEventTarget.prototype.addEventListener = function(eventType, listener) {\n  if (!(eventType in this._listeners)) {\n    this._listeners[eventType] = [];\n  }\n  var arr = this._listeners[eventType];\n  // #4\n  if (arr.indexOf(listener) === -1) {\n    // Make a copy so as not to interfere with a current dispatchEvent.\n    arr = arr.concat([listener]);\n  }\n  this._listeners[eventType] = arr;\n};\n\nEventTarget.prototype.removeEventListener = function(eventType, listener) {\n  var arr = this._listeners[eventType];\n  if (!arr) {\n    return;\n  }\n  var idx = arr.indexOf(listener);\n  if (idx !== -1) {\n    if (arr.length > 1) {\n      // Make a copy so as not to interfere with a current dispatchEvent.\n      this._listeners[eventType] = arr.slice(0, idx).concat(arr.slice(idx + 1));\n    } else {\n      delete this._listeners[eventType];\n    }\n    return;\n  }\n};\n\nEventTarget.prototype.dispatchEvent = function() {\n  var event = arguments[0];\n  var t = event.type;\n  // equivalent of Array.prototype.slice.call(arguments, 0);\n  var args = arguments.length === 1 ? [event] : Array.apply(null, arguments);\n  // TODO: This doesn't match the real behavior; per spec, onfoo get\n  // their place in line from the /first/ time they're set from\n  // non-null. Although WebKit bumps it to the end every time it's\n  // set.\n  if (this['on' + t]) {\n    this['on' + t].apply(this, args);\n  }\n  if (t in this._listeners) {\n    // Grab a reference to the listeners list. removeEventListener may alter the list.\n    var listeners = this._listeners[t];\n    for (var i = 0; i < listeners.length; i++) {\n      listeners[i].apply(this, args);\n    }\n  }\n};\n\nmodule.exports = EventTarget;\n", "'use strict';\n\nvar inherits = require('inherits')\n  , Event = require('./event')\n  ;\n\nfunction TransportMessageEvent(data) {\n  Event.call(this);\n  this.initEvent('message', false, false);\n  this.data = data;\n}\n\ninherits(TransportMessageEvent, Event);\n\nmodule.exports = TransportMessageEvent;\n", "'use strict';\n\nvar iframeUtils = require('./utils/iframe')\n  ;\n\nfunction FacadeJS(transport) {\n  this._transport = transport;\n  transport.on('message', this._transportMessage.bind(this));\n  transport.on('close', this._transportClose.bind(this));\n}\n\nFacadeJS.prototype._transportClose = function(code, reason) {\n  iframeUtils.postMessage('c', JSON.stringify([code, reason]));\n};\nFacadeJS.prototype._transportMessage = function(frame) {\n  iframeUtils.postMessage('t', frame);\n};\nFacadeJS.prototype._send = function(data) {\n  this._transport.send(data);\n};\nFacadeJS.prototype._close = function() {\n  this._transport.close();\n  this._transport.removeAllListeners();\n};\n\nmodule.exports = FacadeJS;\n", "'use strict';\n\nvar urlUtils = require('./utils/url')\n  , eventUtils = require('./utils/event')\n  , FacadeJS = require('./facade')\n  , InfoIframeReceiver = require('./info-iframe-receiver')\n  , iframeUtils = require('./utils/iframe')\n  , loc = require('./location')\n  ;\n\nvar debug = function() {};\nif (\"production\" !== 'production') {\n  debug = require('debug')('sockjs-client:iframe-bootstrap');\n}\n\nmodule.exports = function(SockJS, availableTransports) {\n  var transportMap = {};\n  availableTransports.forEach(function(at) {\n    if (at.facadeTransport) {\n      transportMap[at.facadeTransport.transportName] = at.facadeTransport;\n    }\n  });\n\n  // hard-coded for the info iframe\n  // TODO see if we can make this more dynamic\n  transportMap[InfoIframeReceiver.transportName] = InfoIframeReceiver;\n  var parentOrigin;\n\n  /* eslint-disable camelcase */\n  SockJS.bootstrap_iframe = function() {\n    /* eslint-enable camelcase */\n    var facade;\n    iframeUtils.currentWindowId = loc.hash.slice(1);\n    var onMessage = function(e) {\n      if (e.source !== parent) {\n        return;\n      }\n      if (typeof parentOrigin === 'undefined') {\n        parentOrigin = e.origin;\n      }\n      if (e.origin !== parentOrigin) {\n        return;\n      }\n\n      var iframeMessage;\n      try {\n        iframeMessage = JSON.parse(e.data);\n      } catch (ignored) {\n        debug('bad json', e.data);\n        return;\n      }\n\n      if (iframeMessage.windowId !== iframeUtils.currentWindowId) {\n        return;\n      }\n      switch (iframeMessage.type) {\n      case 's':\n        var p;\n        try {\n          p = JSON.parse(iframeMessage.data);\n        } catch (ignored) {\n          debug('bad json', iframeMessage.data);\n          break;\n        }\n        var version = p[0];\n        var transport = p[1];\n        var transUrl = p[2];\n        var baseUrl = p[3];\n        debug(version, transport, transUrl, baseUrl);\n        // change this to semver logic\n        if (version !== SockJS.version) {\n          throw new Error('Incompatible SockJS! Main site uses:' +\n                    ' \"' + version + '\", the iframe:' +\n                    ' \"' + SockJS.version + '\".');\n        }\n\n        if (!urlUtils.isOriginEqual(transUrl, loc.href) ||\n            !urlUtils.isOriginEqual(baseUrl, loc.href)) {\n          throw new Error('Can\\'t connect to different domain from within an ' +\n                    'iframe. (' + loc.href + ', ' + transUrl + ', ' + baseUrl + ')');\n        }\n        facade = new FacadeJS(new transportMap[transport](transUrl, baseUrl));\n        break;\n      case 'm':\n        facade._send(iframeMessage.data);\n        break;\n      case 'c':\n        if (facade) {\n          facade._close();\n        }\n        facade = null;\n        break;\n      }\n    };\n\n    eventUtils.attachEvent('message', onMessage);\n\n    // Start\n    iframeUtils.postMessage('s');\n  };\n};\n", "'use strict';\n\nvar EventEmitter = require('events').EventEmitter\n  , inherits = require('inherits')\n  , objectUtils = require('./utils/object')\n  ;\n\nvar debug = function() {};\nif (\"production\" !== 'production') {\n  debug = require('debug')('sockjs-client:info-ajax');\n}\n\nfunction InfoAjax(url, AjaxObject) {\n  EventEmitter.call(this);\n\n  var self = this;\n  var t0 = +new Date();\n  this.xo = new AjaxObject('GET', url);\n\n  this.xo.once('finish', function(status, text) {\n    var info, rtt;\n    if (status === 200) {\n      rtt = (+new Date()) - t0;\n      if (text) {\n        try {\n          info = JSON.parse(text);\n        } catch (e) {\n          debug('bad json', text);\n        }\n      }\n\n      if (!objectUtils.isObject(info)) {\n        info = {};\n      }\n    }\n    self.emit('finish', info, rtt);\n    self.removeAllListeners();\n  });\n}\n\ninherits(InfoAjax, EventEmitter);\n\nInfoAjax.prototype.close = function() {\n  this.removeAllListeners();\n  this.xo.close();\n};\n\nmodule.exports = InfoAjax;\n", "'use strict';\n\nvar inherits = require('inherits')\n  , EventEmitter = require('events').EventEmitter\n  , XHRLocalObject = require('./transport/sender/xhr-local')\n  , InfoAjax = require('./info-ajax')\n  ;\n\nfunction InfoReceiverIframe(transUrl) {\n  var self = this;\n  EventEmitter.call(this);\n\n  this.ir = new InfoAjax(transUrl, XHRLocalObject);\n  this.ir.once('finish', function(info, rtt) {\n    self.ir = null;\n    self.emit('message', JSON.stringify([info, rtt]));\n  });\n}\n\ninherits(InfoReceiverIframe, EventEmitter);\n\nInfoReceiverIframe.transportName = 'iframe-info-receiver';\n\nInfoReceiverIframe.prototype.close = function() {\n  if (this.ir) {\n    this.ir.close();\n    this.ir = null;\n  }\n  this.removeAllListeners();\n};\n\nmodule.exports = InfoReceiverIframe;\n", "'use strict';\n\nvar EventEmitter = require('events').EventEmitter\n  , inherits = require('inherits')\n  , utils = require('./utils/event')\n  , IframeTransport = require('./transport/iframe')\n  , InfoReceiverIframe = require('./info-iframe-receiver')\n  ;\n\nvar debug = function() {};\nif (\"production\" !== 'production') {\n  debug = require('debug')('sockjs-client:info-iframe');\n}\n\nfunction InfoIframe(baseUrl, url) {\n  var self = this;\n  EventEmitter.call(this);\n\n  var go = function() {\n    var ifr = self.ifr = new IframeTransport(InfoReceiverIframe.transportName, url, baseUrl);\n\n    ifr.once('message', function(msg) {\n      if (msg) {\n        var d;\n        try {\n          d = JSON.parse(msg);\n        } catch (e) {\n          debug('bad json', msg);\n          self.emit('finish');\n          self.close();\n          return;\n        }\n\n        var info = d[0], rtt = d[1];\n        self.emit('finish', info, rtt);\n      }\n      self.close();\n    });\n\n    ifr.once('close', function() {\n      self.emit('finish');\n      self.close();\n    });\n  };\n\n  // TODO this seems the same as the 'needBody' from transports\n  if (!global.document.body) {\n    utils.attachEvent('load', go);\n  } else {\n    go();\n  }\n}\n\ninherits(InfoIframe, EventEmitter);\n\nInfoIframe.enabled = function() {\n  return IframeTransport.enabled();\n};\n\nInfoIframe.prototype.close = function() {\n  if (this.ifr) {\n    this.ifr.close();\n  }\n  this.removeAllListeners();\n  this.ifr = null;\n};\n\nmodule.exports = InfoIframe;\n", "'use strict';\n\nvar EventEmitter = require('events').EventEmitter\n  , inherits = require('inherits')\n  , urlUtils = require('./utils/url')\n  , XDR = require('./transport/sender/xdr')\n  , XHRCors = require('./transport/sender/xhr-cors')\n  , XHRLocal = require('./transport/sender/xhr-local')\n  , XHRFake = require('./transport/sender/xhr-fake')\n  , InfoIframe = require('./info-iframe')\n  , InfoAjax = require('./info-ajax')\n  ;\n\nvar debug = function() {};\nif (\"production\" !== 'production') {\n  debug = require('debug')('sockjs-client:info-receiver');\n}\n\nfunction InfoReceiver(baseUrl, urlInfo) {\n  debug(baseUrl);\n  var self = this;\n  EventEmitter.call(this);\n\n  setTimeout(function() {\n    self.doXhr(baseUrl, urlInfo);\n  }, 0);\n}\n\ninherits(InfoReceiver, EventEmitter);\n\n// TODO this is currently ignoring the list of available transports and the whitelist\n\nInfoReceiver._getReceiver = function(baseUrl, url, urlInfo) {\n  // determine method of CORS support (if needed)\n  if (urlInfo.sameOrigin) {\n    return new InfoAjax(url, XHRLocal);\n  }\n  if (XHRCors.enabled) {\n    return new InfoAjax(url, XHRCors);\n  }\n  if (XDR.enabled && urlInfo.sameScheme) {\n    return new InfoAjax(url, XDR);\n  }\n  if (InfoIframe.enabled()) {\n    return new InfoIframe(baseUrl, url);\n  }\n  return new InfoAjax(url, XHRFake);\n};\n\nInfoReceiver.prototype.doXhr = function(baseUrl, urlInfo) {\n  var self = this\n    , url = urlUtils.addPath(baseUrl, '/info')\n    ;\n  debug('doXhr', url);\n\n  this.xo = InfoReceiver._getReceiver(baseUrl, url, urlInfo);\n\n  this.timeoutRef = setTimeout(function() {\n    debug('timeout');\n    self._cleanup(false);\n    self.emit('finish');\n  }, InfoReceiver.timeout);\n\n  this.xo.once('finish', function(info, rtt) {\n    debug('finish', info, rtt);\n    self._cleanup(true);\n    self.emit('finish', info, rtt);\n  });\n};\n\nInfoReceiver.prototype._cleanup = function(wasClean) {\n  debug('_cleanup');\n  clearTimeout(this.timeoutRef);\n  this.timeoutRef = null;\n  if (!wasClean && this.xo) {\n    this.xo.close();\n  }\n  this.xo = null;\n};\n\nInfoReceiver.prototype.close = function() {\n  debug('close');\n  this.removeAllListeners();\n  this._cleanup(false);\n};\n\nInfoReceiver.timeout = 8000;\n\nmodule.exports = InfoReceiver;\n", "'use strict';\n\nmodule.exports = global.location || {\n  origin: 'http://localhost:80'\n, protocol: 'http:'\n, host: 'localhost'\n, port: 80\n, href: 'http://localhost/'\n, hash: ''\n};\n", "'use strict';\n\nrequire('./shims');\n\nvar URL = require('url-parse')\n  , inherits = require('inherits')\n  , random = require('./utils/random')\n  , escape = require('./utils/escape')\n  , urlUtils = require('./utils/url')\n  , eventUtils = require('./utils/event')\n  , transport = require('./utils/transport')\n  , objectUtils = require('./utils/object')\n  , browser = require('./utils/browser')\n  , log = require('./utils/log')\n  , Event = require('./event/event')\n  , EventTarget = require('./event/eventtarget')\n  , loc = require('./location')\n  , CloseEvent = require('./event/close')\n  , TransportMessageEvent = require('./event/trans-message')\n  , InfoReceiver = require('./info-receiver')\n  ;\n\nvar debug = function() {};\nif (\"production\" !== 'production') {\n  debug = require('debug')('sockjs-client:main');\n}\n\nvar transports;\n\n// follow constructor steps defined at http://dev.w3.org/html5/websockets/#the-websocket-interface\nfunction SockJS(url, protocols, options) {\n  if (!(this instanceof SockJS)) {\n    return new SockJS(url, protocols, options);\n  }\n  if (arguments.length < 1) {\n    throw new TypeError(\"Failed to construct 'SockJS: 1 argument required, but only 0 present\");\n  }\n  EventTarget.call(this);\n\n  this.readyState = SockJS.CONNECTING;\n  this.extensions = '';\n  this.protocol = '';\n\n  // non-standard extension\n  options = options || {};\n  if (options.protocols_whitelist) {\n    log.warn(\"'protocols_whitelist' is DEPRECATED. Use 'transports' instead.\");\n  }\n  this._transportsWhitelist = options.transports;\n  this._transportOptions = options.transportOptions || {};\n  this._timeout = options.timeout || 0;\n\n  var sessionId = options.sessionId || 8;\n  if (typeof sessionId === 'function') {\n    this._generateSessionId = sessionId;\n  } else if (typeof sessionId === 'number') {\n    this._generateSessionId = function() {\n      return random.string(sessionId);\n    };\n  } else {\n    throw new TypeError('If sessionId is used in the options, it needs to be a number or a function.');\n  }\n\n  this._server = options.server || random.numberString(1000);\n\n  // Step 1 of WS spec - parse and validate the url. Issue #8\n  var parsedUrl = new URL(url);\n  if (!parsedUrl.host || !parsedUrl.protocol) {\n    throw new SyntaxError(\"The URL '\" + url + \"' is invalid\");\n  } else if (parsedUrl.hash) {\n    throw new SyntaxError('The URL must not contain a fragment');\n  } else if (parsedUrl.protocol !== 'http:' && parsedUrl.protocol !== 'https:') {\n    throw new SyntaxError(\"The URL's scheme must be either 'http:' or 'https:'. '\" + parsedUrl.protocol + \"' is not allowed.\");\n  }\n\n  var secure = parsedUrl.protocol === 'https:';\n  // Step 2 - don't allow secure origin with an insecure protocol\n  if (loc.protocol === 'https:' && !secure) {\n    // exception is *********/8 and ::1 urls\n    if (!urlUtils.isLoopbackAddr(parsedUrl.hostname)) {\n      throw new Error('SecurityError: An insecure SockJS connection may not be initiated from a page loaded over HTTPS');\n    }\n  }\n\n  // Step 3 - check port access - no need here\n  // Step 4 - parse protocols argument\n  if (!protocols) {\n    protocols = [];\n  } else if (!Array.isArray(protocols)) {\n    protocols = [protocols];\n  }\n\n  // Step 5 - check protocols argument\n  var sortedProtocols = protocols.sort();\n  sortedProtocols.forEach(function(proto, i) {\n    if (!proto) {\n      throw new SyntaxError(\"The protocols entry '\" + proto + \"' is invalid.\");\n    }\n    if (i < (sortedProtocols.length - 1) && proto === sortedProtocols[i + 1]) {\n      throw new SyntaxError(\"The protocols entry '\" + proto + \"' is duplicated.\");\n    }\n  });\n\n  // Step 6 - convert origin\n  var o = urlUtils.getOrigin(loc.href);\n  this._origin = o ? o.toLowerCase() : null;\n\n  // remove the trailing slash\n  parsedUrl.set('pathname', parsedUrl.pathname.replace(/\\/+$/, ''));\n\n  // store the sanitized url\n  this.url = parsedUrl.href;\n  debug('using url', this.url);\n\n  // Step 7 - start connection in background\n  // obtain server info\n  // http://sockjs.github.io/sockjs-protocol/sockjs-protocol-0.3.3.html#section-26\n  this._urlInfo = {\n    nullOrigin: !browser.hasDomain()\n  , sameOrigin: urlUtils.isOriginEqual(this.url, loc.href)\n  , sameScheme: urlUtils.isSchemeEqual(this.url, loc.href)\n  };\n\n  this._ir = new InfoReceiver(this.url, this._urlInfo);\n  this._ir.once('finish', this._receiveInfo.bind(this));\n}\n\ninherits(SockJS, EventTarget);\n\nfunction userSetCode(code) {\n  return code === 1000 || (code >= 3000 && code <= 4999);\n}\n\nSockJS.prototype.close = function(code, reason) {\n  // Step 1\n  if (code && !userSetCode(code)) {\n    throw new Error('InvalidAccessError: Invalid code');\n  }\n  // Step 2.4 states the max is 123 bytes, but we are just checking length\n  if (reason && reason.length > 123) {\n    throw new SyntaxError('reason argument has an invalid length');\n  }\n\n  // Step 3.1\n  if (this.readyState === SockJS.CLOSING || this.readyState === SockJS.CLOSED) {\n    return;\n  }\n\n  // TODO look at docs to determine how to set this\n  var wasClean = true;\n  this._close(code || 1000, reason || 'Normal closure', wasClean);\n};\n\nSockJS.prototype.send = function(data) {\n  // #13 - convert anything non-string to string\n  // TODO this currently turns objects into [object Object]\n  if (typeof data !== 'string') {\n    data = '' + data;\n  }\n  if (this.readyState === SockJS.CONNECTING) {\n    throw new Error('InvalidStateError: The connection has not been established yet');\n  }\n  if (this.readyState !== SockJS.OPEN) {\n    return;\n  }\n  this._transport.send(escape.quote(data));\n};\n\nSockJS.version = require('./version');\n\nSockJS.CONNECTING = 0;\nSockJS.OPEN = 1;\nSockJS.CLOSING = 2;\nSockJS.CLOSED = 3;\n\nSockJS.prototype._receiveInfo = function(info, rtt) {\n  debug('_receiveInfo', rtt);\n  this._ir = null;\n  if (!info) {\n    this._close(1002, 'Cannot connect to server');\n    return;\n  }\n\n  // establish a round-trip timeout (RTO) based on the\n  // round-trip time (RTT)\n  this._rto = this.countRTO(rtt);\n  // allow server to override url used for the actual transport\n  this._transUrl = info.base_url ? info.base_url : this.url;\n  info = objectUtils.extend(info, this._urlInfo);\n  debug('info', info);\n  // determine list of desired and supported transports\n  var enabledTransports = transports.filterToEnabled(this._transportsWhitelist, info);\n  this._transports = enabledTransports.main;\n  debug(this._transports.length + ' enabled transports');\n\n  this._connect();\n};\n\nSockJS.prototype._connect = function() {\n  for (var Transport = this._transports.shift(); Transport; Transport = this._transports.shift()) {\n    debug('attempt', Transport.transportName);\n    if (Transport.needBody) {\n      if (!global.document.body ||\n          (typeof global.document.readyState !== 'undefined' &&\n            global.document.readyState !== 'complete' &&\n            global.document.readyState !== 'interactive')) {\n        debug('waiting for body');\n        this._transports.unshift(Transport);\n        eventUtils.attachEvent('load', this._connect.bind(this));\n        return;\n      }\n    }\n\n    // calculate timeout based on RTO and round trips. Default to 5s\n    var timeoutMs = Math.max(this._timeout, (this._rto * Transport.roundTrips) || 5000);\n    this._transportTimeoutId = setTimeout(this._transportTimeout.bind(this), timeoutMs);\n    debug('using timeout', timeoutMs);\n\n    var transportUrl = urlUtils.addPath(this._transUrl, '/' + this._server + '/' + this._generateSessionId());\n    var options = this._transportOptions[Transport.transportName];\n    debug('transport url', transportUrl);\n    var transportObj = new Transport(transportUrl, this._transUrl, options);\n    transportObj.on('message', this._transportMessage.bind(this));\n    transportObj.once('close', this._transportClose.bind(this));\n    transportObj.transportName = Transport.transportName;\n    this._transport = transportObj;\n\n    return;\n  }\n  this._close(2000, 'All transports failed', false);\n};\n\nSockJS.prototype._transportTimeout = function() {\n  debug('_transportTimeout');\n  if (this.readyState === SockJS.CONNECTING) {\n    if (this._transport) {\n      this._transport.close();\n    }\n\n    this._transportClose(2007, 'Transport timed out');\n  }\n};\n\nSockJS.prototype._transportMessage = function(msg) {\n  debug('_transportMessage', msg);\n  var self = this\n    , type = msg.slice(0, 1)\n    , content = msg.slice(1)\n    , payload\n    ;\n\n  // first check for messages that don't need a payload\n  switch (type) {\n    case 'o':\n      this._open();\n      return;\n    case 'h':\n      this.dispatchEvent(new Event('heartbeat'));\n      debug('heartbeat', this.transport);\n      return;\n  }\n\n  if (content) {\n    try {\n      payload = JSON.parse(content);\n    } catch (e) {\n      debug('bad json', content);\n    }\n  }\n\n  if (typeof payload === 'undefined') {\n    debug('empty payload', content);\n    return;\n  }\n\n  switch (type) {\n    case 'a':\n      if (Array.isArray(payload)) {\n        payload.forEach(function(p) {\n          debug('message', self.transport, p);\n          self.dispatchEvent(new TransportMessageEvent(p));\n        });\n      }\n      break;\n    case 'm':\n      debug('message', this.transport, payload);\n      this.dispatchEvent(new TransportMessageEvent(payload));\n      break;\n    case 'c':\n      if (Array.isArray(payload) && payload.length === 2) {\n        this._close(payload[0], payload[1], true);\n      }\n      break;\n  }\n};\n\nSockJS.prototype._transportClose = function(code, reason) {\n  debug('_transportClose', this.transport, code, reason);\n  if (this._transport) {\n    this._transport.removeAllListeners();\n    this._transport = null;\n    this.transport = null;\n  }\n\n  if (!userSetCode(code) && code !== 2000 && this.readyState === SockJS.CONNECTING) {\n    this._connect();\n    return;\n  }\n\n  this._close(code, reason);\n};\n\nSockJS.prototype._open = function() {\n  debug('_open', this._transport && this._transport.transportName, this.readyState);\n  if (this.readyState === SockJS.CONNECTING) {\n    if (this._transportTimeoutId) {\n      clearTimeout(this._transportTimeoutId);\n      this._transportTimeoutId = null;\n    }\n    this.readyState = SockJS.OPEN;\n    this.transport = this._transport.transportName;\n    this.dispatchEvent(new Event('open'));\n    debug('connected', this.transport);\n  } else {\n    // The server might have been restarted, and lost track of our\n    // connection.\n    this._close(1006, 'Server lost session');\n  }\n};\n\nSockJS.prototype._close = function(code, reason, wasClean) {\n  debug('_close', this.transport, code, reason, wasClean, this.readyState);\n  var forceFail = false;\n\n  if (this._ir) {\n    forceFail = true;\n    this._ir.close();\n    this._ir = null;\n  }\n  if (this._transport) {\n    this._transport.close();\n    this._transport = null;\n    this.transport = null;\n  }\n\n  if (this.readyState === SockJS.CLOSED) {\n    throw new Error('InvalidStateError: SockJS has already been closed');\n  }\n\n  this.readyState = SockJS.CLOSING;\n  setTimeout(function() {\n    this.readyState = SockJS.CLOSED;\n\n    if (forceFail) {\n      this.dispatchEvent(new Event('error'));\n    }\n\n    var e = new CloseEvent('close');\n    e.wasClean = wasClean || false;\n    e.code = code || 1000;\n    e.reason = reason;\n\n    this.dispatchEvent(e);\n    this.onmessage = this.onclose = this.onerror = null;\n    debug('disconnected');\n  }.bind(this), 0);\n};\n\n// See: http://www.erg.abdn.ac.uk/~gerrit/dccp/notes/ccid2/rto_estimator/\n// and RFC 2988.\nSockJS.prototype.countRTO = function(rtt) {\n  // In a local environment, when using IE8/9 and the `jsonp-polling`\n  // transport the time needed to establish a connection (the time that pass\n  // from the opening of the transport to the call of `_dispatchOpen`) is\n  // around 200msec (the lower bound used in the article above) and this\n  // causes spurious timeouts. For this reason we calculate a value slightly\n  // larger than that used in the article.\n  if (rtt > 100) {\n    return 4 * rtt; // rto > 400msec\n  }\n  return 300 + rtt; // 300msec < rto <= 400msec\n};\n\nmodule.exports = function(availableTransports) {\n  transports = transport(availableTransports);\n  require('./iframe-bootstrap')(SockJS, availableTransports);\n  return SockJS;\n};\n", "/* eslint-disable */\n/* jscs: disable */\n'use strict';\n\n// pulled specific shims from https://github.com/es-shims/es5-shim\n\nvar ArrayPrototype = Array.prototype;\nvar ObjectPrototype = Object.prototype;\nvar FunctionPrototype = Function.prototype;\nvar StringPrototype = String.prototype;\nvar array_slice = ArrayPrototype.slice;\n\nvar _toString = ObjectPrototype.toString;\nvar isFunction = function (val) {\n    return ObjectPrototype.toString.call(val) === '[object Function]';\n};\nvar isArray = function isArray(obj) {\n    return _toString.call(obj) === '[object Array]';\n};\nvar isString = function isString(obj) {\n    return _toString.call(obj) === '[object String]';\n};\n\nvar supportsDescriptors = Object.defineProperty && (function () {\n    try {\n        Object.defineProperty({}, 'x', {});\n        return true;\n    } catch (e) { /* this is ES3 */\n        return false;\n    }\n}());\n\n// Define configurable, writable and non-enumerable props\n// if they don't exist.\nvar defineProperty;\nif (supportsDescriptors) {\n    defineProperty = function (object, name, method, forceAssign) {\n        if (!forceAssign && (name in object)) { return; }\n        Object.defineProperty(object, name, {\n            configurable: true,\n            enumerable: false,\n            writable: true,\n            value: method\n        });\n    };\n} else {\n    defineProperty = function (object, name, method, forceAssign) {\n        if (!forceAssign && (name in object)) { return; }\n        object[name] = method;\n    };\n}\nvar defineProperties = function (object, map, forceAssign) {\n    for (var name in map) {\n        if (ObjectPrototype.hasOwnProperty.call(map, name)) {\n          defineProperty(object, name, map[name], forceAssign);\n        }\n    }\n};\n\nvar toObject = function (o) {\n    if (o == null) { // this matches both null and undefined\n        throw new TypeError(\"can't convert \" + o + ' to object');\n    }\n    return Object(o);\n};\n\n//\n// Util\n// ======\n//\n\n// ES5 9.4\n// http://es5.github.com/#x9.4\n// http://jsperf.com/to-integer\n\nfunction toInteger(num) {\n    var n = +num;\n    if (n !== n) { // isNaN\n        n = 0;\n    } else if (n !== 0 && n !== (1 / 0) && n !== -(1 / 0)) {\n        n = (n > 0 || -1) * Math.floor(Math.abs(n));\n    }\n    return n;\n}\n\nfunction ToUint32(x) {\n    return x >>> 0;\n}\n\n//\n// Function\n// ========\n//\n\n// ES-5 ********\n// http://es5.github.com/#x********\n\nfunction Empty() {}\n\ndefineProperties(FunctionPrototype, {\n    bind: function bind(that) { // .length is 1\n        // 1. Let Target be the this value.\n        var target = this;\n        // 2. If IsCallable(Target) is false, throw a TypeError exception.\n        if (!isFunction(target)) {\n            throw new TypeError('Function.prototype.bind called on incompatible ' + target);\n        }\n        // 3. Let A be a new (possibly empty) internal list of all of the\n        //   argument values provided after thisArg (arg1, arg2 etc), in order.\n        // XXX slicedArgs will stand in for \"A\" if used\n        var args = array_slice.call(arguments, 1); // for normal call\n        // 4. Let F be a new native ECMAScript object.\n        // 11. Set the [[Prototype]] internal property of F to the standard\n        //   built-in Function prototype object as specified in ********.\n        // 12. Set the [[Call]] internal property of F as described in\n        //   ********.1.\n        // 13. Set the [[Construct]] internal property of F as described in\n        //   ********.2.\n        // 14. Set the [[HasInstance]] internal property of F as described in\n        //   ********.3.\n        var binder = function () {\n\n            if (this instanceof bound) {\n                // ********.2 [[Construct]]\n                // When the [[Construct]] internal method of a function object,\n                // F that was created using the bind function is called with a\n                // list of arguments ExtraArgs, the following steps are taken:\n                // 1. Let target be the value of F's [[TargetFunction]]\n                //   internal property.\n                // 2. If target has no [[Construct]] internal method, a\n                //   TypeError exception is thrown.\n                // 3. Let boundArgs be the value of F's [[BoundArgs]] internal\n                //   property.\n                // 4. Let args be a new list containing the same values as the\n                //   list boundArgs in the same order followed by the same\n                //   values as the list ExtraArgs in the same order.\n                // 5. Return the result of calling the [[Construct]] internal\n                //   method of target providing args as the arguments.\n\n                var result = target.apply(\n                    this,\n                    args.concat(array_slice.call(arguments))\n                );\n                if (Object(result) === result) {\n                    return result;\n                }\n                return this;\n\n            } else {\n                // ********.1 [[Call]]\n                // When the [[Call]] internal method of a function object, F,\n                // which was created using the bind function is called with a\n                // this value and a list of arguments ExtraArgs, the following\n                // steps are taken:\n                // 1. Let boundArgs be the value of F's [[BoundArgs]] internal\n                //   property.\n                // 2. Let boundThis be the value of F's [[BoundThis]] internal\n                //   property.\n                // 3. Let target be the value of F's [[TargetFunction]] internal\n                //   property.\n                // 4. Let args be a new list containing the same values as the\n                //   list boundArgs in the same order followed by the same\n                //   values as the list ExtraArgs in the same order.\n                // 5. Return the result of calling the [[Call]] internal method\n                //   of target providing boundThis as the this value and\n                //   providing args as the arguments.\n\n                // equiv: target.call(this, ...boundArgs, ...args)\n                return target.apply(\n                    that,\n                    args.concat(array_slice.call(arguments))\n                );\n\n            }\n\n        };\n\n        // 15. If the [[Class]] internal property of Target is \"Function\", then\n        //     a. Let L be the length property of Target minus the length of A.\n        //     b. Set the length own property of F to either 0 or L, whichever is\n        //       larger.\n        // 16. Else set the length own property of F to 0.\n\n        var boundLength = Math.max(0, target.length - args.length);\n\n        // 17. Set the attributes of the length own property of F to the values\n        //   specified in 15.3.5.1.\n        var boundArgs = [];\n        for (var i = 0; i < boundLength; i++) {\n            boundArgs.push('$' + i);\n        }\n\n        // XXX Build a dynamic function with desired amount of arguments is the only\n        // way to set the length property of a function.\n        // In environments where Content Security Policies enabled (Chrome extensions,\n        // for ex.) all use of eval or Function costructor throws an exception.\n        // However in all of these environments Function.prototype.bind exists\n        // and so this code will never be executed.\n        var bound = Function('binder', 'return function (' + boundArgs.join(',') + '){ return binder.apply(this, arguments); }')(binder);\n\n        if (target.prototype) {\n            Empty.prototype = target.prototype;\n            bound.prototype = new Empty();\n            // Clean up dangling references.\n            Empty.prototype = null;\n        }\n\n        // TODO\n        // 18. Set the [[Extensible]] internal property of F to true.\n\n        // TODO\n        // 19. Let thrower be the [[ThrowTypeError]] function Object (13.2.3).\n        // 20. Call the [[DefineOwnProperty]] internal method of F with\n        //   arguments \"caller\", PropertyDescriptor {[[Get]]: thrower, [[Set]]:\n        //   thrower, [[Enumerable]]: false, [[Configurable]]: false}, and\n        //   false.\n        // 21. Call the [[DefineOwnProperty]] internal method of F with\n        //   arguments \"arguments\", PropertyDescriptor {[[Get]]: thrower,\n        //   [[Set]]: thrower, [[Enumerable]]: false, [[Configurable]]: false},\n        //   and false.\n\n        // TODO\n        // NOTE Function objects created using Function.prototype.bind do not\n        // have a prototype property or the [[Code]], [[FormalParameters]], and\n        // [[Scope]] internal properties.\n        // XXX can't delete prototype in pure-js.\n\n        // 22. Return F.\n        return bound;\n    }\n});\n\n//\n// Array\n// =====\n//\n\n// ES5 ********\n// http://es5.github.com/#x********\n// https://developer.mozilla.org/en/JavaScript/Reference/Global_Objects/Array/isArray\ndefineProperties(Array, { isArray: isArray });\n\n\nvar boxedString = Object('a');\nvar splitString = boxedString[0] !== 'a' || !(0 in boxedString);\n\nvar properlyBoxesContext = function properlyBoxed(method) {\n    // Check node 0.6.21 bug where third parameter is not boxed\n    var properlyBoxesNonStrict = true;\n    var properlyBoxesStrict = true;\n    if (method) {\n        method.call('foo', function (_, __, context) {\n            if (typeof context !== 'object') { properlyBoxesNonStrict = false; }\n        });\n\n        method.call([1], function () {\n            'use strict';\n            properlyBoxesStrict = typeof this === 'string';\n        }, 'x');\n    }\n    return !!method && properlyBoxesNonStrict && properlyBoxesStrict;\n};\n\ndefineProperties(ArrayPrototype, {\n    forEach: function forEach(fun /*, thisp*/) {\n        var object = toObject(this),\n            self = splitString && isString(this) ? this.split('') : object,\n            thisp = arguments[1],\n            i = -1,\n            length = self.length >>> 0;\n\n        // If no callback function or if callback is not a callable function\n        if (!isFunction(fun)) {\n            throw new TypeError(); // TODO message\n        }\n\n        while (++i < length) {\n            if (i in self) {\n                // Invoke the callback function with call, passing arguments:\n                // context, property value, property key, thisArg object\n                // context\n                fun.call(thisp, self[i], i, object);\n            }\n        }\n    }\n}, !properlyBoxesContext(ArrayPrototype.forEach));\n\n// ES5 *********\n// http://es5.github.com/#x*********\n// https://developer.mozilla.org/en/JavaScript/Reference/Global_Objects/Array/indexOf\nvar hasFirefox2IndexOfBug = Array.prototype.indexOf && [0, 1].indexOf(1, 2) !== -1;\ndefineProperties(ArrayPrototype, {\n    indexOf: function indexOf(sought /*, fromIndex */ ) {\n        var self = splitString && isString(this) ? this.split('') : toObject(this),\n            length = self.length >>> 0;\n\n        if (!length) {\n            return -1;\n        }\n\n        var i = 0;\n        if (arguments.length > 1) {\n            i = toInteger(arguments[1]);\n        }\n\n        // handle negative indices\n        i = i >= 0 ? i : Math.max(0, length + i);\n        for (; i < length; i++) {\n            if (i in self && self[i] === sought) {\n                return i;\n            }\n        }\n        return -1;\n    }\n}, hasFirefox2IndexOfBug);\n\n//\n// String\n// ======\n//\n\n// ES5 *********\n// http://es5.github.com/#x*********\n\n// [bugfix, IE lt 9, firefox 4, Konqueror, Opera, obscure browsers]\n// Many browsers do not split properly with regular expressions or they\n// do not perform the split correctly under obscure conditions.\n// See http://blog.stevenlevithan.com/archives/cross-browser-split\n// I've tested in many browsers and this seems to cover the deviant ones:\n//    'ab'.split(/(?:ab)*/) should be [\"\", \"\"], not [\"\"]\n//    '.'.split(/(.?)(.?)/) should be [\"\", \".\", \"\", \"\"], not [\"\", \"\"]\n//    'tesst'.split(/(s)*/) should be [\"t\", undefined, \"e\", \"s\", \"t\"], not\n//       [undefined, \"t\", undefined, \"e\", ...]\n//    ''.split(/.?/) should be [], not [\"\"]\n//    '.'.split(/()()/) should be [\".\"], not [\"\", \"\", \".\"]\n\nvar string_split = StringPrototype.split;\nif (\n    'ab'.split(/(?:ab)*/).length !== 2 ||\n    '.'.split(/(.?)(.?)/).length !== 4 ||\n    'tesst'.split(/(s)*/)[1] === 't' ||\n    'test'.split(/(?:)/, -1).length !== 4 ||\n    ''.split(/.?/).length ||\n    '.'.split(/()()/).length > 1\n) {\n    (function () {\n        var compliantExecNpcg = /()??/.exec('')[1] === void 0; // NPCG: nonparticipating capturing group\n\n        StringPrototype.split = function (separator, limit) {\n            var string = this;\n            if (separator === void 0 && limit === 0) {\n                return [];\n            }\n\n            // If `separator` is not a regex, use native split\n            if (_toString.call(separator) !== '[object RegExp]') {\n                return string_split.call(this, separator, limit);\n            }\n\n            var output = [],\n                flags = (separator.ignoreCase ? 'i' : '') +\n                        (separator.multiline  ? 'm' : '') +\n                        (separator.extended   ? 'x' : '') + // Proposed for ES6\n                        (separator.sticky     ? 'y' : ''), // Firefox 3+\n                lastLastIndex = 0,\n                // Make `global` and avoid `lastIndex` issues by working with a copy\n                separator2, match, lastIndex, lastLength;\n            separator = new RegExp(separator.source, flags + 'g');\n            string += ''; // Type-convert\n            if (!compliantExecNpcg) {\n                // Doesn't need flags gy, but they don't hurt\n                separator2 = new RegExp('^' + separator.source + '$(?!\\\\s)', flags);\n            }\n            /* Values for `limit`, per the spec:\n             * If undefined: 4294967295 // Math.pow(2, 32) - 1\n             * If 0, Infinity, or NaN: 0\n             * If positive number: limit = Math.floor(limit); if (limit > 4294967295) limit -= 4294967296;\n             * If negative number: 4294967296 - Math.floor(Math.abs(limit))\n             * If other: Type-convert, then use the above rules\n             */\n            limit = limit === void 0 ?\n                -1 >>> 0 : // Math.pow(2, 32) - 1\n                ToUint32(limit);\n            while (match = separator.exec(string)) {\n                // `separator.lastIndex` is not reliable cross-browser\n                lastIndex = match.index + match[0].length;\n                if (lastIndex > lastLastIndex) {\n                    output.push(string.slice(lastLastIndex, match.index));\n                    // Fix browsers whose `exec` methods don't consistently return `undefined` for\n                    // nonparticipating capturing groups\n                    if (!compliantExecNpcg && match.length > 1) {\n                        match[0].replace(separator2, function () {\n                            for (var i = 1; i < arguments.length - 2; i++) {\n                                if (arguments[i] === void 0) {\n                                    match[i] = void 0;\n                                }\n                            }\n                        });\n                    }\n                    if (match.length > 1 && match.index < string.length) {\n                        ArrayPrototype.push.apply(output, match.slice(1));\n                    }\n                    lastLength = match[0].length;\n                    lastLastIndex = lastIndex;\n                    if (output.length >= limit) {\n                        break;\n                    }\n                }\n                if (separator.lastIndex === match.index) {\n                    separator.lastIndex++; // Avoid an infinite loop\n                }\n            }\n            if (lastLastIndex === string.length) {\n                if (lastLength || !separator.test('')) {\n                    output.push('');\n                }\n            } else {\n                output.push(string.slice(lastLastIndex));\n            }\n            return output.length > limit ? output.slice(0, limit) : output;\n        };\n    }());\n\n// [bugfix, chrome]\n// If separator is undefined, then the result array contains just one String,\n// which is the this value (converted to a String). If limit is not undefined,\n// then the output array is truncated so that it contains no more than limit\n// elements.\n// \"0\".split(undefined, 0) -> []\n} else if ('0'.split(void 0, 0).length) {\n    StringPrototype.split = function split(separator, limit) {\n        if (separator === void 0 && limit === 0) { return []; }\n        return string_split.call(this, separator, limit);\n    };\n}\n\n// ECMA-262, 3rd B.2.3\n// Not an ECMAScript standard, although ECMAScript 3rd Edition has a\n// non-normative section suggesting uniform semantics and it should be\n// normalized across all browsers\n// [bugfix, IE lt 9] IE < 9 substr() with negative value not working in IE\nvar string_substr = StringPrototype.substr;\nvar hasNegativeSubstrBug = ''.substr && '0b'.substr(-1) !== 'b';\ndefineProperties(StringPrototype, {\n    substr: function substr(start, length) {\n        return string_substr.call(\n            this,\n            start < 0 ? ((start = this.length + start) < 0 ? 0 : start) : start,\n            length\n        );\n    }\n}, hasNegativeSubstrBug);\n", "'use strict';\n\nmodule.exports = [\n  // streaming transports\n  require('./transport/websocket')\n, require('./transport/xhr-streaming')\n, require('./transport/xdr-streaming')\n, require('./transport/eventsource')\n, require('./transport/lib/iframe-wrap')(require('./transport/eventsource'))\n\n  // polling transports\n, require('./transport/htmlfile')\n, require('./transport/lib/iframe-wrap')(require('./transport/htmlfile'))\n, require('./transport/xhr-polling')\n, require('./transport/xdr-polling')\n, require('./transport/lib/iframe-wrap')(require('./transport/xhr-polling'))\n, require('./transport/jsonp-polling')\n];\n", "'use strict';\n\nvar EventEmitter = require('events').EventEmitter\n  , inherits = require('inherits')\n  , utils = require('../../utils/event')\n  , urlUtils = require('../../utils/url')\n  , XHR = global.XMLHttpRequest\n  ;\n\nvar debug = function() {};\nif (\"production\" !== 'production') {\n  debug = require('debug')('sockjs-client:browser:xhr');\n}\n\nfunction AbstractXHRObject(method, url, payload, opts) {\n  debug(method, url);\n  var self = this;\n  EventEmitter.call(this);\n\n  setTimeout(function () {\n    self._start(method, url, payload, opts);\n  }, 0);\n}\n\ninherits(AbstractXHRObject, EventEmitter);\n\nAbstractXHRObject.prototype._start = function(method, url, payload, opts) {\n  var self = this;\n\n  try {\n    this.xhr = new XHR();\n  } catch (x) {\n    // intentionally empty\n  }\n\n  if (!this.xhr) {\n    debug('no xhr');\n    this.emit('finish', 0, 'no xhr support');\n    this._cleanup();\n    return;\n  }\n\n  // several browsers cache POSTs\n  url = urlUtils.addQuery(url, 't=' + (+new Date()));\n\n  // Explorer tends to keep connection open, even after the\n  // tab gets closed: http://bugs.jquery.com/ticket/5280\n  this.unloadRef = utils.unloadAdd(function() {\n    debug('unload cleanup');\n    self._cleanup(true);\n  });\n  try {\n    this.xhr.open(method, url, true);\n    if (this.timeout && 'timeout' in this.xhr) {\n      this.xhr.timeout = this.timeout;\n      this.xhr.ontimeout = function() {\n        debug('xhr timeout');\n        self.emit('finish', 0, '');\n        self._cleanup(false);\n      };\n    }\n  } catch (e) {\n    debug('exception', e);\n    // IE raises an exception on wrong port.\n    this.emit('finish', 0, '');\n    this._cleanup(false);\n    return;\n  }\n\n  if ((!opts || !opts.noCredentials) && AbstractXHRObject.supportsCORS) {\n    debug('withCredentials');\n    // Mozilla docs says https://developer.mozilla.org/en/XMLHttpRequest :\n    // \"This never affects same-site requests.\"\n\n    this.xhr.withCredentials = true;\n  }\n  if (opts && opts.headers) {\n    for (var key in opts.headers) {\n      this.xhr.setRequestHeader(key, opts.headers[key]);\n    }\n  }\n\n  this.xhr.onreadystatechange = function() {\n    if (self.xhr) {\n      var x = self.xhr;\n      var text, status;\n      debug('readyState', x.readyState);\n      switch (x.readyState) {\n      case 3:\n        // IE doesn't like peeking into responseText or status\n        // on Microsoft.XMLHTTP and readystate=3\n        try {\n          status = x.status;\n          text = x.responseText;\n        } catch (e) {\n          // intentionally empty\n        }\n        debug('status', status);\n        // IE returns 1223 for 204: http://bugs.jquery.com/ticket/1450\n        if (status === 1223) {\n          status = 204;\n        }\n\n        // IE does return readystate == 3 for 404 answers.\n        if (status === 200 && text && text.length > 0) {\n          debug('chunk');\n          self.emit('chunk', status, text);\n        }\n        break;\n      case 4:\n        status = x.status;\n        debug('status', status);\n        // IE returns 1223 for 204: http://bugs.jquery.com/ticket/1450\n        if (status === 1223) {\n          status = 204;\n        }\n        // IE returns this for a bad port\n        // http://msdn.microsoft.com/en-us/library/windows/desktop/aa383770(v=vs.85).aspx\n        if (status === 12005 || status === 12029) {\n          status = 0;\n        }\n\n        debug('finish', status, x.responseText);\n        self.emit('finish', status, x.responseText);\n        self._cleanup(false);\n        break;\n      }\n    }\n  };\n\n  try {\n    self.xhr.send(payload);\n  } catch (e) {\n    self.emit('finish', 0, '');\n    self._cleanup(false);\n  }\n};\n\nAbstractXHRObject.prototype._cleanup = function(abort) {\n  debug('cleanup');\n  if (!this.xhr) {\n    return;\n  }\n  this.removeAllListeners();\n  utils.unloadDel(this.unloadRef);\n\n  // IE needs this field to be a function\n  this.xhr.onreadystatechange = function() {};\n  if (this.xhr.ontimeout) {\n    this.xhr.ontimeout = null;\n  }\n\n  if (abort) {\n    try {\n      this.xhr.abort();\n    } catch (x) {\n      // intentionally empty\n    }\n  }\n  this.unloadRef = this.xhr = null;\n};\n\nAbstractXHRObject.prototype.close = function() {\n  debug('close');\n  this._cleanup(true);\n};\n\nAbstractXHRObject.enabled = !!XHR;\n// override XMLHttpRequest for IE6/7\n// obfuscate to avoid firewalls\nvar axo = ['Active'].concat('Object').join('X');\nif (!AbstractXHRObject.enabled && (axo in global)) {\n  debug('overriding xmlhttprequest');\n  XHR = function() {\n    try {\n      return new global[axo]('Microsoft.XMLHTTP');\n    } catch (e) {\n      return null;\n    }\n  };\n  AbstractXHRObject.enabled = !!new XHR();\n}\n\nvar cors = false;\ntry {\n  cors = 'withCredentials' in new XHR();\n} catch (ignored) {\n  // intentionally empty\n}\n\nAbstractXHRObject.supportsCORS = cors;\n\nmodule.exports = AbstractXHRObject;\n", "module.exports = global.EventSource;\n", "'use strict';\n\nvar Driver = global.WebSocket || global.MozWebSocket;\nif (Driver) {\n\tmodule.exports = function WebSocketBrowserDriver(url) {\n\t\treturn new Driver(url);\n\t};\n} else {\n\tmodule.exports = undefined;\n}\n", "'use strict';\n\nvar inherits = require('inherits')\n  , AjaxBasedTransport = require('./lib/ajax-based')\n  , EventSourceReceiver = require('./receiver/eventsource')\n  , XHRCorsObject = require('./sender/xhr-cors')\n  , EventSourceDriver = require('eventsource')\n  ;\n\nfunction EventSourceTransport(transUrl) {\n  if (!EventSourceTransport.enabled()) {\n    throw new Error('Transport created when disabled');\n  }\n\n  AjaxBasedTransport.call(this, transUrl, '/eventsource', EventSourceReceiver, XHRCorsObject);\n}\n\ninherits(EventSourceTransport, AjaxBasedTransport);\n\nEventSourceTransport.enabled = function() {\n  return !!EventSourceDriver;\n};\n\nEventSourceTransport.transportName = 'eventsource';\nEventSourceTransport.roundTrips = 2;\n\nmodule.exports = EventSourceTransport;\n", "'use strict';\n\nvar inherits = require('inherits')\n  , HtmlfileReceiver = require('./receiver/htmlfile')\n  , XHRLocalObject = require('./sender/xhr-local')\n  , AjaxBasedTransport = require('./lib/ajax-based')\n  ;\n\nfunction HtmlFileTransport(transUrl) {\n  if (!HtmlfileReceiver.enabled) {\n    throw new Error('Transport created when disabled');\n  }\n  AjaxBasedTransport.call(this, transUrl, '/htmlfile', HtmlfileReceiver, XHRLocalObject);\n}\n\ninherits(HtmlFileTransport, AjaxBasedTransport);\n\nHtmlFileTransport.enabled = function(info) {\n  return HtmlfileReceiver.enabled && info.sameOrigin;\n};\n\nHtmlFileTransport.transportName = 'htmlfile';\nHtmlFileTransport.roundTrips = 2;\n\nmodule.exports = HtmlFileTransport;\n", "'use strict';\n\n// Few cool transports do work only for same-origin. In order to make\n// them work cross-domain we shall use iframe, served from the\n// remote domain. New browsers have capabilities to communicate with\n// cross domain iframe using postMessage(). In IE it was implemented\n// from IE 8+, but of course, IE got some details wrong:\n//    http://msdn.microsoft.com/en-us/library/cc197015(v=VS.85).aspx\n//    http://stevesouders.com/misc/test-postmessage.php\n\nvar inherits = require('inherits')\n  , EventEmitter = require('events').EventEmitter\n  , version = require('../version')\n  , urlUtils = require('../utils/url')\n  , iframeUtils = require('../utils/iframe')\n  , eventUtils = require('../utils/event')\n  , random = require('../utils/random')\n  ;\n\nvar debug = function() {};\nif (\"production\" !== 'production') {\n  debug = require('debug')('sockjs-client:transport:iframe');\n}\n\nfunction IframeTransport(transport, transUrl, baseUrl) {\n  if (!IframeTransport.enabled()) {\n    throw new Error('Transport created when disabled');\n  }\n  EventEmitter.call(this);\n\n  var self = this;\n  this.origin = urlUtils.getOrigin(baseUrl);\n  this.baseUrl = baseUrl;\n  this.transUrl = transUrl;\n  this.transport = transport;\n  this.windowId = random.string(8);\n\n  var iframeUrl = urlUtils.addPath(baseUrl, '/iframe.html') + '#' + this.windowId;\n  debug(transport, transUrl, iframeUrl);\n\n  this.iframeObj = iframeUtils.createIframe(iframeUrl, function(r) {\n    debug('err callback');\n    self.emit('close', 1006, 'Unable to load an iframe (' + r + ')');\n    self.close();\n  });\n\n  this.onmessageCallback = this._message.bind(this);\n  eventUtils.attachEvent('message', this.onmessageCallback);\n}\n\ninherits(IframeTransport, EventEmitter);\n\nIframeTransport.prototype.close = function() {\n  debug('close');\n  this.removeAllListeners();\n  if (this.iframeObj) {\n    eventUtils.detachEvent('message', this.onmessageCallback);\n    try {\n      // When the iframe is not loaded, IE raises an exception\n      // on 'contentWindow'.\n      this.postMessage('c');\n    } catch (x) {\n      // intentionally empty\n    }\n    this.iframeObj.cleanup();\n    this.iframeObj = null;\n    this.onmessageCallback = this.iframeObj = null;\n  }\n};\n\nIframeTransport.prototype._message = function(e) {\n  debug('message', e.data);\n  if (!urlUtils.isOriginEqual(e.origin, this.origin)) {\n    debug('not same origin', e.origin, this.origin);\n    return;\n  }\n\n  var iframeMessage;\n  try {\n    iframeMessage = JSON.parse(e.data);\n  } catch (ignored) {\n    debug('bad json', e.data);\n    return;\n  }\n\n  if (iframeMessage.windowId !== this.windowId) {\n    debug('mismatched window id', iframeMessage.windowId, this.windowId);\n    return;\n  }\n\n  switch (iframeMessage.type) {\n  case 's':\n    this.iframeObj.loaded();\n    // window global dependency\n    this.postMessage('s', JSON.stringify([\n      version\n    , this.transport\n    , this.transUrl\n    , this.baseUrl\n    ]));\n    break;\n  case 't':\n    this.emit('message', iframeMessage.data);\n    break;\n  case 'c':\n    var cdata;\n    try {\n      cdata = JSON.parse(iframeMessage.data);\n    } catch (ignored) {\n      debug('bad json', iframeMessage.data);\n      return;\n    }\n    this.emit('close', cdata[0], cdata[1]);\n    this.close();\n    break;\n  }\n};\n\nIframeTransport.prototype.postMessage = function(type, data) {\n  debug('postMessage', type, data);\n  this.iframeObj.post(JSON.stringify({\n    windowId: this.windowId\n  , type: type\n  , data: data || ''\n  }), this.origin);\n};\n\nIframeTransport.prototype.send = function(message) {\n  debug('send', message);\n  this.postMessage('m', message);\n};\n\nIframeTransport.enabled = function() {\n  return iframeUtils.iframeEnabled;\n};\n\nIframeTransport.transportName = 'iframe';\nIframeTransport.roundTrips = 2;\n\nmodule.exports = IframeTransport;\n", "'use strict';\n\n// The simplest and most robust transport, using the well-know cross\n// domain hack - JSONP. This transport is quite inefficient - one\n// message could use up to one http request. But at least it works almost\n// everywhere.\n// Known limitations:\n//   o you will get a spinning cursor\n//   o for Konqueror a dumb timer is needed to detect errors\n\nvar inherits = require('inherits')\n  , SenderReceiver = require('./lib/sender-receiver')\n  , JsonpReceiver = require('./receiver/jsonp')\n  , jsonpSender = require('./sender/jsonp')\n  ;\n\nfunction JsonPTransport(transUrl) {\n  if (!JsonPTransport.enabled()) {\n    throw new Error('Transport created when disabled');\n  }\n  SenderReceiver.call(this, transUrl, '/jsonp', jsonpSender, JsonpReceiver);\n}\n\ninherits(JsonPTransport, SenderReceiver);\n\nJsonPTransport.enabled = function() {\n  return !!global.document;\n};\n\nJsonPTransport.transportName = 'jsonp-polling';\nJsonPTransport.roundTrips = 1;\nJsonPTransport.needBody = true;\n\nmodule.exports = JsonPTransport;\n", "'use strict';\n\nvar inherits = require('inherits')\n  , urlUtils = require('../../utils/url')\n  , SenderReceiver = require('./sender-receiver')\n  ;\n\nvar debug = function() {};\nif (\"production\" !== 'production') {\n  debug = require('debug')('sockjs-client:ajax-based');\n}\n\nfunction createAjaxSender(AjaxObject) {\n  return function(url, payload, callback) {\n    debug('create ajax sender', url, payload);\n    var opt = {};\n    if (typeof payload === 'string') {\n      opt.headers = {'Content-type': 'text/plain'};\n    }\n    var ajaxUrl = urlUtils.addPath(url, '/xhr_send');\n    var xo = new AjaxObject('POST', ajaxUrl, payload, opt);\n    xo.once('finish', function(status) {\n      debug('finish', status);\n      xo = null;\n\n      if (status !== 200 && status !== 204) {\n        return callback(new Error('http status ' + status));\n      }\n      callback();\n    });\n    return function() {\n      debug('abort');\n      xo.close();\n      xo = null;\n\n      var err = new Error('Aborted');\n      err.code = 1000;\n      callback(err);\n    };\n  };\n}\n\nfunction AjaxBasedTransport(transUrl, urlSuffix, Receiver, AjaxObject) {\n  SenderReceiver.call(this, transUrl, urlSuffix, createAjaxSender(AjaxObject), Receiver, AjaxObject);\n}\n\ninherits(AjaxBasedTransport, SenderReceiver);\n\nmodule.exports = AjaxBasedTransport;\n", "'use strict';\n\nvar inherits = require('inherits')\n  , EventEmitter = require('events').EventEmitter\n  ;\n\nvar debug = function() {};\nif (\"production\" !== 'production') {\n  debug = require('debug')('sockjs-client:buffered-sender');\n}\n\nfunction BufferedSender(url, sender) {\n  debug(url);\n  EventEmitter.call(this);\n  this.sendBuffer = [];\n  this.sender = sender;\n  this.url = url;\n}\n\ninherits(BufferedSender, EventEmitter);\n\nBufferedSender.prototype.send = function(message) {\n  debug('send', message);\n  this.sendBuffer.push(message);\n  if (!this.sendStop) {\n    this.sendSchedule();\n  }\n};\n\n// For polling transports in a situation when in the message callback,\n// new message is being send. If the sending connection was started\n// before receiving one, it is possible to saturate the network and\n// timeout due to the lack of receiving socket. To avoid that we delay\n// sending messages by some small time, in order to let receiving\n// connection be started beforehand. This is only a halfmeasure and\n// does not fix the big problem, but it does make the tests go more\n// stable on slow networks.\nBufferedSender.prototype.sendScheduleWait = function() {\n  debug('sendScheduleWait');\n  var self = this;\n  var tref;\n  this.sendStop = function() {\n    debug('sendStop');\n    self.sendStop = null;\n    clearTimeout(tref);\n  };\n  tref = setTimeout(function() {\n    debug('timeout');\n    self.sendStop = null;\n    self.sendSchedule();\n  }, 25);\n};\n\nBufferedSender.prototype.sendSchedule = function() {\n  debug('sendSchedule', this.sendBuffer.length);\n  var self = this;\n  if (this.sendBuffer.length > 0) {\n    var payload = '[' + this.sendBuffer.join(',') + ']';\n    this.sendStop = this.sender(this.url, payload, function(err) {\n      self.sendStop = null;\n      if (err) {\n        debug('error', err);\n        self.emit('close', err.code || 1006, 'Sending error: ' + err);\n        self.close();\n      } else {\n        self.sendScheduleWait();\n      }\n    });\n    this.sendBuffer = [];\n  }\n};\n\nBufferedSender.prototype._cleanup = function() {\n  debug('_cleanup');\n  this.removeAllListeners();\n};\n\nBufferedSender.prototype.close = function() {\n  debug('close');\n  this._cleanup();\n  if (this.sendStop) {\n    this.sendStop();\n    this.sendStop = null;\n  }\n};\n\nmodule.exports = BufferedSender;\n", "'use strict';\n\nvar inherits = require('inherits')\n  , IframeTransport = require('../iframe')\n  , objectUtils = require('../../utils/object')\n  ;\n\nmodule.exports = function(transport) {\n\n  function IframeWrapTransport(transUrl, baseUrl) {\n    IframeTransport.call(this, transport.transportName, transUrl, baseUrl);\n  }\n\n  inherits(IframeWrapTransport, IframeTransport);\n\n  IframeWrapTransport.enabled = function(url, info) {\n    if (!global.document) {\n      return false;\n    }\n\n    var iframeInfo = objectUtils.extend({}, info);\n    iframeInfo.sameOrigin = true;\n    return transport.enabled(iframeInfo) && IframeTransport.enabled();\n  };\n\n  IframeWrapTransport.transportName = 'iframe-' + transport.transportName;\n  IframeWrapTransport.needBody = true;\n  IframeWrapTransport.roundTrips = IframeTransport.roundTrips + transport.roundTrips - 1; // html, javascript (2) + transport - no CORS (1)\n\n  IframeWrapTransport.facadeTransport = transport;\n\n  return IframeWrapTransport;\n};\n", "'use strict';\n\nvar inherits = require('inherits')\n  , EventEmitter = require('events').EventEmitter\n  ;\n\nvar debug = function() {};\nif (\"production\" !== 'production') {\n  debug = require('debug')('sockjs-client:polling');\n}\n\nfunction Polling(Receiver, receiveUrl, AjaxObject) {\n  debug(receiveUrl);\n  EventEmitter.call(this);\n  this.Receiver = Receiver;\n  this.receiveUrl = receiveUrl;\n  this.AjaxObject = AjaxObject;\n  this._scheduleReceiver();\n}\n\ninherits(Polling, EventEmitter);\n\nPolling.prototype._scheduleReceiver = function() {\n  debug('_scheduleReceiver');\n  var self = this;\n  var poll = this.poll = new this.Receiver(this.receiveUrl, this.AjaxObject);\n\n  poll.on('message', function(msg) {\n    debug('message', msg);\n    self.emit('message', msg);\n  });\n\n  poll.once('close', function(code, reason) {\n    debug('close', code, reason, self.pollIsClosing);\n    self.poll = poll = null;\n\n    if (!self.pollIsClosing) {\n      if (reason === 'network') {\n        self._scheduleReceiver();\n      } else {\n        self.emit('close', code || 1006, reason);\n        self.removeAllListeners();\n      }\n    }\n  });\n};\n\nPolling.prototype.abort = function() {\n  debug('abort');\n  this.removeAllListeners();\n  this.pollIsClosing = true;\n  if (this.poll) {\n    this.poll.abort();\n  }\n};\n\nmodule.exports = Polling;\n", "'use strict';\n\nvar inherits = require('inherits')\n  , urlUtils = require('../../utils/url')\n  , BufferedSender = require('./buffered-sender')\n  , Polling = require('./polling')\n  ;\n\nvar debug = function() {};\nif (\"production\" !== 'production') {\n  debug = require('debug')('sockjs-client:sender-receiver');\n}\n\nfunction SenderReceiver(transUrl, urlSuffix, senderFunc, Receiver, AjaxObject) {\n  var pollUrl = urlUtils.addPath(transUrl, urlSuffix);\n  debug(pollUrl);\n  var self = this;\n  BufferedSender.call(this, transUrl, senderFunc);\n\n  this.poll = new Polling(Receiver, pollUrl, AjaxObject);\n  this.poll.on('message', function(msg) {\n    debug('poll message', msg);\n    self.emit('message', msg);\n  });\n  this.poll.once('close', function(code, reason) {\n    debug('poll close', code, reason);\n    self.poll = null;\n    self.emit('close', code, reason);\n    self.close();\n  });\n}\n\ninherits(SenderReceiver, BufferedSender);\n\nSenderReceiver.prototype.close = function() {\n  BufferedSender.prototype.close.call(this);\n  debug('close');\n  this.removeAllListeners();\n  if (this.poll) {\n    this.poll.abort();\n    this.poll = null;\n  }\n};\n\nmodule.exports = SenderReceiver;\n", "'use strict';\n\nvar inherits = require('inherits')\n  , EventEmitter = require('events').EventEmitter\n  , EventSourceDriver = require('eventsource')\n  ;\n\nvar debug = function() {};\nif (\"production\" !== 'production') {\n  debug = require('debug')('sockjs-client:receiver:eventsource');\n}\n\nfunction EventSourceReceiver(url) {\n  debug(url);\n  EventEmitter.call(this);\n\n  var self = this;\n  var es = this.es = new EventSourceDriver(url);\n  es.onmessage = function(e) {\n    debug('message', e.data);\n    self.emit('message', decodeURI(e.data));\n  };\n  es.onerror = function(e) {\n    debug('error', es.readyState, e);\n    // ES on reconnection has readyState = 0 or 1.\n    // on network error it's CLOSED = 2\n    var reason = (es.readyState !== 2 ? 'network' : 'permanent');\n    self._cleanup();\n    self._close(reason);\n  };\n}\n\ninherits(EventSourceReceiver, EventEmitter);\n\nEventSourceReceiver.prototype.abort = function() {\n  debug('abort');\n  this._cleanup();\n  this._close('user');\n};\n\nEventSourceReceiver.prototype._cleanup = function() {\n  debug('cleanup');\n  var es = this.es;\n  if (es) {\n    es.onmessage = es.onerror = null;\n    es.close();\n    this.es = null;\n  }\n};\n\nEventSourceReceiver.prototype._close = function(reason) {\n  debug('close', reason);\n  var self = this;\n  // Safari and chrome < 15 crash if we close window before\n  // waiting for ES cleanup. See:\n  // https://code.google.com/p/chromium/issues/detail?id=89155\n  setTimeout(function() {\n    self.emit('close', null, reason);\n    self.removeAllListeners();\n  }, 200);\n};\n\nmodule.exports = EventSourceReceiver;\n", "'use strict';\n\nvar inherits = require('inherits')\n  , iframeUtils = require('../../utils/iframe')\n  , urlUtils = require('../../utils/url')\n  , EventEmitter = require('events').EventEmitter\n  , random = require('../../utils/random')\n  ;\n\nvar debug = function() {};\nif (\"production\" !== 'production') {\n  debug = require('debug')('sockjs-client:receiver:htmlfile');\n}\n\nfunction HtmlfileReceiver(url) {\n  debug(url);\n  EventEmitter.call(this);\n  var self = this;\n  iframeUtils.polluteGlobalNamespace();\n\n  this.id = 'a' + random.string(6);\n  url = urlUtils.addQuery(url, 'c=' + decodeURIComponent(iframeUtils.WPrefix + '.' + this.id));\n\n  debug('using htmlfile', HtmlfileReceiver.htmlfileEnabled);\n  var constructFunc = HtmlfileReceiver.htmlfileEnabled ?\n      iframeUtils.createHtmlfile : iframeUtils.createIframe;\n\n  global[iframeUtils.WPrefix][this.id] = {\n    start: function() {\n      debug('start');\n      self.iframeObj.loaded();\n    }\n  , message: function(data) {\n      debug('message', data);\n      self.emit('message', data);\n    }\n  , stop: function() {\n      debug('stop');\n      self._cleanup();\n      self._close('network');\n    }\n  };\n  this.iframeObj = constructFunc(url, function() {\n    debug('callback');\n    self._cleanup();\n    self._close('permanent');\n  });\n}\n\ninherits(HtmlfileReceiver, EventEmitter);\n\nHtmlfileReceiver.prototype.abort = function() {\n  debug('abort');\n  this._cleanup();\n  this._close('user');\n};\n\nHtmlfileReceiver.prototype._cleanup = function() {\n  debug('_cleanup');\n  if (this.iframeObj) {\n    this.iframeObj.cleanup();\n    this.iframeObj = null;\n  }\n  delete global[iframeUtils.WPrefix][this.id];\n};\n\nHtmlfileReceiver.prototype._close = function(reason) {\n  debug('_close', reason);\n  this.emit('close', null, reason);\n  this.removeAllListeners();\n};\n\nHtmlfileReceiver.htmlfileEnabled = false;\n\n// obfuscate to avoid firewalls\nvar axo = ['Active'].concat('Object').join('X');\nif (axo in global) {\n  try {\n    HtmlfileReceiver.htmlfileEnabled = !!new global[axo]('htmlfile');\n  } catch (x) {\n    // intentionally empty\n  }\n}\n\nHtmlfileReceiver.enabled = HtmlfileReceiver.htmlfileEnabled || iframeUtils.iframeEnabled;\n\nmodule.exports = HtmlfileReceiver;\n", "'use strict';\n\nvar utils = require('../../utils/iframe')\n  , random = require('../../utils/random')\n  , browser = require('../../utils/browser')\n  , urlUtils = require('../../utils/url')\n  , inherits = require('inherits')\n  , EventEmitter = require('events').EventEmitter\n  ;\n\nvar debug = function() {};\nif (\"production\" !== 'production') {\n  debug = require('debug')('sockjs-client:receiver:jsonp');\n}\n\nfunction JsonpReceiver(url) {\n  debug(url);\n  var self = this;\n  EventEmitter.call(this);\n\n  utils.polluteGlobalNamespace();\n\n  this.id = 'a' + random.string(6);\n  var urlWithId = urlUtils.addQuery(url, 'c=' + encodeURIComponent(utils.WPrefix + '.' + this.id));\n\n  global[utils.WPrefix][this.id] = this._callback.bind(this);\n  this._createScript(urlWithId);\n\n  // Fallback mostly for Konqueror - stupid timer, 35 seconds shall be plenty.\n  this.timeoutId = setTimeout(function() {\n    debug('timeout');\n    self._abort(new Error('JSONP script loaded abnormally (timeout)'));\n  }, JsonpReceiver.timeout);\n}\n\ninherits(JsonpReceiver, EventEmitter);\n\nJsonpReceiver.prototype.abort = function() {\n  debug('abort');\n  if (global[utils.WPrefix][this.id]) {\n    var err = new Error('JSONP user aborted read');\n    err.code = 1000;\n    this._abort(err);\n  }\n};\n\nJsonpReceiver.timeout = 35000;\nJsonpReceiver.scriptErrorTimeout = 1000;\n\nJsonpReceiver.prototype._callback = function(data) {\n  debug('_callback', data);\n  this._cleanup();\n\n  if (this.aborting) {\n    return;\n  }\n\n  if (data) {\n    debug('message', data);\n    this.emit('message', data);\n  }\n  this.emit('close', null, 'network');\n  this.removeAllListeners();\n};\n\nJsonpReceiver.prototype._abort = function(err) {\n  debug('_abort', err);\n  this._cleanup();\n  this.aborting = true;\n  this.emit('close', err.code, err.message);\n  this.removeAllListeners();\n};\n\nJsonpReceiver.prototype._cleanup = function() {\n  debug('_cleanup');\n  clearTimeout(this.timeoutId);\n  if (this.script2) {\n    this.script2.parentNode.removeChild(this.script2);\n    this.script2 = null;\n  }\n  if (this.script) {\n    var script = this.script;\n    // Unfortunately, you can't really abort script loading of\n    // the script.\n    script.parentNode.removeChild(script);\n    script.onreadystatechange = script.onerror =\n        script.onload = script.onclick = null;\n    this.script = null;\n  }\n  delete global[utils.WPrefix][this.id];\n};\n\nJsonpReceiver.prototype._scriptError = function() {\n  debug('_scriptError');\n  var self = this;\n  if (this.errorTimer) {\n    return;\n  }\n\n  this.errorTimer = setTimeout(function() {\n    if (!self.loadedOkay) {\n      self._abort(new Error('JSONP script loaded abnormally (onerror)'));\n    }\n  }, JsonpReceiver.scriptErrorTimeout);\n};\n\nJsonpReceiver.prototype._createScript = function(url) {\n  debug('_createScript', url);\n  var self = this;\n  var script = this.script = global.document.createElement('script');\n  var script2;  // Opera synchronous load trick.\n\n  script.id = 'a' + random.string(8);\n  script.src = url;\n  script.type = 'text/javascript';\n  script.charset = 'UTF-8';\n  script.onerror = this._scriptError.bind(this);\n  script.onload = function() {\n    debug('onload');\n    self._abort(new Error('JSONP script loaded abnormally (onload)'));\n  };\n\n  // IE9 fires 'error' event after onreadystatechange or before, in random order.\n  // Use loadedOkay to determine if actually errored\n  script.onreadystatechange = function() {\n    debug('onreadystatechange', script.readyState);\n    if (/loaded|closed/.test(script.readyState)) {\n      if (script && script.htmlFor && script.onclick) {\n        self.loadedOkay = true;\n        try {\n          // In IE, actually execute the script.\n          script.onclick();\n        } catch (x) {\n          // intentionally empty\n        }\n      }\n      if (script) {\n        self._abort(new Error('JSONP script loaded abnormally (onreadystatechange)'));\n      }\n    }\n  };\n  // IE: event/htmlFor/onclick trick.\n  // One can't rely on proper order for onreadystatechange. In order to\n  // make sure, set a 'htmlFor' and 'event' properties, so that\n  // script code will be installed as 'onclick' handler for the\n  // script object. Later, onreadystatechange, manually execute this\n  // code. FF and Chrome doesn't work with 'event' and 'htmlFor'\n  // set. For reference see:\n  //   http://jaubourg.net/2010/07/loading-script-as-onclick-handler-of.html\n  // Also, read on that about script ordering:\n  //   http://wiki.whatwg.org/wiki/Dynamic_Script_Execution_Order\n  if (typeof script.async === 'undefined' && global.document.attachEvent) {\n    // According to mozilla docs, in recent browsers script.async defaults\n    // to 'true', so we may use it to detect a good browser:\n    // https://developer.mozilla.org/en/HTML/Element/script\n    if (!browser.isOpera()) {\n      // Naively assume we're in IE\n      try {\n        script.htmlFor = script.id;\n        script.event = 'onclick';\n      } catch (x) {\n        // intentionally empty\n      }\n      script.async = true;\n    } else {\n      // Opera, second sync script hack\n      script2 = this.script2 = global.document.createElement('script');\n      script2.text = \"try{var a = document.getElementById('\" + script.id + \"'); if(a)a.onerror();}catch(x){};\";\n      script.async = script2.async = false;\n    }\n  }\n  if (typeof script.async !== 'undefined') {\n    script.async = true;\n  }\n\n  var head = global.document.getElementsByTagName('head')[0];\n  head.insertBefore(script, head.firstChild);\n  if (script2) {\n    head.insertBefore(script2, head.firstChild);\n  }\n};\n\nmodule.exports = JsonpReceiver;\n", "'use strict';\n\nvar inherits = require('inherits')\n  , EventEmitter = require('events').EventEmitter\n  ;\n\nvar debug = function() {};\nif (\"production\" !== 'production') {\n  debug = require('debug')('sockjs-client:receiver:xhr');\n}\n\nfunction XhrReceiver(url, AjaxObject) {\n  debug(url);\n  EventEmitter.call(this);\n  var self = this;\n\n  this.bufferPosition = 0;\n\n  this.xo = new AjaxObject('POST', url, null);\n  this.xo.on('chunk', this._chunkHandler.bind(this));\n  this.xo.once('finish', function(status, text) {\n    debug('finish', status, text);\n    self._chunkHandler(status, text);\n    self.xo = null;\n    var reason = status === 200 ? 'network' : 'permanent';\n    debug('close', reason);\n    self.emit('close', null, reason);\n    self._cleanup();\n  });\n}\n\ninherits(XhrReceiver, EventEmitter);\n\nXhrReceiver.prototype._chunkHandler = function(status, text) {\n  debug('_chunkHandler', status);\n  if (status !== 200 || !text) {\n    return;\n  }\n\n  for (var idx = -1; ; this.bufferPosition += idx + 1) {\n    var buf = text.slice(this.bufferPosition);\n    idx = buf.indexOf('\\n');\n    if (idx === -1) {\n      break;\n    }\n    var msg = buf.slice(0, idx);\n    if (msg) {\n      debug('message', msg);\n      this.emit('message', msg);\n    }\n  }\n};\n\nXhrReceiver.prototype._cleanup = function() {\n  debug('_cleanup');\n  this.removeAllListeners();\n};\n\nXhrReceiver.prototype.abort = function() {\n  debug('abort');\n  if (this.xo) {\n    this.xo.close();\n    debug('close');\n    this.emit('close', null, 'user');\n    this.xo = null;\n  }\n  this._cleanup();\n};\n\nmodule.exports = XhrReceiver;\n", "'use strict';\n\nvar random = require('../../utils/random')\n  , urlUtils = require('../../utils/url')\n  ;\n\nvar debug = function() {};\nif (\"production\" !== 'production') {\n  debug = require('debug')('sockjs-client:sender:jsonp');\n}\n\nvar form, area;\n\nfunction createIframe(id) {\n  debug('createIframe', id);\n  try {\n    // ie6 dynamic iframes with target=\"\" support (thanks <PERSON>)\n    return global.document.createElement('<iframe name=\"' + id + '\">');\n  } catch (x) {\n    var iframe = global.document.createElement('iframe');\n    iframe.name = id;\n    return iframe;\n  }\n}\n\nfunction createForm() {\n  debug('createForm');\n  form = global.document.createElement('form');\n  form.style.display = 'none';\n  form.style.position = 'absolute';\n  form.method = 'POST';\n  form.enctype = 'application/x-www-form-urlencoded';\n  form.acceptCharset = 'UTF-8';\n\n  area = global.document.createElement('textarea');\n  area.name = 'd';\n  form.appendChild(area);\n\n  global.document.body.appendChild(form);\n}\n\nmodule.exports = function(url, payload, callback) {\n  debug(url, payload);\n  if (!form) {\n    createForm();\n  }\n  var id = 'a' + random.string(8);\n  form.target = id;\n  form.action = urlUtils.addQuery(urlUtils.addPath(url, '/jsonp_send'), 'i=' + id);\n\n  var iframe = createIframe(id);\n  iframe.id = id;\n  iframe.style.display = 'none';\n  form.appendChild(iframe);\n\n  try {\n    area.value = payload;\n  } catch (e) {\n    // seriously broken browsers get here\n  }\n  form.submit();\n\n  var completed = function(err) {\n    debug('completed', id, err);\n    if (!iframe.onerror) {\n      return;\n    }\n    iframe.onreadystatechange = iframe.onerror = iframe.onload = null;\n    // Opera mini doesn't like if we GC iframe\n    // immediately, thus this timeout.\n    setTimeout(function() {\n      debug('cleaning up', id);\n      iframe.parentNode.removeChild(iframe);\n      iframe = null;\n    }, 500);\n    area.value = '';\n    // It is not possible to detect if the iframe succeeded or\n    // failed to submit our form.\n    callback(err);\n  };\n  iframe.onerror = function() {\n    debug('onerror', id);\n    completed();\n  };\n  iframe.onload = function() {\n    debug('onload', id);\n    completed();\n  };\n  iframe.onreadystatechange = function(e) {\n    debug('onreadystatechange', id, iframe.readyState, e);\n    if (iframe.readyState === 'complete') {\n      completed();\n    }\n  };\n  return function() {\n    debug('aborted', id);\n    completed(new Error('Aborted'));\n  };\n};\n", "'use strict';\n\nvar EventEmitter = require('events').EventEmitter\n  , inherits = require('inherits')\n  , eventUtils = require('../../utils/event')\n  , browser = require('../../utils/browser')\n  , urlUtils = require('../../utils/url')\n  ;\n\nvar debug = function() {};\nif (\"production\" !== 'production') {\n  debug = require('debug')('sockjs-client:sender:xdr');\n}\n\n// References:\n//   http://ajaxian.com/archives/100-line-ajax-wrapper\n//   http://msdn.microsoft.com/en-us/library/cc288060(v=VS.85).aspx\n\nfunction XDRObject(method, url, payload) {\n  debug(method, url);\n  var self = this;\n  EventEmitter.call(this);\n\n  setTimeout(function() {\n    self._start(method, url, payload);\n  }, 0);\n}\n\ninherits(XDRObject, EventEmitter);\n\nXDRObject.prototype._start = function(method, url, payload) {\n  debug('_start');\n  var self = this;\n  var xdr = new global.XDomainRequest();\n  // IE caches even POSTs\n  url = urlUtils.addQuery(url, 't=' + (+new Date()));\n\n  xdr.onerror = function() {\n    debug('onerror');\n    self._error();\n  };\n  xdr.ontimeout = function() {\n    debug('ontimeout');\n    self._error();\n  };\n  xdr.onprogress = function() {\n    debug('progress', xdr.responseText);\n    self.emit('chunk', 200, xdr.responseText);\n  };\n  xdr.onload = function() {\n    debug('load');\n    self.emit('finish', 200, xdr.responseText);\n    self._cleanup(false);\n  };\n  this.xdr = xdr;\n  this.unloadRef = eventUtils.unloadAdd(function() {\n    self._cleanup(true);\n  });\n  try {\n    // Fails with AccessDenied if port number is bogus\n    this.xdr.open(method, url);\n    if (this.timeout) {\n      this.xdr.timeout = this.timeout;\n    }\n    this.xdr.send(payload);\n  } catch (x) {\n    this._error();\n  }\n};\n\nXDRObject.prototype._error = function() {\n  this.emit('finish', 0, '');\n  this._cleanup(false);\n};\n\nXDRObject.prototype._cleanup = function(abort) {\n  debug('cleanup', abort);\n  if (!this.xdr) {\n    return;\n  }\n  this.removeAllListeners();\n  eventUtils.unloadDel(this.unloadRef);\n\n  this.xdr.ontimeout = this.xdr.onerror = this.xdr.onprogress = this.xdr.onload = null;\n  if (abort) {\n    try {\n      this.xdr.abort();\n    } catch (x) {\n      // intentionally empty\n    }\n  }\n  this.unloadRef = this.xdr = null;\n};\n\nXDRObject.prototype.close = function() {\n  debug('close');\n  this._cleanup(true);\n};\n\n// IE 8/9 if the request target uses the same scheme - #79\nXDRObject.enabled = !!(global.XDomainRequest && browser.hasDomain());\n\nmodule.exports = XDRObject;\n", "'use strict';\n\nvar inherits = require('inherits')\n  , XhrDriver = require('../driver/xhr')\n  ;\n\nfunction XHRCorsObject(method, url, payload, opts) {\n  XhrDriver.call(this, method, url, payload, opts);\n}\n\ninherits(XHRCorsObject, XhrDriver);\n\nXHRCorsObject.enabled = XhrDriver.enabled && XhrDriver.supportsCORS;\n\nmodule.exports = XHRCorsObject;\n", "'use strict';\n\nvar EventEmitter = require('events').EventEmitter\n  , inherits = require('inherits')\n  ;\n\nfunction XHRFake(/* method, url, payload, opts */) {\n  var self = this;\n  EventEmitter.call(this);\n\n  this.to = setTimeout(function() {\n    self.emit('finish', 200, '{}');\n  }, XHRFake.timeout);\n}\n\ninherits(XHRFake, EventEmitter);\n\nXHRFake.prototype.close = function() {\n  clearTimeout(this.to);\n};\n\nXHRFake.timeout = 2000;\n\nmodule.exports = XHRFake;\n", "'use strict';\n\nvar inherits = require('inherits')\n  , XhrDriver = require('../driver/xhr')\n  ;\n\nfunction XHRLocalObject(method, url, payload /*, opts */) {\n  XhrDriver.call(this, method, url, payload, {\n    noCredentials: true\n  });\n}\n\ninherits(XHRLocalObject, XhrDriver);\n\nXHRLocalObject.enabled = XhrDriver.enabled;\n\nmodule.exports = XHRLocalObject;\n", "'use strict';\n\nvar utils = require('../utils/event')\n  , urlUtils = require('../utils/url')\n  , inherits = require('inherits')\n  , EventEmitter = require('events').EventEmitter\n  , WebsocketDriver = require('./driver/websocket')\n  ;\n\nvar debug = function() {};\nif (\"production\" !== 'production') {\n  debug = require('debug')('sockjs-client:websocket');\n}\n\nfunction WebSocketTransport(transUrl, ignore, options) {\n  if (!WebSocketTransport.enabled()) {\n    throw new Error('Transport created when disabled');\n  }\n\n  EventEmitter.call(this);\n  debug('constructor', transUrl);\n\n  var self = this;\n  var url = urlUtils.addPath(transUrl, '/websocket');\n  if (url.slice(0, 5) === 'https') {\n    url = 'wss' + url.slice(5);\n  } else {\n    url = 'ws' + url.slice(4);\n  }\n  this.url = url;\n\n  this.ws = new WebsocketDriver(this.url, [], options);\n  this.ws.onmessage = function(e) {\n    debug('message event', e.data);\n    self.emit('message', e.data);\n  };\n  // Firefox has an interesting bug. If a websocket connection is\n  // created after onunload, it stays alive even when user\n  // navigates away from the page. In such situation let's lie -\n  // let's not open the ws connection at all. See:\n  // https://github.com/sockjs/sockjs-client/issues/28\n  // https://bugzilla.mozilla.org/show_bug.cgi?id=696085\n  this.unloadRef = utils.unloadAdd(function() {\n    debug('unload');\n    self.ws.close();\n  });\n  this.ws.onclose = function(e) {\n    debug('close event', e.code, e.reason);\n    self.emit('close', e.code, e.reason);\n    self._cleanup();\n  };\n  this.ws.onerror = function(e) {\n    debug('error event', e);\n    self.emit('close', 1006, 'WebSocket connection broken');\n    self._cleanup();\n  };\n}\n\ninherits(WebSocketTransport, EventEmitter);\n\nWebSocketTransport.prototype.send = function(data) {\n  var msg = '[' + data + ']';\n  debug('send', msg);\n  this.ws.send(msg);\n};\n\nWebSocketTransport.prototype.close = function() {\n  debug('close');\n  var ws = this.ws;\n  this._cleanup();\n  if (ws) {\n    ws.close();\n  }\n};\n\nWebSocketTransport.prototype._cleanup = function() {\n  debug('_cleanup');\n  var ws = this.ws;\n  if (ws) {\n    ws.onmessage = ws.onclose = ws.onerror = null;\n  }\n  utils.unloadDel(this.unloadRef);\n  this.unloadRef = this.ws = null;\n  this.removeAllListeners();\n};\n\nWebSocketTransport.enabled = function() {\n  debug('enabled');\n  return !!WebsocketDriver;\n};\nWebSocketTransport.transportName = 'websocket';\n\n// In theory, ws should require 1 round trip. But in chrome, this is\n// not very stable over SSL. Most likely a ws connection requires a\n// separate SSL connection, in which case 2 round trips are an\n// absolute minumum.\nWebSocketTransport.roundTrips = 2;\n\nmodule.exports = WebSocketTransport;\n", "'use strict';\n\nvar inherits = require('inherits')\n  , AjaxBasedTransport = require('./lib/ajax-based')\n  , XdrStreamingTransport = require('./xdr-streaming')\n  , XhrReceiver = require('./receiver/xhr')\n  , XDRObject = require('./sender/xdr')\n  ;\n\nfunction XdrPollingTransport(transUrl) {\n  if (!XDRObject.enabled) {\n    throw new Error('Transport created when disabled');\n  }\n  AjaxBasedTransport.call(this, transUrl, '/xhr', XhrReceiver, XDRObject);\n}\n\ninherits(XdrPollingTransport, AjaxBasedTransport);\n\nXdrPollingTransport.enabled = XdrStreamingTransport.enabled;\nXdrPollingTransport.transportName = 'xdr-polling';\nXdrPollingTransport.roundTrips = 2; // preflight, ajax\n\nmodule.exports = XdrPollingTransport;\n", "'use strict';\n\nvar inherits = require('inherits')\n  , AjaxBasedTransport = require('./lib/ajax-based')\n  , XhrReceiver = require('./receiver/xhr')\n  , XDRObject = require('./sender/xdr')\n  ;\n\n// According to:\n//   http://stackoverflow.com/questions/1641507/detect-browser-support-for-cross-domain-xmlhttprequests\n//   http://hacks.mozilla.org/2009/07/cross-site-xmlhttprequest-with-cors/\n\nfunction XdrStreamingTransport(transUrl) {\n  if (!XDRObject.enabled) {\n    throw new Error('Transport created when disabled');\n  }\n  AjaxBasedTransport.call(this, transUrl, '/xhr_streaming', XhrReceiver, XDRObject);\n}\n\ninherits(XdrStreamingTransport, AjaxBasedTransport);\n\nXdrStreamingTransport.enabled = function(info) {\n  if (info.cookie_needed || info.nullOrigin) {\n    return false;\n  }\n  return XDRObject.enabled && info.sameScheme;\n};\n\nXdrStreamingTransport.transportName = 'xdr-streaming';\nXdrStreamingTransport.roundTrips = 2; // preflight, ajax\n\nmodule.exports = XdrStreamingTransport;\n", "'use strict';\n\nvar inherits = require('inherits')\n  , AjaxBasedTransport = require('./lib/ajax-based')\n  , XhrReceiver = require('./receiver/xhr')\n  , XHRCorsObject = require('./sender/xhr-cors')\n  , XHRLocalObject = require('./sender/xhr-local')\n  ;\n\nfunction XhrPollingTransport(transUrl) {\n  if (!XHRLocalObject.enabled && !XHRCorsObject.enabled) {\n    throw new Error('Transport created when disabled');\n  }\n  AjaxBasedTransport.call(this, transUrl, '/xhr', XhrReceiver, XHRCorsObject);\n}\n\ninherits(XhrPollingTransport, AjaxBasedTransport);\n\nXhrPollingTransport.enabled = function(info) {\n  if (info.nullOrigin) {\n    return false;\n  }\n\n  if (XHRLocalObject.enabled && info.sameOrigin) {\n    return true;\n  }\n  return XHRCorsObject.enabled;\n};\n\nXhrPollingTransport.transportName = 'xhr-polling';\nXhrPollingTransport.roundTrips = 2; // preflight, ajax\n\nmodule.exports = XhrPollingTransport;\n", "'use strict';\n\nvar inherits = require('inherits')\n  , AjaxBasedTransport = require('./lib/ajax-based')\n  , XhrReceiver = require('./receiver/xhr')\n  , XHRCorsObject = require('./sender/xhr-cors')\n  , XHRLocalObject = require('./sender/xhr-local')\n  , browser = require('../utils/browser')\n  ;\n\nfunction XhrStreamingTransport(transUrl) {\n  if (!XHRLocalObject.enabled && !XHRCorsObject.enabled) {\n    throw new Error('Transport created when disabled');\n  }\n  AjaxBasedTransport.call(this, transUrl, '/xhr_streaming', XhrReceiver, XHRCorsObject);\n}\n\ninherits(XhrStreamingTransport, AjaxBasedTransport);\n\nXhrStreamingTransport.enabled = function(info) {\n  if (info.nullOrigin) {\n    return false;\n  }\n  // Opera doesn't support xhr-streaming #60\n  // But it might be able to #92\n  if (browser.isOpera()) {\n    return false;\n  }\n\n  return XHRCorsObject.enabled;\n};\n\nXhrStreamingTransport.transportName = 'xhr-streaming';\nXhrStreamingTransport.roundTrips = 2; // preflight, ajax\n\n// <PERSON><PERSON> gets confused when a streaming ajax request is started\n// before onload. This causes the load indicator to spin indefinetely.\n// Only require body when used in a browser\nXhrStreamingTransport.needBody = !!global.document;\n\nmodule.exports = XhrStreamingTransport;\n", "'use strict';\n\nif (global.crypto && global.crypto.getRandomValues) {\n  module.exports.randomBytes = function(length) {\n    var bytes = new Uint8Array(length);\n    global.crypto.getRandomValues(bytes);\n    return bytes;\n  };\n} else {\n  module.exports.randomBytes = function(length) {\n    var bytes = new Array(length);\n    for (var i = 0; i < length; i++) {\n      bytes[i] = Math.floor(Math.random() * 256);\n    }\n    return bytes;\n  };\n}\n", "'use strict';\n\nmodule.exports = {\n  isOpera: function() {\n    return global.navigator &&\n      /opera/i.test(global.navigator.userAgent);\n  }\n\n, isKonqueror: function() {\n    return global.navigator &&\n      /konqueror/i.test(global.navigator.userAgent);\n  }\n\n  // #187 wrap document.domain in try/catch because of WP8 from file:///\n, hasDomain: function () {\n    // non-browser client always has a domain\n    if (!global.document) {\n      return true;\n    }\n\n    try {\n      return !!global.document.domain;\n    } catch (e) {\n      return false;\n    }\n  }\n};\n", "'use strict';\n\n// Some extra characters that Chrome gets wrong, and substitutes with\n// something else on the wire.\n// eslint-disable-next-line no-control-regex, no-misleading-character-class\nvar extraEscapable = /[\\x00-\\x1f\\ud800-\\udfff\\ufffe\\uffff\\u0300-\\u0333\\u033d-\\u0346\\u034a-\\u034c\\u0350-\\u0352\\u0357-\\u0358\\u035c-\\u0362\\u0374\\u037e\\u0387\\u0591-\\u05af\\u05c4\\u0610-\\u0617\\u0653-\\u0654\\u0657-\\u065b\\u065d-\\u065e\\u06df-\\u06e2\\u06eb-\\u06ec\\u0730\\u0732-\\u0733\\u0735-\\u0736\\u073a\\u073d\\u073f-\\u0741\\u0743\\u0745\\u0747\\u07eb-\\u07f1\\u0951\\u0958-\\u095f\\u09dc-\\u09dd\\u09df\\u0a33\\u0a36\\u0a59-\\u0a5b\\u0a5e\\u0b5c-\\u0b5d\\u0e38-\\u0e39\\u0f43\\u0f4d\\u0f52\\u0f57\\u0f5c\\u0f69\\u0f72-\\u0f76\\u0f78\\u0f80-\\u0f83\\u0f93\\u0f9d\\u0fa2\\u0fa7\\u0fac\\u0fb9\\u1939-\\u193a\\u1a17\\u1b6b\\u1cda-\\u1cdb\\u1dc0-\\u1dcf\\u1dfc\\u1dfe\\u1f71\\u1f73\\u1f75\\u1f77\\u1f79\\u1f7b\\u1f7d\\u1fbb\\u1fbe\\u1fc9\\u1fcb\\u1fd3\\u1fdb\\u1fe3\\u1feb\\u1fee-\\u1fef\\u1ff9\\u1ffb\\u1ffd\\u2000-\\u2001\\u20d0-\\u20d1\\u20d4-\\u20d7\\u20e7-\\u20e9\\u2126\\u212a-\\u212b\\u2329-\\u232a\\u2adc\\u302b-\\u302c\\uaab2-\\uaab3\\uf900-\\ufa0d\\ufa10\\ufa12\\ufa15-\\ufa1e\\ufa20\\ufa22\\ufa25-\\ufa26\\ufa2a-\\ufa2d\\ufa30-\\ufa6d\\ufa70-\\ufad9\\ufb1d\\ufb1f\\ufb2a-\\ufb36\\ufb38-\\ufb3c\\ufb3e\\ufb40-\\ufb41\\ufb43-\\ufb44\\ufb46-\\ufb4e\\ufff0-\\uffff]/g\n  , extraLookup;\n\n// This may be quite slow, so let's delay until user actually uses bad\n// characters.\nvar unrollLookup = function(escapable) {\n  var i;\n  var unrolled = {};\n  var c = [];\n  for (i = 0; i < 65536; i++) {\n    c.push( String.fromCharCode(i) );\n  }\n  escapable.lastIndex = 0;\n  c.join('').replace(escapable, function(a) {\n    unrolled[ a ] = '\\\\u' + ('0000' + a.charCodeAt(0).toString(16)).slice(-4);\n    return '';\n  });\n  escapable.lastIndex = 0;\n  return unrolled;\n};\n\n// Quote string, also taking care of unicode characters that browsers\n// often break. Especially, take care of unicode surrogates:\n// http://en.wikipedia.org/wiki/Mapping_of_Unicode_characters#Surrogates\nmodule.exports = {\n  quote: function(string) {\n    var quoted = JSON.stringify(string);\n\n    // In most cases this should be very fast and good enough.\n    extraEscapable.lastIndex = 0;\n    if (!extraEscapable.test(quoted)) {\n      return quoted;\n    }\n\n    if (!extraLookup) {\n      extraLookup = unrollLookup(extraEscapable);\n    }\n\n    return quoted.replace(extraEscapable, function(a) {\n      return extraLookup[a];\n    });\n  }\n};\n", "'use strict';\n\nvar random = require('./random');\n\nvar onUnload = {}\n  , afterUnload = false\n    // detect google chrome packaged apps because they don't allow the 'unload' event\n  , isChromePackagedApp = global.chrome && global.chrome.app && global.chrome.app.runtime\n  ;\n\nmodule.exports = {\n  attachEvent: function(event, listener) {\n    if (typeof global.addEventListener !== 'undefined') {\n      global.addEventListener(event, listener, false);\n    } else if (global.document && global.attachEvent) {\n      // IE quirks.\n      // According to: http://stevesouders.com/misc/test-postmessage.php\n      // the message gets delivered only to 'document', not 'window'.\n      global.document.attachEvent('on' + event, listener);\n      // I get 'window' for ie8.\n      global.attachEvent('on' + event, listener);\n    }\n  }\n\n, detachEvent: function(event, listener) {\n    if (typeof global.addEventListener !== 'undefined') {\n      global.removeEventListener(event, listener, false);\n    } else if (global.document && global.detachEvent) {\n      global.document.detachEvent('on' + event, listener);\n      global.detachEvent('on' + event, listener);\n    }\n  }\n\n, unloadAdd: function(listener) {\n    if (isChromePackagedApp) {\n      return null;\n    }\n\n    var ref = random.string(8);\n    onUnload[ref] = listener;\n    if (afterUnload) {\n      setTimeout(this.triggerUnloadCallbacks, 0);\n    }\n    return ref;\n  }\n\n, unloadDel: function(ref) {\n    if (ref in onUnload) {\n      delete onUnload[ref];\n    }\n  }\n\n, triggerUnloadCallbacks: function() {\n    for (var ref in onUnload) {\n      onUnload[ref]();\n      delete onUnload[ref];\n    }\n  }\n};\n\nvar unloadTriggered = function() {\n  if (afterUnload) {\n    return;\n  }\n  afterUnload = true;\n  module.exports.triggerUnloadCallbacks();\n};\n\n// 'unload' alone is not reliable in opera within an iframe, but we\n// can't use `beforeunload` as IE fires it on javascript: links.\nif (!isChromePackagedApp) {\n  module.exports.attachEvent('unload', unloadTriggered);\n}\n", "'use strict';\n\nvar eventUtils = require('./event')\n  , browser = require('./browser')\n  ;\n\nvar debug = function() {};\nif (\"production\" !== 'production') {\n  debug = require('debug')('sockjs-client:utils:iframe');\n}\n\nmodule.exports = {\n  WPrefix: '_jp'\n, currentWindowId: null\n\n, polluteGlobalNamespace: function() {\n    if (!(module.exports.WPrefix in global)) {\n      global[module.exports.WPrefix] = {};\n    }\n  }\n\n, postMessage: function(type, data) {\n    if (global.parent !== global) {\n      global.parent.postMessage(JSON.stringify({\n        windowId: module.exports.currentWindowId\n      , type: type\n      , data: data || ''\n      }), '*');\n    } else {\n      debug('Cannot postMessage, no parent window.', type, data);\n    }\n  }\n\n, createIframe: function(iframeUrl, errorCallback) {\n    var iframe = global.document.createElement('iframe');\n    var tref, unloadRef;\n    var unattach = function() {\n      debug('unattach');\n      clearTimeout(tref);\n      // Explorer had problems with that.\n      try {\n        iframe.onload = null;\n      } catch (x) {\n        // intentionally empty\n      }\n      iframe.onerror = null;\n    };\n    var cleanup = function() {\n      debug('cleanup');\n      if (iframe) {\n        unattach();\n        // This timeout makes chrome fire onbeforeunload event\n        // within iframe. Without the timeout it goes straight to\n        // onunload.\n        setTimeout(function() {\n          if (iframe) {\n            iframe.parentNode.removeChild(iframe);\n          }\n          iframe = null;\n        }, 0);\n        eventUtils.unloadDel(unloadRef);\n      }\n    };\n    var onerror = function(err) {\n      debug('onerror', err);\n      if (iframe) {\n        cleanup();\n        errorCallback(err);\n      }\n    };\n    var post = function(msg, origin) {\n      debug('post', msg, origin);\n      setTimeout(function() {\n        try {\n          // When the iframe is not loaded, IE raises an exception\n          // on 'contentWindow'.\n          if (iframe && iframe.contentWindow) {\n            iframe.contentWindow.postMessage(msg, origin);\n          }\n        } catch (x) {\n          // intentionally empty\n        }\n      }, 0);\n    };\n\n    iframe.src = iframeUrl;\n    iframe.style.display = 'none';\n    iframe.style.position = 'absolute';\n    iframe.onerror = function() {\n      onerror('onerror');\n    };\n    iframe.onload = function() {\n      debug('onload');\n      // `onload` is triggered before scripts on the iframe are\n      // executed. Give it few seconds to actually load stuff.\n      clearTimeout(tref);\n      tref = setTimeout(function() {\n        onerror('onload timeout');\n      }, 2000);\n    };\n    global.document.body.appendChild(iframe);\n    tref = setTimeout(function() {\n      onerror('timeout');\n    }, 15000);\n    unloadRef = eventUtils.unloadAdd(cleanup);\n    return {\n      post: post\n    , cleanup: cleanup\n    , loaded: unattach\n    };\n  }\n\n/* eslint no-undef: \"off\", new-cap: \"off\" */\n, createHtmlfile: function(iframeUrl, errorCallback) {\n    var axo = ['Active'].concat('Object').join('X');\n    var doc = new global[axo]('htmlfile');\n    var tref, unloadRef;\n    var iframe;\n    var unattach = function() {\n      clearTimeout(tref);\n      iframe.onerror = null;\n    };\n    var cleanup = function() {\n      if (doc) {\n        unattach();\n        eventUtils.unloadDel(unloadRef);\n        iframe.parentNode.removeChild(iframe);\n        iframe = doc = null;\n        CollectGarbage();\n      }\n    };\n    var onerror = function(r) {\n      debug('onerror', r);\n      if (doc) {\n        cleanup();\n        errorCallback(r);\n      }\n    };\n    var post = function(msg, origin) {\n      try {\n        // When the iframe is not loaded, IE raises an exception\n        // on 'contentWindow'.\n        setTimeout(function() {\n          if (iframe && iframe.contentWindow) {\n              iframe.contentWindow.postMessage(msg, origin);\n          }\n        }, 0);\n      } catch (x) {\n        // intentionally empty\n      }\n    };\n\n    doc.open();\n    doc.write('<html><s' + 'cript>' +\n              'document.domain=\"' + global.document.domain + '\";' +\n              '</s' + 'cript></html>');\n    doc.close();\n    doc.parentWindow[module.exports.WPrefix] = global[module.exports.WPrefix];\n    var c = doc.createElement('div');\n    doc.body.appendChild(c);\n    iframe = doc.createElement('iframe');\n    c.appendChild(iframe);\n    iframe.src = iframeUrl;\n    iframe.onerror = function() {\n      onerror('onerror');\n    };\n    tref = setTimeout(function() {\n      onerror('timeout');\n    }, 15000);\n    unloadRef = eventUtils.unloadAdd(cleanup);\n    return {\n      post: post\n    , cleanup: cleanup\n    , loaded: unattach\n    };\n  }\n};\n\nmodule.exports.iframeEnabled = false;\nif (global.document) {\n  // postMessage misbehaves in konqueror 4.6.5 - the messages are delivered with\n  // huge delay, or not at all.\n  module.exports.iframeEnabled = (typeof global.postMessage === 'function' ||\n    typeof global.postMessage === 'object') && (!browser.isKonqueror());\n}\n", "'use strict';\n\nvar logObject = {};\n['log', 'debug', 'warn'].forEach(function (level) {\n  var levelExists;\n\n  try {\n    levelExists = global.console && global.console[level] && global.console[level].apply;\n  } catch(e) {\n    // do nothing\n  }\n\n  logObject[level] = levelExists ? function () {\n    return global.console[level].apply(global.console, arguments);\n  } : (level === 'log' ? function () {} : logObject.log);\n});\n\nmodule.exports = logObject;\n", "'use strict';\n\nmodule.exports = {\n  isObject: function(obj) {\n    var type = typeof obj;\n    return type === 'function' || type === 'object' && !!obj;\n  }\n\n, extend: function(obj) {\n    if (!this.isObject(obj)) {\n      return obj;\n    }\n    var source, prop;\n    for (var i = 1, length = arguments.length; i < length; i++) {\n      source = arguments[i];\n      for (prop in source) {\n        if (Object.prototype.hasOwnProperty.call(source, prop)) {\n          obj[prop] = source[prop];\n        }\n      }\n    }\n    return obj;\n  }\n};\n", "'use strict';\n\nvar crypto = require('crypto');\n\n// This string has length 32, a power of 2, so the modulus doesn't introduce a\n// bias.\nvar _randomStringChars = 'abcdefghijklmnopqrstuvwxyz012345';\nmodule.exports = {\n  string: function(length) {\n    var max = _randomStringChars.length;\n    var bytes = crypto.randomBytes(length);\n    var ret = [];\n    for (var i = 0; i < length; i++) {\n      ret.push(_randomStringChars.substr(bytes[i] % max, 1));\n    }\n    return ret.join('');\n  }\n\n, number: function(max) {\n    return Math.floor(Math.random() * max);\n  }\n\n, numberString: function(max) {\n    var t = ('' + (max - 1)).length;\n    var p = new Array(t + 1).join('0');\n    return (p + this.number(max)).slice(-t);\n  }\n};\n", "'use strict';\n\nvar debug = function() {};\nif (\"production\" !== 'production') {\n  debug = require('debug')('sockjs-client:utils:transport');\n}\n\nmodule.exports = function(availableTransports) {\n  return {\n    filterToEnabled: function(transportsWhitelist, info) {\n      var transports = {\n        main: []\n      , facade: []\n      };\n      if (!transportsWhitelist) {\n        transportsWhitelist = [];\n      } else if (typeof transportsWhitelist === 'string') {\n        transportsWhitelist = [transportsWhitelist];\n      }\n\n      availableTransports.forEach(function(trans) {\n        if (!trans) {\n          return;\n        }\n\n        if (trans.transportName === 'websocket' && info.websocket === false) {\n          debug('disabled from server', 'websocket');\n          return;\n        }\n\n        if (transportsWhitelist.length &&\n            transportsWhitelist.indexOf(trans.transportName) === -1) {\n          debug('not in whitelist', trans.transportName);\n          return;\n        }\n\n        if (trans.enabled(info)) {\n          debug('enabled', trans.transportName);\n          transports.main.push(trans);\n          if (trans.facadeTransport) {\n            transports.facade.push(trans.facadeTransport);\n          }\n        } else {\n          debug('disabled', trans.transportName);\n        }\n      });\n      return transports;\n    }\n  };\n};\n", "'use strict';\n\nvar URL = require('url-parse');\n\nvar debug = function() {};\nif (\"production\" !== 'production') {\n  debug = require('debug')('sockjs-client:utils:url');\n}\n\nmodule.exports = {\n  getOrigin: function(url) {\n    if (!url) {\n      return null;\n    }\n\n    var p = new URL(url);\n    if (p.protocol === 'file:') {\n      return null;\n    }\n\n    var port = p.port;\n    if (!port) {\n      port = (p.protocol === 'https:') ? '443' : '80';\n    }\n\n    return p.protocol + '//' + p.hostname + ':' + port;\n  }\n\n, isOriginEqual: function(a, b) {\n    var res = this.getOrigin(a) === this.getOrigin(b);\n    debug('same', a, b, res);\n    return res;\n  }\n\n, isSchemeEqual: function(a, b) {\n    return (a.split(':')[0] === b.split(':')[0]);\n  }\n\n, addPath: function (url, path) {\n    var qs = url.split('?');\n    return qs[0] + path + (qs[1] ? '?' + qs[1] : '');\n  }\n\n, addQuery: function (url, q) {\n    return url + (url.indexOf('?') === -1 ? ('?' + q) : ('&' + q));\n  }\n\n, isLoopbackAddr: function (addr) {\n    return /^127\\.([0-9]{1,3})\\.([0-9]{1,3})\\.([0-9]{1,3})$/i.test(addr) || /^\\[::1\\]$/.test(addr);\n  }\n};\n", "module.exports = '1.6.1';\n", "if (typeof Object.create === 'function') {\n  // implementation from standard node.js 'util' module\n  module.exports = function inherits(ctor, superCtor) {\n    if (superCtor) {\n      ctor.super_ = superCtor\n      ctor.prototype = Object.create(superCtor.prototype, {\n        constructor: {\n          value: ctor,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      })\n    }\n  };\n} else {\n  // old school shim for old browsers\n  module.exports = function inherits(ctor, superCtor) {\n    if (superCtor) {\n      ctor.super_ = superCtor\n      var TempCtor = function () {}\n      TempCtor.prototype = superCtor.prototype\n      ctor.prototype = new TempCtor()\n      ctor.prototype.constructor = ctor\n    }\n  }\n}\n", "'use strict';\n\nvar has = Object.prototype.hasOwnProperty\n  , undef;\n\n/**\n * Decode a URI encoded string.\n *\n * @param {String} input The URI encoded string.\n * @returns {String|Null} The decoded string.\n * @api private\n */\nfunction decode(input) {\n  try {\n    return decodeURIComponent(input.replace(/\\+/g, ' '));\n  } catch (e) {\n    return null;\n  }\n}\n\n/**\n * Attempts to encode a given input.\n *\n * @param {String} input The string that needs to be encoded.\n * @returns {String|Null} The encoded string.\n * @api private\n */\nfunction encode(input) {\n  try {\n    return encodeURIComponent(input);\n  } catch (e) {\n    return null;\n  }\n}\n\n/**\n * Simple query string parser.\n *\n * @param {String} query The query string that needs to be parsed.\n * @returns {Object}\n * @api public\n */\nfunction querystring(query) {\n  var parser = /([^=?&]+)=?([^&]*)/g\n    , result = {}\n    , part;\n\n  while (part = parser.exec(query)) {\n    var key = decode(part[1])\n      , value = decode(part[2]);\n\n    //\n    // Prevent overriding of existing properties. This ensures that build-in\n    // methods like `toString` or __proto__ are not overriden by malicious\n    // querystrings.\n    //\n    // In the case if failed decoding, we want to omit the key/value pairs\n    // from the result.\n    //\n    if (key === null || value === null || key in result) continue;\n    result[key] = value;\n  }\n\n  return result;\n}\n\n/**\n * Transform a query string to an object.\n *\n * @param {Object} obj Object that should be transformed.\n * @param {String} prefix Optional prefix.\n * @returns {String}\n * @api public\n */\nfunction querystringify(obj, prefix) {\n  prefix = prefix || '';\n\n  var pairs = []\n    , value\n    , key;\n\n  //\n  // Optionally prefix with a '?' if needed\n  //\n  if ('string' !== typeof prefix) prefix = '?';\n\n  for (key in obj) {\n    if (has.call(obj, key)) {\n      value = obj[key];\n\n      //\n      // Edge cases where we actually want to encode the value to an empty\n      // string instead of the stringified value.\n      //\n      if (!value && (value === null || value === undef || isNaN(value))) {\n        value = '';\n      }\n\n      key = encodeURIComponent(key);\n      value = encodeURIComponent(value);\n\n      //\n      // If we failed to encode the strings, we should bail out as we don't\n      // want to add invalid strings to the query.\n      //\n      if (key === null || value === null) continue;\n      pairs.push(key +'='+ value);\n    }\n  }\n\n  return pairs.length ? prefix + pairs.join('&') : '';\n}\n\n//\n// Expose the module.\n//\nexports.stringify = querystringify;\nexports.parse = querystring;\n", "'use strict';\n\n/**\n * Check if we're required to add a port number.\n *\n * @see https://url.spec.whatwg.org/#default-port\n * @param {Number|String} port Port number we need to check\n * @param {String} protocol Protocol we need to check against.\n * @returns {Boolean} Is it a default port for the given protocol\n * @api private\n */\nmodule.exports = function required(port, protocol) {\n  protocol = protocol.split(':')[0];\n  port = +port;\n\n  if (!port) return false;\n\n  switch (protocol) {\n    case 'http':\n    case 'ws':\n    return port !== 80;\n\n    case 'https':\n    case 'wss':\n    return port !== 443;\n\n    case 'ftp':\n    return port !== 21;\n\n    case 'gopher':\n    return port !== 70;\n\n    case 'file':\n    return false;\n  }\n\n  return port !== 0;\n};\n", "'use strict';\n\nvar required = require('requires-port')\n  , qs = require('querystringify')\n  , controlOrWhitespace = /^[\\x00-\\x20\\u00a0\\u1680\\u2000-\\u200a\\u2028\\u2029\\u202f\\u205f\\u3000\\ufeff]+/\n  , CRHTLF = /[\\n\\r\\t]/g\n  , slashes = /^[A-Za-z][A-Za-z0-9+-.]*:\\/\\//\n  , port = /:\\d+$/\n  , protocolre = /^([a-z][a-z0-9.+-]*:)?(\\/\\/)?([\\\\/]+)?([\\S\\s]*)/i\n  , windowsDriveLetter = /^[a-zA-Z]:/;\n\n/**\n * Remove control characters and whitespace from the beginning of a string.\n *\n * @param {Object|String} str String to trim.\n * @returns {String} A new string representing `str` stripped of control\n *     characters and whitespace from its beginning.\n * @public\n */\nfunction trimLeft(str) {\n  return (str ? str : '').toString().replace(controlOrWhitespace, '');\n}\n\n/**\n * These are the parse rules for the URL parser, it informs the parser\n * about:\n *\n * 0. The char it Needs to parse, if it's a string it should be done using\n *    indexOf, RegExp using exec and NaN means set as current value.\n * 1. The property we should set when parsing this value.\n * 2. Indication if it's backwards or forward parsing, when set as number it's\n *    the value of extra chars that should be split off.\n * 3. Inherit from location if non existing in the parser.\n * 4. `toLowerCase` the resulting value.\n */\nvar rules = [\n  ['#', 'hash'],                        // Extract from the back.\n  ['?', 'query'],                       // Extract from the back.\n  function sanitize(address, url) {     // Sanitize what is left of the address\n    return isSpecial(url.protocol) ? address.replace(/\\\\/g, '/') : address;\n  },\n  ['/', 'pathname'],                    // Extract from the back.\n  ['@', 'auth', 1],                     // Extract from the front.\n  [NaN, 'host', undefined, 1, 1],       // Set left over value.\n  [/:(\\d*)$/, 'port', undefined, 1],    // RegExp the back.\n  [NaN, 'hostname', undefined, 1, 1]    // Set left over.\n];\n\n/**\n * These properties should not be copied or inherited from. This is only needed\n * for all non blob URL's as a blob URL does not include a hash, only the\n * origin.\n *\n * @type {Object}\n * @private\n */\nvar ignore = { hash: 1, query: 1 };\n\n/**\n * The location object differs when your code is loaded through a normal page,\n * Worker or through a worker using a blob. And with the blobble begins the\n * trouble as the location object will contain the URL of the blob, not the\n * location of the page where our code is loaded in. The actual origin is\n * encoded in the `pathname` so we can thankfully generate a good \"default\"\n * location from it so we can generate proper relative URL's again.\n *\n * @param {Object|String} loc Optional default location object.\n * @returns {Object} lolcation object.\n * @public\n */\nfunction lolcation(loc) {\n  var globalVar;\n\n  if (typeof window !== 'undefined') globalVar = window;\n  else if (typeof global !== 'undefined') globalVar = global;\n  else if (typeof self !== 'undefined') globalVar = self;\n  else globalVar = {};\n\n  var location = globalVar.location || {};\n  loc = loc || location;\n\n  var finaldestination = {}\n    , type = typeof loc\n    , key;\n\n  if ('blob:' === loc.protocol) {\n    finaldestination = new Url(unescape(loc.pathname), {});\n  } else if ('string' === type) {\n    finaldestination = new Url(loc, {});\n    for (key in ignore) delete finaldestination[key];\n  } else if ('object' === type) {\n    for (key in loc) {\n      if (key in ignore) continue;\n      finaldestination[key] = loc[key];\n    }\n\n    if (finaldestination.slashes === undefined) {\n      finaldestination.slashes = slashes.test(loc.href);\n    }\n  }\n\n  return finaldestination;\n}\n\n/**\n * Check whether a protocol scheme is special.\n *\n * @param {String} The protocol scheme of the URL\n * @return {Boolean} `true` if the protocol scheme is special, else `false`\n * @private\n */\nfunction isSpecial(scheme) {\n  return (\n    scheme === 'file:' ||\n    scheme === 'ftp:' ||\n    scheme === 'http:' ||\n    scheme === 'https:' ||\n    scheme === 'ws:' ||\n    scheme === 'wss:'\n  );\n}\n\n/**\n * @typedef ProtocolExtract\n * @type Object\n * @property {String} protocol Protocol matched in the URL, in lowercase.\n * @property {Boolean} slashes `true` if protocol is followed by \"//\", else `false`.\n * @property {String} rest Rest of the URL that is not part of the protocol.\n */\n\n/**\n * Extract protocol information from a URL with/without double slash (\"//\").\n *\n * @param {String} address URL we want to extract from.\n * @param {Object} location\n * @return {ProtocolExtract} Extracted information.\n * @private\n */\nfunction extractProtocol(address, location) {\n  address = trimLeft(address);\n  address = address.replace(CRHTLF, '');\n  location = location || {};\n\n  var match = protocolre.exec(address);\n  var protocol = match[1] ? match[1].toLowerCase() : '';\n  var forwardSlashes = !!match[2];\n  var otherSlashes = !!match[3];\n  var slashesCount = 0;\n  var rest;\n\n  if (forwardSlashes) {\n    if (otherSlashes) {\n      rest = match[2] + match[3] + match[4];\n      slashesCount = match[2].length + match[3].length;\n    } else {\n      rest = match[2] + match[4];\n      slashesCount = match[2].length;\n    }\n  } else {\n    if (otherSlashes) {\n      rest = match[3] + match[4];\n      slashesCount = match[3].length;\n    } else {\n      rest = match[4]\n    }\n  }\n\n  if (protocol === 'file:') {\n    if (slashesCount >= 2) {\n      rest = rest.slice(2);\n    }\n  } else if (isSpecial(protocol)) {\n    rest = match[4];\n  } else if (protocol) {\n    if (forwardSlashes) {\n      rest = rest.slice(2);\n    }\n  } else if (slashesCount >= 2 && isSpecial(location.protocol)) {\n    rest = match[4];\n  }\n\n  return {\n    protocol: protocol,\n    slashes: forwardSlashes || isSpecial(protocol),\n    slashesCount: slashesCount,\n    rest: rest\n  };\n}\n\n/**\n * Resolve a relative URL pathname against a base URL pathname.\n *\n * @param {String} relative Pathname of the relative URL.\n * @param {String} base Pathname of the base URL.\n * @return {String} Resolved pathname.\n * @private\n */\nfunction resolve(relative, base) {\n  if (relative === '') return base;\n\n  var path = (base || '/').split('/').slice(0, -1).concat(relative.split('/'))\n    , i = path.length\n    , last = path[i - 1]\n    , unshift = false\n    , up = 0;\n\n  while (i--) {\n    if (path[i] === '.') {\n      path.splice(i, 1);\n    } else if (path[i] === '..') {\n      path.splice(i, 1);\n      up++;\n    } else if (up) {\n      if (i === 0) unshift = true;\n      path.splice(i, 1);\n      up--;\n    }\n  }\n\n  if (unshift) path.unshift('');\n  if (last === '.' || last === '..') path.push('');\n\n  return path.join('/');\n}\n\n/**\n * The actual URL instance. Instead of returning an object we've opted-in to\n * create an actual constructor as it's much more memory efficient and\n * faster and it pleases my OCD.\n *\n * It is worth noting that we should not use `URL` as class name to prevent\n * clashes with the global URL instance that got introduced in browsers.\n *\n * @constructor\n * @param {String} address URL we want to parse.\n * @param {Object|String} [location] Location defaults for relative paths.\n * @param {Boolean|Function} [parser] Parser for the query string.\n * @private\n */\nfunction Url(address, location, parser) {\n  address = trimLeft(address);\n  address = address.replace(CRHTLF, '');\n\n  if (!(this instanceof Url)) {\n    return new Url(address, location, parser);\n  }\n\n  var relative, extracted, parse, instruction, index, key\n    , instructions = rules.slice()\n    , type = typeof location\n    , url = this\n    , i = 0;\n\n  //\n  // The following if statements allows this module two have compatibility with\n  // 2 different API:\n  //\n  // 1. Node.js's `url.parse` api which accepts a URL, boolean as arguments\n  //    where the boolean indicates that the query string should also be parsed.\n  //\n  // 2. The `URL` interface of the browser which accepts a URL, object as\n  //    arguments. The supplied object will be used as default values / fall-back\n  //    for relative paths.\n  //\n  if ('object' !== type && 'string' !== type) {\n    parser = location;\n    location = null;\n  }\n\n  if (parser && 'function' !== typeof parser) parser = qs.parse;\n\n  location = lolcation(location);\n\n  //\n  // Extract protocol information before running the instructions.\n  //\n  extracted = extractProtocol(address || '', location);\n  relative = !extracted.protocol && !extracted.slashes;\n  url.slashes = extracted.slashes || relative && location.slashes;\n  url.protocol = extracted.protocol || location.protocol || '';\n  address = extracted.rest;\n\n  //\n  // When the authority component is absent the URL starts with a path\n  // component.\n  //\n  if (\n    extracted.protocol === 'file:' && (\n      extracted.slashesCount !== 2 || windowsDriveLetter.test(address)) ||\n    (!extracted.slashes &&\n      (extracted.protocol ||\n        extracted.slashesCount < 2 ||\n        !isSpecial(url.protocol)))\n  ) {\n    instructions[3] = [/(.*)/, 'pathname'];\n  }\n\n  for (; i < instructions.length; i++) {\n    instruction = instructions[i];\n\n    if (typeof instruction === 'function') {\n      address = instruction(address, url);\n      continue;\n    }\n\n    parse = instruction[0];\n    key = instruction[1];\n\n    if (parse !== parse) {\n      url[key] = address;\n    } else if ('string' === typeof parse) {\n      index = parse === '@'\n        ? address.lastIndexOf(parse)\n        : address.indexOf(parse);\n\n      if (~index) {\n        if ('number' === typeof instruction[2]) {\n          url[key] = address.slice(0, index);\n          address = address.slice(index + instruction[2]);\n        } else {\n          url[key] = address.slice(index);\n          address = address.slice(0, index);\n        }\n      }\n    } else if ((index = parse.exec(address))) {\n      url[key] = index[1];\n      address = address.slice(0, index.index);\n    }\n\n    url[key] = url[key] || (\n      relative && instruction[3] ? location[key] || '' : ''\n    );\n\n    //\n    // Hostname, host and protocol should be lowercased so they can be used to\n    // create a proper `origin`.\n    //\n    if (instruction[4]) url[key] = url[key].toLowerCase();\n  }\n\n  //\n  // Also parse the supplied query string in to an object. If we're supplied\n  // with a custom parser as function use that instead of the default build-in\n  // parser.\n  //\n  if (parser) url.query = parser(url.query);\n\n  //\n  // If the URL is relative, resolve the pathname against the base URL.\n  //\n  if (\n      relative\n    && location.slashes\n    && url.pathname.charAt(0) !== '/'\n    && (url.pathname !== '' || location.pathname !== '')\n  ) {\n    url.pathname = resolve(url.pathname, location.pathname);\n  }\n\n  //\n  // Default to a / for pathname if none exists. This normalizes the URL\n  // to always have a /\n  //\n  if (url.pathname.charAt(0) !== '/' && isSpecial(url.protocol)) {\n    url.pathname = '/' + url.pathname;\n  }\n\n  //\n  // We should not add port numbers if they are already the default port number\n  // for a given protocol. As the host also contains the port number we're going\n  // override it with the hostname which contains no port number.\n  //\n  if (!required(url.port, url.protocol)) {\n    url.host = url.hostname;\n    url.port = '';\n  }\n\n  //\n  // Parse down the `auth` for the username and password.\n  //\n  url.username = url.password = '';\n\n  if (url.auth) {\n    index = url.auth.indexOf(':');\n\n    if (~index) {\n      url.username = url.auth.slice(0, index);\n      url.username = encodeURIComponent(decodeURIComponent(url.username));\n\n      url.password = url.auth.slice(index + 1);\n      url.password = encodeURIComponent(decodeURIComponent(url.password))\n    } else {\n      url.username = encodeURIComponent(decodeURIComponent(url.auth));\n    }\n\n    url.auth = url.password ? url.username +':'+ url.password : url.username;\n  }\n\n  url.origin = url.protocol !== 'file:' && isSpecial(url.protocol) && url.host\n    ? url.protocol +'//'+ url.host\n    : 'null';\n\n  //\n  // The href is just the compiled result.\n  //\n  url.href = url.toString();\n}\n\n/**\n * This is convenience method for changing properties in the URL instance to\n * insure that they all propagate correctly.\n *\n * @param {String} part          Property we need to adjust.\n * @param {Mixed} value          The newly assigned value.\n * @param {Boolean|Function} fn  When setting the query, it will be the function\n *                               used to parse the query.\n *                               When setting the protocol, double slash will be\n *                               removed from the final url if it is true.\n * @returns {URL} URL instance for chaining.\n * @public\n */\nfunction set(part, value, fn) {\n  var url = this;\n\n  switch (part) {\n    case 'query':\n      if ('string' === typeof value && value.length) {\n        value = (fn || qs.parse)(value);\n      }\n\n      url[part] = value;\n      break;\n\n    case 'port':\n      url[part] = value;\n\n      if (!required(value, url.protocol)) {\n        url.host = url.hostname;\n        url[part] = '';\n      } else if (value) {\n        url.host = url.hostname +':'+ value;\n      }\n\n      break;\n\n    case 'hostname':\n      url[part] = value;\n\n      if (url.port) value += ':'+ url.port;\n      url.host = value;\n      break;\n\n    case 'host':\n      url[part] = value;\n\n      if (port.test(value)) {\n        value = value.split(':');\n        url.port = value.pop();\n        url.hostname = value.join(':');\n      } else {\n        url.hostname = value;\n        url.port = '';\n      }\n\n      break;\n\n    case 'protocol':\n      url.protocol = value.toLowerCase();\n      url.slashes = !fn;\n      break;\n\n    case 'pathname':\n    case 'hash':\n      if (value) {\n        var char = part === 'pathname' ? '/' : '#';\n        url[part] = value.charAt(0) !== char ? char + value : value;\n      } else {\n        url[part] = value;\n      }\n      break;\n\n    case 'username':\n    case 'password':\n      url[part] = encodeURIComponent(value);\n      break;\n\n    case 'auth':\n      var index = value.indexOf(':');\n\n      if (~index) {\n        url.username = value.slice(0, index);\n        url.username = encodeURIComponent(decodeURIComponent(url.username));\n\n        url.password = value.slice(index + 1);\n        url.password = encodeURIComponent(decodeURIComponent(url.password));\n      } else {\n        url.username = encodeURIComponent(decodeURIComponent(value));\n      }\n  }\n\n  for (var i = 0; i < rules.length; i++) {\n    var ins = rules[i];\n\n    if (ins[4]) url[ins[1]] = url[ins[1]].toLowerCase();\n  }\n\n  url.auth = url.password ? url.username +':'+ url.password : url.username;\n\n  url.origin = url.protocol !== 'file:' && isSpecial(url.protocol) && url.host\n    ? url.protocol +'//'+ url.host\n    : 'null';\n\n  url.href = url.toString();\n\n  return url;\n}\n\n/**\n * Transform the properties back in to a valid and full URL string.\n *\n * @param {Function} stringify Optional query stringify function.\n * @returns {String} Compiled version of the URL.\n * @public\n */\nfunction toString(stringify) {\n  if (!stringify || 'function' !== typeof stringify) stringify = qs.stringify;\n\n  var query\n    , url = this\n    , host = url.host\n    , protocol = url.protocol;\n\n  if (protocol && protocol.charAt(protocol.length - 1) !== ':') protocol += ':';\n\n  var result =\n    protocol +\n    ((url.protocol && url.slashes) || isSpecial(url.protocol) ? '//' : '');\n\n  if (url.username) {\n    result += url.username;\n    if (url.password) result += ':'+ url.password;\n    result += '@';\n  } else if (url.password) {\n    result += ':'+ url.password;\n    result += '@';\n  } else if (\n    url.protocol !== 'file:' &&\n    isSpecial(url.protocol) &&\n    !host &&\n    url.pathname !== '/'\n  ) {\n    //\n    // Add back the empty userinfo, otherwise the original invalid URL\n    // might be transformed into a valid one with `url.pathname` as host.\n    //\n    result += '@';\n  }\n\n  //\n  // Trailing colon is removed from `url.host` when it is parsed. If it still\n  // ends with a colon, then add back the trailing colon that was removed. This\n  // prevents an invalid URL from being transformed into a valid one.\n  //\n  if (host[host.length - 1] === ':' || (port.test(url.hostname) && !url.port)) {\n    host += ':';\n  }\n\n  result += host + url.pathname;\n\n  query = 'object' === typeof url.query ? stringify(url.query) : url.query;\n  if (query) result += '?' !== query.charAt(0) ? '?'+ query : query;\n\n  if (url.hash) result += url.hash;\n\n  return result;\n}\n\nUrl.prototype = { set: set, toString: toString };\n\n//\n// Expose the URL parser and some additional properties that might be useful for\n// others or testing.\n//\nUrl.extractProtocol = extractProtocol;\nUrl.location = lolcation;\nUrl.trimLeft = trimLeft;\nUrl.qs = qs;\n\nmodule.exports = Url;\n"]}