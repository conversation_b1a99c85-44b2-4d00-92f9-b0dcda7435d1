{"_from": "debug@^3.2.7", "_id": "debug@3.2.7", "_inBundle": false, "_integrity": "sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==", "_location": "/sockjs-client/debug", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "debug@^3.2.7", "name": "debug", "escapedName": "debug", "rawSpec": "^3.2.7", "saveSpec": null, "fetchSpec": "^3.2.7"}, "_requiredBy": ["/sockjs-client"], "_resolved": "https://registry.npmmirror.com/debug/-/debug-3.2.7.tgz", "_shasum": "72580b7e9145fb39b6676f9c5e5fb100b934179a", "_spec": "debug@^3.2.7", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\sockjs-client", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "browser": "./src/browser.js", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"ms": "^2.1.1"}, "deprecated": false, "description": "small debugging utility", "devDependencies": {"@babel/cli": "^7.0.0", "@babel/core": "^7.0.0", "@babel/preset-env": "^7.0.0", "browserify": "14.4.0", "chai": "^3.5.0", "concurrently": "^3.1.0", "coveralls": "^3.0.2", "istanbul": "^0.4.5", "karma": "^3.0.0", "karma-chai": "^0.1.0", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "mocha": "^5.2.0", "mocha-lcov-reporter": "^1.2.0", "rimraf": "^2.5.4", "xo": "^0.23.0"}, "files": ["src", "node.js", "dist/debug.js", "LICENSE", "README.md"], "homepage": "https://github.com/visionmedia/debug#readme", "keywords": ["debug", "log", "debugger"], "license": "MIT", "main": "./src/index.js", "name": "debug", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "unpkg": "./dist/debug.js", "version": "3.2.7"}