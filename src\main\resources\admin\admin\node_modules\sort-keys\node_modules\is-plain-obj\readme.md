# is-plain-obj [![Build Status](https://travis-ci.org/sindresorhus/is-plain-obj.svg?branch=master)](https://travis-ci.org/sindresorhus/is-plain-obj)

> Check if a value is a plain object

An object is plain if it's created by either `{}`, `new Object()` or `Object.create(null)`.


## Install

```
$ npm install --save is-plain-obj
```


## Usage

```js
var isPlainObj = require('is-plain-obj');

isPlainObj({foo: 'bar'});
//=> true

isPlainObj([1, 2, 3]);
//=> false
```


## Related

- [is-obj](https://github.com/sindresorhus/is-obj) - Check if a value is an object


## License

MIT © [Sindre Sorhus](http://sindresorhus.com)
