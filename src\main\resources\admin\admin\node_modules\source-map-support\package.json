{"_from": "source-map-support@~0.5.12", "_id": "source-map-support@0.5.21", "_inBundle": false, "_integrity": "sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==", "_location": "/source-map-support", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "source-map-support@~0.5.12", "name": "source-map-support", "escapedName": "source-map-support", "rawSpec": "~0.5.12", "saveSpec": null, "fetchSpec": "~0.5.12"}, "_requiredBy": ["/terser"], "_resolved": "https://registry.npmmirror.com/source-map-support/-/source-map-support-0.5.21.tgz", "_shasum": "04fe7c7f9e1ed2d662233c28cb2b35b9f63f6e4f", "_spec": "source-map-support@~0.5.12", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\terser", "bugs": {"url": "https://github.com/evanw/node-source-map-support/issues"}, "bundleDependencies": false, "dependencies": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}, "deprecated": false, "description": "Fixes stack traces for files with source maps", "devDependencies": {"browserify": "^4.2.3", "coffeescript": "^1.12.7", "http-server": "^0.11.1", "mocha": "^3.5.3", "webpack": "^1.15.0"}, "homepage": "https://github.com/evanw/node-source-map-support#readme", "license": "MIT", "main": "./source-map-support.js", "name": "source-map-support", "repository": {"type": "git", "url": "git+https://github.com/evanw/node-source-map-support.git"}, "scripts": {"build": "node build.js", "prepublish": "npm run build", "serve-tests": "http-server -p 1336", "test": "mocha"}, "version": "0.5.21"}