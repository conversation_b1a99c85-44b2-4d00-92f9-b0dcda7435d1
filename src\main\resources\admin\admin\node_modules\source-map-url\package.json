{"_from": "source-map-url@^0.4.0", "_id": "source-map-url@0.4.1", "_inBundle": false, "_integrity": "sha512-cPiFOTLUKvJFIg4SKVScy4ilPPW6rFgMgfuZJPNoDuMs3nC1HbMUycBoJw77xFIp6z1UJQJOfx6C9GMH80DiTw==", "_location": "/source-map-url", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "source-map-url@^0.4.0", "name": "source-map-url", "escapedName": "source-map-url", "rawSpec": "^0.4.0", "saveSpec": null, "fetchSpec": "^0.4.0"}, "_requiredBy": ["/source-map-resolve"], "_resolved": "https://registry.npmmirror.com/source-map-url/-/source-map-url-0.4.1.tgz", "_shasum": "0af66605a745a5a2f91cf1bbf8a7afbc283dec56", "_spec": "source-map-url@^0.4.0", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\source-map-resolve", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/lydell/source-map-url/issues"}, "bundleDependencies": false, "deprecated": "See https://github.com/lydell/source-map-url#deprecated", "description": "Tools for working with sourceMappingURL comments.", "devDependencies": {"expect.js": "~0.3.1", "jshint": "~2.4.3", "mocha": "~1.17.1"}, "homepage": "https://github.com/lydell/source-map-url#readme", "keywords": ["source map", "sourceMappingURL", "comment", "annotation"], "license": "MIT", "main": "source-map-url.js", "name": "source-map-url", "repository": {"type": "git", "url": "git+https://github.com/lydell/source-map-url.git"}, "scripts": {"lint": "jshint source-map-url.js test/ ", "test": "npm run lint && npm run unit", "unit": "mocha"}, "testling": {"harness": "mocha", "files": "test/*.js", "browsers": ["ie/8..latest", "chrome/latest", "firefox/latest", "opera/12", "opera/latest", "safari/5", "iphone/6", "android-browser/4"]}, "version": "0.4.1"}