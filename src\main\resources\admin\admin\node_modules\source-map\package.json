{"_from": "source-map@^0.6.1", "_id": "source-map@0.6.1", "_inBundle": false, "_integrity": "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==", "_location": "/source-map", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "source-map@^0.6.1", "name": "source-map", "escapedName": "source-map", "rawSpec": "^0.6.1", "saveSpec": null, "fetchSpec": "^0.6.1"}, "_requiredBy": ["/@intervolga/optimize-cssnano-plugin/postcss", "/@types/uglify-js", "/@types/webpack", "/@vue/compiler-sfc", "/@vue/component-compiler-utils", "/@vue/component-compiler-utils/postcss", "/autoprefixer/postcss", "/clean-css", "/css-declaration-sorter/postcss", "/css-loader/postcss", "/cssnano-preset-default/postcss", "/cssnano-util-raw-cache/postcss", "/cssnano/postcss", "/icss-utils/postcss", "/merge-source-map", "/postcss-calc/postcss", "/postcss-colormin/postcss", "/postcss-convert-values/postcss", "/postcss-discard-comments/postcss", "/postcss-discard-duplicates/postcss", "/postcss-discard-empty/postcss", "/postcss-discard-overridden/postcss", "/postcss-loader/postcss", "/postcss-merge-longhand/postcss", "/postcss-merge-rules/postcss", "/postcss-minify-font-values/postcss", "/postcss-minify-gradients/postcss", "/postcss-minify-params/postcss", "/postcss-minify-selectors/postcss", "/postcss-modules-extract-imports/postcss", "/postcss-modules-local-by-default/postcss", "/postcss-modules-scope/postcss", "/postcss-modules-values/postcss", "/postcss-normalize-charset/postcss", "/postcss-normalize-display-values/postcss", "/postcss-normalize-positions/postcss", "/postcss-normalize-repeat-style/postcss", "/postcss-normalize-string/postcss", "/postcss-normalize-timing-functions/postcss", "/postcss-normalize-unicode/postcss", "/postcss-normalize-url/postcss", "/postcss-normalize-whitespace/postcss", "/postcss-ordered-values/postcss", "/postcss-reduce-initial/postcss", "/postcss-reduce-transforms/postcss", "/postcss-svgo/postcss", "/postcss-unique-selectors/postcss", "/source-map-support", "/stylehacks/postcss", "/terser", "/terser-webpack-plugin", "/uglify-js", "/webpack-sources"], "_resolved": "https://registry.npmmirror.com/source-map/-/source-map-0.6.1.tgz", "_shasum": "74722af32e9614e9c287a8d0bbde48b5e2f1a263", "_spec": "source-map@^0.6.1", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\@vue\\compiler-sfc", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/mozilla/source-map/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "usrbincc", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "azu", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jeff<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dj<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "deprecated": false, "description": "Generates and consumes source maps", "devDependencies": {"doctoc": "^0.15.0", "webpack": "^1.12.0"}, "engines": {"node": ">=0.10.0"}, "files": ["source-map.js", "source-map.d.ts", "lib/", "dist/source-map.debug.js", "dist/source-map.js", "dist/source-map.min.js", "dist/source-map.min.js.map"], "homepage": "https://github.com/mozilla/source-map", "license": "BSD-3-<PERSON><PERSON>", "main": "./source-map.js", "name": "source-map", "repository": {"type": "git", "url": "git+ssh://**************/mozilla/source-map.git"}, "scripts": {"build": "webpack --color", "test": "npm run build && node test/run-tests.js", "toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md"}, "typings": "source-map", "version": "0.6.1"}