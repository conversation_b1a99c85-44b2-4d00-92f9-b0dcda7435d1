{"_from": "spdx-correct@^3.0.0", "_id": "spdx-correct@3.2.0", "_inBundle": false, "_integrity": "sha512-kN9dJbvnySHULIluDHy32WHRUu3Og7B9sbY7tsFLctQkIqnMh3hErYgdMjTYuqmcXX+lK5T1lnUt3G7zNswmZA==", "_location": "/spdx-correct", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "spdx-correct@^3.0.0", "name": "spdx-correct", "escapedName": "spdx-correct", "rawSpec": "^3.0.0", "saveSpec": null, "fetchSpec": "^3.0.0"}, "_requiredBy": ["/validate-npm-package-license"], "_resolved": "https://registry.npmmirror.com/spdx-correct/-/spdx-correct-3.2.0.tgz", "_shasum": "4f5ab0668f0059e34f9c00dce331784a12de4e9c", "_spec": "spdx-correct@^3.0.0", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\validate-npm-package-license", "bugs": {"url": "https://github.com/jslicense/spdx-correct.js/issues"}, "bundleDependencies": false, "dependencies": {"spdx-expression-parse": "^3.0.0", "spdx-license-ids": "^3.0.0"}, "deprecated": false, "description": "correct invalid SPDX expressions", "devDependencies": {"defence-cli": "^3.0.1", "replace-require-self": "^1.0.0", "standard": "^14.3.4", "standard-markdown": "^6.0.0", "tape": "^5.0.1"}, "files": ["index.js"], "homepage": "https://github.com/jslicense/spdx-correct.js#readme", "keywords": ["SPDX", "law", "legal", "license", "metadata"], "license": "Apache-2.0", "name": "spdx-correct", "repository": {"type": "git", "url": "git+https://github.com/jslicense/spdx-correct.js.git"}, "scripts": {"lint": "standard && standard-markdown README.md", "test": "defence README.md | replace-require-self | node && node test.js"}, "version": "3.2.0"}