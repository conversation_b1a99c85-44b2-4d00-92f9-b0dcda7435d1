{"_from": "spdx-exceptions@^2.1.0", "_id": "spdx-exceptions@2.5.0", "_inBundle": false, "_integrity": "sha512-PiU42r+xO4UbUS1buo3LPJkjlO7430Xn5SVAhdpzzsPHsjbYVflnnFdATgabnLude+Cqu25p6N+g2lw/PFsa4w==", "_location": "/spdx-exceptions", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "spdx-exceptions@^2.1.0", "name": "spdx-exceptions", "escapedName": "spdx-exceptions", "rawSpec": "^2.1.0", "saveSpec": null, "fetchSpec": "^2.1.0"}, "_requiredBy": ["/spdx-expression-parse"], "_resolved": "https://registry.npmmirror.com/spdx-exceptions/-/spdx-exceptions-2.5.0.tgz", "_shasum": "5d607d27fc806f66d7b64a766650fa890f04ed66", "_spec": "spdx-exceptions@^2.1.0", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\spdx-expression-parse", "author": {"name": "The Linux Foundation"}, "bugs": {"url": "https://github.com/kemitchell/spdx-exceptions.json/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://kemitchell.com/"}], "deprecated": false, "description": "list of SPDX standard license exceptions", "files": ["index.json", "deprecated.json"], "homepage": "https://github.com/kemitchell/spdx-exceptions.json#readme", "license": "CC-BY-3.0", "name": "spdx-exceptions", "repository": {"type": "git", "url": "git+https://github.com/kemitchell/spdx-exceptions.json.git"}, "scripts": {"build": "node build.js", "latest": "node latest.js"}, "version": "2.5.0"}