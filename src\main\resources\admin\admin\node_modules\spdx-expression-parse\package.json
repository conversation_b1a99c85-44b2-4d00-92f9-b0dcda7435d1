{"_from": "spdx-expression-parse@^3.0.0", "_id": "spdx-expression-parse@3.0.1", "_inBundle": false, "_integrity": "sha512-cbqHunsQWnJNE6KhVSMsMeH5H/L9EpymbzqTQ3uLwNCLZ1Q481oWaofqH7nO6V07xlXwY6PhQdQ2IedWx/ZK4Q==", "_location": "/spdx-expression-parse", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "spdx-expression-parse@^3.0.0", "name": "spdx-expression-parse", "escapedName": "spdx-expression-parse", "rawSpec": "^3.0.0", "saveSpec": null, "fetchSpec": "^3.0.0"}, "_requiredBy": ["/spdx-correct", "/validate-npm-package-license"], "_resolved": "https://registry.npmmirror.com/spdx-expression-parse/-/spdx-expression-parse-3.0.1.tgz", "_shasum": "cf70f50482eefdc98e3ce0a6833e4a53ceeba679", "_spec": "spdx-expression-parse@^3.0.0", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\validate-npm-package-license", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://kemitchell.com"}, "bugs": {"url": "https://github.com/jslicense/spdx-expression-parse.js/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://cscott.net"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://kemitchell.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"spdx-exceptions": "^2.1.0", "spdx-license-ids": "^3.0.0"}, "deprecated": false, "description": "parse SPDX license expressions", "devDependencies": {"defence-cli": "^3.0.1", "replace-require-self": "^1.0.0", "standard": "^14.1.0"}, "files": ["AUTHORS", "index.js", "parse.js", "scan.js"], "homepage": "https://github.com/jslicense/spdx-expression-parse.js#readme", "keywords": ["SPDX", "law", "legal", "license", "metadata", "package", "package.json", "standards"], "license": "MIT", "name": "spdx-expression-parse", "repository": {"type": "git", "url": "git+https://github.com/jslicense/spdx-expression-parse.js.git"}, "scripts": {"lint": "standard", "test": "npm run test:suite && npm run test:readme", "test:readme": "defence -i javascript README.md | replace-require-self | node", "test:suite": "node test.js"}, "version": "3.0.1"}