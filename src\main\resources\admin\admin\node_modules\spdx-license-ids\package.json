{"_from": "spdx-license-ids@^3.0.0", "_id": "spdx-license-ids@3.0.21", "_inBundle": false, "_integrity": "sha512-Bvg/8F5XephndSK3JffaRqdT+gyhfqIPwDHpX80tJrF8QQRYMo8sNMeaZ2Dp5+jhwKnUmIOyFFQfHRkjJm5nXg==", "_location": "/spdx-license-ids", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "spdx-license-ids@^3.0.0", "name": "spdx-license-ids", "escapedName": "spdx-license-ids", "rawSpec": "^3.0.0", "saveSpec": null, "fetchSpec": "^3.0.0"}, "_requiredBy": ["/spdx-correct", "/spdx-expression-parse"], "_resolved": "https://registry.npmmirror.com/spdx-license-ids/-/spdx-license-ids-3.0.21.tgz", "_shasum": "6d6e980c9df2b6fc905343a3b2d702a6239536c3", "_spec": "spdx-license-ids@^3.0.0", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\spdx-correct", "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/shinnn"}, "bugs": {"url": "https://github.com/jslicense/spdx-license-ids/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A list of SPDX license identifiers", "files": ["deprecated.json", "index.json"], "homepage": "https://github.com/jslicense/spdx-license-ids#readme", "keywords": ["spdx", "license", "licenses", "id", "identifier", "identifiers", "json", "array", "oss"], "license": "CC0-1.0", "name": "spdx-license-ids", "repository": {"type": "git", "url": "git+https://github.com/jslicense/spdx-license-ids.git"}, "scripts": {"build": "node build.js", "latest": "node latest.js", "pretest": "npm run build", "test": "node test.js"}, "version": "3.0.21"}