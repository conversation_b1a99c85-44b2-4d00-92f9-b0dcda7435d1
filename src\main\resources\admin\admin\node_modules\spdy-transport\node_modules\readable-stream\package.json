{"_from": "readable-stream@^3.0.6", "_id": "readable-stream@3.6.2", "_inBundle": false, "_integrity": "sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==", "_location": "/spdy-transport/readable-stream", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "readable-stream@^3.0.6", "name": "readable-stream", "escapedName": "readable-stream", "rawSpec": "^3.0.6", "saveSpec": null, "fetchSpec": "^3.0.6"}, "_requiredBy": ["/spdy-transport"], "_resolved": "https://registry.npmmirror.com/readable-stream/-/readable-stream-3.6.2.tgz", "_shasum": "56a9b36ea965c00c5a93ef31eb111a0f11056967", "_spec": "readable-stream@^3.0.6", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\spdy-transport", "browser": {"util": false, "worker_threads": false, "./errors": "./errors-browser.js", "./readable.js": "./readable-browser.js", "./lib/internal/streams/from.js": "./lib/internal/streams/from-browser.js", "./lib/internal/streams/stream.js": "./lib/internal/streams/stream-browser.js"}, "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "bundleDependencies": false, "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "deprecated": false, "description": "Streams3, a user-land copy of the stream library from Node.js", "devDependencies": {"@babel/cli": "^7.2.0", "@babel/core": "^7.2.0", "@babel/polyfill": "^7.0.0", "@babel/preset-env": "^7.2.0", "airtap": "0.0.9", "assert": "^1.4.0", "bl": "^2.0.0", "deep-strict-equal": "^0.2.0", "events.once": "^2.0.2", "glob": "^7.1.2", "gunzip-maybe": "^1.4.1", "hyperquest": "^2.1.3", "lolex": "^2.6.0", "nyc": "^11.0.0", "pump": "^3.0.0", "rimraf": "^2.6.2", "tap": "^12.0.0", "tape": "^4.9.0", "tar-fs": "^1.16.2", "util-promisify": "^2.1.0"}, "engines": {"node": ">= 6"}, "homepage": "https://github.com/nodejs/readable-stream#readme", "keywords": ["readable", "stream", "pipe"], "license": "MIT", "main": "readable.js", "name": "readable-stream", "nyc": {"include": ["lib/**.js"]}, "repository": {"type": "git", "url": "git://github.com/nodejs/readable-stream.git"}, "scripts": {"ci": "TAP=1 tap --no-esm test/parallel/*.js test/ours/*.js | tee test.tap", "cover": "nyc npm test", "report": "nyc report --reporter=lcov", "test": "tap -J --no-esm test/parallel/*.js test/ours/*.js", "test-browser-local": "airtap --open --local -- test/browser.js", "test-browsers": "airtap --sauce-connect --loopback airtap.local -- test/browser.js", "update-browser-errors": "babel -o errors-browser.js errors.js"}, "version": "3.6.2"}