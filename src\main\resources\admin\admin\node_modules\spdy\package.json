{"_from": "spdy@^4.0.2", "_id": "spdy@4.0.2", "_inBundle": false, "_integrity": "sha512-r46gZQZQV+Kl9oItvl1JZZqJKGr+oEkB08A6BzkiR7593/7IbtuncXHd2YoYeTsG4157ZssMu9KYvUHLcjcDoA==", "_location": "/spdy", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "spdy@^4.0.2", "name": "spdy", "escapedName": "spdy", "rawSpec": "^4.0.2", "saveSpec": null, "fetchSpec": "^4.0.2"}, "_requiredBy": ["/webpack-dev-server"], "_resolved": "https://registry.npmmirror.com/spdy/-/spdy-4.0.2.tgz", "_shasum": "b74f466203a3eda452c02492b91fb9e84a27677b", "_spec": "spdy@^4.0.2", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\webpack-dev-server", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/spdy-http2/node-spdy/issues", "email": "<EMAIL>"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"debug": "^4.1.0", "handle-thing": "^2.0.0", "http-deceiver": "^1.2.7", "select-hose": "^2.0.0", "spdy-transport": "^3.0.0"}, "deprecated": false, "description": "Implementation of the SPDY protocol on node.js.", "devDependencies": {"istanbul": "^0.4.5", "mocha": "^6.2.3", "pre-commit": "^1.2.2", "standard": "^13.1.0"}, "engines": {"node": ">=6.0.0"}, "homepage": "https://github.com/indutny/node-spdy", "keywords": ["spdy"], "license": "MIT", "main": "./lib/spdy", "name": "spdy", "optionalDependencies": {}, "pre-commit": ["lint", "test"], "repository": {"type": "git", "url": "git://github.com/indutny/node-spdy.git"}, "scripts": {"coverage": "istanbul cover node_modules/.bin/_mocha -- --reporter=spec test/**/*-test.js", "lint": "standard", "test": "mocha --reporter=spec test/*-test.js"}, "version": "4.0.2"}