{"_from": "sshpk@^1.7.0", "_id": "sshpk@1.18.0", "_inBundle": false, "_integrity": "sha512-2p2KJZTSqQ/I3+HX42EpYOa2l3f8Erv8MWKsy2I9uf4wA7yFIkXRffYdsx86y6z4vHtV8u7g+pPlr8/4ouAxsQ==", "_location": "/sshpk", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "sshpk@^1.7.0", "name": "sshpk", "escapedName": "sshpk", "rawSpec": "^1.7.0", "saveSpec": null, "fetchSpec": "^1.7.0"}, "_requiredBy": ["/http-signature"], "_resolved": "https://registry.npmmirror.com/sshpk/-/sshpk-1.18.0.tgz", "_shasum": "1663e55cddf4d688b86a46b77f0d5fe363aba028", "_spec": "sshpk@^1.7.0", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\http-signature", "author": {"name": "Joyent, Inc"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "bugs": {"url": "https://github.com/arekinath/node-sshpk/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "getpass": "^0.1.1", "jsbn": "~0.1.0", "safer-buffer": "^2.0.2", "tweetnacl": "~0.14.0"}, "deprecated": false, "description": "A library for finding and using SSH public keys", "devDependencies": {"benchmark": "^1.0.0", "sinon": "^1.17.2", "tape": "^3.5.0", "temp": "^0.8.2"}, "directories": {"bin": "./bin", "lib": "./lib", "man": "./man/man1"}, "engines": {"node": ">=0.10.0"}, "homepage": "https://github.com/arekinath/node-sshpk#readme", "license": "MIT", "main": "lib/index.js", "man": ["G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\sshpk\\man\\man1\\sshpk-conv.1", "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\sshpk\\man\\man1\\sshpk-sign.1", "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\sshpk\\man\\man1\\sshpk-verify.1"], "name": "sshpk", "optionalDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/joyent/node-sshpk.git"}, "scripts": {"test": "tape test/*.js"}, "version": "1.18.0"}