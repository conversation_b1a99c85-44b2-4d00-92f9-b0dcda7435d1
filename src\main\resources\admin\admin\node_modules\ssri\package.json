{"_from": "ssri@^6.0.1", "_id": "ssri@6.0.2", "_inBundle": false, "_integrity": "sha512-cepbSq/neFK7xB6A50KHN0xHDotYzq58wWCa5LeWqnPrHG8GzfEjO/4O8kpmcGW+oaxkvhEJCWgbgNk4/ZV93Q==", "_location": "/ssri", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "ssri@^6.0.1", "name": "ssri", "escapedName": "ssri", "rawSpec": "^6.0.1", "saveSpec": null, "fetchSpec": "^6.0.1"}, "_requiredBy": ["/cacache"], "_resolved": "https://registry.npmmirror.com/ssri/-/ssri-6.0.2.tgz", "_shasum": "157939134f20464e7301ddba3e90ffa8f7728ac5", "_spec": "ssri@^6.0.1", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\cacache", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/zkat/ssri/issues"}, "bundleDependencies": false, "config": {"nyc": {"exclude": ["node_modules/**", "test/**"]}}, "dependencies": {"figgy-pudding": "^3.5.1"}, "deprecated": false, "description": "Standard Subresource Integrity library --  parses, serializes, generates, and verifies integrity metadata according to the SRI spec.", "devDependencies": {"nyc": "^11.4.1", "standard": "^10.0.3", "standard-version": "^4.3.0", "tap": "^11.1.0", "weallbehave": "^1.2.0", "weallcontribute": "^1.0.8"}, "files": ["*.js"], "homepage": "https://github.com/zkat/ssri#readme", "keywords": ["w3c", "web", "security", "integrity", "checksum", "hashing", "subresource integrity", "sri", "sri hash", "sri string", "sri generator", "html"], "license": "ISC", "main": "index.js", "name": "ssri", "repository": {"type": "git", "url": "git+https://github.com/zkat/ssri.git"}, "scripts": {"postrelease": "npm publish && git push --follow-tags", "prerelease": "npm t", "pretest": "standard", "release": "standard-version -s", "test": "tap -J --coverage test/*.js", "update-coc": "weallbehave -o . && git add CODE_OF_CONDUCT.md && git commit -m 'docs(coc): updated CODE_OF_CONDUCT.md'", "update-contrib": "weallcontribute -o . && git add CONTRIBUTING.md && git commit -m 'docs(contributing): updated CONTRIBUTING.md'"}, "version": "6.0.2"}