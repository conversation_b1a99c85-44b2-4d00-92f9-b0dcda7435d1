{"_from": "stackframe@^1.3.4", "_id": "stackframe@1.3.4", "_inBundle": false, "_integrity": "sha512-oeVtt7eWQS+Na6F//S4kJ2K2VbRlS9D43mAlMyVpVWovy9o+jfgH8O9agzANzaiLjclA0oYzUXEM4PurhSUChw==", "_location": "/stackframe", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "stackframe@^1.3.4", "name": "stackframe", "escapedName": "stackframe", "rawSpec": "^1.3.4", "saveSpec": null, "fetchSpec": "^1.3.4"}, "_requiredBy": ["/error-stack-parser"], "_resolved": "https://registry.npmmirror.com/stackframe/-/stackframe-1.3.4.tgz", "_shasum": "b881a004c8c149a5e8efef37d51b16e412943310", "_spec": "stackframe@^1.3.4", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\error-stack-parser", "bugs": {"url": "https://github.com/stacktracejs/stackframe/issues"}, "bundleDependencies": false, "deprecated": false, "description": "JS Object representation of a stack frame", "devDependencies": {"eslint": "^8.17.0", "jasmine": "^4.1.0", "jasmine-core": "^4.1.1", "karma": "^6.3.20", "karma-chrome-launcher": "^3.1.1", "karma-coverage": "^2.2.0", "karma-coveralls": "^2.1.0", "karma-firefox-launcher": "^2.1.2", "karma-ie-launcher": "^1.0.0", "karma-jasmine": "^4.0.2", "karma-opera-launcher": "^1.0.0", "karma-phantomjs-launcher": "^1.0.4", "karma-safari-launcher": "^1.0.0", "karma-sauce-launcher": "^4.3.6", "karma-spec-reporter": "^0.0.34", "uglify-es": "^3.3.9"}, "files": ["LICENSE", "README.md", "stackframe.js", "stackframe.d.ts", "dist/"], "homepage": "https://www.stacktracejs.com", "keywords": ["stacktrace", "error", "debugger", "stack frame"], "license": "MIT", "main": "./stackframe.js", "maintainers": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.eriwen.com"}, {"name": "<PERSON>", "email": "v<PERSON><PERSON><PERSON><PERSON>@gmail.com", "url": "https://github.com/victor-ho<PERSON><PERSON>ov"}, {"name": "<PERSON>", "url": "https://github.com/oliversalzburg"}], "name": "stackframe", "repository": {"type": "git", "url": "git://github.com/stacktracejs/stackframe.git"}, "scripts": {"lint": "eslint", "prepare": "cp stackframe.js dist/ && uglifyjs stackframe.js -o dist/stackframe.min.js --compress --mangle --source-map \"url=stackframe.min.js.map\"", "test": "karma start karma.conf.js --single-run", "test-ci": "karma start karma.conf.ci.js --single-run", "test-pr": "karma start karma.conf.js --single-run --browsers Firefox,Chrome_No_Sandbox"}, "typings": "./stackframe.d.ts", "version": "1.3.4"}