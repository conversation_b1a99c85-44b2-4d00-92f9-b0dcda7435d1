{"_from": "stdout-stream@^1.4.0", "_id": "stdout-stream@1.4.1", "_inBundle": false, "_integrity": "sha512-j4emi03KXqJWcIeF8eIXkjMFN1Cmb8gUlDYGeBALLPo5qdyTfA9bOtl8m33lRoC+vFMkP3gl0WsDr6+gzxbbTA==", "_location": "/stdout-stream", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "stdout-stream@^1.4.0", "name": "stdout-stream", "escapedName": "stdout-stream", "rawSpec": "^1.4.0", "saveSpec": null, "fetchSpec": "^1.4.0"}, "_requiredBy": ["/node-sass"], "_resolved": "https://registry.npmmirror.com/stdout-stream/-/stdout-stream-1.4.1.tgz", "_shasum": "5ac174cdd5cd726104aa0c0b2bd83815d8d535de", "_spec": "stdout-stream@^1.4.0", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\node-sass", "bugs": {"url": "https://github.com/mafintosh/stdout-stream/issues"}, "bundleDependencies": false, "dependencies": {"readable-stream": "^2.0.1"}, "deprecated": false, "description": "Non-blocking stdout stream", "devDependencies": {"tape": "~2.12.3"}, "homepage": "https://github.com/mafintosh/stdout-stream#readme", "license": "MIT", "name": "stdout-stream", "repository": {"type": "git", "url": "git+https://github.com/mafintosh/stdout-stream.git"}, "scripts": {"test": "tape test/index.js"}, "version": "1.4.1"}