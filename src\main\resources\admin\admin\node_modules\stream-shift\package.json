{"_from": "stream-shift@^1.0.0", "_id": "stream-shift@1.0.3", "_inBundle": false, "_integrity": "sha512-76ORR0DO1o1hlKwTbi/DM3EXWGf3ZJYO8cXX5RJwnul2DEg2oyoZyjLNoQM8WsvZiFKCRfC1O0J7iCvie3RZmQ==", "_location": "/stream-shift", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "stream-shift@^1.0.0", "name": "stream-shift", "escapedName": "stream-shift", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/duplexify", "/stream-each"], "_resolved": "https://registry.npmmirror.com/stream-shift/-/stream-shift-1.0.3.tgz", "_shasum": "85b8fab4d71010fc3ba8772e8046cc49b8a3864b", "_spec": "stream-shift@^1.0.0", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\duplexify", "author": {"name": "<PERSON>", "url": "@mafintosh"}, "bugs": {"url": "https://github.com/mafintosh/stream-shift/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Returns the next buffer/object in a stream's readable queue", "devDependencies": {"standard": "^7.1.2", "tape": "^4.6.0", "through2": "^2.0.1"}, "homepage": "https://github.com/mafintosh/stream-shift", "license": "MIT", "main": "index.js", "name": "stream-shift", "repository": {"type": "git", "url": "git+https://github.com/mafintosh/stream-shift.git"}, "scripts": {"test": "standard && tape test.js"}, "version": "1.0.3"}