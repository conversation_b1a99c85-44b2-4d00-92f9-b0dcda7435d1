{"_from": "string.prototype.trim@^1.2.10", "_id": "string.prototype.trim@1.2.10", "_inBundle": false, "_integrity": "sha512-Rs66F0P/1kedk5lyYyH9uBzuiI/kNRmwJAR9quK6VOtIpZ2G+hMZd+HQbbv25MgCA6gEffoMZYxlTod4WcdrKA==", "_location": "/string.prototype.trim", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "string.prototype.trim@^1.2.10", "name": "string.prototype.trim", "escapedName": "string.prototype.trim", "rawSpec": "^1.2.10", "saveSpec": null, "fetchSpec": "^1.2.10"}, "_requiredBy": ["/es-abstract"], "_resolved": "https://registry.npmmirror.com/string.prototype.trim/-/string.prototype.trim-1.2.10.tgz", "_shasum": "40b2dd5ee94c959b4dcfb1d65ce72e90da480c81", "_spec": "string.prototype.trim@^1.2.10", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\es-abstract", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/es-shims/String.prototype.trim/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.2", "define-data-property": "^1.1.4", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-object-atoms": "^1.0.0", "has-property-descriptors": "^1.0.2"}, "deprecated": false, "description": "ES5 spec-compliant shim for String.prototype.trim", "devDependencies": {"@es-shims/api": "^2.5.1", "@ljharb/eslint-config": "^21.1.1", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "eslint": "=8.8.0", "for-each": "^0.3.3", "functions-have-names": "^1.2.3", "has-strict-mode": "^1.0.1", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/es-shims/String.prototype.trim#readme", "keywords": ["String.prototype.trim", "string", "ES5", "shim", "trim", "polyfill", "es-shim API"], "license": "MIT", "main": "index.js", "name": "string.prototype.trim", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git://github.com/es-shims/String.prototype.trim.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "es-shim-api --bound", "posttest": "npx npm@'>= 10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/9.0..latest", "firefox/4.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/11.6..latest", "opera/next", "safari/5.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "version": "1.2.10"}