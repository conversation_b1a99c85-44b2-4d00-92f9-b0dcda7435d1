{"_from": "ansi-regex@^5.0.1", "_id": "ansi-regex@5.0.1", "_inBundle": false, "_integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==", "_location": "/strip-ansi/ansi-regex", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "ansi-regex@^5.0.1", "name": "ansi-regex", "escapedName": "ansi-regex", "rawSpec": "^5.0.1", "saveSpec": null, "fetchSpec": "^5.0.1"}, "_requiredBy": ["/strip-ansi"], "_resolved": "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-5.0.1.tgz", "_shasum": "082cb2c89c9fe8659a311a53bd6a4dc5301db304", "_spec": "ansi-regex@^5.0.1", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\strip-ansi", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/chalk/ansi-regex/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Regular expression for matching ANSI escape codes", "devDependencies": {"ava": "^2.4.0", "tsd": "^0.9.0", "xo": "^0.25.3"}, "engines": {"node": ">=8"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/chalk/ansi-regex#readme", "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "license": "MIT", "name": "ansi-regex", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-regex.git"}, "scripts": {"test": "xo && ava && tsd", "view-supported": "node fixtures/view-codes.js"}, "version": "5.0.1"}