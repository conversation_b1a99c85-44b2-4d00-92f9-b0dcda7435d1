{"_from": "strip-ansi@^6.0.0", "_id": "strip-ansi@6.0.1", "_inBundle": false, "_integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==", "_location": "/strip-ansi", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "strip-ansi@^6.0.0", "name": "strip-ansi", "escapedName": "strip-ansi", "rawSpec": "^6.0.0", "saveSpec": null, "fetchSpec": "^6.0.0"}, "_requiredBy": ["/@soda/friendly-errors-webpack-plugin", "/@vue/cli-shared-utils", "/cliui", "/cliui/wrap-ansi", "/inquirer", "/string-width", "/wrap-ansi", "/yargs/cliui"], "_resolved": "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz", "_shasum": "9e26c63d30f53443e9489495b2105d37b67a85d9", "_spec": "strip-ansi@^6.0.0", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\@vue\\cli-shared-utils", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/chalk/strip-ansi/issues"}, "bundleDependencies": false, "dependencies": {"ansi-regex": "^5.0.1"}, "deprecated": false, "description": "Strip ANSI escape codes from a string", "devDependencies": {"ava": "^2.4.0", "tsd": "^0.10.0", "xo": "^0.25.3"}, "engines": {"node": ">=8"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/chalk/strip-ansi#readme", "keywords": ["strip", "trim", "remove", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "name": "strip-ansi", "repository": {"type": "git", "url": "git+https://github.com/chalk/strip-ansi.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "6.0.1"}