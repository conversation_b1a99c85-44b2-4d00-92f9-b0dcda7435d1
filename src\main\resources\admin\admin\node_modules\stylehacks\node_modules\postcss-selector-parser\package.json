{"_from": "postcss-selector-parser@^3.0.0", "_id": "postcss-selector-parser@3.1.2", "_inBundle": false, "_integrity": "sha512-h7fJ/5uWuRVyOtkO45pnt1Ih40CEleeyCHzipqAZO2e5H20g25Y48uYnFUiShvY4rZWNJ/Bib/KVPmanaCtOhA==", "_location": "/stylehacks/postcss-selector-parser", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "postcss-selector-parser@^3.0.0", "name": "postcss-selector-parser", "escapedName": "postcss-selector-parser", "rawSpec": "^3.0.0", "saveSpec": null, "fetchSpec": "^3.0.0"}, "_requiredBy": ["/stylehacks"], "_resolved": "https://registry.npmmirror.com/postcss-selector-parser/-/postcss-selector-parser-3.1.2.tgz", "_shasum": "b310f5c4c0fdaf76f94902bbaa30db6aa84f5270", "_spec": "postcss-selector-parser@^3.0.0", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\stylehacks", "ava": {"require": "babel-register", "concurrency": 5}, "bugs": {"url": "https://github.com/postcss/postcss-selector-parser/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twitter.com/chrise<PERSON><PERSON>"}], "dependencies": {"dot-prop": "^5.2.0", "indexes-of": "^1.0.1", "uniq": "^1.0.1"}, "deprecated": false, "description": "> Selector parser with built in methods for working with selector strings.", "devDependencies": {"ava": "^0.20.0", "babel-cli": "^6.4.0", "babel-core": "^6.4.0", "babel-plugin-add-module-exports": "^0.2.0", "babel-plugin-precompile-charcodes": "^1.0.0", "babel-preset-es2015": "^6.3.13", "babel-preset-es2015-loose": "^7.0.0", "babel-preset-stage-0": "^6.3.13", "babel-register": "^6.9.0", "coveralls": "^2.11.6", "del-cli": "^0.2.0", "eslint": "^3.0.0", "eslint-config-cssnano": "^3.0.0", "eslint-plugin-babel": "^3.3.0", "eslint-plugin-import": "^1.10.2", "glob": "^7.0.3", "minimist": "^1.2.0", "nyc": "^10.0.0", "postcss": "^6.0.6"}, "engines": {"node": ">=8"}, "eslintConfig": {"extends": "cssnano"}, "files": ["API.md", "CHANGELOG.md", "LICENSE-MIT", "dist", "postcss-selector-parser.d.ts"], "homepage": "https://github.com/postcss/postcss-selector-parser", "license": "MIT", "main": "dist/index.js", "name": "postcss-selector-parser", "nyc": {"exclude": ["node_modules", "**/__tests__"]}, "repository": {"type": "git", "url": "git+https://github.com/postcss/postcss-selector-parser.git"}, "scripts": {"prepublish": "del-cli dist && BABEL_ENV=publish babel src --out-dir dist --ignore /__tests__/", "pretest": "eslint src", "report": "nyc report --reporter=html", "test": "nyc ava src/__tests__/*.js"}, "types": "postcss-selector-parser.d.ts", "version": "3.1.2"}