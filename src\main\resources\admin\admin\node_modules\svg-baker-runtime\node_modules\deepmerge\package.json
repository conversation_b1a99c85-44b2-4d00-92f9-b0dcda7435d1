{"_from": "deepmerge@1.3.2", "_id": "deepmerge@1.3.2", "_inBundle": false, "_integrity": "sha512-qjMjTrk+RKv/sp4RPDpV5CnKhxjFI9p+GkLBOls5A8EEElldYWCWA9zceAkmfd0xIo2aU1nxiaLFoiya2sb6Cg==", "_location": "/svg-baker-runtime/deepmerge", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "deepmerge@1.3.2", "name": "deepmerge", "escapedName": "deepmerge", "rawSpec": "1.3.2", "saveSpec": null, "fetchSpec": "1.3.2"}, "_requiredBy": ["/svg-baker-runtime"], "_resolved": "https://registry.npmmirror.com/deepmerge/-/deepmerge-1.3.2.tgz", "_shasum": "1663691629d4dbfe364fa12a2a4f0aa86aa3a050", "_spec": "deepmerge@1.3.2", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\svg-baker-runtime", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/KyleAMathews/deepmerge/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "A library for deep (recursive) merging of Javascript objects", "devDependencies": {"jsmd": "0.3.1", "tap": "~7.1.2"}, "engines": {"node": ">=0.10.0"}, "homepage": "https://github.com/KyleAMathews/deepmerge", "keywords": ["merge", "deep", "extend", "copy", "clone", "recursive"], "license": "MIT", "main": "index", "name": "deepmerge", "repository": {"type": "git", "url": "git://github.com/KyleAMathews/deepmerge.git"}, "scripts": {"test": "tap test/*.js && jsmd README.markdown"}, "version": "1.3.2"}