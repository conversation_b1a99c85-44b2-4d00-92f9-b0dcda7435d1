{"_from": "svg-baker-runtime@^1.4.0", "_id": "svg-baker-runtime@1.4.7", "_inBundle": false, "_integrity": "sha512-Zorfwwj5+lWjk/oxwSMsRdS2sPQQdTmmsvaSpzU+i9ZWi3zugHLt6VckWfnswphQP0LmOel3nggpF5nETbt6xw==", "_location": "/svg-baker-runtime", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "svg-baker-runtime@^1.4.0", "name": "svg-baker-runtime", "escapedName": "svg-baker-runtime", "rawSpec": "^1.4.0", "saveSpec": null, "fetchSpec": "^1.4.0"}, "_requiredBy": ["/svg-sprite-loader"], "_resolved": "https://registry.npmmirror.com/svg-baker-runtime/-/svg-baker-runtime-1.4.7.tgz", "_shasum": "f4720637f5b6202eef6378d81f1fead0815f8a4e", "_spec": "svg-baker-runtime@^1.4.0", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\svg-sprite-loader", "author": {"name": "JetBrains"}, "browser": "browser-sprite.js", "bundleDependencies": false, "dependencies": {"deepmerge": "1.3.2", "mitt": "1.1.2", "svg-baker": "^1.7.0"}, "deprecated": false, "description": "", "devDependencies": {"babel-core": "^6.24.1", "babel-loader": "6.4.1", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-plugin-transform-runtime": "6.23.0", "babel-preset-es2015": "6.24.0", "bluebird": "^3.5.0", "chai": "3.5.0", "electron": "^1.6.6", "glob": "^7.1.1", "json-loader": "^0.5.4", "karma": "1.6.0", "karma-chai-plugins": "0.9.0", "karma-chrome-launcher": "^2.0.0", "karma-mocha": "^1.3.0", "karma-mocha-reporter": "^2.2.3", "karma-sourcemap-loader": "^0.3.7", "karma-webpack": "^2.0.3", "query-string": "4.3.4", "rollup": "^0.41.6", "rollup-plugin-buble": "^0.15.0", "rollup-plugin-commonjs": "^8.0.2", "rollup-plugin-node-resolve": "^3.0.0", "string-loader": "^0.0.1", "wallaby-webpack": "0.0.38", "webpack": "2.4.1"}, "files": ["src/", "browser-sprite.js", "browser-symbol.js", "sprite.js", "symbol.js", "README.md"], "license": "MIT", "main": "browser-sprite.js", "module": "src/browser-sprite.js", "name": "svg-baker-runtime", "repository": {"type": "git", "url": "https://github.com/JetBrains/svg-mixer/tree/v1/packages/svg-baker-runtime"}, "scripts": {"build": "node scripts/build-runtime", "generate-test-data": "node scripts/generate-test-data", "lint": "eslint src test", "test": "yarn build && karma start"}, "version": "1.4.7"}