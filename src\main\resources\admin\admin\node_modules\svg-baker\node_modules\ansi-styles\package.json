{"_from": "ansi-styles@^2.2.1", "_id": "ansi-styles@2.2.1", "_inBundle": false, "_integrity": "sha512-kmCevFghRiWM7HB5zTPULl4r9bVFSWjz62MhqizDGUrq2NWuNMQyuv4tHHoKJHs69M/MF64lEcHdYIocrdWQYA==", "_location": "/svg-baker/ansi-styles", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "ansi-styles@^2.2.1", "name": "ansi-styles", "escapedName": "ansi-styles", "rawSpec": "^2.2.1", "saveSpec": null, "fetchSpec": "^2.2.1"}, "_requiredBy": ["/svg-baker/chalk"], "_resolved": "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-2.2.1.tgz", "_shasum": "b432dd3358b634cf75e1e4664368240533c1ddbe", "_spec": "ansi-styles@^2.2.1", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\svg-baker\\node_modules\\chalk", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/chalk/ansi-styles/issues"}, "bundleDependencies": false, "deprecated": false, "description": "ANSI escape codes for styling strings in the terminal", "devDependencies": {"mocha": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/chalk/ansi-styles#readme", "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "jbnicolai.com"}], "name": "ansi-styles", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-styles.git"}, "scripts": {"test": "mocha"}, "version": "2.2.1"}