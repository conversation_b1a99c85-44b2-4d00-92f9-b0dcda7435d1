{"_from": "define-property@^1.0.0", "_id": "define-property@1.0.0", "_inBundle": false, "_integrity": "sha512-cZTYKFWspt9jZsMscWo8sc/5lbPC9Q0N5nBLgb+Yd915iL3udB1uFgS3B8YCx66UVHq018DAVFoee7x+gxggeA==", "_location": "/svg-baker/define-property", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "define-property@^1.0.0", "name": "define-property", "escapedName": "define-property", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/svg-baker/micromatch"], "_resolved": "https://registry.npmmirror.com/define-property/-/define-property-1.0.0.tgz", "_shasum": "769ebaaf3f4a63aad3af9e8d304c9bbe79bfb0e6", "_spec": "define-property@^1.0.0", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\svg-baker\\node_modules\\micromatch", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/define-property/issues"}, "bundleDependencies": false, "dependencies": {"is-descriptor": "^1.0.0"}, "deprecated": false, "description": "Define a non-enumerable property on an object.", "devDependencies": {"gulp-format-md": "^0.1.12", "mocha": "^3.2.0"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/define-property", "keywords": ["define", "define-property", "enumerable", "key", "non", "non-enumerable", "object", "prop", "property", "value"], "license": "MIT", "main": "index.js", "name": "define-property", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/define-property.git"}, "scripts": {"test": "mocha"}, "verb": {"related": {"list": ["extend-shallow", "merge-deep", "assign-deep", "mixin-deep"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}}, "version": "1.0.0"}