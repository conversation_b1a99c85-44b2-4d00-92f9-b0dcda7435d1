{"_from": "kind-of@^5.0.2", "_id": "kind-of@5.1.0", "_inBundle": false, "_integrity": "sha512-NGEErnH6F2vUuXDh+OlbcKW7/wOcfdRHaZ7VWtqCztfHri/++YKmP51OdWeGPuqCOba6kk2OTe5d02VmTB80Pw==", "_location": "/svg-baker/kind-of", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "kind-of@^5.0.2", "name": "kind-of", "escapedName": "kind-of", "rawSpec": "^5.0.2", "saveSpec": null, "fetchSpec": "^5.0.2"}, "_requiredBy": ["/svg-baker/micromatch"], "_resolved": "https://registry.npmmirror.com/kind-of/-/kind-of-5.1.0.tgz", "_shasum": "729c91e2d857b7a419a1f9aa65685c4c33f5845d", "_spec": "kind-of@^5.0.2", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\svg-baker\\node_modules\\micromatch", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/kind-of/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://dtothefp.github.io/me"}, {"name": "<PERSON>", "url": "https://twitter.com/aretecode"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "kensheedlo.com"}, {"name": "laggingreflex", "url": "https://github.com/laggingreflex"}, {"name": "<PERSON>", "url": "https://miguelmota.com"}, {"name": "<PERSON>", "url": "http://about.me/peterdehaan"}, {"name": "tunnckoCore", "url": "https://i.am.charlike.online"}], "deprecated": false, "description": "Get the native type of a value.", "devDependencies": {"ansi-bold": "^0.1.1", "benchmarked": "^1.1.1", "browserify": "^14.4.0", "gulp-format-md": "^0.1.12", "matched": "^0.4.4", "mocha": "^3.4.2", "type-of": "^2.0.1", "typeof": "^1.0.0"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/kind-of", "keywords": ["arguments", "array", "boolean", "check", "date", "function", "is", "is-type", "is-type-of", "kind", "kind-of", "number", "object", "of", "regexp", "string", "test", "type", "type-of", "typeof", "types"], "license": "MIT", "main": "index.js", "name": "kind-of", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/kind-of.git"}, "scripts": {"prepublish": "browserify -o browser.js -e index.js -s index --bare", "test": "mocha"}, "verb": {"related": {"list": ["is-glob", "is-number", "is-primitive"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "reflinks": ["type-of", "typeof", "verb"]}, "version": "5.1.0"}