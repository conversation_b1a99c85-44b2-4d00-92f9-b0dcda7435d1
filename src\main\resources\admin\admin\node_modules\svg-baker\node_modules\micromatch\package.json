{"_from": "micromatch@3.1.0", "_id": "micromatch@3.1.0", "_inBundle": false, "_integrity": "sha512-3StSelAE+hnRvMs8IdVW7Uhk8CVed5tp+kLLGlBP6WiRAXS21GPGu/Nat4WNPXj2Eoc24B02SaeoyozPMfj0/g==", "_location": "/svg-baker/micromatch", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "micromatch@3.1.0", "name": "micromatch", "escapedName": "micromatch", "rawSpec": "3.1.0", "saveSpec": null, "fetchSpec": "3.1.0"}, "_requiredBy": ["/svg-baker"], "_resolved": "https://registry.npmmirror.com/micromatch/-/micromatch-3.1.0.tgz", "_shasum": "5102d4eaf20b6997d6008e3acfe1c44a3fa815e2", "_spec": "micromatch@3.1.0", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\svg-baker", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/micromatch/micromatch/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON>", "url": "amilajack.com"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/TrySound"}, {"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/es128"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://ultcombo.js.org"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "https://kolarik.sk"}, {"name": "<PERSON>", "url": "paulmillr.com"}, {"name": "<PERSON>", "url": "https://github.com/tomByrer"}, {"name": "tunnckoCore", "url": "https://i.am.charlike.online"}, {"name": "<PERSON>", "url": "http://rumkin.com"}, {"url": "https://github.com/<PERSON><PERSON>ey"}], "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.2.2", "define-property": "^1.0.0", "extend-shallow": "^2.0.1", "extglob": "^2.0.2", "fragment-cache": "^0.2.1", "kind-of": "^5.0.2", "nanomatch": "^1.2.1", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "deprecated": false, "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch.", "devDependencies": {"bash-match": "^1.0.2", "for-own": "^1.0.0", "gulp": "^3.9.1", "gulp-format-md": "^1.0.0", "gulp-istanbul": "^1.1.2", "gulp-mocha": "^3.0.1", "gulp-unused": "^0.2.1", "is-windows": "^1.0.1", "minimatch": "^3.0.4", "minimist": "^1.2.0", "mocha": "^3.5.0", "multimatch": "^2.1.0"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js", "lib"], "homepage": "https://github.com/micromatch/micromatch", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "micromatch", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "license": "MIT", "lintDeps": {"dependencies": {"options": {"lock": {"snapdragon": "^0.8.1"}}}, "devDependencies": {"files": {"options": {"ignore": ["benchmark/**"]}}}}, "main": "index.js", "name": "micromatch", "repository": {"type": "git", "url": "git+https://github.com/micromatch/micromatch.git"}, "scripts": {"test": "mocha"}, "verb": {"toc": "collapsible", "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "helpers": ["./benchmark/helper.js"], "related": {"list": ["braces", "expand-brackets", "extglob", "fill-range", "nanomatch"]}, "lint": {"reflinks": true}, "reflinks": ["expand-brackets", "extglob", "glob-object", "minimatch", "multimatch", "snapdragon"]}, "version": "3.1.0"}