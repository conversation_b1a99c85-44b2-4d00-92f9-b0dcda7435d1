{"_from": "svg-baker@^1.4.0", "_id": "svg-baker@1.7.0", "_inBundle": false, "_integrity": "sha512-nibslMbkXOIkqKVrfcncwha45f97fGuAOn1G99YwnwTj8kF9YiM6XexPcUso97NxOm6GsP0SIvYVIosBis1xLg==", "_location": "/svg-baker", "_phantomChildren": {"arr-diff": "4.0.0", "array-unique": "0.3.2", "big.js": "5.2.2", "braces": "2.3.2", "emojis-list": "3.0.0", "escape-string-regexp": "1.0.5", "extglob": "2.0.4", "fragment-cache": "0.2.1", "has-ansi": "2.0.0", "is-accessor-descriptor": "1.0.1", "is-data-descriptor": "1.0.1", "is-extendable": "0.1.1", "js-base64": "2.6.4", "minimist": "1.2.8", "nanomatch": "1.2.13", "object.pick": "1.3.0", "regex-not": "1.0.2", "snapdragon": "0.8.2", "to-regex": "3.0.2"}, "_requested": {"type": "range", "registry": true, "raw": "svg-baker@^1.4.0", "name": "svg-baker", "escapedName": "svg-baker", "rawSpec": "^1.4.0", "saveSpec": null, "fetchSpec": "^1.4.0"}, "_requiredBy": ["/svg-baker-runtime", "/svg-sprite-loader"], "_resolved": "https://registry.npmmirror.com/svg-baker/-/svg-baker-1.7.0.tgz", "_shasum": "8367f78d875550c52fe4756f7303d5c5d7c2e9a7", "_spec": "svg-baker@^1.4.0", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\svg-sprite-loader", "author": {"name": "JetBrains"}, "bundleDependencies": false, "dependencies": {"bluebird": "^3.5.0", "clone": "^2.1.1", "he": "^1.1.1", "image-size": "^0.5.1", "loader-utils": "^1.1.0", "merge-options": "1.0.1", "micromatch": "3.1.0", "postcss": "^5.2.17", "postcss-prefix-selector": "^1.6.0", "posthtml-rename-id": "^1.0", "posthtml-svg-mode": "^1.0.3", "query-string": "^4.3.2", "traverse": "^0.6.6"}, "deprecated": false, "description": "", "files": ["lib/", "namespaces.js", "README.md"], "homepage": "https://github.com/JetBrains/svg-mixer/tree/v1", "license": "MIT", "main": "lib/compiler.js", "name": "svg-baker", "repository": {"type": "git", "url": "https://github.com/JetBrains/svg-mixer/tree/v1"}, "scripts": {"lint": "eslint lib test", "test": "nyc --reporter=lcov mocha --recursive -r ../../test/mocha-setup.js"}, "version": "1.7.0"}