{"_from": "svg-sprite-loader@4.1.3", "_id": "svg-sprite-loader@4.1.3", "_inBundle": false, "_integrity": "sha512-lOLDSJoyriYnOeGYc7nhxTDYj2vfdBVKpxIS/XK70//kA3VB55H89T1lct2OEClY4w5kQLZJAvDGQ41g3YTogQ==", "_location": "/svg-sprite-loader", "_phantomChildren": {"big.js": "5.2.2", "emojis-list": "3.0.0", "minimist": "1.2.8"}, "_requested": {"type": "version", "registry": true, "raw": "svg-sprite-loader@4.1.3", "name": "svg-sprite-loader", "escapedName": "svg-sprite-loader", "rawSpec": "4.1.3", "saveSpec": null, "fetchSpec": "4.1.3"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://registry.npmmirror.com/svg-sprite-loader/-/svg-sprite-loader-4.1.3.tgz", "_shasum": "d25cfa75a5c4e499f7b5282281db6eb3bda13fe0", "_spec": "svg-sprite-loader@4.1.3", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin", "author": {"name": "kisenka"}, "bugs": {"url": "https://github.com/kisenka/svg-sprite-loader/issues"}, "bundleDependencies": false, "config": {"commitizen": {"path": "node_modules/cz-customizable"}, "cz-customizable": {"config": ".cz-config.js"}}, "dependencies": {"bluebird": "^3.5.0", "deepmerge": "1.3.2", "domready": "1.0.8", "escape-string-regexp": "1.0.5", "html-webpack-plugin": "^3.2.0", "loader-utils": "^1.1.0", "svg-baker": "^1.4.0", "svg-baker-runtime": "^1.4.0", "url-slug": "2.0.0"}, "deprecated": false, "description": "Webpack loader for creating SVG sprites", "devDependencies": {"babel-core": "^6.24.1", "babel-loader": "^6.4.1", "babel-plugin-transform-object-rest-spread": "6.23.0", "babel-preset-es2015": "^6.24.0", "babel-preset-react": "6.24.1", "chai": "^3.5.0", "ci-publish": "^1.3.0", "codeclimate-test-reporter": "0.4.1", "commitizen": "2.9.6", "css-loader": "^0.28.0", "cz-customizable": "^5.0.0", "eslint": "^3.18.0", "eslint-config-airbnb-base": "^11.1.2", "eslint-plugin-import": "^2.2.0", "extract-text-webpack-plugin": "4.0.0-beta.0", "glob": "7.1.1", "html-loader": "^0.5.5", "husky": "^0.13.3", "istanbul": "^0.4.5", "memory-fs": "^0.4.1", "minimist": "^1.2.0", "mocha": "^3.2.0", "nyc": "^10.3.2", "pascal-case": "2.0.1", "react": "15.5.4", "react-dom": "15.5.4", "rollup": "^0.41.6", "rollup-plugin-buble": "^0.15.0", "rollup-plugin-commonjs": "^8.0.2", "rollup-plugin-node-resolve": "^3.0.0", "shelljs": "0.7.7", "standard-version": "4.0.0", "svgo": "^0.7.2", "svgo-loader": "^1.2.1", "validate-commit-msg": "^2.12.1", "wallaby-webpack": "0.0.38", "webpack": "^4.20.2", "webpack-cli": "^2.1.5", "webpack-toolkit": "^1.0.0"}, "engines": {"node": ">=6"}, "files": ["examples/**", "lib/**", "runtime/*.js", "plugin.js", "README.md", "LICENSE"], "homepage": "https://github.com/kisenka/svg-sprite-loader#readme", "keywords": ["svg", "sprite", "svg sprite", "svg stack", "webpack", "webpack2", "webpack3", "webpack loader", "webpack plugin"], "license": "MIT", "main": "lib/loader.js", "name": "svg-sprite-loader", "repository": {"type": "git", "url": "git+https://github.com/kisenka/svg-sprite-loader.git"}, "scripts": {"bootstrap": "node scripts/bootstrap", "build:examples": "npm run build:runtime && node scripts/build-examples-dll && node scripts/build-examples", "build:runtime": "node scripts/build-runtime.js", "commit": "git-cz", "commitmsg": "validate-commit-msg", "env": "node scripts/select-env", "lint": "eslint --quiet lib runtime scripts test", "precommit": "yarn lint", "prerelease": "yarn build:runtime && yarn lint && yarn test:all", "pretest": "yarn build:runtime", "release": "yarn build:examples && git add examples && standard-version -a && git push --follow-tags origin master", "release:dry-run": "standard-version --no-verify", "test": "mocha test/*.test.js", "test:all": "yarn test:webpack-2 && yarn test:webpack-3 && yarn test:webpack-4", "test:webpack-2": "yarn env webpack-2 && yarn test", "test:webpack-3": "yarn env webpack-3 && yarn test", "test:webpack-4": "yarn env webpack-4 && yarn test", "upload-coverage": "codeclimate-test-reporter < coverage/lcov.info"}, "version": "4.1.3"}