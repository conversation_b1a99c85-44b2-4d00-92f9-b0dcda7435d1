{"_from": "svgo@1.2.2", "_id": "svgo@1.2.2", "_inBundle": false, "_integrity": "sha512-rAfulcwp2D9jjdGu+0CuqlrAUin6bBWrpoqXWwKDZZZJfXcUXQSxLJOFJCQCSA0x0pP2U0TxSlJu2ROq5Bq6qA==", "_location": "/svgo", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "svgo@1.2.2", "name": "svgo", "escapedName": "svgo", "rawSpec": "1.2.2", "saveSpec": null, "fetchSpec": "1.2.2"}, "_requiredBy": ["#DEV:/", "/postcss-svgo"], "_resolved": "https://registry.npmmirror.com/svgo/-/svgo-1.2.2.tgz", "_shasum": "0253d34eccf2aed4ad4f283e11ee75198f9d7316", "_spec": "svgo@1.2.2", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/deepsweet"}, "bin": {"svgo": "bin/svgo"}, "bugs": {"url": "https://github.com/svg/svgo/issues", "email": "<EMAIL>"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/arikon"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/GreLI"}], "dependencies": {"chalk": "^2.4.1", "coa": "^2.0.2", "css-select": "^2.0.0", "css-select-base-adapter": "^0.1.1", "css-tree": "1.0.0-alpha.28", "css-url-regex": "^1.1.0", "csso": "^3.5.1", "js-yaml": "^3.13.1", "mkdirp": "~0.5.1", "object.values": "^1.1.0", "sax": "~1.2.4", "stable": "^0.1.8", "unquote": "~1.1.1", "util.promisify": "~1.0.0"}, "deprecated": "This SVGO version is no longer supported. Upgrade to v2.x.x.", "description": "Nodejs-based tool for optimizing SVG vector graphics files", "devDependencies": {"coveralls": "^3.0.3", "fs-extra": "~4.0.3", "istanbul": "~0.4.5", "jshint": "~2.9.5", "mocha": "~4.0.1", "mocha-istanbul": "~0.3.0", "mock-stdin": "~0.3.1", "should": "~13.1.2"}, "directories": {"bin": "./bin", "lib": "./lib", "example": "./examples"}, "engines": {"node": ">=4.0.0"}, "homepage": "https://github.com/svg/svgo", "keywords": ["svgo", "svg", "optimize", "minify"], "license": "MIT", "main": "./lib/svgo.js", "name": "svgo", "repository": {"type": "git", "url": "git://github.com/svg/svgo.git"}, "scripts": {"jshint": "npm run lint", "lint": "jshint --show-non-errors .", "test": "set NODE_ENV=test && mocha"}, "version": "1.2.2"}