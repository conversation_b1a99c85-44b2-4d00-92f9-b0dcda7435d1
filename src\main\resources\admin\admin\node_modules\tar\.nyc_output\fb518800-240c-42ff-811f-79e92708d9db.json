{"/Users/<USER>/dev/js/tar/lib/pack.js": {"path": "/Users/<USER>/dev/js/tar/lib/pack.js", "statementMap": {"0": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 21}}, "1": {"start": {"line": 6, "column": 18}, "end": {"line": 6, "column": 46}}, "2": {"start": {"line": 7, "column": 13}, "end": {"line": 7, "column": 37}}, "3": {"start": {"line": 8, "column": 11}, "end": {"line": 8, "column": 26}}, "4": {"start": {"line": 9, "column": 15}, "end": {"line": 9, "column": 34}}, "5": {"start": {"line": 10, "column": 25}, "end": {"line": 10, "column": 61}}, "6": {"start": {"line": 11, "column": 14}, "end": {"line": 11, "column": 40}}, "7": {"start": {"line": 12, "column": 10}, "end": {"line": 12, "column": 25}}, "8": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 41}}, "9": {"start": {"line": 14, "column": 13}, "end": {"line": 14, "column": 14}}, "10": {"start": {"line": 14, "column": 31}, "end": {"line": 14, "column": 41}}, "11": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 22}}, "12": {"start": {"line": 20, "column": 11}, "end": {"line": 20, "column": 15}}, "13": {"start": {"line": 21, "column": 2}, "end": {"line": 21, "column": 51}}, "14": {"start": {"line": 21, "column": 29}, "end": {"line": 21, "column": 51}}, "15": {"start": {"line": 23, "column": 2}, "end": {"line": 24, "column": 32}}, "16": {"start": {"line": 23, "column": 13}, "end": {"line": 23, "column": 52}}, "17": {"start": {"line": 24, "column": 7}, "end": {"line": 24, "column": 32}}, "18": {"start": {"line": 26, "column": 2}, "end": {"line": 26, "column": 20}}, "19": {"start": {"line": 28, "column": 2}, "end": {"line": 28, "column": 20}}, "20": {"start": {"line": 29, "column": 2}, "end": {"line": 29, "column": 20}}, "21": {"start": {"line": 30, "column": 2}, "end": {"line": 30, "column": 17}}, "22": {"start": {"line": 32, "column": 2}, "end": {"line": 32, "column": 25}}, "23": {"start": {"line": 33, "column": 2}, "end": {"line": 33, "column": 24}}, "24": {"start": {"line": 35, "column": 2}, "end": {"line": 35, "column": 21}}, "25": {"start": {"line": 36, "column": 2}, "end": {"line": 43, "column": 4}}, "26": {"start": {"line": 37, "column": 4}, "end": {"line": 37, "column": 41}}, "27": {"start": {"line": 37, "column": 35}, "end": {"line": 37, "column": 41}}, "28": {"start": {"line": 38, "column": 4}, "end": {"line": 38, "column": 22}}, "29": {"start": {"line": 39, "column": 4}, "end": {"line": 41, "column": 6}}, "30": {"start": {"line": 40, "column": 6}, "end": {"line": 40, "column": 25}}, "31": {"start": {"line": 42, "column": 4}, "end": {"line": 42, "column": 15}}, "32": {"start": {"line": 46, "column": 0}, "end": {"line": 57, "column": 1}}, "33": {"start": {"line": 48, "column": 2}, "end": {"line": 48, "column": 29}}, "34": {"start": {"line": 48, "column": 23}, "end": {"line": 48, "column": 29}}, "35": {"start": {"line": 49, "column": 2}, "end": {"line": 49, "column": 24}}, "36": {"start": {"line": 51, "column": 11}, "end": {"line": 51, "column": 15}}, "37": {"start": {"line": 52, "column": 2}, "end": {"line": 56, "column": 10}}, "38": {"start": {"line": 54, "column": 6}, "end": {"line": 54, "column": 24}}, "39": {"start": {"line": 59, "column": 0}, "end": {"line": 69, "column": 1}}, "40": {"start": {"line": 60, "column": 2}, "end": {"line": 60, "column": 68}}, "41": {"start": {"line": 60, "column": 40}, "end": {"line": 60, "column": 68}}, "42": {"start": {"line": 62, "column": 2}, "end": {"line": 62, "column": 72}}, "43": {"start": {"line": 62, "column": 19}, "end": {"line": 62, "column": 72}}, "44": {"start": {"line": 64, "column": 2}, "end": {"line": 64, "column": 17}}, "45": {"start": {"line": 65, "column": 2}, "end": {"line": 65, "column": 27}}, "46": {"start": {"line": 66, "column": 2}, "end": {"line": 66, "column": 17}}, "47": {"start": {"line": 67, "column": 2}, "end": {"line": 67, "column": 43}}, "48": {"start": {"line": 68, "column": 2}, "end": {"line": 68, "column": 25}}, "49": {"start": {"line": 71, "column": 0}, "end": {"line": 75, "column": 1}}, "50": {"start": {"line": 72, "column": 2}, "end": {"line": 72, "column": 21}}, "51": {"start": {"line": 73, "column": 2}, "end": {"line": 73, "column": 52}}, "52": {"start": {"line": 73, "column": 26}, "end": {"line": 73, "column": 52}}, "53": {"start": {"line": 74, "column": 2}, "end": {"line": 74, "column": 20}}, "54": {"start": {"line": 77, "column": 0}, "end": {"line": 82, "column": 1}}, "55": {"start": {"line": 78, "column": 2}, "end": {"line": 78, "column": 22}}, "56": {"start": {"line": 79, "column": 2}, "end": {"line": 79, "column": 53}}, "57": {"start": {"line": 79, "column": 26}, "end": {"line": 79, "column": 53}}, "58": {"start": {"line": 80, "column": 2}, "end": {"line": 80, "column": 21}}, "59": {"start": {"line": 81, "column": 2}, "end": {"line": 81, "column": 17}}, "60": {"start": {"line": 84, "column": 0}, "end": {"line": 88, "column": 1}}, "61": {"start": {"line": 85, "column": 2}, "end": {"line": 85, "column": 20}}, "62": {"start": {"line": 86, "column": 2}, "end": {"line": 86, "column": 24}}, "63": {"start": {"line": 87, "column": 2}, "end": {"line": 87, "column": 17}}, "64": {"start": {"line": 90, "column": 0}, "end": {"line": 233, "column": 1}}, "65": {"start": {"line": 91, "column": 11}, "end": {"line": 91, "column": 15}}, "66": {"start": {"line": 92, "column": 2}, "end": {"line": 94, "column": 3}}, "67": {"start": {"line": 93, "column": 4}, "end": {"line": 93, "column": 10}}, "68": {"start": {"line": 96, "column": 14}, "end": {"line": 96, "column": 32}}, "69": {"start": {"line": 98, "column": 2}, "end": {"line": 103, "column": 3}}, "70": {"start": {"line": 99, "column": 4}, "end": {"line": 101, "column": 5}}, "71": {"start": {"line": 100, "column": 6}, "end": {"line": 100, "column": 22}}, "72": {"start": {"line": 102, "column": 4}, "end": {"line": 102, "column": 10}}, "73": {"start": {"line": 105, "column": 2}, "end": {"line": 113, "column": 3}}, "74": {"start": {"line": 107, "column": 4}, "end": {"line": 107, "column": 29}}, "75": {"start": {"line": 108, "column": 4}, "end": {"line": 111, "column": 6}}, "76": {"start": {"line": 110, "column": 6}, "end": {"line": 110, "column": 19}}, "77": {"start": {"line": 112, "column": 4}, "end": {"line": 112, "column": 10}}, "78": {"start": {"line": 115, "column": 2}, "end": {"line": 115, "column": 23}}, "79": {"start": {"line": 117, "column": 2}, "end": {"line": 124, "column": 3}}, "80": {"start": {"line": 119, "column": 4}, "end": {"line": 119, "column": 24}}, "81": {"start": {"line": 120, "column": 4}, "end": {"line": 120, "column": 24}}, "82": {"start": {"line": 121, "column": 4}, "end": {"line": 121, "column": 18}}, "83": {"start": {"line": 122, "column": 4}, "end": {"line": 122, "column": 20}}, "84": {"start": {"line": 123, "column": 4}, "end": {"line": 123, "column": 10}}, "85": {"start": {"line": 134, "column": 13}, "end": {"line": 134, "column": 53}}, "86": {"start": {"line": 135, "column": 2}, "end": {"line": 138, "column": 3}}, "87": {"start": {"line": 137, "column": 4}, "end": {"line": 137, "column": 27}}, "88": {"start": {"line": 140, "column": 15}, "end": {"line": 140, "column": 17}}, "89": {"start": {"line": 142, "column": 2}, "end": {"line": 144, "column": 4}}, "90": {"start": {"line": 143, "column": 4}, "end": {"line": 143, "column": 30}}, "91": {"start": {"line": 146, "column": 2}, "end": {"line": 146, "column": 52}}, "92": {"start": {"line": 146, "column": 25}, "end": {"line": 146, "column": 52}}, "93": {"start": {"line": 148, "column": 2}, "end": {"line": 148, "column": 53}}, "94": {"start": {"line": 151, "column": 2}, "end": {"line": 153, "column": 3}}, "95": {"start": {"line": 152, "column": 4}, "end": {"line": 152, "column": 49}}, "96": {"start": {"line": 155, "column": 2}, "end": {"line": 156, "column": 29}}, "97": {"start": {"line": 156, "column": 4}, "end": {"line": 156, "column": 29}}, "98": {"start": {"line": 158, "column": 2}, "end": {"line": 179, "column": 3}}, "99": {"start": {"line": 161, "column": 6}, "end": {"line": 161, "column": 12}}, "100": {"start": {"line": 164, "column": 6}, "end": {"line": 164, "column": 24}}, "101": {"start": {"line": 165, "column": 6}, "end": {"line": 165, "column": 21}}, "102": {"start": {"line": 166, "column": 6}, "end": {"line": 166, "column": 11}}, "103": {"start": {"line": 169, "column": 15}, "end": {"line": 169, "column": 69}}, "104": {"start": {"line": 170, "column": 6}, "end": {"line": 170, "column": 54}}, "105": {"start": {"line": 171, "column": 6}, "end": {"line": 171, "column": 21}}, "106": {"start": {"line": 172, "column": 6}, "end": {"line": 172, "column": 11}}, "107": {"start": {"line": 175, "column": 15}, "end": {"line": 175, "column": 69}}, "108": {"start": {"line": 176, "column": 6}, "end": {"line": 176, "column": 74}}, "109": {"start": {"line": 177, "column": 6}, "end": {"line": 177, "column": 21}}, "110": {"start": {"line": 178, "column": 6}, "end": {"line": 178, "column": 11}}, "111": {"start": {"line": 187, "column": 15}, "end": {"line": 187, "column": 53}}, "112": {"start": {"line": 189, "column": 2}, "end": {"line": 189, "column": 20}}, "113": {"start": {"line": 195, "column": 2}, "end": {"line": 197, "column": 4}}, "114": {"start": {"line": 196, "column": 4}, "end": {"line": 196, "column": 22}}, "115": {"start": {"line": 199, "column": 2}, "end": {"line": 205, "column": 4}}, "116": {"start": {"line": 200, "column": 4}, "end": {"line": 202, "column": 5}}, "117": {"start": {"line": 201, "column": 6}, "end": {"line": 201, "column": 50}}, "118": {"start": {"line": 204, "column": 4}, "end": {"line": 204, "column": 44}}, "119": {"start": {"line": 204, "column": 33}, "end": {"line": 204, "column": 44}}, "120": {"start": {"line": 206, "column": 2}, "end": {"line": 206, "column": 31}}, "121": {"start": {"line": 208, "column": 14}, "end": {"line": 208, "column": 19}}, "122": {"start": {"line": 210, "column": 4}, "end": {"line": 210, "column": 21}}, "123": {"start": {"line": 210, "column": 15}, "end": {"line": 210, "column": 21}}, "124": {"start": {"line": 211, "column": 4}, "end": {"line": 211, "column": 16}}, "125": {"start": {"line": 215, "column": 4}, "end": {"line": 215, "column": 27}}, "126": {"start": {"line": 216, "column": 4}, "end": {"line": 216, "column": 26}}, "127": {"start": {"line": 217, "column": 4}, "end": {"line": 217, "column": 17}}, "128": {"start": {"line": 220, "column": 2}, "end": {"line": 223, "column": 4}}, "129": {"start": {"line": 222, "column": 4}, "end": {"line": 222, "column": 24}}, "130": {"start": {"line": 227, "column": 2}, "end": {"line": 230, "column": 3}}, "131": {"start": {"line": 229, "column": 4}, "end": {"line": 229, "column": 21}}, "132": {"start": {"line": 232, "column": 2}, "end": {"line": 232, "column": 20}}, "133": {"start": {"line": 235, "column": 0}, "end": {"line": 235, "column": 39}}, "134": {"start": {"line": 236, "column": 0}, "end": {"line": 236, "column": 37}}}, "fnMap": {"0": {"name": "Pack", "decl": {"start": {"line": 18, "column": 9}, "end": {"line": 18, "column": 13}}, "loc": {"start": {"line": 18, "column": 22}, "end": {"line": 44, "column": 1}}, "line": 18}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 36, "column": 16}, "end": {"line": 36, "column": 17}}, "loc": {"start": {"line": 36, "column": 31}, "end": {"line": 43, "column": 3}}, "line": 36}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 39, "column": 18}, "end": {"line": 39, "column": 19}}, "loc": {"start": {"line": 39, "column": 30}, "end": {"line": 41, "column": 5}}, "line": 39}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 46, "column": 27}, "end": {"line": 46, "column": 28}}, "loc": {"start": {"line": 46, "column": 44}, "end": {"line": 57, "column": 1}}, "line": 46}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 53, "column": 16}, "end": {"line": 53, "column": 17}}, "loc": {"start": {"line": 53, "column": 29}, "end": {"line": 55, "column": 5}}, "line": 53}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 59, "column": 21}, "end": {"line": 59, "column": 22}}, "loc": {"start": {"line": 59, "column": 39}, "end": {"line": 69, "column": 1}}, "line": 59}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 71, "column": 23}, "end": {"line": 71, "column": 24}}, "loc": {"start": {"line": 71, "column": 35}, "end": {"line": 75, "column": 1}}, "line": 71}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 77, "column": 24}, "end": {"line": 77, "column": 25}}, "loc": {"start": {"line": 77, "column": 36}, "end": {"line": 82, "column": 1}}, "line": 77}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 84, "column": 21}, "end": {"line": 84, "column": 22}}, "loc": {"start": {"line": 84, "column": 33}, "end": {"line": 88, "column": 1}}, "line": 84}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 90, "column": 26}, "end": {"line": 90, "column": 27}}, "loc": {"start": {"line": 90, "column": 38}, "end": {"line": 233, "column": 1}}, "line": 90}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 108, "column": 22}, "end": {"line": 108, "column": 23}}, "loc": {"start": {"line": 108, "column": 34}, "end": {"line": 111, "column": 5}}, "line": 108}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 142, "column": 41}, "end": {"line": 142, "column": 42}}, "loc": {"start": {"line": 142, "column": 54}, "end": {"line": 144, "column": 3}}, "line": 142}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 195, "column": 20}, "end": {"line": 195, "column": 21}}, "loc": {"start": {"line": 195, "column": 33}, "end": {"line": 197, "column": 3}}, "line": 195}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 199, "column": 22}, "end": {"line": 199, "column": 23}}, "loc": {"start": {"line": 199, "column": 34}, "end": {"line": 205, "column": 3}}, "line": 199}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 200, "column": 30}, "end": {"line": 200, "column": 31}}, "loc": {"start": {"line": 200, "column": 42}, "end": {"line": 202, "column": 5}}, "line": 200}, "15": {"name": "nextEntry", "decl": {"start": {"line": 209, "column": 11}, "end": {"line": 209, "column": 20}}, "loc": {"start": {"line": 209, "column": 24}, "end": {"line": 218, "column": 3}}, "line": 209}, "16": {"name": "(anonymous_16)", "decl": {"start": {"line": 220, "column": 21}, "end": {"line": 220, "column": 22}}, "loc": {"start": {"line": 220, "column": 35}, "end": {"line": 223, "column": 3}}, "line": 220}, "17": {"name": "(anonymous_17)", "decl": {"start": {"line": 235, "column": 25}, "end": {"line": 235, "column": 26}}, "loc": {"start": {"line": 235, "column": 37}, "end": {"line": 235, "column": 39}}, "line": 235}, "18": {"name": "(anonymous_18)", "decl": {"start": {"line": 236, "column": 23}, "end": {"line": 236, "column": 24}}, "loc": {"start": {"line": 236, "column": 35}, "end": {"line": 236, "column": 37}}, "line": 236}}, "branchMap": {"0": {"loc": {"start": {"line": 21, "column": 2}, "end": {"line": 21, "column": 51}}, "type": "if", "locations": [{"start": {"line": 21, "column": 2}, "end": {"line": 21, "column": 51}}, {"start": {"line": 21, "column": 2}, "end": {"line": 21, "column": 51}}], "line": 21}, "1": {"loc": {"start": {"line": 23, "column": 2}, "end": {"line": 24, "column": 32}}, "type": "if", "locations": [{"start": {"line": 23, "column": 2}, "end": {"line": 24, "column": 32}}, {"start": {"line": 23, "column": 2}, "end": {"line": 24, "column": 32}}], "line": 23}, "2": {"loc": {"start": {"line": 37, "column": 4}, "end": {"line": 37, "column": 41}}, "type": "if", "locations": [{"start": {"line": 37, "column": 4}, "end": {"line": 37, "column": 41}}, {"start": {"line": 37, "column": 4}, "end": {"line": 37, "column": 41}}], "line": 37}, "3": {"loc": {"start": {"line": 48, "column": 2}, "end": {"line": 48, "column": 29}}, "type": "if", "locations": [{"start": {"line": 48, "column": 2}, "end": {"line": 48, "column": 29}}, {"start": {"line": 48, "column": 2}, "end": {"line": 48, "column": 29}}], "line": 48}, "4": {"loc": {"start": {"line": 60, "column": 2}, "end": {"line": 60, "column": 68}}, "type": "if", "locations": [{"start": {"line": 60, "column": 2}, "end": {"line": 60, "column": 68}}, {"start": {"line": 60, "column": 2}, "end": {"line": 60, "column": 68}}], "line": 60}, "5": {"loc": {"start": {"line": 60, "column": 6}, "end": {"line": 60, "column": 38}}, "type": "binary-expr", "locations": [{"start": {"line": 60, "column": 6}, "end": {"line": 60, "column": 18}}, {"start": {"line": 60, "column": 22}, "end": {"line": 60, "column": 38}}], "line": 60}, "6": {"loc": {"start": {"line": 62, "column": 2}, "end": {"line": 62, "column": 72}}, "type": "if", "locations": [{"start": {"line": 62, "column": 2}, "end": {"line": 62, "column": 72}}, {"start": {"line": 62, "column": 2}, "end": {"line": 62, "column": 72}}], "line": 62}, "7": {"loc": {"start": {"line": 73, "column": 2}, "end": {"line": 73, "column": 52}}, "type": "if", "locations": [{"start": {"line": 73, "column": 2}, "end": {"line": 73, "column": 52}}, {"start": {"line": 73, "column": 2}, "end": {"line": 73, "column": 52}}], "line": 73}, "8": {"loc": {"start": {"line": 79, "column": 2}, "end": {"line": 79, "column": 53}}, "type": "if", "locations": [{"start": {"line": 79, "column": 2}, "end": {"line": 79, "column": 53}}, {"start": {"line": 79, "column": 2}, "end": {"line": 79, "column": 53}}], "line": 79}, "9": {"loc": {"start": {"line": 92, "column": 2}, "end": {"line": 94, "column": 3}}, "type": "if", "locations": [{"start": {"line": 92, "column": 2}, "end": {"line": 94, "column": 3}}, {"start": {"line": 92, "column": 2}, "end": {"line": 94, "column": 3}}], "line": 92}, "10": {"loc": {"start": {"line": 92, "column": 6}, "end": {"line": 92, "column": 34}}, "type": "binary-expr", "locations": [{"start": {"line": 92, "column": 6}, "end": {"line": 92, "column": 16}}, {"start": {"line": 92, "column": 20}, "end": {"line": 92, "column": 34}}], "line": 92}, "11": {"loc": {"start": {"line": 98, "column": 2}, "end": {"line": 103, "column": 3}}, "type": "if", "locations": [{"start": {"line": 98, "column": 2}, "end": {"line": 103, "column": 3}}, {"start": {"line": 98, "column": 2}, "end": {"line": 103, "column": 3}}], "line": 98}, "12": {"loc": {"start": {"line": 99, "column": 4}, "end": {"line": 101, "column": 5}}, "type": "if", "locations": [{"start": {"line": 99, "column": 4}, "end": {"line": 101, "column": 5}}, {"start": {"line": 99, "column": 4}, "end": {"line": 101, "column": 5}}], "line": 99}, "13": {"loc": {"start": {"line": 105, "column": 2}, "end": {"line": 113, "column": 3}}, "type": "if", "locations": [{"start": {"line": 105, "column": 2}, "end": {"line": 113, "column": 3}}, {"start": {"line": 105, "column": 2}, "end": {"line": 113, "column": 3}}], "line": 105}, "14": {"loc": {"start": {"line": 117, "column": 2}, "end": {"line": 124, "column": 3}}, "type": "if", "locations": [{"start": {"line": 117, "column": 2}, "end": {"line": 124, "column": 3}}, {"start": {"line": 117, "column": 2}, "end": {"line": 124, "column": 3}}], "line": 117}, "15": {"loc": {"start": {"line": 134, "column": 27}, "end": {"line": 134, "column": 46}}, "type": "binary-expr", "locations": [{"start": {"line": 134, "column": 27}, "end": {"line": 134, "column": 37}}, {"start": {"line": 134, "column": 41}, "end": {"line": 134, "column": 46}}], "line": 134}, "16": {"loc": {"start": {"line": 135, "column": 2}, "end": {"line": 138, "column": 3}}, "type": "if", "locations": [{"start": {"line": 135, "column": 2}, "end": {"line": 138, "column": 3}}, {"start": {"line": 135, "column": 2}, "end": {"line": 138, "column": 3}}], "line": 135}, "17": {"loc": {"start": {"line": 135, "column": 6}, "end": {"line": 135, "column": 72}}, "type": "binary-expr", "locations": [{"start": {"line": 135, "column": 6}, "end": {"line": 135, "column": 16}}, {"start": {"line": 135, "column": 20}, "end": {"line": 135, "column": 39}}, {"start": {"line": 135, "column": 43}, "end": {"line": 135, "column": 53}}, {"start": {"line": 135, "column": 57}, "end": {"line": 135, "column": 72}}], "line": 135}, "18": {"loc": {"start": {"line": 142, "column": 14}, "end": {"line": 142, "column": 31}}, "type": "binary-expr", "locations": [{"start": {"line": 142, "column": 14}, "end": {"line": 142, "column": 25}}, {"start": {"line": 142, "column": 29}, "end": {"line": 142, "column": 31}}], "line": 142}, "19": {"loc": {"start": {"line": 146, "column": 2}, "end": {"line": 146, "column": 52}}, "type": "if", "locations": [{"start": {"line": 146, "column": 2}, "end": {"line": 146, "column": 52}}, {"start": {"line": 146, "column": 2}, "end": {"line": 146, "column": 52}}], "line": 146}, "20": {"loc": {"start": {"line": 148, "column": 36}, "end": {"line": 148, "column": 52}}, "type": "binary-expr", "locations": [{"start": {"line": 148, "column": 36}, "end": {"line": 148, "column": 46}}, {"start": {"line": 148, "column": 50}, "end": {"line": 148, "column": 52}}], "line": 148}, "21": {"loc": {"start": {"line": 151, "column": 2}, "end": {"line": 153, "column": 3}}, "type": "if", "locations": [{"start": {"line": 151, "column": 2}, "end": {"line": 153, "column": 3}}, {"start": {"line": 151, "column": 2}, "end": {"line": 153, "column": 3}}], "line": 151}, "22": {"loc": {"start": {"line": 155, "column": 2}, "end": {"line": 156, "column": 29}}, "type": "if", "locations": [{"start": {"line": 155, "column": 2}, "end": {"line": 156, "column": 29}}, {"start": {"line": 155, "column": 2}, "end": {"line": 156, "column": 29}}], "line": 155}, "23": {"loc": {"start": {"line": 158, "column": 2}, "end": {"line": 179, "column": 3}}, "type": "switch", "locations": [{"start": {"line": 160, "column": 4}, "end": {"line": 161, "column": 12}}, {"start": {"line": 163, "column": 4}, "end": {"line": 166, "column": 11}}, {"start": {"line": 168, "column": 4}, "end": {"line": 172, "column": 11}}, {"start": {"line": 174, "column": 4}, "end": {"line": 178, "column": 11}}], "line": 158}, "24": {"loc": {"start": {"line": 170, "column": 24}, "end": {"line": 170, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 170, "column": 24}, "end": {"line": 170, "column": 47}}, {"start": {"line": 170, "column": 51}, "end": {"line": 170, "column": 54}}], "line": 170}, "25": {"loc": {"start": {"line": 176, "column": 24}, "end": {"line": 176, "column": 74}}, "type": "binary-expr", "locations": [{"start": {"line": 176, "column": 24}, "end": {"line": 176, "column": 67}}, {"start": {"line": 176, "column": 71}, "end": {"line": 176, "column": 74}}], "line": 176}, "26": {"loc": {"start": {"line": 204, "column": 4}, "end": {"line": 204, "column": 44}}, "type": "if", "locations": [{"start": {"line": 204, "column": 4}, "end": {"line": 204, "column": 44}}, {"start": {"line": 204, "column": 4}, "end": {"line": 204, "column": 44}}], "line": 204}, "27": {"loc": {"start": {"line": 210, "column": 4}, "end": {"line": 210, "column": 21}}, "type": "if", "locations": [{"start": {"line": 210, "column": 4}, "end": {"line": 210, "column": 21}}, {"start": {"line": 210, "column": 4}, "end": {"line": 210, "column": 21}}], "line": 210}, "28": {"loc": {"start": {"line": 227, "column": 2}, "end": {"line": 230, "column": 3}}, "type": "if", "locations": [{"start": {"line": 227, "column": 2}, "end": {"line": 230, "column": 3}}, {"start": {"line": 227, "column": 2}, "end": {"line": 230, "column": 3}}], "line": 227}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 512, "11": 1, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 1, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 1, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 1, "50": 0, "51": 0, "52": 0, "53": 0, "54": 1, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 1, "61": 0, "62": 0, "63": 0, "64": 1, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0, "108": 0, "109": 0, "110": 0, "111": 0, "112": 0, "113": 0, "114": 0, "115": 0, "116": 0, "117": 0, "118": 0, "119": 0, "120": 0, "121": 0, "122": 0, "123": 0, "124": 0, "125": 0, "126": 0, "127": 0, "128": 0, "129": 0, "130": 0, "131": 0, "132": 0, "133": 1, "134": 1}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0, 0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0, 0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0], "27": [0, 0], "28": [0, 0]}, "_coverageSchema": "43e27e138ebf9cfc5966b082cf9a028302ed4184", "hash": "8beb16ccde52e484aa1dd87f5ece33d696c808ab", "contentHash": "7bb2b7ebe1111c0deefbf9a61d17bf9d2dbe951adb585db96f9d89663ce5909d"}, "/Users/<USER>/dev/js/tar/lib/entry-writer.js": {"path": "/Users/<USER>/dev/js/tar/lib/entry-writer.js", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 28}}, "1": {"start": {"line": 3, "column": 10}, "end": {"line": 3, "column": 30}}, "2": {"start": {"line": 4, "column": 16}, "end": {"line": 4, "column": 38}}, "3": {"start": {"line": 5, "column": 12}, "end": {"line": 5, "column": 33}}, "4": {"start": {"line": 6, "column": 15}, "end": {"line": 6, "column": 34}}, "5": {"start": {"line": 7, "column": 18}, "end": {"line": 7, "column": 41}}, "6": {"start": {"line": 9, "column": 13}, "end": {"line": 9, "column": 37}}, "7": {"start": {"line": 10, "column": 10}, "end": {"line": 10, "column": 12}}, "8": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 29}}, "9": {"start": {"line": 15, "column": 11}, "end": {"line": 15, "column": 15}}, "10": {"start": {"line": 17, "column": 2}, "end": {"line": 19, "column": 3}}, "11": {"start": {"line": 18, "column": 4}, "end": {"line": 18, "column": 33}}, "12": {"start": {"line": 21, "column": 2}, "end": {"line": 21, "column": 20}}, "13": {"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": 20}}, "14": {"start": {"line": 24, "column": 2}, "end": {"line": 24, "column": 20}}, "15": {"start": {"line": 26, "column": 2}, "end": {"line": 26, "column": 35}}, "16": {"start": {"line": 28, "column": 2}, "end": {"line": 30, "column": 4}}, "17": {"start": {"line": 29, "column": 4}, "end": {"line": 29, "column": 22}}, "18": {"start": {"line": 32, "column": 2}, "end": {"line": 34, "column": 4}}, "19": {"start": {"line": 33, "column": 4}, "end": {"line": 33, "column": 20}}, "20": {"start": {"line": 36, "column": 2}, "end": {"line": 39, "column": 4}}, "21": {"start": {"line": 37, "column": 4}, "end": {"line": 37, "column": 18}}, "22": {"start": {"line": 38, "column": 4}, "end": {"line": 38, "column": 20}}, "23": {"start": {"line": 41, "column": 2}, "end": {"line": 41, "column": 18}}, "24": {"start": {"line": 42, "column": 2}, "end": {"line": 44, "column": 3}}, "25": {"start": {"line": 43, "column": 4}, "end": {"line": 43, "column": 18}}, "26": {"start": {"line": 45, "column": 2}, "end": {"line": 45, "column": 25}}, "27": {"start": {"line": 46, "column": 2}, "end": {"line": 46, "column": 23}}, "28": {"start": {"line": 47, "column": 2}, "end": {"line": 47, "column": 22}}, "29": {"start": {"line": 49, "column": 2}, "end": {"line": 49, "column": 17}}, "30": {"start": {"line": 50, "column": 2}, "end": {"line": 50, "column": 23}}, "31": {"start": {"line": 51, "column": 2}, "end": {"line": 51, "column": 18}}, "32": {"start": {"line": 53, "column": 2}, "end": {"line": 55, "column": 4}}, "33": {"start": {"line": 54, "column": 4}, "end": {"line": 54, "column": 17}}, "34": {"start": {"line": 58, "column": 0}, "end": {"line": 65, "column": 1}}, "35": {"start": {"line": 60, "column": 2}, "end": {"line": 60, "column": 74}}, "36": {"start": {"line": 60, "column": 19}, "end": {"line": 60, "column": 74}}, "37": {"start": {"line": 61, "column": 2}, "end": {"line": 61, "column": 22}}, "38": {"start": {"line": 62, "column": 2}, "end": {"line": 62, "column": 17}}, "39": {"start": {"line": 63, "column": 2}, "end": {"line": 63, "column": 43}}, "40": {"start": {"line": 64, "column": 2}, "end": {"line": 64, "column": 25}}, "41": {"start": {"line": 67, "column": 0}, "end": {"line": 74, "column": 1}}, "42": {"start": {"line": 69, "column": 2}, "end": {"line": 69, "column": 29}}, "43": {"start": {"line": 69, "column": 9}, "end": {"line": 69, "column": 29}}, "44": {"start": {"line": 70, "column": 2}, "end": {"line": 70, "column": 24}}, "45": {"start": {"line": 71, "column": 2}, "end": {"line": 71, "column": 20}}, "46": {"start": {"line": 72, "column": 2}, "end": {"line": 72, "column": 17}}, "47": {"start": {"line": 73, "column": 2}, "end": {"line": 73, "column": 43}}, "48": {"start": {"line": 76, "column": 0}, "end": {"line": 80, "column": 1}}, "49": {"start": {"line": 78, "column": 2}, "end": {"line": 78, "column": 21}}, "50": {"start": {"line": 79, "column": 2}, "end": {"line": 79, "column": 20}}, "51": {"start": {"line": 82, "column": 0}, "end": {"line": 87, "column": 1}}, "52": {"start": {"line": 84, "column": 2}, "end": {"line": 84, "column": 22}}, "53": {"start": {"line": 85, "column": 2}, "end": {"line": 85, "column": 21}}, "54": {"start": {"line": 86, "column": 2}, "end": {"line": 86, "column": 17}}, "55": {"start": {"line": 89, "column": 0}, "end": {"line": 98, "column": 1}}, "56": {"start": {"line": 91, "column": 2}, "end": {"line": 91, "column": 69}}, "57": {"start": {"line": 91, "column": 20}, "end": {"line": 91, "column": 69}}, "58": {"start": {"line": 95, "column": 2}, "end": {"line": 95, "column": 30}}, "59": {"start": {"line": 95, "column": 20}, "end": {"line": 95, "column": 30}}, "60": {"start": {"line": 97, "column": 2}, "end": {"line": 97, "column": 31}}, "61": {"start": {"line": 100, "column": 0}, "end": {"line": 126, "column": 1}}, "62": {"start": {"line": 102, "column": 2}, "end": {"line": 102, "column": 29}}, "63": {"start": {"line": 102, "column": 23}, "end": {"line": 102, "column": 29}}, "64": {"start": {"line": 103, "column": 2}, "end": {"line": 103, "column": 24}}, "65": {"start": {"line": 105, "column": 20}, "end": {"line": 105, "column": 48}}, "66": {"start": {"line": 107, "column": 2}, "end": {"line": 121, "column": 3}}, "67": {"start": {"line": 108, "column": 13}, "end": {"line": 108, "column": 17}}, "68": {"start": {"line": 110, "column": 4}, "end": {"line": 111, "column": 44}}, "69": {"start": {"line": 113, "column": 4}, "end": {"line": 120, "column": 12}}, "70": {"start": {"line": 115, "column": 8}, "end": {"line": 115, "column": 26}}, "71": {"start": {"line": 118, "column": 8}, "end": {"line": 118, "column": 28}}, "72": {"start": {"line": 124, "column": 2}, "end": {"line": 124, "column": 32}}, "73": {"start": {"line": 125, "column": 2}, "end": {"line": 125, "column": 21}}, "74": {"start": {"line": 128, "column": 0}, "end": {"line": 167, "column": 1}}, "75": {"start": {"line": 130, "column": 2}, "end": {"line": 132, "column": 3}}, "76": {"start": {"line": 131, "column": 4}, "end": {"line": 131, "column": 18}}, "77": {"start": {"line": 134, "column": 2}, "end": {"line": 137, "column": 3}}, "78": {"start": {"line": 136, "column": 4}, "end": {"line": 136, "column": 10}}, "79": {"start": {"line": 139, "column": 2}, "end": {"line": 139, "column": 25}}, "80": {"start": {"line": 141, "column": 12}, "end": {"line": 141, "column": 24}}, "81": {"start": {"line": 142, "column": 2}, "end": {"line": 159, "column": 3}}, "82": {"start": {"line": 142, "column": 15}, "end": {"line": 142, "column": 16}}, "83": {"start": {"line": 145, "column": 12}, "end": {"line": 145, "column": 18}}, "84": {"start": {"line": 147, "column": 4}, "end": {"line": 148, "column": 30}}, "85": {"start": {"line": 147, "column": 19}, "end": {"line": 147, "column": 37}}, "86": {"start": {"line": 148, "column": 9}, "end": {"line": 148, "column": 30}}, "87": {"start": {"line": 150, "column": 4}, "end": {"line": 158, "column": 5}}, "88": {"start": {"line": 152, "column": 6}, "end": {"line": 152, "column": 30}}, "89": {"start": {"line": 153, "column": 6}, "end": {"line": 156, "column": 7}}, "90": {"start": {"line": 154, "column": 8}, "end": {"line": 154, "column": 30}}, "91": {"start": {"line": 155, "column": 8}, "end": {"line": 155, "column": 39}}, "92": {"start": {"line": 157, "column": 6}, "end": {"line": 157, "column": 12}}, "93": {"start": {"line": 162, "column": 2}, "end": {"line": 162, "column": 25}}, "94": {"start": {"line": 163, "column": 2}, "end": {"line": 163, "column": 26}}, "95": {"start": {"line": 166, "column": 2}, "end": {"line": 166, "column": 20}}, "96": {"start": {"line": 169, "column": 0}, "end": {"line": 169, "column": 46}}}, "fnMap": {"0": {"name": "EntryWriter", "decl": {"start": {"line": 14, "column": 9}, "end": {"line": 14, "column": 20}}, "loc": {"start": {"line": 14, "column": 29}, "end": {"line": 56, "column": 1}}, "line": 14}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 28, "column": 24}, "end": {"line": 28, "column": 25}}, "loc": {"start": {"line": 28, "column": 37}, "end": {"line": 30, "column": 3}}, "line": 28}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 32, "column": 25}, "end": {"line": 32, "column": 26}}, "loc": {"start": {"line": 32, "column": 37}, "end": {"line": 34, "column": 3}}, "line": 32}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 36, "column": 23}, "end": {"line": 36, "column": 24}}, "loc": {"start": {"line": 36, "column": 35}, "end": {"line": 39, "column": 3}}, "line": 36}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 53, "column": 16}, "end": {"line": 53, "column": 17}}, "loc": {"start": {"line": 53, "column": 28}, "end": {"line": 55, "column": 3}}, "line": 53}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 58, "column": 30}, "end": {"line": 58, "column": 31}}, "loc": {"start": {"line": 58, "column": 43}, "end": {"line": 65, "column": 1}}, "line": 58}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 67, "column": 28}, "end": {"line": 67, "column": 29}}, "loc": {"start": {"line": 67, "column": 41}, "end": {"line": 74, "column": 1}}, "line": 67}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 76, "column": 30}, "end": {"line": 76, "column": 31}}, "loc": {"start": {"line": 76, "column": 42}, "end": {"line": 80, "column": 1}}, "line": 76}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 82, "column": 31}, "end": {"line": 82, "column": 32}}, "loc": {"start": {"line": 82, "column": 43}, "end": {"line": 87, "column": 1}}, "line": 82}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 89, "column": 28}, "end": {"line": 89, "column": 29}}, "loc": {"start": {"line": 89, "column": 45}, "end": {"line": 98, "column": 1}}, "line": 89}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 100, "column": 32}, "end": {"line": 100, "column": 33}}, "loc": {"start": {"line": 100, "column": 44}, "end": {"line": 126, "column": 1}}, "line": 100}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 114, "column": 18}, "end": {"line": 114, "column": 19}}, "loc": {"start": {"line": 114, "column": 31}, "end": {"line": 116, "column": 7}}, "line": 114}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 117, "column": 19}, "end": {"line": 117, "column": 20}}, "loc": {"start": {"line": 117, "column": 33}, "end": {"line": 119, "column": 7}}, "line": 117}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 128, "column": 33}, "end": {"line": 128, "column": 34}}, "loc": {"start": {"line": 128, "column": 45}, "end": {"line": 167, "column": 1}}, "line": 128}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 169, "column": 32}, "end": {"line": 169, "column": 33}}, "loc": {"start": {"line": 169, "column": 44}, "end": {"line": 169, "column": 46}}, "line": 169}}, "branchMap": {"0": {"loc": {"start": {"line": 17, "column": 2}, "end": {"line": 19, "column": 3}}, "type": "if", "locations": [{"start": {"line": 17, "column": 2}, "end": {"line": 19, "column": 3}}, {"start": {"line": 17, "column": 2}, "end": {"line": 19, "column": 3}}], "line": 17}, "1": {"loc": {"start": {"line": 42, "column": 2}, "end": {"line": 44, "column": 3}}, "type": "if", "locations": [{"start": {"line": 42, "column": 2}, "end": {"line": 44, "column": 3}}, {"start": {"line": 42, "column": 2}, "end": {"line": 44, "column": 3}}], "line": 42}, "2": {"loc": {"start": {"line": 60, "column": 2}, "end": {"line": 60, "column": 74}}, "type": "if", "locations": [{"start": {"line": 60, "column": 2}, "end": {"line": 60, "column": 74}}, {"start": {"line": 60, "column": 2}, "end": {"line": 60, "column": 74}}], "line": 60}, "3": {"loc": {"start": {"line": 69, "column": 2}, "end": {"line": 69, "column": 29}}, "type": "if", "locations": [{"start": {"line": 69, "column": 2}, "end": {"line": 69, "column": 29}}, {"start": {"line": 69, "column": 2}, "end": {"line": 69, "column": 29}}], "line": 69}, "4": {"loc": {"start": {"line": 91, "column": 2}, "end": {"line": 91, "column": 69}}, "type": "if", "locations": [{"start": {"line": 91, "column": 2}, "end": {"line": 91, "column": 69}}, {"start": {"line": 91, "column": 2}, "end": {"line": 91, "column": 69}}], "line": 91}, "5": {"loc": {"start": {"line": 95, "column": 2}, "end": {"line": 95, "column": 30}}, "type": "if", "locations": [{"start": {"line": 95, "column": 2}, "end": {"line": 95, "column": 30}}, {"start": {"line": 95, "column": 2}, "end": {"line": 95, "column": 30}}], "line": 95}, "6": {"loc": {"start": {"line": 102, "column": 2}, "end": {"line": 102, "column": 29}}, "type": "if", "locations": [{"start": {"line": 102, "column": 2}, "end": {"line": 102, "column": 29}}, {"start": {"line": 102, "column": 2}, "end": {"line": 102, "column": 29}}], "line": 102}, "7": {"loc": {"start": {"line": 107, "column": 2}, "end": {"line": 121, "column": 3}}, "type": "if", "locations": [{"start": {"line": 107, "column": 2}, "end": {"line": 121, "column": 3}}, {"start": {"line": 107, "column": 2}, "end": {"line": 121, "column": 3}}], "line": 107}, "8": {"loc": {"start": {"line": 107, "column": 6}, "end": {"line": 107, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 107, "column": 6}, "end": {"line": 107, "column": 29}}, {"start": {"line": 107, "column": 33}, "end": {"line": 107, "column": 44}}], "line": 107}, "9": {"loc": {"start": {"line": 110, "column": 27}, "end": {"line": 111, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 110, "column": 27}, "end": {"line": 110, "column": 47}}, {"start": {"line": 111, "column": 6}, "end": {"line": 111, "column": 44}}], "line": 110}, "10": {"loc": {"start": {"line": 130, "column": 2}, "end": {"line": 132, "column": 3}}, "type": "if", "locations": [{"start": {"line": 130, "column": 2}, "end": {"line": 132, "column": 3}}, {"start": {"line": 130, "column": 2}, "end": {"line": 132, "column": 3}}], "line": 130}, "11": {"loc": {"start": {"line": 130, "column": 6}, "end": {"line": 130, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 130, "column": 6}, "end": {"line": 130, "column": 22}}, {"start": {"line": 130, "column": 26}, "end": {"line": 130, "column": 37}}], "line": 130}, "12": {"loc": {"start": {"line": 134, "column": 2}, "end": {"line": 137, "column": 3}}, "type": "if", "locations": [{"start": {"line": 134, "column": 2}, "end": {"line": 137, "column": 3}}, {"start": {"line": 134, "column": 2}, "end": {"line": 137, "column": 3}}], "line": 134}, "13": {"loc": {"start": {"line": 134, "column": 6}, "end": {"line": 134, "column": 38}}, "type": "binary-expr", "locations": [{"start": {"line": 134, "column": 6}, "end": {"line": 134, "column": 18}}, {"start": {"line": 134, "column": 22}, "end": {"line": 134, "column": 38}}], "line": 134}, "14": {"loc": {"start": {"line": 147, "column": 4}, "end": {"line": 148, "column": 30}}, "type": "if", "locations": [{"start": {"line": 147, "column": 4}, "end": {"line": 148, "column": 30}}, {"start": {"line": 147, "column": 4}, "end": {"line": 148, "column": 30}}], "line": 147}, "15": {"loc": {"start": {"line": 150, "column": 4}, "end": {"line": 158, "column": 5}}, "type": "if", "locations": [{"start": {"line": 150, "column": 4}, "end": {"line": 158, "column": 5}}, {"start": {"line": 150, "column": 4}, "end": {"line": 158, "column": 5}}], "line": 150}, "16": {"loc": {"start": {"line": 153, "column": 6}, "end": {"line": 156, "column": 7}}, "type": "if", "locations": [{"start": {"line": 153, "column": 6}, "end": {"line": 156, "column": 7}}, {"start": {"line": 153, "column": 6}, "end": {"line": 156, "column": 7}}], "line": 153}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 1, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 1, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 1, "49": 0, "50": 0, "51": 1, "52": 0, "53": 0, "54": 0, "55": 1, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 1, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 1, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 1}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0]}, "_coverageSchema": "43e27e138ebf9cfc5966b082cf9a028302ed4184", "hash": "3e169418508e031df78186ff63b5fa5aa2484ab8", "contentHash": "84191ed4732b6763ae8701256051735aebbe5e95c932c15bef34da01b33ba1a0"}, "/Users/<USER>/dev/js/tar/lib/entry.js": {"path": "/Users/<USER>/dev/js/tar/lib/entry.js", "statementMap": {"0": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 22}}, "1": {"start": {"line": 9, "column": 16}, "end": {"line": 9, "column": 38}}, "2": {"start": {"line": 10, "column": 10}, "end": {"line": 10, "column": 27}}, "3": {"start": {"line": 11, "column": 13}, "end": {"line": 11, "column": 33}}, "4": {"start": {"line": 12, "column": 13}, "end": {"line": 12, "column": 37}}, "5": {"start": {"line": 13, "column": 15}, "end": {"line": 13, "column": 34}}, "6": {"start": {"line": 14, "column": 14}, "end": {"line": 14, "column": 41}}, "7": {"start": {"line": 17, "column": 2}, "end": {"line": 17, "column": 19}}, "8": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": 22}}, "9": {"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": 22}}, "10": {"start": {"line": 21, "column": 2}, "end": {"line": 21, "column": 25}}, "11": {"start": {"line": 22, "column": 2}, "end": {"line": 22, "column": 22}}, "12": {"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": 23}}, "13": {"start": {"line": 24, "column": 2}, "end": {"line": 24, "column": 22}}, "14": {"start": {"line": 25, "column": 2}, "end": {"line": 25, "column": 21}}, "15": {"start": {"line": 26, "column": 2}, "end": {"line": 26, "column": 21}}, "16": {"start": {"line": 27, "column": 2}, "end": {"line": 27, "column": 21}}, "17": {"start": {"line": 28, "column": 2}, "end": {"line": 28, "column": 18}}, "18": {"start": {"line": 29, "column": 2}, "end": {"line": 29, "column": 17}}, "19": {"start": {"line": 30, "column": 2}, "end": {"line": 30, "column": 20}}, "20": {"start": {"line": 32, "column": 2}, "end": {"line": 32, "column": 36}}, "21": {"start": {"line": 34, "column": 2}, "end": {"line": 34, "column": 17}}, "22": {"start": {"line": 35, "column": 2}, "end": {"line": 35, "column": 23}}, "23": {"start": {"line": 36, "column": 2}, "end": {"line": 36, "column": 33}}, "24": {"start": {"line": 40, "column": 2}, "end": {"line": 40, "column": 19}}, "25": {"start": {"line": 41, "column": 11}, "end": {"line": 41, "column": 15}}, "26": {"start": {"line": 42, "column": 2}, "end": {"line": 44, "column": 4}}, "27": {"start": {"line": 43, "column": 4}, "end": {"line": 43, "column": 29}}, "28": {"start": {"line": 46, "column": 2}, "end": {"line": 46, "column": 18}}, "29": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 23}}, "30": {"start": {"line": 51, "column": 0}, "end": {"line": 78, "column": 1}}, "31": {"start": {"line": 52, "column": 2}, "end": {"line": 52, "column": 65}}, "32": {"start": {"line": 52, "column": 20}, "end": {"line": 52, "column": 65}}, "33": {"start": {"line": 53, "column": 2}, "end": {"line": 55, "column": 3}}, "34": {"start": {"line": 54, "column": 4}, "end": {"line": 54, "column": 40}}, "35": {"start": {"line": 59, "column": 2}, "end": {"line": 61, "column": 3}}, "36": {"start": {"line": 60, "column": 4}, "end": {"line": 60, "column": 35}}, "37": {"start": {"line": 62, "column": 2}, "end": {"line": 62, "column": 29}}, "38": {"start": {"line": 65, "column": 11}, "end": {"line": 65, "column": 25}}, "39": {"start": {"line": 66, "column": 2}, "end": {"line": 66, "column": 21}}, "40": {"start": {"line": 67, "column": 2}, "end": {"line": 67, "column": 19}}, "41": {"start": {"line": 69, "column": 2}, "end": {"line": 69, "column": 14}}, "42": {"start": {"line": 72, "column": 2}, "end": {"line": 75, "column": 3}}, "43": {"start": {"line": 73, "column": 4}, "end": {"line": 73, "column": 26}}, "44": {"start": {"line": 74, "column": 4}, "end": {"line": 74, "column": 16}}, "45": {"start": {"line": 77, "column": 2}, "end": {"line": 77, "column": 13}}, "46": {"start": {"line": 80, "column": 0}, "end": {"line": 84, "column": 1}}, "47": {"start": {"line": 81, "column": 2}, "end": {"line": 81, "column": 22}}, "48": {"start": {"line": 81, "column": 9}, "end": {"line": 81, "column": 22}}, "49": {"start": {"line": 82, "column": 2}, "end": {"line": 82, "column": 21}}, "50": {"start": {"line": 83, "column": 2}, "end": {"line": 83, "column": 14}}, "51": {"start": {"line": 86, "column": 0}, "end": {"line": 89, "column": 1}}, "52": {"start": {"line": 87, "column": 2}, "end": {"line": 87, "column": 21}}, "53": {"start": {"line": 88, "column": 2}, "end": {"line": 88, "column": 20}}, "54": {"start": {"line": 91, "column": 0}, "end": {"line": 97, "column": 1}}, "55": {"start": {"line": 93, "column": 2}, "end": {"line": 93, "column": 21}}, "56": {"start": {"line": 94, "column": 2}, "end": {"line": 94, "column": 22}}, "57": {"start": {"line": 95, "column": 2}, "end": {"line": 95, "column": 14}}, "58": {"start": {"line": 96, "column": 2}, "end": {"line": 96, "column": 41}}, "59": {"start": {"line": 100, "column": 0}, "end": {"line": 139, "column": 1}}, "60": {"start": {"line": 103, "column": 2}, "end": {"line": 103, "column": 58}}, "61": {"start": {"line": 103, "column": 52}, "end": {"line": 103, "column": 58}}, "62": {"start": {"line": 107, "column": 2}, "end": {"line": 107, "column": 22}}, "63": {"start": {"line": 110, "column": 2}, "end": {"line": 113, "column": 3}}, "64": {"start": {"line": 111, "column": 16}, "end": {"line": 111, "column": 43}}, "65": {"start": {"line": 112, "column": 4}, "end": {"line": 112, "column": 28}}, "66": {"start": {"line": 116, "column": 2}, "end": {"line": 126, "column": 3}}, "67": {"start": {"line": 117, "column": 4}, "end": {"line": 117, "column": 57}}, "68": {"start": {"line": 118, "column": 4}, "end": {"line": 121, "column": 5}}, "69": {"start": {"line": 119, "column": 6}, "end": {"line": 119, "column": 29}}, "70": {"start": {"line": 120, "column": 6}, "end": {"line": 120, "column": 24}}, "71": {"start": {"line": 122, "column": 4}, "end": {"line": 125, "column": 5}}, "72": {"start": {"line": 123, "column": 6}, "end": {"line": 123, "column": 24}}, "73": {"start": {"line": 124, "column": 6}, "end": {"line": 124, "column": 22}}, "74": {"start": {"line": 130, "column": 12}, "end": {"line": 130, "column": 29}}, "75": {"start": {"line": 131, "column": 2}, "end": {"line": 136, "column": 3}}, "76": {"start": {"line": 132, "column": 4}, "end": {"line": 132, "column": 36}}, "77": {"start": {"line": 133, "column": 4}, "end": {"line": 133, "column": 22}}, "78": {"start": {"line": 134, "column": 4}, "end": {"line": 134, "column": 25}}, "79": {"start": {"line": 135, "column": 4}, "end": {"line": 135, "column": 40}}, "80": {"start": {"line": 138, "column": 2}, "end": {"line": 138, "column": 23}}, "81": {"start": {"line": 141, "column": 0}, "end": {"line": 211, "column": 1}}, "82": {"start": {"line": 143, "column": 15}, "end": {"line": 143, "column": 27}}, "83": {"start": {"line": 144, "column": 17}, "end": {"line": 144, "column": 31}}, "84": {"start": {"line": 145, "column": 15}, "end": {"line": 145, "column": 27}}, "85": {"start": {"line": 146, "column": 14}, "end": {"line": 146, "column": 24}}, "86": {"start": {"line": 149, "column": 15}, "end": {"line": 149, "column": 25}}, "87": {"start": {"line": 150, "column": 2}, "end": {"line": 154, "column": 3}}, "88": {"start": {"line": 150, "column": 15}, "end": {"line": 150, "column": 16}}, "89": {"start": {"line": 151, "column": 16}, "end": {"line": 151, "column": 25}}, "90": {"start": {"line": 152, "column": 14}, "end": {"line": 152, "column": 27}}, "91": {"start": {"line": 153, "column": 4}, "end": {"line": 153, "column": 54}}, "92": {"start": {"line": 153, "column": 36}, "end": {"line": 153, "column": 54}}, "93": {"start": {"line": 158, "column": 3}, "end": {"line": 165, "column": 3}}, "94": {"start": {"line": 159, "column": 4}, "end": {"line": 161, "column": 6}}, "95": {"start": {"line": 160, "column": 6}, "end": {"line": 160, "column": 54}}, "96": {"start": {"line": 160, "column": 39}, "end": {"line": 160, "column": 54}}, "97": {"start": {"line": 165, "column": 3}, "end": {"line": 173, "column": 3}}, "98": {"start": {"line": 166, "column": 4}, "end": {"line": 168, "column": 5}}, "99": {"start": {"line": 167, "column": 6}, "end": {"line": 167, "column": 40}}, "100": {"start": {"line": 173, "column": 3}, "end": {"line": 177, "column": 4}}, "101": {"start": {"line": 174, "column": 4}, "end": {"line": 176, "column": 5}}, "102": {"start": {"line": 175, "column": 6}, "end": {"line": 175, "column": 42}}, "103": {"start": {"line": 181, "column": 2}, "end": {"line": 203, "column": 3}}, "104": {"start": {"line": 184, "column": 6}, "end": {"line": 184, "column": 19}}, "105": {"start": {"line": 185, "column": 6}, "end": {"line": 185, "column": 11}}, "106": {"start": {"line": 188, "column": 6}, "end": {"line": 188, "column": 24}}, "107": {"start": {"line": 189, "column": 6}, "end": {"line": 189, "column": 11}}, "108": {"start": {"line": 192, "column": 6}, "end": {"line": 192, "column": 22}}, "109": {"start": {"line": 193, "column": 6}, "end": {"line": 193, "column": 11}}, "110": {"start": {"line": 202, "column": 6}, "end": {"line": 202, "column": 34}}, "111": {"start": {"line": 205, "column": 2}, "end": {"line": 205, "column": 18}}, "112": {"start": {"line": 206, "column": 2}, "end": {"line": 206, "column": 24}}, "113": {"start": {"line": 207, "column": 2}, "end": {"line": 207, "column": 24}}, "114": {"start": {"line": 210, "column": 2}, "end": {"line": 210, "column": 30}}, "115": {"start": {"line": 215, "column": 0}, "end": {"line": 217, "column": 1}}, "116": {"start": {"line": 216, "column": 2}, "end": {"line": 216, "column": 20}}, "117": {"start": {"line": 219, "column": 0}, "end": {"line": 219, "column": 35}}, "118": {"start": {"line": 220, "column": 0}, "end": {"line": 220, "column": 37}}}, "fnMap": {"0": {"name": "Entry", "decl": {"start": {"line": 16, "column": 9}, "end": {"line": 16, "column": 14}}, "loc": {"start": {"line": 16, "column": 42}, "end": {"line": 47, "column": 1}}, "line": 16}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 42, "column": 36}, "end": {"line": 42, "column": 37}}, "loc": {"start": {"line": 42, "column": 49}, "end": {"line": 44, "column": 3}}, "line": 42}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 51, "column": 24}, "end": {"line": 51, "column": 25}}, "loc": {"start": {"line": 51, "column": 37}, "end": {"line": 78, "column": 1}}, "line": 51}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 80, "column": 22}, "end": {"line": 80, "column": 23}}, "loc": {"start": {"line": 80, "column": 35}, "end": {"line": 84, "column": 1}}, "line": 80}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 86, "column": 24}, "end": {"line": 86, "column": 25}}, "loc": {"start": {"line": 86, "column": 36}, "end": {"line": 89, "column": 1}}, "line": 86}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 91, "column": 25}, "end": {"line": 91, "column": 26}}, "loc": {"start": {"line": 91, "column": 37}, "end": {"line": 97, "column": 1}}, "line": 91}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 100, "column": 24}, "end": {"line": 100, "column": 25}}, "loc": {"start": {"line": 100, "column": 36}, "end": {"line": 139, "column": 1}}, "line": 100}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 141, "column": 28}, "end": {"line": 141, "column": 29}}, "loc": {"start": {"line": 141, "column": 40}, "end": {"line": 211, "column": 1}}, "line": 141}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 158, "column": 30}, "end": {"line": 158, "column": 31}}, "loc": {"start": {"line": 158, "column": 43}, "end": {"line": 162, "column": 3}}, "line": 158}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 159, "column": 27}, "end": {"line": 159, "column": 28}}, "loc": {"start": {"line": 159, "column": 40}, "end": {"line": 161, "column": 5}}, "line": 159}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 165, "column": 32}, "end": {"line": 165, "column": 33}}, "loc": {"start": {"line": 165, "column": 45}, "end": {"line": 169, "column": 3}}, "line": 165}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 173, "column": 39}, "end": {"line": 173, "column": 40}}, "loc": {"start": {"line": 173, "column": 52}, "end": {"line": 177, "column": 3}}, "line": 173}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 215, "column": 24}, "end": {"line": 215, "column": 25}}, "loc": {"start": {"line": 215, "column": 34}, "end": {"line": 217, "column": 1}}, "line": 215}}, "branchMap": {"0": {"loc": {"start": {"line": 36, "column": 19}, "end": {"line": 36, "column": 33}}, "type": "binary-expr", "locations": [{"start": {"line": 36, "column": 19}, "end": {"line": 36, "column": 27}}, {"start": {"line": 36, "column": 31}, "end": {"line": 36, "column": 33}}], "line": 36}, "1": {"loc": {"start": {"line": 42, "column": 14}, "end": {"line": 42, "column": 26}}, "type": "binary-expr", "locations": [{"start": {"line": 42, "column": 14}, "end": {"line": 42, "column": 20}}, {"start": {"line": 42, "column": 24}, "end": {"line": 42, "column": 26}}], "line": 42}, "2": {"loc": {"start": {"line": 52, "column": 2}, "end": {"line": 52, "column": 65}}, "type": "if", "locations": [{"start": {"line": 52, "column": 2}, "end": {"line": 52, "column": 65}}, {"start": {"line": 52, "column": 2}, "end": {"line": 52, "column": 65}}], "line": 52}, "3": {"loc": {"start": {"line": 53, "column": 2}, "end": {"line": 55, "column": 3}}, "type": "if", "locations": [{"start": {"line": 53, "column": 2}, "end": {"line": 55, "column": 3}}, {"start": {"line": 53, "column": 2}, "end": {"line": 55, "column": 3}}], "line": 53}, "4": {"loc": {"start": {"line": 59, "column": 2}, "end": {"line": 61, "column": 3}}, "type": "if", "locations": [{"start": {"line": 59, "column": 2}, "end": {"line": 61, "column": 3}}, {"start": {"line": 59, "column": 2}, "end": {"line": 61, "column": 3}}], "line": 59}, "5": {"loc": {"start": {"line": 72, "column": 2}, "end": {"line": 75, "column": 3}}, "type": "if", "locations": [{"start": {"line": 72, "column": 2}, "end": {"line": 75, "column": 3}}, {"start": {"line": 72, "column": 2}, "end": {"line": 75, "column": 3}}], "line": 72}, "6": {"loc": {"start": {"line": 72, "column": 6}, "end": {"line": 72, "column": 28}}, "type": "binary-expr", "locations": [{"start": {"line": 72, "column": 6}, "end": {"line": 72, "column": 18}}, {"start": {"line": 72, "column": 22}, "end": {"line": 72, "column": 28}}], "line": 72}, "7": {"loc": {"start": {"line": 81, "column": 2}, "end": {"line": 81, "column": 22}}, "type": "if", "locations": [{"start": {"line": 81, "column": 2}, "end": {"line": 81, "column": 22}}, {"start": {"line": 81, "column": 2}, "end": {"line": 81, "column": 22}}], "line": 81}, "8": {"loc": {"start": {"line": 103, "column": 2}, "end": {"line": 103, "column": 58}}, "type": "if", "locations": [{"start": {"line": 103, "column": 2}, "end": {"line": 103, "column": 58}}, {"start": {"line": 103, "column": 2}, "end": {"line": 103, "column": 58}}], "line": 103}, "9": {"loc": {"start": {"line": 103, "column": 6}, "end": {"line": 103, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 103, "column": 6}, "end": {"line": 103, "column": 18}}, {"start": {"line": 103, "column": 22}, "end": {"line": 103, "column": 35}}, {"start": {"line": 103, "column": 39}, "end": {"line": 103, "column": 50}}], "line": 103}, "10": {"loc": {"start": {"line": 110, "column": 9}, "end": {"line": 110, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 110, "column": 9}, "end": {"line": 110, "column": 37}}, {"start": {"line": 110, "column": 41}, "end": {"line": 110, "column": 54}}], "line": 110}, "11": {"loc": {"start": {"line": 116, "column": 2}, "end": {"line": 126, "column": 3}}, "type": "if", "locations": [{"start": {"line": 116, "column": 2}, "end": {"line": 126, "column": 3}}, {"start": {"line": 116, "column": 2}, "end": {"line": 126, "column": 3}}], "line": 116}, "12": {"loc": {"start": {"line": 118, "column": 4}, "end": {"line": 121, "column": 5}}, "type": "if", "locations": [{"start": {"line": 118, "column": 4}, "end": {"line": 121, "column": 5}}, {"start": {"line": 118, "column": 4}, "end": {"line": 121, "column": 5}}], "line": 118}, "13": {"loc": {"start": {"line": 122, "column": 4}, "end": {"line": 125, "column": 5}}, "type": "if", "locations": [{"start": {"line": 122, "column": 4}, "end": {"line": 125, "column": 5}}, {"start": {"line": 122, "column": 4}, "end": {"line": 125, "column": 5}}], "line": 122}, "14": {"loc": {"start": {"line": 131, "column": 2}, "end": {"line": 136, "column": 3}}, "type": "if", "locations": [{"start": {"line": 131, "column": 2}, "end": {"line": 136, "column": 3}}, {"start": {"line": 131, "column": 2}, "end": {"line": 136, "column": 3}}], "line": 131}, "15": {"loc": {"start": {"line": 131, "column": 6}, "end": {"line": 131, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 131, "column": 6}, "end": {"line": 131, "column": 26}}, {"start": {"line": 131, "column": 30}, "end": {"line": 131, "column": 45}}], "line": 131}, "16": {"loc": {"start": {"line": 153, "column": 4}, "end": {"line": 153, "column": 54}}, "type": "if", "locations": [{"start": {"line": 153, "column": 4}, "end": {"line": 153, "column": 54}}, {"start": {"line": 153, "column": 4}, "end": {"line": 153, "column": 54}}], "line": 153}, "17": {"loc": {"start": {"line": 160, "column": 6}, "end": {"line": 160, "column": 54}}, "type": "if", "locations": [{"start": {"line": 160, "column": 6}, "end": {"line": 160, "column": 54}}, {"start": {"line": 160, "column": 6}, "end": {"line": 160, "column": 54}}], "line": 160}, "18": {"loc": {"start": {"line": 166, "column": 4}, "end": {"line": 168, "column": 5}}, "type": "if", "locations": [{"start": {"line": 166, "column": 4}, "end": {"line": 168, "column": 5}}, {"start": {"line": 166, "column": 4}, "end": {"line": 168, "column": 5}}], "line": 166}, "19": {"loc": {"start": {"line": 174, "column": 4}, "end": {"line": 176, "column": 5}}, "type": "if", "locations": [{"start": {"line": 174, "column": 4}, "end": {"line": 176, "column": 5}}, {"start": {"line": 174, "column": 4}, "end": {"line": 176, "column": 5}}], "line": 174}, "20": {"loc": {"start": {"line": 181, "column": 2}, "end": {"line": 203, "column": 3}}, "type": "switch", "locations": [{"start": {"line": 182, "column": 4}, "end": {"line": 182, "column": 19}}, {"start": {"line": 183, "column": 4}, "end": {"line": 185, "column": 11}}, {"start": {"line": 187, "column": 4}, "end": {"line": 189, "column": 11}}, {"start": {"line": 191, "column": 4}, "end": {"line": 193, "column": 11}}, {"start": {"line": 195, "column": 4}, "end": {"line": 195, "column": 16}}, {"start": {"line": 196, "column": 4}, "end": {"line": 196, "column": 24}}, {"start": {"line": 197, "column": 4}, "end": {"line": 197, "column": 27}}, {"start": {"line": 198, "column": 4}, "end": {"line": 198, "column": 23}}, {"start": {"line": 199, "column": 4}, "end": {"line": 199, "column": 21}}, {"start": {"line": 200, "column": 4}, "end": {"line": 200, "column": 16}}, {"start": {"line": 201, "column": 4}, "end": {"line": 202, "column": 34}}], "line": 181}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 1, "30": 1, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 1, "47": 0, "48": 0, "49": 0, "50": 0, "51": 1, "52": 0, "53": 0, "54": 1, "55": 0, "56": 0, "57": 0, "58": 0, "59": 1, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 1, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0, "108": 0, "109": 0, "110": 0, "111": 0, "112": 0, "113": 0, "114": 0, "115": 1, "116": 0, "117": 1, "118": 1}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, "_coverageSchema": "43e27e138ebf9cfc5966b082cf9a028302ed4184", "hash": "2cc4f925db2a60e89bf0743124faec4e8996dfdb", "contentHash": "245d5cb2b23d975dc5b9a0ed2553c93c587727d50a7961242f50cec333c04ba9"}, "/Users/<USER>/dev/js/tar/lib/global-header-writer.js": {"path": "/Users/<USER>/dev/js/tar/lib/global-header-writer.js", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 35}}, "1": {"start": {"line": 3, "column": 27}, "end": {"line": 3, "column": 65}}, "2": {"start": {"line": 4, "column": 15}, "end": {"line": 4, "column": 34}}, "3": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 50}}, "4": {"start": {"line": 9, "column": 2}, "end": {"line": 11, "column": 3}}, "5": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 40}}, "6": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": 40}}, "7": {"start": {"line": 13, "column": 2}, "end": {"line": 13, "column": 23}}}, "fnMap": {"0": {"name": "GlobalHeaderWriter", "decl": {"start": {"line": 8, "column": 9}, "end": {"line": 8, "column": 27}}, "loc": {"start": {"line": 8, "column": 36}, "end": {"line": 14, "column": 1}}, "line": 8}}, "branchMap": {"0": {"loc": {"start": {"line": 9, "column": 2}, "end": {"line": 11, "column": 3}}, "type": "if", "locations": [{"start": {"line": 9, "column": 2}, "end": {"line": 11, "column": 3}}, {"start": {"line": 9, "column": 2}, "end": {"line": 11, "column": 3}}], "line": 9}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 0, "5": 0, "6": 0, "7": 0}, "f": {"0": 0}, "b": {"0": [0, 0]}, "_coverageSchema": "43e27e138ebf9cfc5966b082cf9a028302ed4184", "hash": "3d0c8589f62a5c8ccec606d52d3abd98e5627d57", "contentHash": "a2570ca840cf3e44d77e15af0aee52f7c145da39b76366006f3672b9708def3e"}, "/Users/<USER>/dev/js/tar/lib/parse.js": {"path": "/Users/<USER>/dev/js/tar/lib/parse.js", "statementMap": {"0": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 37}}, "1": {"start": {"line": 8, "column": 13}, "end": {"line": 8, "column": 30}}, "2": {"start": {"line": 9, "column": 13}, "end": {"line": 9, "column": 26}}, "3": {"start": {"line": 10, "column": 18}, "end": {"line": 10, "column": 41}}, "4": {"start": {"line": 11, "column": 10}, "end": {"line": 11, "column": 30}}, "5": {"start": {"line": 12, "column": 16}, "end": {"line": 12, "column": 38}}, "6": {"start": {"line": 13, "column": 12}, "end": {"line": 13, "column": 33}}, "7": {"start": {"line": 14, "column": 18}, "end": {"line": 14, "column": 46}}, "8": {"start": {"line": 15, "column": 21}, "end": {"line": 15, "column": 52}}, "9": {"start": {"line": 16, "column": 13}, "end": {"line": 16, "column": 33}}, "10": {"start": {"line": 17, "column": 15}, "end": {"line": 17, "column": 34}}, "11": {"start": {"line": 18, "column": 14}, "end": {"line": 18, "column": 32}}, "12": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 31}}, "13": {"start": {"line": 28, "column": 11}, "end": {"line": 28, "column": 15}}, "14": {"start": {"line": 29, "column": 2}, "end": {"line": 29, "column": 48}}, "15": {"start": {"line": 29, "column": 30}, "end": {"line": 29, "column": 48}}, "16": {"start": {"line": 34, "column": 2}, "end": {"line": 34, "column": 18}}, "17": {"start": {"line": 36, "column": 2}, "end": {"line": 36, "column": 20}}, "18": {"start": {"line": 37, "column": 2}, "end": {"line": 37, "column": 20}}, "19": {"start": {"line": 38, "column": 2}, "end": {"line": 38, "column": 35}}, "20": {"start": {"line": 39, "column": 2}, "end": {"line": 39, "column": 17}}, "21": {"start": {"line": 40, "column": 2}, "end": {"line": 40, "column": 19}}, "22": {"start": {"line": 41, "column": 2}, "end": {"line": 41, "column": 20}}, "23": {"start": {"line": 43, "column": 2}, "end": {"line": 45, "column": 4}}, "24": {"start": {"line": 44, "column": 4}, "end": {"line": 44, "column": 23}}, "25": {"start": {"line": 47, "column": 2}, "end": {"line": 49, "column": 4}}, "26": {"start": {"line": 48, "column": 4}, "end": {"line": 48, "column": 18}}, "27": {"start": {"line": 51, "column": 2}, "end": {"line": 53, "column": 4}}, "28": {"start": {"line": 52, "column": 4}, "end": {"line": 52, "column": 19}}, "29": {"start": {"line": 55, "column": 2}, "end": {"line": 57, "column": 4}}, "30": {"start": {"line": 56, "column": 4}, "end": {"line": 56, "column": 20}}, "31": {"start": {"line": 63, "column": 0}, "end": {"line": 67, "column": 1}}, "32": {"start": {"line": 64, "column": 11}, "end": {"line": 64, "column": 15}}, "33": {"start": {"line": 65, "column": 2}, "end": {"line": 65, "column": 57}}, "34": {"start": {"line": 65, "column": 31}, "end": {"line": 65, "column": 57}}, "35": {"start": {"line": 66, "column": 2}, "end": {"line": 66, "column": 16}}, "36": {"start": {"line": 72, "column": 0}, "end": {"line": 83, "column": 1}}, "37": {"start": {"line": 73, "column": 2}, "end": {"line": 81, "column": 3}}, "38": {"start": {"line": 77, "column": 4}, "end": {"line": 79, "column": 5}}, "39": {"start": {"line": 77, "column": 17}, "end": {"line": 77, "column": 18}}, "40": {"start": {"line": 77, "column": 24}, "end": {"line": 77, "column": 32}}, "41": {"start": {"line": 78, "column": 6}, "end": {"line": 78, "column": 62}}, "42": {"start": {"line": 78, "column": 22}, "end": {"line": 78, "column": 62}}, "43": {"start": {"line": 80, "column": 4}, "end": {"line": 80, "column": 10}}, "44": {"start": {"line": 82, "column": 2}, "end": {"line": 82, "column": 30}}, "45": {"start": {"line": 85, "column": 0}, "end": {"line": 88, "column": 1}}, "46": {"start": {"line": 86, "column": 2}, "end": {"line": 86, "column": 20}}, "47": {"start": {"line": 87, "column": 2}, "end": {"line": 87, "column": 28}}, "48": {"start": {"line": 94, "column": 0}, "end": {"line": 94, "column": 38}}, "49": {"start": {"line": 96, "column": 0}, "end": {"line": 137, "column": 1}}, "50": {"start": {"line": 97, "column": 2}, "end": {"line": 97, "column": 59}}, "51": {"start": {"line": 104, "column": 2}, "end": {"line": 134, "column": 3}}, "52": {"start": {"line": 105, "column": 16}, "end": {"line": 105, "column": 27}}, "53": {"start": {"line": 106, "column": 4}, "end": {"line": 110, "column": 5}}, "54": {"start": {"line": 106, "column": 22}, "end": {"line": 106, "column": 36}}, "55": {"start": {"line": 108, "column": 6}, "end": {"line": 108, "column": 34}}, "56": {"start": {"line": 109, "column": 6}, "end": {"line": 109, "column": 51}}, "57": {"start": {"line": 109, "column": 31}, "end": {"line": 109, "column": 51}}, "58": {"start": {"line": 111, "column": 4}, "end": {"line": 114, "column": 5}}, "59": {"start": {"line": 112, "column": 6}, "end": {"line": 112, "column": 17}}, "60": {"start": {"line": 113, "column": 6}, "end": {"line": 113, "column": 24}}, "61": {"start": {"line": 117, "column": 15}, "end": {"line": 117, "column": 19}}, "62": {"start": {"line": 118, "column": 4}, "end": {"line": 120, "column": 5}}, "63": {"start": {"line": 118, "column": 17}, "end": {"line": 118, "column": 18}}, "64": {"start": {"line": 119, "column": 6}, "end": {"line": 119, "column": 23}}, "65": {"start": {"line": 126, "column": 4}, "end": {"line": 133, "column": 5}}, "66": {"start": {"line": 127, "column": 6}, "end": {"line": 128, "column": 26}}, "67": {"start": {"line": 128, "column": 8}, "end": {"line": 128, "column": 26}}, "68": {"start": {"line": 129, "column": 6}, "end": {"line": 129, "column": 29}}, "69": {"start": {"line": 131, "column": 6}, "end": {"line": 131, "column": 30}}, "70": {"start": {"line": 132, "column": 6}, "end": {"line": 132, "column": 25}}, "71": {"start": {"line": 136, "column": 2}, "end": {"line": 136, "column": 22}}, "72": {"start": {"line": 140, "column": 0}, "end": {"line": 285, "column": 1}}, "73": {"start": {"line": 141, "column": 15}, "end": {"line": 141, "column": 31}}, "74": {"start": {"line": 142, "column": 13}, "end": {"line": 142, "column": 17}}, "75": {"start": {"line": 147, "column": 13}, "end": {"line": 147, "column": 18}}, "76": {"start": {"line": 149, "column": 2}, "end": {"line": 155, "column": 3}}, "77": {"start": {"line": 150, "column": 12}, "end": {"line": 150, "column": 41}}, "78": {"start": {"line": 151, "column": 4}, "end": {"line": 151, "column": 21}}, "79": {"start": {"line": 152, "column": 4}, "end": {"line": 152, "column": 37}}, "80": {"start": {"line": 153, "column": 4}, "end": {"line": 153, "column": 37}}, "81": {"start": {"line": 154, "column": 4}, "end": {"line": 154, "column": 32}}, "82": {"start": {"line": 157, "column": 2}, "end": {"line": 229, "column": 3}}, "83": {"start": {"line": 171, "column": 6}, "end": {"line": 171, "column": 23}}, "84": {"start": {"line": 172, "column": 6}, "end": {"line": 172, "column": 18}}, "85": {"start": {"line": 173, "column": 6}, "end": {"line": 173, "column": 11}}, "86": {"start": {"line": 177, "column": 6}, "end": {"line": 177, "column": 32}}, "87": {"start": {"line": 178, "column": 6}, "end": {"line": 183, "column": 7}}, "88": {"start": {"line": 179, "column": 8}, "end": {"line": 179, "column": 41}}, "89": {"start": {"line": 180, "column": 8}, "end": {"line": 182, "column": 10}}, "90": {"start": {"line": 181, "column": 10}, "end": {"line": 181, "column": 43}}, "91": {"start": {"line": 184, "column": 6}, "end": {"line": 184, "column": 33}}, "92": {"start": {"line": 185, "column": 6}, "end": {"line": 185, "column": 17}}, "93": {"start": {"line": 186, "column": 6}, "end": {"line": 186, "column": 11}}, "94": {"start": {"line": 191, "column": 6}, "end": {"line": 191, "column": 32}}, "95": {"start": {"line": 192, "column": 6}, "end": {"line": 194, "column": 7}}, "96": {"start": {"line": 193, "column": 8}, "end": {"line": 193, "column": 37}}, "97": {"start": {"line": 195, "column": 6}, "end": {"line": 195, "column": 27}}, "98": {"start": {"line": 196, "column": 6}, "end": {"line": 196, "column": 17}}, "99": {"start": {"line": 197, "column": 6}, "end": {"line": 197, "column": 11}}, "100": {"start": {"line": 201, "column": 6}, "end": {"line": 201, "column": 29}}, "101": {"start": {"line": 202, "column": 6}, "end": {"line": 205, "column": 7}}, "102": {"start": {"line": 203, "column": 8}, "end": {"line": 203, "column": 45}}, "103": {"start": {"line": 204, "column": 8}, "end": {"line": 204, "column": 44}}, "104": {"start": {"line": 206, "column": 6}, "end": {"line": 206, "column": 25}}, "105": {"start": {"line": 207, "column": 6}, "end": {"line": 207, "column": 17}}, "106": {"start": {"line": 208, "column": 6}, "end": {"line": 208, "column": 11}}, "107": {"start": {"line": 213, "column": 6}, "end": {"line": 213, "column": 29}}, "108": {"start": {"line": 214, "column": 6}, "end": {"line": 217, "column": 7}}, "109": {"start": {"line": 215, "column": 8}, "end": {"line": 215, "column": 45}}, "110": {"start": {"line": 216, "column": 8}, "end": {"line": 216, "column": 40}}, "111": {"start": {"line": 218, "column": 6}, "end": {"line": 218, "column": 21}}, "112": {"start": {"line": 219, "column": 6}, "end": {"line": 219, "column": 17}}, "113": {"start": {"line": 220, "column": 6}, "end": {"line": 220, "column": 11}}, "114": {"start": {"line": 226, "column": 6}, "end": {"line": 226, "column": 23}}, "115": {"start": {"line": 227, "column": 6}, "end": {"line": 227, "column": 25}}, "116": {"start": {"line": 228, "column": 6}, "end": {"line": 228, "column": 11}}, "117": {"start": {"line": 232, "column": 2}, "end": {"line": 241, "column": 3}}, "118": {"start": {"line": 233, "column": 4}, "end": {"line": 233, "column": 28}}, "119": {"start": {"line": 235, "column": 17}, "end": {"line": 235, "column": 29}}, "120": {"start": {"line": 236, "column": 19}, "end": {"line": 236, "column": 33}}, "121": {"start": {"line": 240, "column": 4}, "end": {"line": 240, "column": 25}}, "122": {"start": {"line": 242, "column": 2}, "end": {"line": 242, "column": 49}}, "123": {"start": {"line": 243, "column": 2}, "end": {"line": 243, "column": 19}}, "124": {"start": {"line": 246, "column": 2}, "end": {"line": 250, "column": 3}}, "125": {"start": {"line": 247, "column": 4}, "end": {"line": 249, "column": 6}}, "126": {"start": {"line": 248, "column": 6}, "end": {"line": 248, "column": 24}}, "127": {"start": {"line": 252, "column": 2}, "end": {"line": 252, "column": 35}}, "128": {"start": {"line": 252, "column": 13}, "end": {"line": 252, "column": 35}}, "129": {"start": {"line": 254, "column": 2}, "end": {"line": 256, "column": 3}}, "130": {"start": {"line": 255, "column": 4}, "end": {"line": 255, "column": 23}}, "131": {"start": {"line": 258, "column": 2}, "end": {"line": 258, "column": 21}}, "132": {"start": {"line": 260, "column": 2}, "end": {"line": 262, "column": 3}}, "133": {"start": {"line": 261, "column": 4}, "end": {"line": 261, "column": 39}}, "134": {"start": {"line": 264, "column": 11}, "end": {"line": 264, "column": 15}}, "135": {"start": {"line": 266, "column": 2}, "end": {"line": 268, "column": 4}}, "136": {"start": {"line": 267, "column": 4}, "end": {"line": 267, "column": 14}}, "137": {"start": {"line": 270, "column": 2}, "end": {"line": 272, "column": 4}}, "138": {"start": {"line": 271, "column": 4}, "end": {"line": 271, "column": 15}}, "139": {"start": {"line": 274, "column": 2}, "end": {"line": 276, "column": 3}}, "140": {"start": {"line": 275, "column": 4}, "end": {"line": 275, "column": 29}}, "141": {"start": {"line": 278, "column": 2}, "end": {"line": 278, "column": 22}}, "142": {"start": {"line": 281, "column": 2}, "end": {"line": 284, "column": 3}}, "143": {"start": {"line": 282, "column": 4}, "end": {"line": 282, "column": 15}}, "144": {"start": {"line": 283, "column": 4}, "end": {"line": 283, "column": 22}}}, "fnMap": {"0": {"name": "Parse", "decl": {"start": {"line": 27, "column": 9}, "end": {"line": 27, "column": 14}}, "loc": {"start": {"line": 27, "column": 18}, "end": {"line": 58, "column": 1}}, "line": 27}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 43, "column": 25}, "end": {"line": 43, "column": 26}}, "loc": {"start": {"line": 43, "column": 38}, "end": {"line": 45, "column": 3}}, "line": 43}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 47, "column": 24}, "end": {"line": 47, "column": 25}}, "loc": {"start": {"line": 47, "column": 37}, "end": {"line": 49, "column": 3}}, "line": 47}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 51, "column": 23}, "end": {"line": 51, "column": 24}}, "loc": {"start": {"line": 51, "column": 35}, "end": {"line": 53, "column": 3}}, "line": 51}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 55, "column": 25}, "end": {"line": 55, "column": 26}}, "loc": {"start": {"line": 55, "column": 37}, "end": {"line": 57, "column": 3}}, "line": 55}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 63, "column": 29}, "end": {"line": 63, "column": 30}}, "loc": {"start": {"line": 63, "column": 41}, "end": {"line": 67, "column": 1}}, "line": 63}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 72, "column": 24}, "end": {"line": 72, "column": 25}}, "loc": {"start": {"line": 72, "column": 37}, "end": {"line": 83, "column": 1}}, "line": 72}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 85, "column": 22}, "end": {"line": 85, "column": 23}}, "loc": {"start": {"line": 85, "column": 35}, "end": {"line": 88, "column": 1}}, "line": 85}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 94, "column": 24}, "end": {"line": 94, "column": 25}}, "loc": {"start": {"line": 94, "column": 36}, "end": {"line": 94, "column": 38}}, "line": 94}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 96, "column": 27}, "end": {"line": 96, "column": 28}}, "loc": {"start": {"line": 96, "column": 40}, "end": {"line": 137, "column": 1}}, "line": 96}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 140, "column": 30}, "end": {"line": 140, "column": 31}}, "loc": {"start": {"line": 140, "column": 43}, "end": {"line": 285, "column": 1}}, "line": 140}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 178, "column": 14}, "end": {"line": 178, "column": 15}}, "loc": {"start": {"line": 178, "column": 26}, "end": {"line": 183, "column": 7}}, "line": 178}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 180, "column": 42}, "end": {"line": 180, "column": 43}}, "loc": {"start": {"line": 180, "column": 55}, "end": {"line": 182, "column": 9}}, "line": 180}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 192, "column": 14}, "end": {"line": 192, "column": 15}}, "loc": {"start": {"line": 192, "column": 26}, "end": {"line": 194, "column": 7}}, "line": 192}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 202, "column": 14}, "end": {"line": 202, "column": 15}}, "loc": {"start": {"line": 202, "column": 26}, "end": {"line": 205, "column": 7}}, "line": 202}, "15": {"name": "(anonymous_15)", "decl": {"start": {"line": 214, "column": 14}, "end": {"line": 214, "column": 15}}, "loc": {"start": {"line": 214, "column": 26}, "end": {"line": 217, "column": 7}}, "line": 214}, "16": {"name": "(anonymous_16)", "decl": {"start": {"line": 247, "column": 21}, "end": {"line": 247, "column": 22}}, "loc": {"start": {"line": 247, "column": 34}, "end": {"line": 249, "column": 5}}, "line": 247}, "17": {"name": "(anonymous_17)", "decl": {"start": {"line": 266, "column": 20}, "end": {"line": 266, "column": 21}}, "loc": {"start": {"line": 266, "column": 32}, "end": {"line": 268, "column": 3}}, "line": 266}, "18": {"name": "(anonymous_18)", "decl": {"start": {"line": 270, "column": 21}, "end": {"line": 270, "column": 22}}, "loc": {"start": {"line": 270, "column": 33}, "end": {"line": 272, "column": 3}}, "line": 270}}, "branchMap": {"0": {"loc": {"start": {"line": 29, "column": 2}, "end": {"line": 29, "column": 48}}, "type": "if", "locations": [{"start": {"line": 29, "column": 2}, "end": {"line": 29, "column": 48}}, {"start": {"line": 29, "column": 2}, "end": {"line": 29, "column": 48}}], "line": 29}, "1": {"loc": {"start": {"line": 65, "column": 2}, "end": {"line": 65, "column": 57}}, "type": "if", "locations": [{"start": {"line": 65, "column": 2}, "end": {"line": 65, "column": 57}}, {"start": {"line": 65, "column": 2}, "end": {"line": 65, "column": 57}}], "line": 65}, "2": {"loc": {"start": {"line": 65, "column": 6}, "end": {"line": 65, "column": 29}}, "type": "binary-expr", "locations": [{"start": {"line": 65, "column": 6}, "end": {"line": 65, "column": 16}}, {"start": {"line": 65, "column": 20}, "end": {"line": 65, "column": 29}}], "line": 65}, "3": {"loc": {"start": {"line": 73, "column": 2}, "end": {"line": 81, "column": 3}}, "type": "if", "locations": [{"start": {"line": 73, "column": 2}, "end": {"line": 81, "column": 3}}, {"start": {"line": 73, "column": 2}, "end": {"line": 81, "column": 3}}], "line": 73}, "4": {"loc": {"start": {"line": 78, "column": 6}, "end": {"line": 78, "column": 62}}, "type": "if", "locations": [{"start": {"line": 78, "column": 6}, "end": {"line": 78, "column": 62}}, {"start": {"line": 78, "column": 6}, "end": {"line": 78, "column": 62}}], "line": 78}, "5": {"loc": {"start": {"line": 97, "column": 9}, "end": {"line": 97, "column": 30}}, "type": "binary-expr", "locations": [{"start": {"line": 97, "column": 9}, "end": {"line": 97, "column": 10}}, {"start": {"line": 97, "column": 14}, "end": {"line": 97, "column": 30}}], "line": 97}, "6": {"loc": {"start": {"line": 104, "column": 2}, "end": {"line": 134, "column": 3}}, "type": "if", "locations": [{"start": {"line": 104, "column": 2}, "end": {"line": 134, "column": 3}}, {"start": {"line": 104, "column": 2}, "end": {"line": 134, "column": 3}}], "line": 104}, "7": {"loc": {"start": {"line": 106, "column": 4}, "end": {"line": 110, "column": 5}}, "type": "if", "locations": [{"start": {"line": 106, "column": 4}, "end": {"line": 110, "column": 5}}, {"start": {"line": 106, "column": 4}, "end": {"line": 110, "column": 5}}], "line": 106}, "8": {"loc": {"start": {"line": 109, "column": 6}, "end": {"line": 109, "column": 51}}, "type": "if", "locations": [{"start": {"line": 109, "column": 6}, "end": {"line": 109, "column": 51}}, {"start": {"line": 109, "column": 6}, "end": {"line": 109, "column": 51}}], "line": 109}, "9": {"loc": {"start": {"line": 111, "column": 4}, "end": {"line": 114, "column": 5}}, "type": "if", "locations": [{"start": {"line": 111, "column": 4}, "end": {"line": 114, "column": 5}}, {"start": {"line": 111, "column": 4}, "end": {"line": 114, "column": 5}}], "line": 111}, "10": {"loc": {"start": {"line": 118, "column": 20}, "end": {"line": 118, "column": 35}}, "type": "binary-expr", "locations": [{"start": {"line": 118, "column": 20}, "end": {"line": 118, "column": 27}}, {"start": {"line": 118, "column": 31}, "end": {"line": 118, "column": 35}}], "line": 118}, "11": {"loc": {"start": {"line": 126, "column": 4}, "end": {"line": 133, "column": 5}}, "type": "if", "locations": [{"start": {"line": 126, "column": 4}, "end": {"line": 133, "column": 5}}, {"start": {"line": 126, "column": 4}, "end": {"line": 133, "column": 5}}], "line": 126}, "12": {"loc": {"start": {"line": 127, "column": 6}, "end": {"line": 128, "column": 26}}, "type": "if", "locations": [{"start": {"line": 127, "column": 6}, "end": {"line": 128, "column": 26}}, {"start": {"line": 127, "column": 6}, "end": {"line": 128, "column": 26}}], "line": 127}, "13": {"loc": {"start": {"line": 149, "column": 2}, "end": {"line": 155, "column": 3}}, "type": "if", "locations": [{"start": {"line": 149, "column": 2}, "end": {"line": 155, "column": 3}}, {"start": {"line": 149, "column": 2}, "end": {"line": 155, "column": 3}}], "line": 149}, "14": {"loc": {"start": {"line": 149, "column": 6}, "end": {"line": 149, "column": 48}}, "type": "binary-expr", "locations": [{"start": {"line": 149, "column": 6}, "end": {"line": 149, "column": 26}}, {"start": {"line": 149, "column": 30}, "end": {"line": 149, "column": 48}}], "line": 149}, "15": {"loc": {"start": {"line": 157, "column": 2}, "end": {"line": 229, "column": 3}}, "type": "switch", "locations": [{"start": {"line": 158, "column": 4}, "end": {"line": 158, "column": 16}}, {"start": {"line": 159, "column": 4}, "end": {"line": 159, "column": 19}}, {"start": {"line": 160, "column": 4}, "end": {"line": 160, "column": 16}}, {"start": {"line": 161, "column": 4}, "end": {"line": 161, "column": 24}}, {"start": {"line": 162, "column": 4}, "end": {"line": 162, "column": 27}}, {"start": {"line": 163, "column": 4}, "end": {"line": 163, "column": 23}}, {"start": {"line": 164, "column": 4}, "end": {"line": 164, "column": 21}}, {"start": {"line": 165, "column": 4}, "end": {"line": 165, "column": 16}}, {"start": {"line": 166, "column": 4}, "end": {"line": 166, "column": 26}}, {"start": {"line": 167, "column": 4}, "end": {"line": 173, "column": 11}}, {"start": {"line": 175, "column": 4}, "end": {"line": 186, "column": 11}}, {"start": {"line": 188, "column": 4}, "end": {"line": 188, "column": 26}}, {"start": {"line": 189, "column": 4}, "end": {"line": 197, "column": 11}}, {"start": {"line": 199, "column": 4}, "end": {"line": 208, "column": 11}}, {"start": {"line": 210, "column": 4}, "end": {"line": 210, "column": 31}}, {"start": {"line": 211, "column": 4}, "end": {"line": 220, "column": 11}}, {"start": {"line": 222, "column": 4}, "end": {"line": 228, "column": 11}}], "line": 157}, "16": {"loc": {"start": {"line": 179, "column": 23}, "end": {"line": 179, "column": 41}}, "type": "binary-expr", "locations": [{"start": {"line": 179, "column": 23}, "end": {"line": 179, "column": 35}}, {"start": {"line": 179, "column": 39}, "end": {"line": 179, "column": 41}}], "line": 179}, "17": {"loc": {"start": {"line": 203, "column": 25}, "end": {"line": 203, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 203, "column": 25}, "end": {"line": 203, "column": 39}}, {"start": {"line": 203, "column": 43}, "end": {"line": 203, "column": 45}}], "line": 203}, "18": {"loc": {"start": {"line": 215, "column": 25}, "end": {"line": 215, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 215, "column": 25}, "end": {"line": 215, "column": 39}}, {"start": {"line": 215, "column": 43}, "end": {"line": 215, "column": 45}}], "line": 215}, "19": {"loc": {"start": {"line": 232, "column": 2}, "end": {"line": 241, "column": 3}}, "type": "if", "locations": [{"start": {"line": 232, "column": 2}, "end": {"line": 241, "column": 3}}, {"start": {"line": 232, "column": 2}, "end": {"line": 241, "column": 3}}], "line": 232}, "20": {"loc": {"start": {"line": 246, "column": 2}, "end": {"line": 250, "column": 3}}, "type": "if", "locations": [{"start": {"line": 246, "column": 2}, "end": {"line": 250, "column": 3}}, {"start": {"line": 246, "column": 2}, "end": {"line": 250, "column": 3}}], "line": 246}, "21": {"loc": {"start": {"line": 252, "column": 2}, "end": {"line": 252, "column": 35}}, "type": "if", "locations": [{"start": {"line": 252, "column": 2}, "end": {"line": 252, "column": 35}}, {"start": {"line": 252, "column": 2}, "end": {"line": 252, "column": 35}}], "line": 252}, "22": {"loc": {"start": {"line": 254, "column": 2}, "end": {"line": 256, "column": 3}}, "type": "if", "locations": [{"start": {"line": 254, "column": 2}, "end": {"line": 256, "column": 3}}, {"start": {"line": 254, "column": 2}, "end": {"line": 256, "column": 3}}], "line": 254}, "23": {"loc": {"start": {"line": 254, "column": 6}, "end": {"line": 254, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 254, "column": 6}, "end": {"line": 254, "column": 27}}, {"start": {"line": 254, "column": 31}, "end": {"line": 254, "column": 58}}], "line": 254}, "24": {"loc": {"start": {"line": 260, "column": 2}, "end": {"line": 262, "column": 3}}, "type": "if", "locations": [{"start": {"line": 260, "column": 2}, "end": {"line": 262, "column": 3}}, {"start": {"line": 260, "column": 2}, "end": {"line": 262, "column": 3}}], "line": 260}, "25": {"loc": {"start": {"line": 274, "column": 2}, "end": {"line": 276, "column": 3}}, "type": "if", "locations": [{"start": {"line": 274, "column": 2}, "end": {"line": 276, "column": 3}}, {"start": {"line": 274, "column": 2}, "end": {"line": 276, "column": 3}}], "line": 274}, "26": {"loc": {"start": {"line": 281, "column": 2}, "end": {"line": 284, "column": 3}}, "type": "if", "locations": [{"start": {"line": 281, "column": 2}, "end": {"line": 284, "column": 3}}, {"start": {"line": 281, "column": 2}, "end": {"line": 284, "column": 3}}], "line": 281}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 1, "12": 1, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 1, "32": 0, "33": 0, "34": 0, "35": 0, "36": 1, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 1, "46": 0, "47": 0, "48": 1, "49": 1, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 1, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0, "108": 0, "109": 0, "110": 0, "111": 0, "112": 0, "113": 0, "114": 0, "115": 0, "116": 0, "117": 0, "118": 0, "119": 0, "120": 0, "121": 0, "122": 0, "123": 0, "124": 0, "125": 0, "126": 0, "127": 0, "128": 0, "129": 0, "130": 0, "131": 0, "132": 0, "133": 0, "134": 0, "135": 0, "136": 0, "137": 0, "138": 0, "139": 0, "140": 0, "141": 0, "142": 0, "143": 0, "144": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0]}, "_coverageSchema": "43e27e138ebf9cfc5966b082cf9a028302ed4184", "hash": "4a8f51a3a12b59eb69694618530032a813fbf8cb", "contentHash": "d347e73abcd3109143efee20401d9f0bfc6d68a4a6323bd4559ada44c741786a"}, "/Users/<USER>/dev/js/tar/lib/buffer-entry.js": {"path": "/Users/<USER>/dev/js/tar/lib/buffer-entry.js", "statementMap": {"0": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 28}}, "1": {"start": {"line": 10, "column": 15}, "end": {"line": 10, "column": 34}}, "2": {"start": {"line": 11, "column": 12}, "end": {"line": 11, "column": 33}}, "3": {"start": {"line": 14, "column": 2}, "end": {"line": 14, "column": 30}}, "4": {"start": {"line": 15, "column": 2}, "end": {"line": 15, "column": 44}}, "5": {"start": {"line": 16, "column": 2}, "end": {"line": 16, "column": 18}}, "6": {"start": {"line": 17, "column": 2}, "end": {"line": 17, "column": 16}}, "7": {"start": {"line": 18, "column": 2}, "end": {"line": 20, "column": 4}}, "8": {"start": {"line": 19, "column": 4}, "end": {"line": 19, "column": 52}}, "9": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 28}}, "10": {"start": {"line": 26, "column": 0}, "end": {"line": 30, "column": 1}}, "11": {"start": {"line": 27, "column": 2}, "end": {"line": 27, "column": 36}}, "12": {"start": {"line": 28, "column": 2}, "end": {"line": 28, "column": 26}}, "13": {"start": {"line": 29, "column": 2}, "end": {"line": 29, "column": 37}}}, "fnMap": {"0": {"name": "BufferEntry", "decl": {"start": {"line": 13, "column": 9}, "end": {"line": 13, "column": 20}}, "loc": {"start": {"line": 13, "column": 24}, "end": {"line": 21, "column": 1}}, "line": 13}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 18, "column": 17}, "end": {"line": 18, "column": 18}}, "loc": {"start": {"line": 18, "column": 29}, "end": {"line": 20, "column": 3}}, "line": 18}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 26, "column": 30}, "end": {"line": 26, "column": 31}}, "loc": {"start": {"line": 26, "column": 43}, "end": {"line": 30, "column": 1}}, "line": 26}}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 1, "10": 1, "11": 0, "12": 0, "13": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {}, "_coverageSchema": "43e27e138ebf9cfc5966b082cf9a028302ed4184", "hash": "d8ad66df900443c9c9d8aefe5fc126a55ba268ec", "contentHash": "25e5b3c51e476fcffac7affa191a0ed0f54d64d3d3b760e887bd45ba08fffa65"}, "/Users/<USER>/dev/js/tar/lib/extended-header.js": {"path": "/Users/<USER>/dev/js/tar/lib/extended-header.js", "statementMap": {"0": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 31}}, "1": {"start": {"line": 11, "column": 12}, "end": {"line": 11, "column": 33}}, "2": {"start": {"line": 12, "column": 15}, "end": {"line": 12, "column": 34}}, "3": {"start": {"line": 13, "column": 10}, "end": {"line": 13, "column": 30}}, "4": {"start": {"line": 14, "column": 14}, "end": {"line": 14, "column": 25}}, "5": {"start": {"line": 15, "column": 15}, "end": {"line": 17, "column": 42}}, "6": {"start": {"line": 20, "column": 2}, "end": {"line": 20, "column": 30}}, "7": {"start": {"line": 21, "column": 2}, "end": {"line": 21, "column": 30}}, "8": {"start": {"line": 22, "column": 2}, "end": {"line": 22, "column": 18}}, "9": {"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": 20}}, "10": {"start": {"line": 24, "column": 2}, "end": {"line": 24, "column": 20}}, "11": {"start": {"line": 25, "column": 2}, "end": {"line": 25, "column": 20}}, "12": {"start": {"line": 26, "column": 2}, "end": {"line": 26, "column": 20}}, "13": {"start": {"line": 27, "column": 2}, "end": {"line": 27, "column": 19}}, "14": {"start": {"line": 28, "column": 2}, "end": {"line": 28, "column": 19}}, "15": {"start": {"line": 29, "column": 2}, "end": {"line": 29, "column": 17}}, "16": {"start": {"line": 30, "column": 2}, "end": {"line": 30, "column": 16}}, "17": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 31}}, "18": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 39}}, "19": {"start": {"line": 36, "column": 8}, "end": {"line": 36, "column": 9}}, "20": {"start": {"line": 37, "column": 13}, "end": {"line": 37, "column": 39}}, "21": {"start": {"line": 38, "column": 11}, "end": {"line": 38, "column": 28}}, "22": {"start": {"line": 39, "column": 11}, "end": {"line": 39, "column": 28}}, "23": {"start": {"line": 40, "column": 11}, "end": {"line": 40, "column": 28}}, "24": {"start": {"line": 41, "column": 11}, "end": {"line": 41, "column": 28}}, "25": {"start": {"line": 43, "column": 0}, "end": {"line": 45, "column": 2}}, "26": {"start": {"line": 44, "column": 2}, "end": {"line": 44, "column": 31}}, "27": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 16}}, "28": {"start": {"line": 50, "column": 9}, "end": {"line": 50, "column": 26}}, "29": {"start": {"line": 51, "column": 9}, "end": {"line": 51, "column": 26}}, "30": {"start": {"line": 52, "column": 12}, "end": {"line": 52, "column": 29}}, "31": {"start": {"line": 53, "column": 8}, "end": {"line": 53, "column": 25}}, "32": {"start": {"line": 54, "column": 8}, "end": {"line": 54, "column": 25}}, "33": {"start": {"line": 55, "column": 8}, "end": {"line": 55, "column": 25}}, "34": {"start": {"line": 56, "column": 8}, "end": {"line": 56, "column": 25}}, "35": {"start": {"line": 57, "column": 12}, "end": {"line": 57, "column": 29}}, "36": {"start": {"line": 58, "column": 9}, "end": {"line": 58, "column": 26}}, "37": {"start": {"line": 59, "column": 9}, "end": {"line": 59, "column": 27}}, "38": {"start": {"line": 62, "column": 2}, "end": {"line": 62, "column": 33}}, "39": {"start": {"line": 62, "column": 27}, "end": {"line": 62, "column": 33}}, "40": {"start": {"line": 64, "column": 2}, "end": {"line": 130, "column": 3}}, "41": {"start": {"line": 64, "column": 16}, "end": {"line": 64, "column": 17}}, "42": {"start": {"line": 64, "column": 23}, "end": {"line": 64, "column": 31}}, "43": {"start": {"line": 69, "column": 12}, "end": {"line": 69, "column": 16}}, "44": {"start": {"line": 71, "column": 4}, "end": {"line": 74, "column": 5}}, "45": {"start": {"line": 72, "column": 6}, "end": {"line": 72, "column": 53}}, "46": {"start": {"line": 73, "column": 6}, "end": {"line": 73, "column": 12}}, "47": {"start": {"line": 76, "column": 4}, "end": {"line": 129, "column": 5}}, "48": {"start": {"line": 77, "column": 16}, "end": {"line": 77, "column": 22}}, "49": {"start": {"line": 81, "column": 8}, "end": {"line": 87, "column": 9}}, "50": {"start": {"line": 82, "column": 10}, "end": {"line": 82, "column": 27}}, "51": {"start": {"line": 84, "column": 10}, "end": {"line": 84, "column": 73}}, "52": {"start": {"line": 85, "column": 10}, "end": {"line": 85, "column": 34}}, "53": {"start": {"line": 86, "column": 10}, "end": {"line": 86, "column": 18}}, "54": {"start": {"line": 88, "column": 8}, "end": {"line": 91, "column": 9}}, "55": {"start": {"line": 89, "column": 10}, "end": {"line": 89, "column": 68}}, "56": {"start": {"line": 90, "column": 10}, "end": {"line": 90, "column": 16}}, "57": {"start": {"line": 92, "column": 8}, "end": {"line": 92, "column": 29}}, "58": {"start": {"line": 93, "column": 8}, "end": {"line": 93, "column": 16}}, "59": {"start": {"line": 97, "column": 8}, "end": {"line": 103, "column": 9}}, "60": {"start": {"line": 98, "column": 10}, "end": {"line": 98, "column": 27}}, "61": {"start": {"line": 99, "column": 10}, "end": {"line": 99, "column": 57}}, "62": {"start": {"line": 100, "column": 10}, "end": {"line": 100, "column": 66}}, "63": {"start": {"line": 100, "column": 35}, "end": {"line": 100, "column": 66}}, "64": {"start": {"line": 101, "column": 10}, "end": {"line": 101, "column": 33}}, "65": {"start": {"line": 102, "column": 10}, "end": {"line": 102, "column": 18}}, "66": {"start": {"line": 104, "column": 8}, "end": {"line": 104, "column": 28}}, "67": {"start": {"line": 105, "column": 8}, "end": {"line": 105, "column": 16}}, "68": {"start": {"line": 109, "column": 8}, "end": {"line": 126, "column": 9}}, "69": {"start": {"line": 111, "column": 10}, "end": {"line": 114, "column": 11}}, "70": {"start": {"line": 112, "column": 12}, "end": {"line": 112, "column": 55}}, "71": {"start": {"line": 113, "column": 12}, "end": {"line": 113, "column": 18}}, "72": {"start": {"line": 115, "column": 20}, "end": {"line": 115, "column": 55}}, "73": {"start": {"line": 116, "column": 10}, "end": {"line": 118, "column": 11}}, "74": {"start": {"line": 117, "column": 12}, "end": {"line": 117, "column": 33}}, "75": {"start": {"line": 119, "column": 10}, "end": {"line": 119, "column": 38}}, "76": {"start": {"line": 121, "column": 10}, "end": {"line": 121, "column": 33}}, "77": {"start": {"line": 122, "column": 10}, "end": {"line": 122, "column": 28}}, "78": {"start": {"line": 123, "column": 10}, "end": {"line": 123, "column": 25}}, "79": {"start": {"line": 124, "column": 10}, "end": {"line": 124, "column": 29}}, "80": {"start": {"line": 125, "column": 10}, "end": {"line": 125, "column": 18}}, "81": {"start": {"line": 127, "column": 8}, "end": {"line": 127, "column": 28}}, "82": {"start": {"line": 128, "column": 8}, "end": {"line": 128, "column": 16}}, "83": {"start": {"line": 134, "column": 2}, "end": {"line": 136, "column": 42}}, "84": {"start": {"line": 138, "column": 2}, "end": {"line": 138, "column": 15}}, "85": {"start": {"line": 139, "column": 2}, "end": {"line": 139, "column": 16}}}, "fnMap": {"0": {"name": "ExtendedHeader", "decl": {"start": {"line": 19, "column": 9}, "end": {"line": 19, "column": 23}}, "loc": {"start": {"line": 19, "column": 27}, "end": {"line": 31, "column": 1}}, "line": 19}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 43, "column": 28}, "end": {"line": 43, "column": 29}}, "loc": {"start": {"line": 43, "column": 41}, "end": {"line": 45, "column": 1}}, "line": 43}, "2": {"name": "parse", "decl": {"start": {"line": 61, "column": 9}, "end": {"line": 61, "column": 14}}, "loc": {"start": {"line": 61, "column": 19}, "end": {"line": 131, "column": 1}}, "line": 61}, "3": {"name": "error", "decl": {"start": {"line": 133, "column": 9}, "end": {"line": 133, "column": 14}}, "loc": {"start": {"line": 133, "column": 25}, "end": {"line": 140, "column": 1}}, "line": 133}}, "branchMap": {"0": {"loc": {"start": {"line": 62, "column": 2}, "end": {"line": 62, "column": 33}}, "type": "if", "locations": [{"start": {"line": 62, "column": 2}, "end": {"line": 62, "column": 33}}, {"start": {"line": 62, "column": 2}, "end": {"line": 62, "column": 33}}], "line": 62}, "1": {"loc": {"start": {"line": 71, "column": 4}, "end": {"line": 74, "column": 5}}, "type": "if", "locations": [{"start": {"line": 71, "column": 4}, "end": {"line": 74, "column": 5}}, {"start": {"line": 71, "column": 4}, "end": {"line": 74, "column": 5}}], "line": 71}, "2": {"loc": {"start": {"line": 71, "column": 8}, "end": {"line": 71, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 71, "column": 8}, "end": {"line": 71, "column": 23}}, {"start": {"line": 71, "column": 27}, "end": {"line": 71, "column": 54}}], "line": 71}, "3": {"loc": {"start": {"line": 76, "column": 4}, "end": {"line": 129, "column": 5}}, "type": "switch", "locations": [{"start": {"line": 77, "column": 6}, "end": {"line": 77, "column": 22}}, {"start": {"line": 79, "column": 6}, "end": {"line": 93, "column": 16}}, {"start": {"line": 95, "column": 6}, "end": {"line": 105, "column": 16}}, {"start": {"line": 107, "column": 6}, "end": {"line": 128, "column": 16}}], "line": 76}, "4": {"loc": {"start": {"line": 81, "column": 8}, "end": {"line": 87, "column": 9}}, "type": "if", "locations": [{"start": {"line": 81, "column": 8}, "end": {"line": 87, "column": 9}}, {"start": {"line": 81, "column": 8}, "end": {"line": 87, "column": 9}}], "line": 81}, "5": {"loc": {"start": {"line": 88, "column": 8}, "end": {"line": 91, "column": 9}}, "type": "if", "locations": [{"start": {"line": 88, "column": 8}, "end": {"line": 91, "column": 9}}, {"start": {"line": 88, "column": 8}, "end": {"line": 91, "column": 9}}], "line": 88}, "6": {"loc": {"start": {"line": 88, "column": 12}, "end": {"line": 88, "column": 28}}, "type": "binary-expr", "locations": [{"start": {"line": 88, "column": 12}, "end": {"line": 88, "column": 18}}, {"start": {"line": 88, "column": 22}, "end": {"line": 88, "column": 28}}], "line": 88}, "7": {"loc": {"start": {"line": 97, "column": 8}, "end": {"line": 103, "column": 9}}, "type": "if", "locations": [{"start": {"line": 97, "column": 8}, "end": {"line": 103, "column": 9}}, {"start": {"line": 97, "column": 8}, "end": {"line": 103, "column": 9}}], "line": 97}, "8": {"loc": {"start": {"line": 100, "column": 10}, "end": {"line": 100, "column": 66}}, "type": "if", "locations": [{"start": {"line": 100, "column": 10}, "end": {"line": 100, "column": 66}}, {"start": {"line": 100, "column": 10}, "end": {"line": 100, "column": 66}}], "line": 100}, "9": {"loc": {"start": {"line": 109, "column": 8}, "end": {"line": 126, "column": 9}}, "type": "if", "locations": [{"start": {"line": 109, "column": 8}, "end": {"line": 126, "column": 9}}, {"start": {"line": 109, "column": 8}, "end": {"line": 126, "column": 9}}], "line": 109}, "10": {"loc": {"start": {"line": 111, "column": 10}, "end": {"line": 114, "column": 11}}, "type": "if", "locations": [{"start": {"line": 111, "column": 10}, "end": {"line": 114, "column": 11}}, {"start": {"line": 111, "column": 10}, "end": {"line": 114, "column": 11}}], "line": 111}, "11": {"loc": {"start": {"line": 116, "column": 10}, "end": {"line": 118, "column": 11}}, "type": "if", "locations": [{"start": {"line": 116, "column": 10}, "end": {"line": 118, "column": 11}}, {"start": {"line": 116, "column": 10}, "end": {"line": 118, "column": 11}}], "line": 116}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 1, "18": 1, "19": 1, "20": 1, "21": 1, "22": 1, "23": 1, "24": 1, "25": 1, "26": 4, "27": 1, "28": 1, "29": 1, "30": 1, "31": 1, "32": 1, "33": 1, "34": 1, "35": 1, "36": 1, "37": 1, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0}, "f": {"0": 0, "1": 4, "2": 0, "3": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0, 0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0]}, "_coverageSchema": "43e27e138ebf9cfc5966b082cf9a028302ed4184", "hash": "77cb382c69a46833a08275dbc55bf34db0dcd796", "contentHash": "78a790af1726a774425fb478f1423068fe64414b747d89b597baf45139e7ae3d"}, "/Users/<USER>/dev/js/tar/lib/extract.js": {"path": "/Users/<USER>/dev/js/tar/lib/extract.js", "statementMap": {"0": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 24}}, "1": {"start": {"line": 5, "column": 10}, "end": {"line": 5, "column": 30}}, "2": {"start": {"line": 6, "column": 14}, "end": {"line": 6, "column": 32}}, "3": {"start": {"line": 7, "column": 15}, "end": {"line": 7, "column": 34}}, "4": {"start": {"line": 8, "column": 11}, "end": {"line": 8, "column": 26}}, "5": {"start": {"line": 11, "column": 2}, "end": {"line": 11, "column": 58}}, "6": {"start": {"line": 11, "column": 34}, "end": {"line": 11, "column": 58}}, "7": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": 23}}, "8": {"start": {"line": 14, "column": 2}, "end": {"line": 16, "column": 3}}, "9": {"start": {"line": 15, "column": 4}, "end": {"line": 15, "column": 25}}, "10": {"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": 59}}, "11": {"start": {"line": 20, "column": 2}, "end": {"line": 20, "column": 25}}, "12": {"start": {"line": 21, "column": 2}, "end": {"line": 21, "column": 23}}, "13": {"start": {"line": 24, "column": 2}, "end": {"line": 24, "column": 26}}, "14": {"start": {"line": 25, "column": 2}, "end": {"line": 25, "column": 52}}, "15": {"start": {"line": 25, "column": 38}, "end": {"line": 25, "column": 52}}, "16": {"start": {"line": 27, "column": 2}, "end": {"line": 27, "column": 34}}, "17": {"start": {"line": 29, "column": 2}, "end": {"line": 29, "column": 14}}, "18": {"start": {"line": 30, "column": 11}, "end": {"line": 30, "column": 15}}, "19": {"start": {"line": 35, "column": 2}, "end": {"line": 60, "column": 4}}, "20": {"start": {"line": 38, "column": 4}, "end": {"line": 45, "column": 5}}, "21": {"start": {"line": 39, "column": 14}, "end": {"line": 39, "column": 63}}, "22": {"start": {"line": 40, "column": 6}, "end": {"line": 40, "column": 39}}, "23": {"start": {"line": 41, "column": 6}, "end": {"line": 44, "column": 7}}, "24": {"start": {"line": 42, "column": 17}, "end": {"line": 42, "column": 70}}, "25": {"start": {"line": 43, "column": 8}, "end": {"line": 43, "column": 50}}, "26": {"start": {"line": 46, "column": 4}, "end": {"line": 49, "column": 5}}, "27": {"start": {"line": 47, "column": 6}, "end": {"line": 48, "column": 66}}, "28": {"start": {"line": 51, "column": 4}, "end": {"line": 59, "column": 5}}, "29": {"start": {"line": 52, "column": 15}, "end": {"line": 52, "column": 45}}, "30": {"start": {"line": 53, "column": 21}, "end": {"line": 53, "column": 41}}, "31": {"start": {"line": 54, "column": 19}, "end": {"line": 54, "column": 56}}, "32": {"start": {"line": 55, "column": 6}, "end": {"line": 57, "column": 7}}, "33": {"start": {"line": 56, "column": 8}, "end": {"line": 56, "column": 65}}, "34": {"start": {"line": 58, "column": 6}, "end": {"line": 58, "column": 54}}, "35": {"start": {"line": 62, "column": 2}, "end": {"line": 65, "column": 4}}, "36": {"start": {"line": 63, "column": 4}, "end": {"line": 63, "column": 36}}, "37": {"start": {"line": 64, "column": 4}, "end": {"line": 64, "column": 15}}, "38": {"start": {"line": 67, "column": 2}, "end": {"line": 69, "column": 4}}, "39": {"start": {"line": 68, "column": 4}, "end": {"line": 68, "column": 25}}, "40": {"start": {"line": 71, "column": 2}, "end": {"line": 73, "column": 4}}, "41": {"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 20}}, "42": {"start": {"line": 79, "column": 2}, "end": {"line": 84, "column": 4}}, "43": {"start": {"line": 81, "column": 4}, "end": {"line": 81, "column": 21}}, "44": {"start": {"line": 82, "column": 4}, "end": {"line": 82, "column": 18}}, "45": {"start": {"line": 83, "column": 4}, "end": {"line": 83, "column": 20}}, "46": {"start": {"line": 87, "column": 0}, "end": {"line": 87, "column": 28}}, "47": {"start": {"line": 89, "column": 0}, "end": {"line": 94, "column": 1}}, "48": {"start": {"line": 90, "column": 11}, "end": {"line": 90, "column": 15}}, "49": {"start": {"line": 91, "column": 2}, "end": {"line": 91, "column": 57}}, "50": {"start": {"line": 91, "column": 31}, "end": {"line": 91, "column": 57}}, "51": {"start": {"line": 92, "column": 2}, "end": {"line": 92, "column": 15}}}, "fnMap": {"0": {"name": "Extract", "decl": {"start": {"line": 10, "column": 9}, "end": {"line": 10, "column": 16}}, "loc": {"start": {"line": 10, "column": 24}, "end": {"line": 85, "column": 1}}, "line": 10}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 35, "column": 17}, "end": {"line": 35, "column": 18}}, "loc": {"start": {"line": 35, "column": 34}, "end": {"line": 60, "column": 3}}, "line": 35}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 62, "column": 24}, "end": {"line": 62, "column": 25}}, "loc": {"start": {"line": 62, "column": 36}, "end": {"line": 65, "column": 3}}, "line": 62}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 67, "column": 24}, "end": {"line": 67, "column": 25}}, "loc": {"start": {"line": 67, "column": 38}, "end": {"line": 69, "column": 3}}, "line": 67}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 71, "column": 24}, "end": {"line": 71, "column": 25}}, "loc": {"start": {"line": 71, "column": 35}, "end": {"line": 73, "column": 3}}, "line": 71}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 79, "column": 24}, "end": {"line": 79, "column": 25}}, "loc": {"start": {"line": 79, "column": 36}, "end": {"line": 84, "column": 3}}, "line": 79}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 89, "column": 31}, "end": {"line": 89, "column": 32}}, "loc": {"start": {"line": 89, "column": 43}, "end": {"line": 94, "column": 1}}, "line": 89}}, "branchMap": {"0": {"loc": {"start": {"line": 11, "column": 2}, "end": {"line": 11, "column": 58}}, "type": "if", "locations": [{"start": {"line": 11, "column": 2}, "end": {"line": 11, "column": 58}}, {"start": {"line": 11, "column": 2}, "end": {"line": 11, "column": 58}}], "line": 11}, "1": {"loc": {"start": {"line": 14, "column": 2}, "end": {"line": 16, "column": 3}}, "type": "if", "locations": [{"start": {"line": 14, "column": 2}, "end": {"line": 16, "column": 3}}, {"start": {"line": 14, "column": 2}, "end": {"line": 16, "column": 3}}], "line": 14}, "2": {"loc": {"start": {"line": 19, "column": 14}, "end": {"line": 19, "column": 59}}, "type": "binary-expr", "locations": [{"start": {"line": 19, "column": 14}, "end": {"line": 19, "column": 23}}, {"start": {"line": 19, "column": 27}, "end": {"line": 19, "column": 59}}], "line": 19}, "3": {"loc": {"start": {"line": 25, "column": 2}, "end": {"line": 25, "column": 52}}, "type": "if", "locations": [{"start": {"line": 25, "column": 2}, "end": {"line": 25, "column": 52}}, {"start": {"line": 25, "column": 2}, "end": {"line": 25, "column": 52}}], "line": 25}, "4": {"loc": {"start": {"line": 25, "column": 6}, "end": {"line": 25, "column": 36}}, "type": "binary-expr", "locations": [{"start": {"line": 25, "column": 6}, "end": {"line": 25, "column": 17}}, {"start": {"line": 25, "column": 21}, "end": {"line": 25, "column": 36}}], "line": 25}, "5": {"loc": {"start": {"line": 38, "column": 4}, "end": {"line": 45, "column": 5}}, "type": "if", "locations": [{"start": {"line": 38, "column": 4}, "end": {"line": 45, "column": 5}}, {"start": {"line": 38, "column": 4}, "end": {"line": 45, "column": 5}}], "line": 38}, "6": {"loc": {"start": {"line": 41, "column": 6}, "end": {"line": 44, "column": 7}}, "type": "if", "locations": [{"start": {"line": 41, "column": 6}, "end": {"line": 44, "column": 7}}, {"start": {"line": 41, "column": 6}, "end": {"line": 44, "column": 7}}], "line": 41}, "7": {"loc": {"start": {"line": 46, "column": 4}, "end": {"line": 49, "column": 5}}, "type": "if", "locations": [{"start": {"line": 46, "column": 4}, "end": {"line": 49, "column": 5}}, {"start": {"line": 46, "column": 4}, "end": {"line": 49, "column": 5}}], "line": 46}, "8": {"loc": {"start": {"line": 51, "column": 4}, "end": {"line": 59, "column": 5}}, "type": "if", "locations": [{"start": {"line": 51, "column": 4}, "end": {"line": 59, "column": 5}}, {"start": {"line": 51, "column": 4}, "end": {"line": 59, "column": 5}}], "line": 51}, "9": {"loc": {"start": {"line": 52, "column": 15}, "end": {"line": 52, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 52, "column": 15}, "end": {"line": 52, "column": 39}}, {"start": {"line": 52, "column": 43}, "end": {"line": 52, "column": 45}}], "line": 52}, "10": {"loc": {"start": {"line": 55, "column": 6}, "end": {"line": 57, "column": 7}}, "type": "if", "locations": [{"start": {"line": 55, "column": 6}, "end": {"line": 57, "column": 7}}, {"start": {"line": 55, "column": 6}, "end": {"line": 57, "column": 7}}], "line": 55}, "11": {"loc": {"start": {"line": 91, "column": 2}, "end": {"line": 91, "column": 57}}, "type": "if", "locations": [{"start": {"line": 91, "column": 2}, "end": {"line": 91, "column": 57}}, {"start": {"line": 91, "column": 2}, "end": {"line": 91, "column": 57}}], "line": 91}, "12": {"loc": {"start": {"line": 91, "column": 6}, "end": {"line": 91, "column": 29}}, "type": "binary-expr", "locations": [{"start": {"line": 91, "column": 6}, "end": {"line": 91, "column": 16}}, {"start": {"line": 91, "column": 20}, "end": {"line": 91, "column": 29}}], "line": 91}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 1, "47": 1, "48": 0, "49": 0, "50": 0, "51": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0]}, "_coverageSchema": "43e27e138ebf9cfc5966b082cf9a028302ed4184", "hash": "3fb9f60facc409fe944dc36bd2e14fc6153e403d", "contentHash": "3d9485ca58c6c768b022a39d276296b53647ccf39986885459406e933921c962"}}