{"uuid": "ea0194cb-f599-402f-8876-84fb8251e8db", "parent": "37d58de4-deea-4808-bb77-d27685bd1501", "pid": 93239, "argv": ["/usr/local/bin/node", "/Users/<USER>/dev/js/tar/test/00-setup-fixtures.js"], "execArgv": ["-r", "/usr/local/lib/node_modules/tap/node_modules/esm/esm.js"], "cwd": "/Users/<USER>/dev/js/tar", "time": 1557878801284, "ppid": 93238, "root": "e52f8603-1293-44df-8bfa-ed740bdd2b77", "coverageFilename": "/Users/<USER>/dev/js/tar/.nyc_output/ea0194cb-f599-402f-8876-84fb8251e8db.json", "externalId": "test/00-setup-fixtures.js", "files": []}