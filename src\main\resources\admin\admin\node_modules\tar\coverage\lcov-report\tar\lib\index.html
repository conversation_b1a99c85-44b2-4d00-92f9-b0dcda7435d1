<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for tar/lib</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">All files</a> tar/lib
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">99.94% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>1782/1783</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">99.67% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>1224/1228</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">100% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>271/271</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">99.94% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>1735/1736</span>
      </div>
    </div>
  </div>
  <div class='status-line high'></div>
<div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="create.js"><a href="create.js.html">create.js</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="59" class="abs high">59/59</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="35" class="abs high">35/35</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="12" class="abs high">12/12</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="59" class="abs high">59/59</td>
	</tr>

<tr>
	<td class="file high" data-value="extract.js"><a href="extract.js.html">extract.js</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="61" class="abs high">61/61</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="45" class="abs high">45/45</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="12" class="abs high">12/12</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="60" class="abs high">60/60</td>
	</tr>

<tr>
	<td class="file high" data-value="header.js"><a href="header.js.html">header.js</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="161" class="abs high">161/161</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="120" class="abs high">120/120</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="20" class="abs high">20/20</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="159" class="abs high">159/159</td>
	</tr>

<tr>
	<td class="file high" data-value="high-level-opt.js"><a href="high-level-opt.js.html">high-level-opt.js</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="5" class="abs high">5/5</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="3" class="abs high">3/3</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="3" class="abs high">3/3</td>
	</tr>

<tr>
	<td class="file high" data-value="large-numbers.js"><a href="large-numbers.js.html">large-numbers.js</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="59" class="abs high">59/59</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="20" class="abs high">20/20</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="8" class="abs high">8/8</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="57" class="abs high">57/57</td>
	</tr>

<tr>
	<td class="file high" data-value="list.js"><a href="list.js.html">list.js</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="80" class="abs high">80/80</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="53" class="abs high">53/53</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="14" class="abs high">14/14</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="77" class="abs high">77/77</td>
	</tr>

<tr>
	<td class="file high" data-value="mkdir.js"><a href="mkdir.js.html">mkdir.js</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="130" class="abs high">130/130</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="91" class="abs high">91/91</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="15" class="abs high">15/15</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="128" class="abs high">128/128</td>
	</tr>

<tr>
	<td class="file high" data-value="pack.js"><a href="pack.js.html">pack.js</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="228" class="abs high">228/228</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="123" class="abs high">123/123</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="44" class="abs high">44/44</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="222" class="abs high">222/222</td>
	</tr>

<tr>
	<td class="file high" data-value="parse.js"><a href="parse.js.html">parse.js</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="242" class="abs high">242/242</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="155" class="abs high">155/155</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="24" class="abs high">24/24</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="236" class="abs high">236/236</td>
	</tr>

<tr>
	<td class="file high" data-value="pax.js"><a href="pax.js.html">pax.js</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="62" class="abs high">62/62</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="75" class="abs high">75/75</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="9" class="abs high">9/9</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="60" class="abs high">60/60</td>
	</tr>

<tr>
	<td class="file high" data-value="read-entry.js"><a href="read-entry.js.html">read-entry.js</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="53" class="abs high">53/53</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="36" class="abs high">36/36</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="3" class="abs high">3/3</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="51" class="abs high">51/51</td>
	</tr>

<tr>
	<td class="file high" data-value="replace.js"><a href="replace.js.html">replace.js</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="131" class="abs high">131/131</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="70" class="abs high">70/70</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="18" class="abs high">18/18</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="128" class="abs high">128/128</td>
	</tr>

<tr>
	<td class="file high" data-value="types.js"><a href="types.js.html">types.js</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="3" class="abs high">3/3</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="2" class="abs high">2/2</td>
	</tr>

<tr>
	<td class="file high" data-value="unpack.js"><a href="unpack.js.html">unpack.js</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="254" class="abs high">254/254</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="194" class="abs high">194/194</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="44" class="abs high">44/44</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="247" class="abs high">247/247</td>
	</tr>

<tr>
	<td class="file high" data-value="update.js"><a href="update.js.html">update.js</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="20" class="abs high">20/20</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="15" class="abs high">15/15</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="20" class="abs high">20/20</td>
	</tr>

<tr>
	<td class="file high" data-value="warn-mixin.js"><a href="warn-mixin.js.html">warn-mixin.js</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="9" class="abs high">9/9</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="2" class="abs high">2/2</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="8" class="abs high">8/8</td>
	</tr>

<tr>
	<td class="file high" data-value="winchars.js"><a href="winchars.js.html">winchars.js</a></td>
	<td data-value="100" class="pic high"><div class="chart"><div class="cover-fill cover-full" style="width: 100%;"></div><div class="cover-empty" style="width:0%;"></div></div></td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="12" class="abs high">12/12</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="7" class="abs high">7/7</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="8" class="abs high">8/8</td>
	</tr>

<tr>
	<td class="file high" data-value="write-entry.js"><a href="write-entry.js.html">write-entry.js</a></td>
	<td data-value="99.53" class="pic high"><div class="chart"><div class="cover-fill" style="width: 99%;"></div><div class="cover-empty" style="width:1%;"></div></div></td>
	<td data-value="99.53" class="pct high">99.53%</td>
	<td data-value="214" class="abs high">213/214</td>
	<td data-value="97.87" class="pct high">97.87%</td>
	<td data-value="188" class="abs high">184/188</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="31" class="abs high">31/31</td>
	<td data-value="99.53" class="pct high">99.53%</td>
	<td data-value="211" class="abs high">210/211</td>
	</tr>

</tbody>
</table>
</div><div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="https://istanbul.js.org/" target="_blank">istanbul</a> at Mon Nov 20 2017 16:00:38 GMT-0800 (PST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
