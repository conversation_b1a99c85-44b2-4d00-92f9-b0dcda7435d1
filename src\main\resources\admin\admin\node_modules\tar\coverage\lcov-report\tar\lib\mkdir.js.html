<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for tar/lib/mkdir.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="../../index.html">All files</a> / <a href="index.html">tar/lib</a> mkdir.js
    </h1>
    <div class='clearfix'>
      <div class='fl pad1y space-right2'>
        <span class="strong">100% </span>
        <span class="quiet">Statements</span>
        <span class='fraction'>130/130</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">100% </span>
        <span class="quiet">Branches</span>
        <span class='fraction'>91/91</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">100% </span>
        <span class="quiet">Functions</span>
        <span class='fraction'>15/15</span>
      </div>
      <div class='fl pad1y space-right2'>
        <span class="strong">100% </span>
        <span class="quiet">Lines</span>
        <span class='fraction'>128/128</span>
      </div>
    </div>
  </div>
  <div class='status-line high'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3x</span>
<span class="cline-any cline-yes">3x</span>
<span class="cline-any cline-yes">3x</span>
<span class="cline-any cline-yes">3x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">8x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">12x</span>
<span class="cline-any cline-yes">12x</span>
<span class="cline-any cline-yes">12x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">36x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">451x</span>
<span class="cline-any cline-yes">451x</span>
<span class="cline-any cline-yes">451x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">451x</span>
<span class="cline-any cline-yes">451x</span>
<span class="cline-any cline-yes">451x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">451x</span>
<span class="cline-any cline-yes">451x</span>
<span class="cline-any cline-yes">451x</span>
<span class="cline-any cline-yes">451x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">451x</span>
<span class="cline-any cline-yes">457x</span>
<span class="cline-any cline-yes">15x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">442x</span>
<span class="cline-any cline-yes">442x</span>
<span class="cline-any cline-yes">6x</span>
<span class="cline-any cline-yes">436x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">435x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">451x</span>
<span class="cline-any cline-yes">272x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">179x</span>
<span class="cline-any cline-yes">40x</span>
<span class="cline-any cline-yes">40x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">40x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">139x</span>
<span class="cline-any cline-yes">8x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">131x</span>
<span class="cline-any cline-yes">131x</span>
<span class="cline-any cline-yes">131x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3x</span>
<span class="cline-any cline-yes">1062x</span>
<span class="cline-any cline-yes">119x</span>
<span class="cline-any cline-yes">943x</span>
<span class="cline-any cline-yes">943x</span>
<span class="cline-any cline-yes">943x</span>
<span class="cline-any cline-yes">723x</span>
<span class="cline-any cline-yes">220x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">222x</span>
<span class="cline-any cline-yes">222x</span>
<span class="cline-any cline-yes">36x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">32x</span>
<span class="cline-any cline-yes">32x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">28x</span>
<span class="cline-any cline-yes">22x</span>
<span class="cline-any cline-yes">6x</span>
<span class="cline-any cline-yes">3x</span>
<span class="cline-any cline-yes">3x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">186x</span>
<span class="cline-any cline-yes">186x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">276x</span>
<span class="cline-any cline-yes">276x</span>
<span class="cline-any cline-yes">276x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">276x</span>
<span class="cline-any cline-yes">276x</span>
<span class="cline-any cline-yes">276x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">276x</span>
<span class="cline-any cline-yes">276x</span>
<span class="cline-any cline-yes">276x</span>
<span class="cline-any cline-yes">276x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">276x</span>
<span class="cline-any cline-yes">266x</span>
<span class="cline-any cline-yes">266x</span>
<span class="cline-any cline-yes">6x</span>
<span class="cline-any cline-yes">266x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">266x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">276x</span>
<span class="cline-any cline-yes">128x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">148x</span>
<span class="cline-any cline-yes">34x</span>
<span class="cline-any cline-yes">34x</span>
<span class="cline-any cline-yes">34x</span>
<span class="cline-any cline-yes">34x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">34x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">32x</span>
<span class="cline-any cline-yes">32x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">114x</span>
<span class="cline-any cline-yes">7x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">107x</span>
<span class="cline-any cline-yes">107x</span>
<span class="cline-any cline-yes">107x</span>
<span class="cline-any cline-yes">107x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">792x</span>
<span class="cline-any cline-yes">608x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">184x</span>
<span class="cline-any cline-yes">184x</span>
<span class="cline-any cline-yes">169x</span>
<span class="cline-any cline-yes">169x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">15x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">11x</span>
<span class="cline-any cline-yes">9x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">5x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">3x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">99x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">'use strict'
// wrapper around mkdirp for tar's needs.
&nbsp;
// TODO: This should probably be a class, not functionally
// passing around state in a gazillion args.
&nbsp;
const mkdirp = require('mkdirp')
const fs = require('fs')
const path = require('path')
const chownr = require('chownr')
&nbsp;
class SymlinkError extends Error {
  constructor (symlink, path) {
    super('Cannot extract through symbolic link')
    this.path = path
    this.symlink = symlink
  }
&nbsp;
  get name () {
    return 'SylinkError'
  }
}
&nbsp;
class CwdError extends Error {
  constructor (path, code) {
    super(code + ': Cannot cd into \'' + path + '\'')
    this.path = path
    this.code = code
  }
&nbsp;
  get name () {
    return 'CwdError'
  }
}
&nbsp;
const mkdir = module.exports = (dir, opt, cb) =&gt; {
  // if there's any overlap between mask and mode,
  // then we'll need an explicit chmod
  const umask = opt.umask
  const mode = opt.mode | 0o0700
  const needChmod = (mode &amp; umask) !== 0
&nbsp;
  const uid = opt.uid
  const gid = opt.gid
  const doChown = typeof uid === 'number' &amp;&amp;
    typeof gid === 'number' &amp;&amp;
    ( uid !== opt.processUid || gid !== opt.processGid )
&nbsp;
  const preserve = opt.preserve
  const unlink = opt.unlink
  const cache = opt.cache
  const cwd = opt.cwd
&nbsp;
  const done = (er, created) =&gt; {
    if (er)
      cb(er)
    else {
      cache.set(dir, true)
      if (created &amp;&amp; doChown)
        chownr(created, uid, gid, er =&gt; done(er))
      else if (needChmod)
        fs.chmod(dir, mode, cb)
      else
        cb()
    }
  }
&nbsp;
  if (cache &amp;&amp; cache.get(dir) === true)
    return done()
&nbsp;
  if (dir === cwd)
    return fs.lstat(dir, (er, st) =&gt; {
      if (er || !st.isDirectory())
        er = new CwdError(dir, er &amp;&amp; er.code || 'ENOTDIR')
      done(er)
    })
&nbsp;
  if (preserve)
    return mkdirp(dir, mode, done)
&nbsp;
  const sub = path.relative(cwd, dir)
  const parts = sub.split(/\/|\\/)
  mkdir_(cwd, parts, mode, cache, unlink, cwd, null, done)
}
&nbsp;
const mkdir_ = (base, parts, mode, cache, unlink, cwd, created, cb) =&gt; {
  if (!parts.length)
    return cb(null, created)
  const p = parts.shift()
  const part = base + '/' + p
  if (cache.get(part))
    return mkdir_(part, parts, mode, cache, unlink, cwd, created, cb)
  fs.mkdir(part, mode, onmkdir(part, parts, mode, cache, unlink, cwd, created, cb))
}
&nbsp;
const onmkdir = (part, parts, mode, cache, unlink, cwd, created, cb) =&gt; er =&gt; {
  if (er) {
    if (er.path &amp;&amp; path.dirname(er.path) === cwd &amp;&amp;
        (er.code === 'ENOTDIR' || er.code === 'ENOENT'))
      return cb(new CwdError(cwd, er.code))
&nbsp;
    fs.lstat(part, (statEr, st) =&gt; {
      if (statEr)
        cb(statEr)
      else if (st.isDirectory())
        mkdir_(part, parts, mode, cache, unlink, cwd, created, cb)
      else if (unlink)
        fs.unlink(part, er =&gt; {
          if (er)
            return cb(er)
          fs.mkdir(part, mode, onmkdir(part, parts, mode, cache, unlink, cwd, created, cb))
        })
      else if (st.isSymbolicLink())
        return cb(new SymlinkError(part, part + '/' + parts.join('/')))
      else
        cb(er)
    })
  } else {
    created = created || part
    mkdir_(part, parts, mode, cache, unlink, cwd, created, cb)
  }
}
&nbsp;
const mkdirSync = module.exports.sync = (dir, opt) =&gt; {
  // if there's any overlap between mask and mode,
  // then we'll need an explicit chmod
  const umask = opt.umask
  const mode = opt.mode | 0o0700
  const needChmod = (mode &amp; umask) !== 0
&nbsp;
  const uid = opt.uid
  const gid = opt.gid
  const doChown = typeof uid === 'number' &amp;&amp;
    typeof gid === 'number' &amp;&amp;
    ( uid !== opt.processUid || gid !== opt.processGid )
&nbsp;
  const preserve = opt.preserve
  const unlink = opt.unlink
  const cache = opt.cache
  const cwd = opt.cwd
&nbsp;
  const done = (created) =&gt; {
    cache.set(dir, true)
    if (created &amp;&amp; doChown)
      chownr.sync(created, uid, gid)
    if (needChmod)
      fs.chmodSync(dir, mode)
    cache.set(dir, true)
  }
&nbsp;
  if (cache &amp;&amp; cache.get(dir) === true)
    return done()
&nbsp;
  if (dir === cwd) {
    let ok = false
    let code = 'ENOTDIR'
    try {
      ok = fs.lstatSync(dir).isDirectory()
    } catch (er) {
      code = er.code
    } finally {
      if (!ok)
        throw new CwdError(dir, code)
    }
    done()
    return
  }
&nbsp;
  if (preserve)
    return done(mkdirp.sync(dir, mode))
&nbsp;
  const sub = path.relative(cwd, dir)
  const parts = sub.split(/\/|\\/)
  let created = null
  for (let p = parts.shift(), part = cwd;
       p &amp;&amp; (part += '/' + p);
       p = parts.shift()) {
&nbsp;
    if (cache.get(part))
      continue
&nbsp;
    try {
      fs.mkdirSync(part, mode)
      created = created || part
      cache.set(part, true)
    } catch (er) {
      if (er.path &amp;&amp; path.dirname(er.path) === cwd &amp;&amp;
          (er.code === 'ENOTDIR' || er.code === 'ENOENT'))
        return new CwdError(cwd, er.code)
&nbsp;
      const st = fs.lstatSync(part)
      if (st.isDirectory()) {
        cache.set(part, true)
        continue
      } else if (unlink) {
        fs.unlinkSync(part)
        fs.mkdirSync(part, mode)
        created = created || part
        cache.set(part, true)
        continue
      } else if (st.isSymbolicLink())
        return new SymlinkError(part, part + '/' + parts.join('/'))
    }
  }
&nbsp;
  return done(created)
}
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="https://istanbul.js.org/" target="_blank">istanbul</a> at Mon Nov 20 2017 16:00:38 GMT-0800 (PST)
</div>
</div>
<script src="../../prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="../../sorter.js"></script>
</body>
</html>
