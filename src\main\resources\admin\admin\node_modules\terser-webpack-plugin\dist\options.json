{"additionalProperties": false, "definitions": {"file-conditions": {"anyOf": [{"instanceof": "RegExp"}, {"type": "string"}]}}, "properties": {"test": {"anyOf": [{"$ref": "#/definitions/file-conditions"}, {"items": {"anyOf": [{"$ref": "#/definitions/file-conditions"}]}, "type": "array"}]}, "include": {"anyOf": [{"$ref": "#/definitions/file-conditions"}, {"items": {"anyOf": [{"$ref": "#/definitions/file-conditions"}]}, "type": "array"}]}, "exclude": {"anyOf": [{"$ref": "#/definitions/file-conditions"}, {"items": {"anyOf": [{"$ref": "#/definitions/file-conditions"}]}, "type": "array"}]}, "chunkFilter": {"instanceof": "Function"}, "cache": {"anyOf": [{"type": "boolean"}, {"type": "string"}]}, "cacheKeys": {"instanceof": "Function"}, "parallel": {"anyOf": [{"type": "boolean"}, {"type": "integer"}]}, "sourceMap": {"type": "boolean"}, "minify": {"instanceof": "Function"}, "terserOptions": {"additionalProperties": true, "type": "object"}, "extractComments": {"anyOf": [{"type": "boolean"}, {"type": "string"}, {"instanceof": "RegExp"}, {"instanceof": "Function"}, {"additionalProperties": false, "properties": {"condition": {"anyOf": [{"type": "boolean"}, {"type": "string"}, {"instanceof": "RegExp"}, {"instanceof": "Function"}]}, "filename": {"anyOf": [{"type": "string"}, {"instanceof": "Function"}]}, "banner": {"anyOf": [{"type": "boolean"}, {"type": "string"}, {"instanceof": "Function"}]}}, "type": "object"}]}, "warningsFilter": {"instanceof": "Function"}}, "type": "object"}