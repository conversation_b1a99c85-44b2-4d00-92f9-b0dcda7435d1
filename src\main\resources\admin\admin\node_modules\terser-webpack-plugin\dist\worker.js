"use strict";

var _minify = _interopRequireDefault(require("./minify"));
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
module.exports = (options, callback) => {
  try {
    // 'use strict' => this === undefined (Clean Scope)
    // Safer for possible security issues, albeit not critical at all here
    // eslint-disable-next-line no-new-func, no-param-reassign
    options = new Function('exports', 'require', 'module', '__filename', '__dirname', `'use strict'\nreturn ${options}`)(exports, require, module, __filename, __dirname);
    callback(null, (0, _minify.default)(options));
  } catch (errors) {
    callback(errors);
  }
};