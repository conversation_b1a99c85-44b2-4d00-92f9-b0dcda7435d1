{"_from": "find-cache-dir@^2.1.0", "_id": "find-cache-dir@2.1.0", "_inBundle": false, "_integrity": "sha512-Tq6PixE0w/VMFfCgbONnkiQIVol/JJL7nRMi20fqzA4NRs9AfeqMGeRdPi3wIhYkxjeBaWh2rxwapn5Tu3IqOQ==", "_location": "/terser-webpack-plugin/find-cache-dir", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "find-cache-dir@^2.1.0", "name": "find-cache-dir", "escapedName": "find-cache-dir", "rawSpec": "^2.1.0", "saveSpec": null, "fetchSpec": "^2.1.0"}, "_requiredBy": ["/terser-webpack-plugin"], "_resolved": "https://registry.npmmirror.com/find-cache-dir/-/find-cache-dir-2.1.0.tgz", "_shasum": "8d0f94cd13fe43c6c7c261a0d86115ca918c05f7", "_spec": "find-cache-dir@^2.1.0", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\terser-webpack-plugin", "bugs": {"url": "https://github.com/avajs/find-cache-dir/issues"}, "bundleDependencies": false, "dependencies": {"commondir": "^1.0.1", "make-dir": "^2.0.0", "pkg-dir": "^3.0.0"}, "deprecated": false, "description": "Finds the common standard cache directory", "devDependencies": {"ava": "^1.3.1", "coveralls": "^3.0.3", "del": "^4.0.0", "nyc": "^13.3.0", "xo": "^0.24.0"}, "engines": {"node": ">=6"}, "files": ["index.js"], "homepage": "https://github.com/avajs/find-cache-dir#readme", "keywords": ["cache", "directory", "dir", "caching", "find", "search"], "license": "MIT", "name": "find-cache-dir", "nyc": {"reporter": ["lcov", "text"]}, "repository": {"type": "git", "url": "git+https://github.com/avajs/find-cache-dir.git"}, "scripts": {"test": "xo && nyc ava"}, "version": "2.1.0"}