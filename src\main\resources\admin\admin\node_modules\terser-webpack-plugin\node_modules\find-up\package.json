{"_from": "find-up@^3.0.0", "_id": "find-up@3.0.0", "_inBundle": false, "_integrity": "sha512-1yD6RmLI1XBfxugvORwlck6f75tYL+iR0jqwsOrOxMZyGYqUuDhJ0l4AXdO1iX/FTs9cBAMEk1gWSEx1kSbylg==", "_location": "/terser-webpack-plugin/find-up", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "find-up@^3.0.0", "name": "find-up", "escapedName": "find-up", "rawSpec": "^3.0.0", "saveSpec": null, "fetchSpec": "^3.0.0"}, "_requiredBy": ["/terser-webpack-plugin/pkg-dir"], "_resolved": "https://registry.npmmirror.com/find-up/-/find-up-3.0.0.tgz", "_shasum": "49169f1d7993430646da61ecc5ae355c21c97b73", "_spec": "find-up@^3.0.0", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\terser-webpack-plugin\\node_modules\\pkg-dir", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/find-up/issues"}, "bundleDependencies": false, "dependencies": {"locate-path": "^3.0.0"}, "deprecated": false, "description": "Find a file or directory by walking up parent directories", "devDependencies": {"ava": "*", "tempy": "^0.2.1", "xo": "*"}, "engines": {"node": ">=6"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/find-up#readme", "keywords": ["find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "package", "resolve", "parent", "parents", "folder", "directory", "dir", "walk", "walking", "path"], "license": "MIT", "name": "find-up", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/find-up.git"}, "scripts": {"test": "xo && ava"}, "version": "3.0.0"}