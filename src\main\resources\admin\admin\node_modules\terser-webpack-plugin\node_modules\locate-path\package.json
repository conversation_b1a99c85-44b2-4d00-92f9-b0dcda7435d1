{"_from": "locate-path@^3.0.0", "_id": "locate-path@3.0.0", "_inBundle": false, "_integrity": "sha512-7AO748wWnIhNqAuaty2ZWHkQHRSNfPVIsPIfwEOWO22AmaoVrWavlOcMR5nzTLNYvp36X220/maaRsrec1G65A==", "_location": "/terser-webpack-plugin/locate-path", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "locate-path@^3.0.0", "name": "locate-path", "escapedName": "locate-path", "rawSpec": "^3.0.0", "saveSpec": null, "fetchSpec": "^3.0.0"}, "_requiredBy": ["/terser-webpack-plugin/find-up"], "_resolved": "https://registry.npmmirror.com/locate-path/-/locate-path-3.0.0.tgz", "_shasum": "dbec3b3ab759758071b58fe59fc41871af21400e", "_spec": "locate-path@^3.0.0", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\terser-webpack-plugin\\node_modules\\find-up", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/locate-path/issues"}, "bundleDependencies": false, "dependencies": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}, "deprecated": false, "description": "Get the first path that exists on disk of multiple paths", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=6"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/locate-path#readme", "keywords": ["locate", "path", "paths", "file", "files", "exists", "find", "finder", "search", "searcher", "array", "iterable", "iterator"], "license": "MIT", "name": "locate-path", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/locate-path.git"}, "scripts": {"test": "xo && ava"}, "version": "3.0.0"}