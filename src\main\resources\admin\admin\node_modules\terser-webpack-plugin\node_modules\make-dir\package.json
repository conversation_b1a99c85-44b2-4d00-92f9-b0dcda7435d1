{"_from": "make-dir@^2.0.0", "_id": "make-dir@2.1.0", "_inBundle": false, "_integrity": "sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA==", "_location": "/terser-webpack-plugin/make-dir", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "make-dir@^2.0.0", "name": "make-dir", "escapedName": "make-dir", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/terser-webpack-plugin/find-cache-dir"], "_resolved": "https://registry.npmmirror.com/make-dir/-/make-dir-2.1.0.tgz", "_shasum": "5f0310e18b8be898cc07009295a30ae41e91e6f5", "_spec": "make-dir@^2.0.0", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\terser-webpack-plugin\\node_modules\\find-cache-dir", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/make-dir/issues"}, "bundleDependencies": false, "dependencies": {"pify": "^4.0.1", "semver": "^5.6.0"}, "deprecated": false, "description": "Make a directory and its parents if needed - Think `mkdir -p`", "devDependencies": {"@types/graceful-fs": "^4.1.3", "@types/node": "^11.10.4", "ava": "^1.2.0", "codecov": "^3.0.0", "graceful-fs": "^4.1.11", "nyc": "^13.1.0", "path-type": "^3.0.0", "tempy": "^0.2.1", "tsd-check": "^0.3.0", "xo": "^0.24.0"}, "engines": {"node": ">=6"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/sindresorhus/make-dir#readme", "keywords": ["mkdir", "mkdirp", "make", "directories", "dir", "dirs", "folders", "directory", "folder", "path", "parent", "parents", "intermediate", "recursively", "recursive", "create", "fs", "filesystem", "file-system"], "license": "MIT", "name": "make-dir", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/make-dir.git"}, "scripts": {"test": "xo && nyc ava && tsd-check"}, "version": "2.1.0"}