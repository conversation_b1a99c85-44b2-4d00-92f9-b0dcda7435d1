{"_from": "path-exists@^3.0.0", "_id": "path-exists@3.0.0", "_inBundle": false, "_integrity": "sha512-bpC7GYwiDYQ4wYLe+FA8lhRjhQCMcQGuSgGGqDkg/QerRWw9CmGRT0iSOVRSZJ29NMLZgIzqaljJ63oaL4NIJQ==", "_location": "/terser-webpack-plugin/path-exists", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "path-exists@^3.0.0", "name": "path-exists", "escapedName": "path-exists", "rawSpec": "^3.0.0", "saveSpec": null, "fetchSpec": "^3.0.0"}, "_requiredBy": ["/terser-webpack-plugin/locate-path"], "_resolved": "https://registry.npmmirror.com/path-exists/-/path-exists-3.0.0.tgz", "_shasum": "ce0ebeaa5f78cb18925ea7d810d7b59b010fd515", "_spec": "path-exists@^3.0.0", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\terser-webpack-plugin\\node_modules\\locate-path", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/path-exists/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Check if a path exists", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/path-exists#readme", "keywords": ["path", "exists", "exist", "file", "filepath", "fs", "filesystem", "file-system", "access", "stat"], "license": "MIT", "name": "path-exists", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/path-exists.git"}, "scripts": {"test": "xo && ava"}, "version": "3.0.0", "xo": {"esnext": true}}