{"_from": "pkg-dir@^3.0.0", "_id": "pkg-dir@3.0.0", "_inBundle": false, "_integrity": "sha512-/E57AYkoeQ25qkxMj5PBOVgF8Kiu/h7cYS30Z5+R7WaiCCBfLq58ZI/dSeaEKb9WVJV5n/03QwrN3IeWIFllvw==", "_location": "/terser-webpack-plugin/pkg-dir", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "pkg-dir@^3.0.0", "name": "pkg-dir", "escapedName": "pkg-dir", "rawSpec": "^3.0.0", "saveSpec": null, "fetchSpec": "^3.0.0"}, "_requiredBy": ["/terser-webpack-plugin/find-cache-dir"], "_resolved": "https://registry.npmmirror.com/pkg-dir/-/pkg-dir-3.0.0.tgz", "_shasum": "2749020f239ed990881b1f71210d51eb6523bea3", "_spec": "pkg-dir@^3.0.0", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\terser-webpack-plugin\\node_modules\\find-cache-dir", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/pkg-dir/issues"}, "bundleDependencies": false, "dependencies": {"find-up": "^3.0.0"}, "deprecated": false, "description": "Find the root directory of a Node.js project or npm package", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=6"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/pkg-dir#readme", "keywords": ["package", "json", "root", "npm", "entry", "find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "resolve", "parent", "parents", "folder", "directory", "dir", "walk", "walking", "path"], "license": "MIT", "name": "pkg-dir", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/pkg-dir.git"}, "scripts": {"test": "xo && ava"}, "version": "3.0.0"}