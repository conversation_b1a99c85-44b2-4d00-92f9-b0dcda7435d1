{"_from": "semver@^5.6.0", "_id": "semver@5.7.2", "_inBundle": false, "_integrity": "sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g==", "_location": "/terser-webpack-plugin/semver", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "semver@^5.6.0", "name": "semver", "escapedName": "semver", "rawSpec": "^5.6.0", "saveSpec": null, "fetchSpec": "^5.6.0"}, "_requiredBy": ["/terser-webpack-plugin/make-dir"], "_resolved": "https://registry.npmmirror.com/semver/-/semver-5.7.2.tgz", "_shasum": "48d55db737c3287cd4835e17fa13feace1c41ef8", "_spec": "semver@^5.6.0", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\terser-webpack-plugin\\node_modules\\make-dir", "author": {"name": "GitHub Inc."}, "bin": {"semver": "bin/semver"}, "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bundleDependencies": false, "deprecated": false, "description": "The semantic version parser used by npm.", "devDependencies": {"@npmcli/template-oss": "4.17.0", "tap": "^12.7.0"}, "files": ["bin", "range.bnf", "semver.js"], "homepage": "https://github.com/npm/node-semver#readme", "license": "ISC", "main": "semver.js", "name": "semver", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "scripts": {"lint": "echo linting disabled", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "snap": "tap test/ --100 --timeout=30", "template-oss-apply": "template-oss-apply --force", "test": "tap test/ --100 --timeout=30"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "content": "./scripts/template-oss", "version": "4.17.0"}, "version": "5.7.2"}