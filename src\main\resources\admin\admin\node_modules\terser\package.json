{"_from": "terser@^4.1.2", "_id": "terser@4.8.1", "_inBundle": false, "_integrity": "sha512-4GnLC0x667eJG0ewJTa6z/yXrbLGv80D9Ru6HIpCQmO+Q4PfEtBFi0ObSckqwL6VyQv/7ENJieXHo2ANmdQwgw==", "_location": "/terser", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "terser@^4.1.2", "name": "terser", "escapedName": "terser", "rawSpec": "^4.1.2", "saveSpec": null, "fetchSpec": "^4.1.2"}, "_requiredBy": ["/terser-webpack-plugin"], "_resolved": "https://registry.npmmirror.com/terser/-/terser-4.8.1.tgz", "_shasum": "a00e5634562de2239fd404c649051bf6fc21144f", "_spec": "terser@^4.1.2", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\terser-webpack-plugin", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://lisperator.net/"}, "bin": {"terser": "bin/terser"}, "bugs": {"url": "https://github.com/terser/terser/issues"}, "bundleDependencies": false, "dependencies": {"commander": "^2.20.0", "source-map": "~0.6.1", "source-map-support": "~0.5.12"}, "deprecated": false, "description": "JavaScript parser, mangler/compressor and beautifier toolkit for ES6+", "devDependencies": {"acorn": "^7.1.1", "astring": "^1.4.1", "eslint": "^6.3.0", "eslump": "^2.0.0", "mocha": "^7.1.2", "mochallel": "^2.0.0", "pre-commit": "^1.2.2", "rimraf": "^3.0.0", "rollup": "2.0.6", "rollup-plugin-terser": "5.3.0", "semver": "^7.1.3"}, "engines": {"node": ">=6.0.0"}, "eslintConfig": {"parserOptions": {"sourceType": "module"}, "env": {"es6": true}, "globals": {"describe": false, "it": false, "require": false, "global": false, "process": false}, "rules": {"brace-style": ["error", "1tbs", {"allowSingleLine": true}], "quotes": ["error", "double", "avoid-escape"], "no-debugger": "error", "no-undef": "error", "no-unused-vars": ["error", {"varsIgnorePattern": "^_$"}], "no-tabs": "error", "semi": ["error", "always"], "no-extra-semi": "error", "no-irregular-whitespace": "error", "space-before-blocks": ["error", "always"]}}, "files": ["bin", "dist", "tools", "LICENSE", "README.md", "CHANGELOG.md", "PATRONS.md"], "homepage": "https://terser.org", "keywords": ["uglify", "terser", "uglify-es", "uglify-js", "minify", "minifier", "javascript", "ecmascript", "es5", "es6", "es7", "es8", "es2015", "es2016", "es2017", "async", "await"], "license": "BSD-2-<PERSON><PERSON>", "main": "dist/bundle.min.js", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "name": "terser", "pre-commit": ["lint-fix", "test"], "repository": {"type": "git", "url": "git+https://github.com/terser/terser.git"}, "scripts": {"build": "rimraf dist/* && rollup --config --silent", "lint": "eslint lib", "lint-fix": "eslint --fix lib", "postversion": "echo 'Remember to update the changelog!'", "prepare": "npm run build", "test": "npm run build -- --configTest && node test/run-tests.js", "test:compress": "npm run build -- --configTest && node test/compress.js", "test:mocha": "npm run build -- --configTest && node test/mocha.js"}, "types": "tools/terser.d.ts", "version": "4.8.1"}