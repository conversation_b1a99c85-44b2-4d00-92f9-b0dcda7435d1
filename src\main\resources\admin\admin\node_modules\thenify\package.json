{"_from": "thenify@>= 3.1.0 < 4", "_id": "thenify@3.3.1", "_inBundle": false, "_integrity": "sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==", "_location": "/thenify", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "thenify@>= 3.1.0 < 4", "name": "thenify", "escapedName": "thenify", "rawSpec": ">= 3.1.0 < 4", "saveSpec": null, "fetchSpec": ">= 3.1.0 < 4"}, "_requiredBy": ["/thenify-all"], "_resolved": "https://registry.npmmirror.com/thenify/-/thenify-3.3.1.tgz", "_shasum": "8932e686a4066038a016dd9e2ca46add9838a95f", "_spec": "thenify@>= 3.1.0 < 4", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\thenify-all", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}, "bugs": {"url": "https://github.com/thenables/thenify/issues"}, "bundleDependencies": false, "dependencies": {"any-promise": "^1.0.0"}, "deprecated": false, "description": "Promisify a callback-based function", "devDependencies": {"bluebird": "^3.1.1", "istanbul": "^0.4.0", "mocha": "^3.0.2"}, "files": ["index.js"], "homepage": "https://github.com/thenables/thenify#readme", "keywords": ["promisify", "promise", "thenify", "then", "es6"], "license": "MIT", "name": "thenify", "repository": {"type": "git", "url": "git+https://github.com/thenables/thenify.git"}, "scripts": {"test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}, "version": "3.3.1"}