{"_from": "json5@^1.0.1", "_id": "json5@1.0.2", "_inBundle": false, "_integrity": "sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA==", "_location": "/thread-loader/json5", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "json5@^1.0.1", "name": "json5", "escapedName": "json5", "rawSpec": "^1.0.1", "saveSpec": null, "fetchSpec": "^1.0.1"}, "_requiredBy": ["/thread-loader/loader-utils"], "_resolved": "https://registry.npmmirror.com/json5/-/json5-1.0.2.tgz", "_shasum": "63d98d60f21b313b77c4d6da18bfa69d80e1d593", "_spec": "json5@^1.0.1", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\thread-loader\\node_modules\\loader-utils", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bin": {"json5": "lib/cli.js"}, "browser": "dist/index.js", "bugs": {"url": "https://github.com/json5/json5/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jordan<PERSON><EMAIL>"}], "dependencies": {"minimist": "^1.2.0"}, "deprecated": false, "description": "JSON for humans.", "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-istanbul": "^4.1.5", "babel-preset-env": "^1.6.1", "babel-register": "^6.26.0", "babelrc-rollup": "^3.0.0", "coveralls": "^3.0.0", "cross-env": "^5.1.4", "del": "^3.0.0", "eslint": "^4.18.2", "eslint-config-standard": "^11.0.0", "eslint-plugin-import": "^2.9.0", "eslint-plugin-node": "^6.0.1", "eslint-plugin-promise": "^3.7.0", "eslint-plugin-standard": "^3.0.1", "mocha": "^5.0.4", "nyc": "^11.4.1", "regenerate": "^1.3.3", "rollup": "^0.56.5", "rollup-plugin-babel": "^3.0.3", "rollup-plugin-commonjs": "^9.0.0", "rollup-plugin-node-resolve": "^3.2.0", "rollup-plugin-uglify": "^3.0.0", "sinon": "^4.4.2", "unicode-9.0.0": "^0.7.5"}, "files": ["lib/", "dist/"], "homepage": "http://json5.org/", "keywords": ["json", "json5", "es5", "es2015", "ecmascript"], "license": "MIT", "main": "lib/index.js", "name": "json5", "repository": {"type": "git", "url": "git+https://github.com/json5/json5.git"}, "scripts": {"build": "babel-node build/build.js && babel src -d lib && rollup -c", "coverage": "nyc report --reporter=text-lcov | coveralls", "lint": "eslint --fix build src", "prepublishOnly": "npm run lint && npm test && npm run production", "pretest": "cross-env NODE_ENV=test npm run build", "preversion": "npm run lint && npm test && npm run production", "production": "cross-env NODE_ENV=production npm run build", "test": "nyc --reporter=html --reporter=text mocha"}, "version": "1.0.2"}