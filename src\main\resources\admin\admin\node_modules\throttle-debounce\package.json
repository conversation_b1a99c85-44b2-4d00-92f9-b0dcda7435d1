{"_from": "throttle-debounce@^1.0.1", "_id": "throttle-debounce@1.1.0", "_inBundle": false, "_integrity": "sha512-XH8UiPCQcWNuk2LYePibW/4qL97+ZQ1AN3FNXwZRBNPPowo/NRU5fAlDCSNBJIYCKbioZfuYtMhG4quqoJhVzg==", "_location": "/throttle-debounce", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "throttle-debounce@^1.0.1", "name": "throttle-debounce", "escapedName": "throttle-debounce", "rawSpec": "^1.0.1", "saveSpec": null, "fetchSpec": "^1.0.1"}, "_requiredBy": ["/element-ui"], "_resolved": "https://registry.npmmirror.com/throttle-debounce/-/throttle-debounce-1.1.0.tgz", "_shasum": "51853da37be68a155cb6e827b3514a3c422e89cd", "_spec": "throttle-debounce@^1.0.1", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\element-ui", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ivannikolic.com/"}, "bugs": {"url": "https://github.com/niksy/throttle-debounce/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "http://benalman.com"}], "dependencies": {}, "deprecated": false, "description": "Throttle/debounce your functions.", "devDependencies": {"browserify": "^13.0.0", "eslint": "^1.10.3", "eslint-config-niksy": "^1.0.6", "karma": "^0.13.22", "karma-browserify": "^5.0.4", "karma-chrome-launcher": "^0.2.3", "karma-firefox-launcher": "^0.1.7", "karma-mocha-reporter": "^2.0.2", "karma-phantomjs-launcher": "^1.0.0", "karma-qunit": "^0.1.9", "mocha": "^2.4.5", "phantomjs-prebuilt": "^2.1.7", "qunitjs": "^1.23.1", "watchify": "^3.7.0"}, "directories": {"test": "test"}, "engines": {"node": ">=4"}, "homepage": "https://github.com/niksy/throttle-debounce#readme", "keywords": ["throttle", "debounce", "browserify"], "license": "MIT", "main": "index.js", "name": "throttle-debounce", "repository": {"type": "git", "url": "git+https://github.com/niksy/throttle-debounce.git"}, "scripts": {"test": "eslint {index,test/*}.js && karma start --single-run --browsers PhantomJS"}, "typings": "index.d.ts", "version": "1.1.0"}