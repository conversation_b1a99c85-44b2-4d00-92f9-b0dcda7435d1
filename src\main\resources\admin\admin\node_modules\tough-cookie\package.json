{"_from": "tough-cookie@~2.5.0", "_id": "tough-cookie@2.5.0", "_inBundle": false, "_integrity": "sha512-nlLsUzgm1kfLXSXfRZMc1KLAugd4hqJHDTvc2hDIwS3mZAfMEuMbc03SujMF+GEcpaX/qboeycw6iO8JwVv2+g==", "_location": "/tough-cookie", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "tough-cookie@~2.5.0", "name": "tough-cookie", "escapedName": "tough-cookie", "rawSpec": "~2.5.0", "saveSpec": null, "fetchSpec": "~2.5.0"}, "_requiredBy": ["/request"], "_resolved": "https://registry.npmmirror.com/tough-cookie/-/tough-cookie-2.5.0.tgz", "_shasum": "cd9fb2a0aa1d5a12b473bd9fb96fa3dcff65ade2", "_spec": "tough-cookie@~2.5.0", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\request", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/salesforce/tough-cookie/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "dependencies": {"psl": "^1.1.28", "punycode": "^2.1.1"}, "deprecated": false, "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "devDependencies": {"async": "^1.4.2", "genversion": "^2.1.0", "nyc": "^11.6.0", "string.prototype.repeat": "^0.2.0", "vows": "^0.8.2"}, "engines": {"node": ">=0.8"}, "files": ["lib"], "homepage": "https://github.com/salesforce/tough-cookie", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "license": "BSD-3-<PERSON><PERSON>", "main": "./lib/cookie", "name": "tough-cookie", "repository": {"type": "git", "url": "git://github.com/salesforce/tough-cookie.git"}, "scripts": {"cover": "nyc --reporter=lcov --reporter=html vows test/*_test.js", "test": "vows test/*_test.js", "version": "genversion lib/version.js && git add lib/version.js"}, "version": "2.5.0"}