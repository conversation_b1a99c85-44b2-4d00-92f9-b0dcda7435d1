{"_from": "traverse@^0.6.6", "_id": "traverse@0.6.11", "_inBundle": false, "_integrity": "sha512-vxXDZg8/+p3gblxB6BhhG5yWVn1kGRlaL8O78UDXc3wRnPizB5g83dcvWV1jpDMIPnjZjOFuxlMmE82XJ4407w==", "_location": "/traverse", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "traverse@^0.6.6", "name": "traverse", "escapedName": "traverse", "rawSpec": "^0.6.6", "saveSpec": null, "fetchSpec": "^0.6.6"}, "_requiredBy": ["/svg-baker"], "_resolved": "https://registry.npmmirror.com/traverse/-/traverse-0.6.11.tgz", "_shasum": "e8daa071b101ae66767fffa6f177aa6f7110068e", "_spec": "traverse@^0.6.6", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\svg-baker", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/ljharb/js-traverse/issues"}, "bundleDependencies": false, "dependencies": {"gopd": "^1.2.0", "typedarray.prototype.slice": "^1.0.5", "which-typed-array": "^1.1.18"}, "deprecated": false, "description": "traverse and transform objects by visiting every node on a recursive walk", "devDependencies": {"@ljharb/eslint-config": "^21.1.1", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "es-value-fixtures": "^1.7.0", "eslint": "=8.8.0", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0"}, "directories": {"example": "example", "test": "test"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/ljharb/js-traverse", "keywords": ["traverse", "walk", "recursive", "map", "for<PERSON>ach", "deep", "clone"], "license": "MIT", "main": "index.js", "name": "traverse", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git://github.com/ljharb/js-traverse.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "posttest": "npx npm@'>=10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "testling": {"files": "test/*.js", "browsers": {"iexplore": ["6.0", "7.0", "8.0", "9.0"], "chrome": ["10.0", "20.0"], "firefox": ["10.0", "15.0"], "safari": ["5.1"], "opera": ["12.0"]}}, "version": "0.6.11"}