{"_from": "true-case-path@^1.0.2", "_id": "true-case-path@1.0.3", "_inBundle": false, "_integrity": "sha512-m6s2OdQe5wgpFMC+pAJ+q9djG82O2jcHPOI6RNg1yy9rCYR+WD6Nbpl32fDpfC56nirdRy+opFa/Vk7HYhqaew==", "_location": "/true-case-path", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "true-case-path@^1.0.2", "name": "true-case-path", "escapedName": "true-case-path", "rawSpec": "^1.0.2", "saveSpec": null, "fetchSpec": "^1.0.2"}, "_requiredBy": ["/node-sass"], "_resolved": "https://registry.npmmirror.com/true-case-path/-/true-case-path-1.0.3.tgz", "_shasum": "f813b5a8c86b40da59606722b144e3225799f47d", "_spec": "true-case-path@^1.0.2", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\node-sass", "author": {"name": "barsh"}, "bugs": {"url": "https://github.com/barsh/true-case-path/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/mklement0"}], "dependencies": {"glob": "^7.1.2"}, "deprecated": false, "description": "Given a possibly case-variant version of an existing filesystem path, returns the case-exact, normalized version as stored in the filesystem.", "homepage": "https://github.com/barsh/true-case-path#readme", "license": "Apache-2.0", "main": "index.js", "name": "true-case-path", "repository": {"type": "git", "url": "git+https://github.com/barsh/true-case-path.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "version": "1.0.3"}