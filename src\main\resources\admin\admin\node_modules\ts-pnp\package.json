{"_from": "ts-pnp@^1.1.6", "_id": "ts-pnp@1.2.0", "_inBundle": false, "_integrity": "sha512-csd+vJOb/gkzvcCHgTGSChYpy5f1/XKNsmvBGO4JXS+z1v2HobugDz4s1IeFXM3wZB44uczs+eazB5Q/ccdhQw==", "_location": "/ts-pnp", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "ts-pnp@^1.1.6", "name": "ts-pnp", "escapedName": "ts-pnp", "rawSpec": "^1.1.6", "saveSpec": null, "fetchSpec": "^1.1.6"}, "_requiredBy": ["/pnp-webpack-plugin"], "_resolved": "https://registry.npmmirror.com/ts-pnp/-/ts-pnp-1.2.0.tgz", "_shasum": "a500ad084b0798f1c3071af391e65912c86bca92", "_spec": "ts-pnp@^1.1.6", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\pnp-webpack-plugin", "bugs": {"url": "https://github.com/arcanis/ts-pnp/issues"}, "bundleDependencies": false, "deprecated": false, "description": "plug'n'play resolver for TypeScript", "devDependencies": {"typescript": "3.5.3"}, "engines": {"node": ">=6"}, "homepage": "https://github.com/arcanis/ts-pnp", "keywords": ["typescript", "yarn", "plugnplay", "pnp"], "license": "MIT", "name": "ts-pnp", "peerDependenciesMeta": {"typescript": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/arcanis/ts-pnp.git"}, "version": "1.2.0"}