{"_from": "tslib@2.3.0", "_id": "tslib@2.3.0", "_inBundle": false, "_integrity": "sha512-N82ooyxVNm6h1riLCoyS9e3fuJ3AMG2zIZs2Gd1ATcSFjSA23Q0fzjjZeh0jbJvWVDZ0cJT8yaNNaaXHzueNjg==", "_location": "/tslib", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "tslib@2.3.0", "name": "tslib", "escapedName": "tslib", "rawSpec": "2.3.0", "saveSpec": null, "fetchSpec": "2.3.0"}, "_requiredBy": ["/echarts", "/zrender"], "_resolved": "https://registry.npmmirror.com/tslib/-/tslib-2.3.0.tgz", "_shasum": "803b8cdab3e12ba581a4ca41c8839bbb0dacb09e", "_spec": "tslib@2.3.0", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\echarts", "author": {"name": "Microsoft Corp."}, "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Runtime library for TypeScript helper functions", "exports": {".": {"module": "./tslib.es6.js", "import": "./modules/index.js", "default": "./tslib.js"}, "./": "./"}, "homepage": "https://www.typescriptlang.org/", "jsnext:main": "tslib.es6.js", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "license": "0BSD", "main": "tslib.js", "module": "tslib.es6.js", "name": "tslib", "repository": {"type": "git", "url": "git+https://github.com/Microsoft/tslib.git"}, "sideEffects": false, "typings": "tslib.d.ts", "version": "2.3.0"}