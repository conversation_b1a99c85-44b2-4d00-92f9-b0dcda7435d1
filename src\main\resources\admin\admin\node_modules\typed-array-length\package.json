{"_from": "typed-array-length@^1.0.7", "_id": "typed-array-length@1.0.7", "_inBundle": false, "_integrity": "sha512-3KS2b+kL7fsuk/eJZ7EQdnEmQoaho/r6KUef7hxvltNA5DR8NAUM+8wJMbJyZ4G9/7i3v5zPBIMN5aybAh2/Jg==", "_location": "/typed-array-length", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "typed-array-length@^1.0.7", "name": "typed-array-length", "escapedName": "typed-array-length", "rawSpec": "^1.0.7", "saveSpec": null, "fetchSpec": "^1.0.7"}, "_requiredBy": ["/es-abstract"], "_resolved": "https://registry.npmmirror.com/typed-array-length/-/typed-array-length-1.0.7.tgz", "_shasum": "ee4deff984b64be1e118b0de8c9c877d5ce73d3d", "_spec": "typed-array-length@^1.0.7", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\es-abstract", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/inspect-js/typed-array-length/issues"}, "bundleDependencies": false, "dependencies": {"call-bind": "^1.0.7", "for-each": "^0.3.3", "gopd": "^1.0.1", "is-typed-array": "^1.1.13", "possible-typed-array-names": "^1.0.0", "reflect.getprototypeof": "^1.0.6"}, "deprecated": false, "description": "Robustly get the length of a Typed Array", "devDependencies": {"@arethetypeswrong/cli": "^0.17.0", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.0", "@types/call-bind": "^1.0.5", "@types/for-each": "^0.3.3", "@types/gopd": "^1.0.3", "@types/is-callable": "^1.1.2", "@types/make-arrow-function": "^1.2.2", "@types/make-generator-function": "^2.0.3", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.6.4", "auto-changelog": "^2.5.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "is-callable": "^1.2.7", "make-arrow-function": "^1.2.0", "make-generator-function": "^2.0.0", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.3", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/inspect-js/typed-array-length#readme", "keywords": ["typed", "array", "length", "robust", "es", "Int8Array", "Uint8Array", "Uint8ClampedArray", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array"], "license": "MIT", "main": "index.js", "name": "typed-array-length", "publishConfig": {"ignore": [".github/workflows", "types"]}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/typed-array-length.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && attw -P", "posttest": "npx npm@'>=10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only && npm run test:harmony", "test:harmony": "nyc node --harmony --es-staging test", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "testling": {"files": "test/index.js"}, "types": "./index.d.ts", "version": "1.0.7"}