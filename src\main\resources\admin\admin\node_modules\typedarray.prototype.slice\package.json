{"_from": "typedarray.prototype.slice@^1.0.5", "_id": "typedarray.prototype.slice@1.0.5", "_inBundle": false, "_integrity": "sha512-q7QNVDGTdl702bVFiI5eY4l/HkgCM6at9KhcFbgUAzezHFbOVy4+0O/lCjsABEQwbZPravVfBIiBVGo89yzHFg==", "_location": "/typedarray.prototype.slice", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "typedarray.prototype.slice@^1.0.5", "name": "typedarray.prototype.slice", "escapedName": "typedarray.prototype.slice", "rawSpec": "^1.0.5", "saveSpec": null, "fetchSpec": "^1.0.5"}, "_requiredBy": ["/traverse"], "_resolved": "https://registry.npmmirror.com/typedarray.prototype.slice/-/typedarray.prototype.slice-1.0.5.tgz", "_shasum": "a40f896968573b33cbb466a61622d3ee615a0728", "_spec": "typedarray.prototype.slice@^1.0.5", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\traverse", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/es-shims/TypedArray.prototype.slice/issues"}, "bundleDependencies": false, "dependencies": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.9", "es-errors": "^1.3.0", "get-proto": "^1.0.1", "math-intrinsics": "^1.1.0", "typed-array-buffer": "^1.0.3", "typed-array-byte-offset": "^1.0.4"}, "deprecated": false, "description": "ES spec-compliant shim for TypedArray.prototype.slice", "devDependencies": {"@es-shims/api": "^2.5.1", "@ljharb/eslint-config": "^21.1.1", "auto-changelog": "^2.5.0", "available-typed-arrays": "^1.0.7", "eclint": "^2.8.1", "encoding": "^0.1.13", "es-value-fixtures": "^1.5.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "functions-have-names": "^1.2.3", "has-strict-mode": "^1.0.1", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.3", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./auto": "./auto.js", "./polyfill": "./polyfill.js", "./implementation": "./implementation.js", "./shim": "./shim.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/es-shims/TypedArray.prototype.slice#readme", "keywords": ["javascript", "ecmascript", "TypedArray.prototype.slice", "polyfill", "shim", "Typed Arrays", "array", "buffer", "TypedArray#slice", "slice", "typed array", "es-shim API"], "license": "MIT", "main": "index.js", "name": "typedarray.prototype.slice", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/es-shims/TypedArray.prototype.slice.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "evalmd README.md && es-shim-api --bound", "posttest": "npx npm@'>= 10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "testling": {"files": "test/index.js"}, "version": "1.0.5"}