{"_from": "undici-types@~6.21.0", "_id": "undici-types@6.21.0", "_inBundle": false, "_integrity": "sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ==", "_location": "/undici-types", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "undici-types@~6.21.0", "name": "undici-types", "escapedName": "undici-types", "rawSpec": "~6.21.0", "saveSpec": null, "fetchSpec": "~6.21.0"}, "_requiredBy": ["/@types/node"], "_resolved": "https://registry.npmmirror.com/undici-types/-/undici-types-6.21.0.tgz", "_shasum": "691d00af3909be93a7faa13be61b3a5b50ef12cb", "_spec": "undici-types@~6.21.0", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\@types\\node", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/dnlup"}, {"name": "<PERSON>", "url": "https://github.com/ethan-arrowood"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/KhafraDev"}, {"name": "<PERSON>", "url": "https://github.com/ronag"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/szmarczak"}, {"name": "<PERSON>", "url": "https://github.com/delvedor"}], "deprecated": false, "description": "A stand-alone types package for Undici", "files": ["*.d.ts"], "homepage": "https://undici.nodejs.org", "license": "MIT", "name": "undici-types", "repository": {"type": "git", "url": "git+https://github.com/nodejs/undici.git"}, "types": "index.d.ts", "version": "6.21.0"}