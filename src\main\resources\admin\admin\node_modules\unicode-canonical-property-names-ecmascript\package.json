{"_from": "unicode-canonical-property-names-ecmascript@^2.0.0", "_id": "unicode-canonical-property-names-ecmascript@2.0.1", "_inBundle": false, "_integrity": "sha512-dA8WbNeb2a6oQzAQ55YlT5vQAWGV9WXOsi3SskE3bcCdM0P4SDd+24zS/OCacdRq5BkdsRj9q3Pg6YyQoxIGqg==", "_location": "/unicode-canonical-property-names-ecmascript", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "unicode-canonical-property-names-ecmascript@^2.0.0", "name": "unicode-canonical-property-names-ecmascript", "escapedName": "unicode-canonical-property-names-ecmascript", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/unicode-match-property-ecmascript"], "_resolved": "https://registry.npmmirror.com/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-2.0.1.tgz", "_shasum": "cb3173fe47ca743e228216e4a3ddc4c84d628cc2", "_spec": "unicode-canonical-property-names-ecmascript@^2.0.0", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\unicode-match-property-ecmascript", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "bugs": {"url": "https://github.com/mathiasbynens/unicode-canonical-property-names-ecmascript/issues"}, "bundleDependencies": false, "deprecated": false, "description": "The set of canonical Unicode property names supported in ECMAScript RegExp property escapes.", "devDependencies": {"ava": "*"}, "engines": {"node": ">=4"}, "files": ["LICENSE-MIT.txt", "index.js"], "homepage": "https://github.com/mathiasbynens/unicode-canonical-property-names-ecmascript", "keywords": ["unicode", "unicode properties"], "license": "MIT", "main": "index.js", "name": "unicode-canonical-property-names-ecmascript", "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/unicode-canonical-property-names-ecmascript.git"}, "scripts": {"test": "ava tests/tests.js"}, "version": "2.0.1"}