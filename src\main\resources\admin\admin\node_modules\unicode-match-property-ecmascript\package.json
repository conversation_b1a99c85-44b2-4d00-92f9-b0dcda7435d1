{"_from": "unicode-match-property-ecmascript@^2.0.0", "_id": "unicode-match-property-ecmascript@2.0.0", "_inBundle": false, "_integrity": "sha512-5kaZCrbp5mmbz5ulBkDkbY0SsPOjKqVS35VpL9ulMPfSl0J0Xsm+9Evphv9CoIZFwre7aJoa94AY6seMKGVN5Q==", "_location": "/unicode-match-property-ecmascript", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "unicode-match-property-ecmascript@^2.0.0", "name": "unicode-match-property-ecmascript", "escapedName": "unicode-match-property-ecmascript", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/regexpu-core"], "_resolved": "https://registry.npmmirror.com/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-2.0.0.tgz", "_shasum": "54fd16e0ecb167cf04cf1f756bdcc92eba7976c3", "_spec": "unicode-match-property-ecmascript@^2.0.0", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\regexpu-core", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "bugs": {"url": "https://github.com/mathiasbynens/unicode-match-property-ecmascript/issues"}, "bundleDependencies": false, "dependencies": {"unicode-canonical-property-names-ecmascript": "^2.0.0", "unicode-property-aliases-ecmascript": "^2.0.0"}, "deprecated": false, "description": "Match a Unicode property or property alias to its canonical property name per the algorithm used for RegExp Unicode property escapes in ECMAScript.", "devDependencies": {"ava": "*"}, "engines": {"node": ">=4"}, "files": ["LICENSE-MIT.txt", "index.js"], "homepage": "https://github.com/mathiasbynens/unicode-match-property-ecmascript", "keywords": ["unicode", "unicode properties", "unicode property aliases"], "license": "MIT", "main": "index.js", "name": "unicode-match-property-ecmascript", "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/unicode-match-property-ecmascript.git"}, "scripts": {"test": "ava ./tests/*"}, "version": "2.0.0"}