{"_from": "unicode-match-property-value-ecmascript@^2.1.0", "_id": "unicode-match-property-value-ecmascript@2.2.0", "_inBundle": false, "_integrity": "sha512-4IehN3V/+kkr5YeSSDDQG8QLqO26XpL2XP3GQtqwlT/QYSECAwFztxVHjlbh0+gjJ3XmNLS0zDsbgs9jWKExLg==", "_location": "/unicode-match-property-value-ecmascript", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "unicode-match-property-value-ecmascript@^2.1.0", "name": "unicode-match-property-value-ecmascript", "escapedName": "unicode-match-property-value-ecmascript", "rawSpec": "^2.1.0", "saveSpec": null, "fetchSpec": "^2.1.0"}, "_requiredBy": ["/regexpu-core"], "_resolved": "https://registry.npmmirror.com/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-2.2.0.tgz", "_shasum": "a0401aee72714598f739b68b104e4fe3a0cb3c71", "_spec": "unicode-match-property-value-ecmascript@^2.1.0", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\regexpu-core", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "bugs": {"url": "https://github.com/mathiasbynens/unicode-match-property-value-ecmascript/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Match a Unicode property or property alias to its canonical property name per the algorithm used for RegExp Unicode property escapes in ECMAScript.", "devDependencies": {"ava": "*", "jsesc": "^3.0.2", "unicode-property-value-aliases-ecmascript": "^2.2.0"}, "engines": {"node": ">=4"}, "files": ["LICENSE-MIT.txt", "data/mappings.js", "index.js"], "homepage": "https://github.com/mathiasbynens/unicode-match-property-value-ecmascript", "keywords": ["unicode", "unicode property values", "unicode property value aliases"], "license": "MIT", "main": "index.js", "name": "unicode-match-property-value-ecmascript", "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/unicode-match-property-value-ecmascript.git"}, "scripts": {"build": "node scripts/build.js", "test": "ava tests/tests.js"}, "version": "2.2.0"}