{"_from": "unicode-property-aliases-ecmascript@^2.0.0", "_id": "unicode-property-aliases-ecmascript@2.1.0", "_inBundle": false, "_integrity": "sha512-6t3foTQI9qne+OZoVQB/8x8rk2k1eVy1gRXhV3oFQ5T6R1dqQ1xtin3XqSlx3+ATBkliTaR/hHyJBm+LVPNM8w==", "_location": "/unicode-property-aliases-ecmascript", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "unicode-property-aliases-ecmascript@^2.0.0", "name": "unicode-property-aliases-ecmascript", "escapedName": "unicode-property-aliases-ecmascript", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/unicode-match-property-ecmascript"], "_resolved": "https://registry.npmmirror.com/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-2.1.0.tgz", "_shasum": "43d41e3be698bd493ef911077c9b131f827e8ccd", "_spec": "unicode-property-aliases-ecmascript@^2.0.0", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\unicode-match-property-ecmascript", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "bugs": {"url": "https://github.com/mathiasbynens/unicode-property-aliases-ecmascript/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Unicode property alias mappings in JavaScript format for property names that are supported in ECMAScript RegExp property escapes.", "devDependencies": {"ava": "*", "jsesc": "^3.0.2", "unicode-canonical-property-names-ecmascript": "^2.0.0"}, "engines": {"node": ">=4"}, "files": ["LICENSE-MIT.txt", "index.js"], "homepage": "https://github.com/mathiasbynens/unicode-property-aliases-ecmascript", "keywords": ["unicode", "unicode-data", "alias", "aliases", "property alias"], "license": "MIT", "main": "index.js", "name": "unicode-property-aliases-ecmascript", "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/unicode-property-aliases-ecmascript.git"}, "scripts": {"build": "node scripts/build.js", "download": "curl http://unicode.org/Public/15.0.0/ucd/PropertyAliases.txt > data/PropertyAliases.txt", "test": "ava tests/tests.js"}, "version": "2.1.0"}