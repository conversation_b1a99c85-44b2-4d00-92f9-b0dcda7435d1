{"_from": "unidecode@0.1.8", "_id": "unidecode@0.1.8", "_inBundle": false, "_integrity": "sha512-SdoZNxCWpN2tXTCrGkPF/0rL2HEq+i2gwRG1ReBvx8/0yTzC3enHfugOf8A9JBShVwwrRIkLX0YcDUGbzjbVCA==", "_location": "/unidecode", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "unidecode@0.1.8", "name": "unidecode", "escapedName": "unidecode", "rawSpec": "0.1.8", "saveSpec": null, "fetchSpec": "0.1.8"}, "_requiredBy": ["/url-slug"], "_resolved": "https://registry.npmmirror.com/unidecode/-/unidecode-0.1.8.tgz", "_shasum": "efbb301538bc45246a9ac8c559d72f015305053e", "_spec": "unidecode@0.1.8", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\url-slug", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://fgribreau.com"}, "bugs": {"url": "https://github.com/FGRibreau/node-unidecode/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "ASCII transliterations of Unicode text", "devDependencies": {"jshint": "^2.5.11", "mocha": "^2.0.1"}, "engines": {"node": ">= 0.4.12"}, "homepage": "http://blog.fgribreau.com/2012/05/unidecode-for-javascript-nodejs.html", "keywords": ["unidecode", "unicode", "utf8"], "licenses": [{"type": "BSD", "url": "http://github.com/FGRibreau/node-unidecode/blob/master/LICENSE"}], "main": "./unidecode", "name": "unidecode", "repository": {"type": "git", "url": "git+ssh://**************/FGRibreau/node-unidecode.git"}, "scripts": {"lint": "jshint *.js */*.js", "test": "mocha"}, "version": "0.1.8"}