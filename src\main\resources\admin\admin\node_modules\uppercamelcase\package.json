{"_from": "uppercamelcase@^1.1.0", "_id": "uppercamelcase@1.1.0", "_inBundle": false, "_integrity": "sha512-C7YEMvhgrvTEKEEVqA7LXNID/1TvvIwYZqNIKLquS6y/MGSkRQAav9LnTTILlC1RqUM8eTVBOe1U/fnB652PRA==", "_location": "/uppercamelcase", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "uppercamelcase@^1.1.0", "name": "uppercamelcase", "escapedName": "uppercamelcase", "rawSpec": "^1.1.0", "saveSpec": null, "fetchSpec": "^1.1.0"}, "_requiredBy": ["/vue-amap"], "_resolved": "https://registry.npmmirror.com/uppercamelcase/-/uppercamelcase-1.1.0.tgz", "_shasum": "324d98a6b3afc7e8a8953e10641509b0e4e23f97", "_spec": "uppercamelcase@^1.1.0", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\vue-amap", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/SamVerschueren/uppercamelcase/issues"}, "bundleDependencies": false, "dependencies": {"camelcase": "^1.2.1"}, "deprecated": false, "description": "Convert a dash/dot/underscore/space separated string to UpperCamelCase", "devDependencies": {"ava": "*", "xo": "*"}, "files": ["index.js"], "homepage": "https://github.com/SamVerschueren/uppercamelcase#readme", "keywords": ["camelcase", "uppercamelcase", "upper", "camel", "case"], "license": "MIT", "name": "uppercamelcase", "repository": {"type": "git", "url": "git+https://github.com/SamVerschueren/uppercamelcase.git"}, "scripts": {"test": "xo && ava"}, "version": "1.1.0"}