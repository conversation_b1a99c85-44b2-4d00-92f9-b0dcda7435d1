{"_from": "uri-js@^4.2.2", "_id": "uri-js@4.4.1", "_inBundle": false, "_integrity": "sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==", "_location": "/uri-js", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "uri-js@^4.2.2", "name": "uri-js", "escapedName": "uri-js", "rawSpec": "^4.2.2", "saveSpec": null, "fetchSpec": "^4.2.2"}, "_requiredBy": ["/ajv"], "_resolved": "https://registry.npmmirror.com/uri-js/-/uri-js-4.4.1.tgz", "_shasum": "9b1a52595225859e55f669d928f88c6c57f2a77e", "_spec": "uri-js@^4.2.2", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\ajv", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/garycourt/uri-js/issues"}, "bundleDependencies": false, "dependencies": {"punycode": "^2.1.0"}, "deprecated": false, "description": "An RFC 3986/3987 compliant, scheme extendable URI/IRI parsing/validating/resolving library for JavaScript.", "devDependencies": {"babel-cli": "^6.26.0", "babel-plugin-external-helpers": "^6.22.0", "babel-preset-latest": "^6.24.1", "mocha": "^8.2.1", "mocha-qunit-ui": "^0.1.3", "rollup": "^0.41.6", "rollup-plugin-babel": "^2.7.1", "rollup-plugin-node-resolve": "^2.0.0", "sorcery": "^0.10.0", "typescript": "^2.8.1", "uglify-js": "^2.8.14"}, "directories": {"test": "tests"}, "files": ["dist", "package.json", "yarn.lock", "README.md", "CHANGELOG", "LICENSE"], "homepage": "https://github.com/garycourt/uri-js", "keywords": ["URI", "IRI", "IDN", "URN", "UUID", "HTTP", "HTTPS", "WS", "WSS", "MAILTO", "RFC3986", "RFC3987", "RFC5891", "RFC2616", "RFC2818", "RFC2141", "RFC4122", "RFC4291", "RFC5952", "RFC6068", "RFC6455", "RFC6874"], "license": "BSD-2-<PERSON><PERSON>", "main": "dist/es5/uri.all.js", "name": "uri-js", "repository": {"type": "git", "url": "git+ssh://**************/garycourt/uri-js.git"}, "scripts": {"build": "npm run build:esnext && npm run build:es5 && npm run build:es5:min", "build:es5": "rollup -c && cp dist/esnext/uri.d.ts dist/es5/uri.all.d.ts && npm run build:es5:fix-sourcemap", "build:es5:fix-sourcemap": "sorcery -i dist/es5/uri.all.js", "build:es5:min": "uglifyjs dist/es5/uri.all.js --support-ie8 --output dist/es5/uri.all.min.js --in-source-map dist/es5/uri.all.js.map --source-map uri.all.min.js.map --comments --compress --mangle --pure-funcs merge subexp && mv uri.all.min.js.map dist/es5/ && cp dist/es5/uri.all.d.ts dist/es5/uri.all.min.d.ts", "build:esnext": "tsc", "clean": "rm -rf dist", "test": "mocha -u mocha-qunit-ui dist/es5/uri.all.js tests/tests.js"}, "types": "dist/es5/uri.all.d.ts", "version": "4.4.1"}