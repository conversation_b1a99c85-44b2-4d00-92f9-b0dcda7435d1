{"_from": "url-slug@2.0.0", "_id": "url-slug@2.0.0", "_inBundle": false, "_integrity": "sha512-aiNmSsVgrjCiJ2+KWPferjT46YFKoE8i0YX04BlMVDue022Xwhg/zYlnZ6V9/mP3p8Wj7LEp0myiTkC/p6sxew==", "_location": "/url-slug", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "url-slug@2.0.0", "name": "url-slug", "escapedName": "url-slug", "rawSpec": "2.0.0", "saveSpec": null, "fetchSpec": "2.0.0"}, "_requiredBy": ["/svg-sprite-loader"], "_resolved": "https://registry.npmmirror.com/url-slug/-/url-slug-2.0.0.tgz", "_shasum": "a789d5aed4995c0d95af33377ad1d5c68d4d7027", "_spec": "url-slug@2.0.0", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\svg-sprite-loader", "author": {"name": "Saulo Toledo", "url": "sbtoledo.github.io"}, "bugs": {"url": "https://github.com/sbtoledo/url-slug/issues"}, "bundleDependencies": false, "dependencies": {"unidecode": "0.1.8"}, "deprecated": false, "description": "RFC 3986 compliant slug generator with support for multiple languages", "devDependencies": {"chai": "3.5.0", "mocha": "2.5.3"}, "homepage": "https://github.com/sbtoledo/url-slug#readme", "keywords": ["slug", "slugs", "slugify", "url", "string", "seo"], "license": "MIT", "main": "index.js", "name": "url-slug", "repository": {"type": "git", "url": "git+https://github.com/sbtoledo/url-slug.git"}, "scripts": {"test": "mocha test"}, "version": "2.0.0"}