{"_from": "url@^0.11.0", "_id": "url@0.11.4", "_inBundle": false, "_integrity": "sha512-oCwdVC7mTuWiPyjLUz/COz5TLk6wgp0RCsN+wHZ2Ekneac9w8uuV0njcbbie2ME+Vs+d6duwmYuR3HgQXs1fOg==", "_location": "/url", "_phantomChildren": {"side-channel": "1.1.0"}, "_requested": {"type": "range", "registry": true, "raw": "url@^0.11.0", "name": "url", "escapedName": "url", "rawSpec": "^0.11.0", "saveSpec": null, "fetchSpec": "^0.11.0"}, "_requiredBy": ["/node-libs-browser", "/webpack-dev-server"], "_resolved": "https://registry.npmmirror.com/url/-/url-0.11.4.tgz", "_shasum": "adca77b3562d56b72746e76b330b7f27b6721f3c", "_spec": "url@^0.11.0", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\node-libs-browser", "author": {"name": "defunctzombie"}, "bugs": {"url": "https://github.com/defunctzombie/node-url/issues"}, "bundleDependencies": false, "dependencies": {"punycode": "^1.4.1", "qs": "^6.12.3"}, "deprecated": false, "description": "The core `url` packaged standalone for use with Browserify.", "devDependencies": {"@ljharb/eslint-config": "^21.1.1", "acorn": "^8.12.1", "aud": "^2.0.4", "eslint": "=8.8.0", "mocha": "^3.5.3", "nyc": "^10.3.2", "zuul": "^3.12.0"}, "engines": {"node": ">= 0.4"}, "homepage": "https://github.com/defunctzombie/node-url#readme", "keywords": ["parsing", "url", "analyze"], "license": "MIT", "main": "./url.js", "name": "url", "repository": {"type": "git", "url": "git+https://github.com/defunctzombie/node-url.git"}, "scripts": {"lint": "eslint .", "posttest": "aud --production", "pretest": "npm run lint", "test": "npm run tests-only", "test-local": "zuul --local -- test/index.js", "tests-only": "nyc mocha", "zuul": "zuul -- test/index.js"}, "version": "0.11.4"}