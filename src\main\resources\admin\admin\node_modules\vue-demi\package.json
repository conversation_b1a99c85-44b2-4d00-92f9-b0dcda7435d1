{"_from": "vue-demi@^0.13.11", "_id": "vue-demi@0.13.11", "_inBundle": false, "_integrity": "sha512-IR8HoEEGM65YY3ZJYAjMlKygDQn25D5ajNFNoKh9RSDMQtlzCxtfQjdQgv9jjK+m3377SsJXY8ysq8kLCZL25A==", "_location": "/vue-demi", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "vue-demi@^0.13.11", "name": "vue-demi", "escapedName": "vue-demi", "rawSpec": "^0.13.11", "saveSpec": null, "fetchSpec": "^0.13.11"}, "_requiredBy": ["/vue-echarts"], "_resolved": "https://registry.npmmirror.com/vue-demi/-/vue-demi-0.13.11.tgz", "_shasum": "7d90369bdae8974d87b1973564ad390182410d99", "_spec": "vue-demi@^0.13.11", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\vue-echarts", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "bugs": {"url": "https://github.com/antfu/vue-demi/issues"}, "bundleDependencies": false, "deprecated": false, "description": "<p align=\"center\"> <img src=\"https://github.com/vueuse/vue-demi/blob/main/assets/banner.png?raw=true\" width=\"600\"/> <br> <a href='https://www.npmjs.com/package/vue-demi'><img src='https://img.shields.io/npm/v/vue-demi?color=42b883' alt='npm'></a> </p>", "engines": {"node": ">=12"}, "exports": {".": {"require": "./lib/index.cjs", "import": "./lib/index.mjs", "browser": "./lib/index.mjs", "types": "./lib/index.d.ts"}, "./*": "./*"}, "files": ["lib", "bin", "scripts"], "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/antfu/vue-demi#readme", "jsdelivr": "lib/index.iife.js", "license": "MIT", "main": "lib/index.cjs", "module": "lib/index.mjs", "name": "vue-demi", "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/antfu/vue-demi.git"}, "scripts": {"postinstall": "node ./scripts/postinstall.js", "release": "npx bumpp --tag --commit --push && npm publish"}, "types": "lib/index.d.ts", "unpkg": "lib/index.iife.js", "version": "0.13.11"}