{"_from": "vue-echarts@^6.2.3", "_id": "vue-echarts@6.7.3", "_inBundle": false, "_integrity": "sha512-vXLKpALFjbPphW9IfQPOVfb1KjGZ/f8qa/FZHi9lZIWzAnQC1DgnmEK3pJgEkyo6EP7UnX6Bv/V3Ke7p+qCNXA==", "_location": "/vue-echarts", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "vue-echarts@^6.2.3", "name": "vue-echarts", "escapedName": "vue-echarts", "rawSpec": "^6.2.3", "saveSpec": null, "fetchSpec": "^6.2.3"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmmirror.com/vue-echarts/-/vue-echarts-6.7.3.tgz", "_shasum": "30efafc51a4a9de1b8117d3b63e74b0c761ff3ba", "_spec": "vue-echarts@^6.2.3", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin", "author": {"name": "GU Yiling", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/ecomfe/vue-echarts/issues"}, "bundleDependencies": false, "dependencies": {"resize-detector": "^0.3.0", "vue-demi": "^0.13.11"}, "deprecated": false, "description": "Vue.js component for Apache ECharts™.", "devDependencies": {"@babel/core": "^7.24.4", "@highlightjs/vue-plugin": "^2.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-replace": "^5.0.5", "@rollup/plugin-terser": "^0.4.4", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "@vercel/analytics": "^1.2.2", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/compiler-sfc": "^3.4.24", "@vue/composition-api": "^1.7.2", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^10.0.0", "@vueuse/core": "^10.9.0", "comment-mark": "^1.1.1", "core-js": "^3.37.0", "echarts": "^5.5.0", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "esbuild-wasm": "^0.19.12", "eslint": "^7.32.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-vue": "^8.7.1", "highlight.js": "^11.9.0", "pinia": "^2.1.7", "postcss": "^8.4.38", "postcss-loader": "^5.3.0", "postcss-nested": "^5.0.6", "prettier": "^2.8.8", "raw-loader": "^4.0.2", "resize-detector": "^0.3.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "rollup-plugin-dts": "^4.2.3", "rollup-plugin-styles": "^4.0.0", "rollup-plugin-ts": "^2.0.7", "tslib": "^2.6.2", "typescript": "4.6.4", "vue": "^3.4.24", "vue2": "npm:vue@^2.7.16", "webpack": "^5.91.0"}, "files": ["dist", "scripts/postinstall.js"], "homepage": "https://github.com/ecomfe/vue-echarts#readme", "jsdelivr": "dist/index.umd.min.js", "license": "MIT", "main": "dist/index.cjs.min.js", "module": "dist/index.esm.min.js", "name": "vue-echarts", "peerDependencies": {"@vue/composition-api": "^1.0.5", "@vue/runtime-core": "^3.0.0", "echarts": "^5.4.1", "vue": "^2.6.12 || ^3.1.1"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}, "@vue/runtime-core": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/ecomfe/vue-echarts.git"}, "scripts": {"build": "pnpm run docs && rimraf dist && pnpm run build:2 && pnpm run build:3 && vue-demi-switch 3", "build:2": "vue-demi-switch 2 vue2 && rollup -c rollup.vue2.config.js", "build:3": "vue-demi-switch 3 && rollup -c rollup.config.js", "build:demo": "vue-cli-service build", "docs": "node ./scripts/docs.js", "lint": "vue-cli-service lint", "postinstall": "node ./scripts/postinstall.js", "serve": "vue-cli-service serve"}, "types": "dist/index.vue2_7.d.ts", "unpkg": "dist/index.umd.min.js", "version": "6.7.3"}