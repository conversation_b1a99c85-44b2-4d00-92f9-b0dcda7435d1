{"_from": "vue-json-excel@^0.3.0", "_id": "vue-json-excel@0.3.0", "_inBundle": false, "_integrity": "sha512-FrSh0tVUpw4K+ilLO8g0Qp52eFJw/hkk3rZPTEKo9qVkJgVfQtZwzj3UWc5ACYxA3jLk9HtjK+f9xKHCN4Kgag==", "_location": "/vue-json-excel", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "vue-json-excel@^0.3.0", "name": "vue-json-excel", "escapedName": "vue-json-excel", "rawSpec": "^0.3.0", "saveSpec": null, "fetchSpec": "^0.3.0"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmmirror.com/vue-json-excel/-/vue-json-excel-0.3.0.tgz", "_shasum": "72cbe4a004720259edc65555a3f072198cf79146", "_spec": "vue-json-excel@^0.3.0", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/jecovier/vue-json-excel/issues"}, "bundleDependencies": false, "dependencies": {"downloadjs": "^1.4.7"}, "deprecated": false, "description": "Download your JSON as an excel or CSV file directly from the browser", "devDependencies": {"rollup": "^1.7.4", "rollup-plugin-commonjs": "^9.2.2", "rollup-plugin-node-resolve": "^4.0.1", "rollup-plugin-vue": "^4.7.2", "vue-template-compiler": "^2.6.10"}, "homepage": "https://github.com/jecovier/vue-json-excel#readme", "keywords": ["vue", "v<PERSON><PERSON><PERSON>", "vue2", "Excel", "xls", "csv", "json", "export", "json excel", "download", "component"], "license": "MIT", "main": "dist/vue-json-excel.umd.js", "module": "dist/vue-json-excel.esm.js", "name": "vue-json-excel", "repository": {"type": "git", "url": "git+https://github.com/jecovier/vue-json-excel.git"}, "scripts": {"build:dist": "rollup -c ./rollup.config.js", "test": "echo \"Error: no test specified\" && exit 1"}, "version": "0.3.0"}