{"_from": "supports-color@^7.1.0", "_id": "supports-color@7.2.0", "_inBundle": false, "_integrity": "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==", "_location": "/vue-loader-v16/supports-color", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "supports-color@^7.1.0", "name": "supports-color", "escapedName": "supports-color", "rawSpec": "^7.1.0", "saveSpec": null, "fetchSpec": "^7.1.0"}, "_requiredBy": ["/vue-loader-v16/chalk"], "_resolved": "https://registry.npmmirror.com/supports-color/-/supports-color-7.2.0.tgz", "_shasum": "1b7dcdcb32b8138801b3e478ba6a51caa89648da", "_spec": "supports-color@^7.1.0", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\vue-loader-v16\\node_modules\\chalk", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "browser": "browser.js", "bugs": {"url": "https://github.com/chalk/supports-color/issues"}, "bundleDependencies": false, "dependencies": {"has-flag": "^4.0.0"}, "deprecated": false, "description": "Detect whether a terminal supports color", "devDependencies": {"ava": "^1.4.1", "import-fresh": "^3.0.0", "xo": "^0.24.0"}, "engines": {"node": ">=8"}, "files": ["index.js", "browser.js"], "homepage": "https://github.com/chalk/supports-color#readme", "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "license": "MIT", "name": "supports-color", "repository": {"type": "git", "url": "git+https://github.com/chalk/supports-color.git"}, "scripts": {"test": "xo && ava"}, "version": "7.2.0"}