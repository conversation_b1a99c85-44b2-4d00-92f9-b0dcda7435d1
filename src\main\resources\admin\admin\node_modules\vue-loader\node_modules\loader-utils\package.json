{"_from": "loader-utils@^1.1.0", "_id": "loader-utils@1.4.2", "_inBundle": false, "_integrity": "sha512-I5d00Pd/jwMD2QCduo657+YM/6L3KZu++pmX9VFncxaxvHcru9jx1lBaFft+r4Mt2jK0Yhp41XlRAihzPxHNCg==", "_location": "/vue-loader/loader-utils", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "loader-utils@^1.1.0", "name": "loader-utils", "escapedName": "loader-utils", "rawSpec": "^1.1.0", "saveSpec": null, "fetchSpec": "^1.1.0"}, "_requiredBy": ["/vue-loader"], "_resolved": "https://registry.npmmirror.com/loader-utils/-/loader-utils-1.4.2.tgz", "_shasum": "29a957f3a63973883eb684f10ffd3d151fec01a3", "_spec": "loader-utils@^1.1.0", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\vue-loader", "author": {"name": "<PERSON> @sokra"}, "bugs": {"url": "https://github.com/webpack/loader-utils/issues"}, "bundleDependencies": false, "dependencies": {"big.js": "^5.2.2", "emojis-list": "^3.0.0", "json5": "^1.0.1"}, "deprecated": false, "description": "utils for webpack loaders", "devDependencies": {"coveralls": "^3.0.2", "eslint": "^5.11.0", "eslint-plugin-node": "^8.0.0", "eslint-plugin-prettier": "^3.0.0", "jest": "^21.2.1", "prettier": "^1.19.1", "standard-version": "^4.0.0"}, "engines": {"node": ">=4.0.0"}, "files": ["lib"], "homepage": "https://github.com/webpack/loader-utils#readme", "license": "MIT", "main": "lib/index.js", "name": "loader-utils", "repository": {"type": "git", "url": "git+https://github.com/webpack/loader-utils.git"}, "scripts": {"lint": "eslint lib test", "pretest": "yarn lint", "release": "yarn test && standard-version", "test": "jest", "test:ci": "jest --coverage"}, "version": "1.4.2"}