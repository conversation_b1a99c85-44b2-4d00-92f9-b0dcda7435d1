{"_from": "vue-loader@^15.9.2", "_id": "vue-loader@15.11.1", "_inBundle": false, "_integrity": "sha512-0iw4VchYLePqJfJu9s62ACWUXeSqM30SQqlIftbYWM3C+jpPcEHKSPUZBLjSF9au4HTHQ/naF6OGnO3Q/qGR3Q==", "_location": "/vue-loader", "_phantomChildren": {"big.js": "5.2.2", "emojis-list": "3.0.0", "minimist": "1.2.8"}, "_requested": {"type": "range", "registry": true, "raw": "vue-loader@^15.9.2", "name": "vue-loader", "escapedName": "vue-loader", "rawSpec": "^15.9.2", "saveSpec": null, "fetchSpec": "^15.9.2"}, "_requiredBy": ["/@vue/cli-service"], "_resolved": "https://registry.npmmirror.com/vue-loader/-/vue-loader-15.11.1.tgz", "_shasum": "dee91169211276ed43c5715caef88a56b1f497b0", "_spec": "vue-loader@^15.9.2", "_where": "G:\\Develop\\springboot\\公司财务管理系统\\admin\\node_modules\\@vue\\cli-service", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/vue-loader/issues"}, "bundleDependencies": false, "dependencies": {"@vue/component-compiler-utils": "^3.1.0", "hash-sum": "^1.0.2", "loader-utils": "^1.1.0", "vue-hot-reload-api": "^2.3.0", "vue-style-loader": "^4.1.0"}, "deprecated": false, "description": "Vue single-file component loader for Webpack", "devDependencies": {"@types/webpack": "^4.4.27", "babel-core": "^6.26.0", "babel-loader": "^7.1.4", "babel-preset-env": "^1.6.1", "cache-loader": "^2.0.1", "conventional-changelog-cli": "^1.3.22", "cross-env": "^7.0.3", "css-loader": "^1.0.0", "eslint": "^4.19.0", "eslint-plugin-vue-libs": "^2.1.0", "file-loader": "^1.1.11", "html-webpack-plugin": "^4.0.0", "javascript-stringify": "^1.6.0", "jest": "^23.5.0", "jsdom": "^11.6.2", "json": "^9.0.6", "lint-staged": "^7.0.0", "markdown-loader": "^2.0.2", "memfs": "^3.1.2", "mini-css-extract-plugin": "^0.4.1", "normalize-newline": "^3.0.0", "null-loader": "^0.1.1", "postcss-loader": "^2.1.2", "pug": "^2.0.1", "pug-plain-loader": "^1.0.0", "raw-loader": "^0.5.1", "source-map": "^0.5.0", "stylus": "^0.54.5", "stylus-loader": "^3.0.2", "sugarss": "^1.0.1", "thread-loader": "^3.0.4", "ts-loader": "^4.2.0", "typescript": "^4.8.4", "url-loader": "^1.0.1", "vue": "^2.7.14", "vue-server-renderer": "^2.7.14", "vue-template-compiler": "^2.7.14", "vuepress": "^0.14.2", "vuepress-theme-vue": "^1.1.0", "webpack": "^4.46.0", "webpack-cli": "^3.2.0", "webpack-dev-server": "^3.1.1", "webpack-merge": "^4.1.2", "yorkie": "^1.0.3"}, "gitHooks": {"pre-commit": "lint-staged"}, "homepage": "https://github.com/vuejs/vue-loader", "license": "MIT", "lint-staged": {"lib/**/*.js": ["eslint --fix", "git add"], "test/**/*.js": ["eslint --fix", "git add"]}, "main": "lib/index.js", "name": "vue-loader", "packageManager": "pnpm@8.9.2", "peerDependencies": {"css-loader": "*", "webpack": "^3.0.0 || ^4.1.0 || ^5.0.0-0"}, "peerDependenciesMeta": {"cache-loader": {"optional": true}, "vue-template-compiler": {"optional": true}, "prettier": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-loader.git"}, "scripts": {"build": "webpack --config example/webpack.config.js --hide-modules", "dev": "webpack-dev-server --config example/webpack.config.js --inline --hot", "docs": "vuepress dev docs", "docs:build": "vuepress build docs", "lint": "eslint lib test --fix", "test": "jest --env node", "test:match-resource": "cross-env INLINE_MATCH_RESOURCE=true jest --env node"}, "typings": "lib/index.d.ts", "version": "15.11.1"}